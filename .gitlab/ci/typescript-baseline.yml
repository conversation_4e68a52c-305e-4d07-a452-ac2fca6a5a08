# TypeScript 错误基线检查
# 该配置实现了 TypeScript 错误数量的基线检查机制
# 目标：确保新的合并请求不会引入更多的 TypeScript 错误
#
# 逃生舱机制使用说明:
# 如果由于特殊情况需要忽略 TypeScript 错误基线检查失败，可通过以下方式触发：
# 1. 在 MR 的描述中添加 [ignore-ts-errors] 标记
# 2. 在提交信息中包含 [ignore-ts-errors] 标记
# 注意: 此机制仅作为临时应急使用，不应长期依赖

# ===== TypeScript 错误基线配置 =====

# TypeScript 错误基线检查 - 完整流程
# 在一个任务中完成所有步骤：获取基线、检查错误、更新基线
typescript_baseline_check:
  # 继承 pipeline 中定义的 linux docker job 配置
  extends: .linux-docker-job
  # id_tokens:
  #   !reference [.vault, token]
  stage: test
  variables:
    PACKAGE_NAME: '${CI_PROJECT_PATH_SLUG}-ts-baseline'
    # 修正版本名：将分支名中的斜杠替换为下划线以符合 GitLab Package Registry 要求
    CLEAN_BRANCH_NAME: "${CI_COMMIT_REF_NAME//\//_}"
    # 根据分支策略设置基线更新条件
    # 仅在 main 分支或 release/* 分支上更新基线
    FORCE_UPDATE_BASELINE: 'false'
    # 是否忽略 TypeScript 错误基线检查结果（应急使用，默认为 false）
    IGNORE_TS_ERRORS: 'false'
    # 是否输出 zx 命令详细信息
    ZX_VERBOSE: 'false'
    # 调试信息（设置为 1 开启更多日志）
    # TS_BASELINE_DEBUG: "1"
  before_script:
    # 确保 Node.js 和 npm 可用
    - node -v
    - npm -v
    # 全局安装 zx（解决依赖问题）
    - npm install -g zx
    # 检查已安装的 zx 版本
    - zx --version
  script:
    # 检查 MR 描述或提交信息中是否有忽略标记
    - |
      if [[ -n "$CI_MERGE_REQUEST_DESCRIPTION" && "$CI_MERGE_REQUEST_DESCRIPTION" == *"[ignore-ts-errors]"* ]]; then
        echo "检测到 MR 描述中包含 [ignore-ts-errors] 标记，将忽略 TypeScript 错误检查失败"
        export IGNORE_TS_ERRORS="true"
      elif [[ -n "$CI_COMMIT_MESSAGE" && "$CI_COMMIT_MESSAGE" == *"[ignore-ts-errors]"* ]]; then
        echo "检测到提交信息中包含 [ignore-ts-errors] 标记，将忽略 TypeScript 错误检查失败"
        export IGNORE_TS_ERRORS="true"
      fi

    # 一体化 TypeScript 错误基线检查流程
    - echo "[TSBaseline] 开始执行 TypeScript 错误基线检查"

    # 步骤 1: 依赖安装（使用 CI 的重试机制）
    - |
      echo "[TSBaseline] 安装 NPM 依赖..."
      export SKIP_INSTALL_SIMPLE_GIT_HOOKS=1
      npm ci || npm ci || npm ci

    # 步骤 2: 处理 proto 依赖（如果存在）
    - |
      if grep -q '"proto:check"' package.json; then
        echo "[TSBaseline] 执行 proto:check 脚本 (仅生成 web 平台代码)..."
        npm run proto:check -- -p web || npm run proto:check -- -p web || exit 1
      fi

    # 步骤 3: 执行 expo 生成命令
    - |
      echo "[TSBaseline] 执行 expo generate 命令..."
      npx expo customize tsconfig.json || exit 1

    # 步骤 4: 执行 TypeScript 错误基线检查（使用内置重试机制）
    - echo "[TSBaseline] 执行 TypeScript 错误基线检查..."
    - chmod +x scripts/ts-baseline-check.mjs || true
    - |
      set +e  # 允许命令失败

      # 简单执行脚本，复杂逻辑已移到脚本内部
      npx zx scripts/ts-baseline-check.mjs \
        --packageName="${PACKAGE_NAME}" \
        --token="${CI_JOB_TOKEN}" \
        --forceUploadOnFailure="${FORCE_UPLOAD_ON_FAILURE}"

      ZX_EXIT_CODE=$?  # 捕获退出码
      set -e  # 恢复错误检查

      echo "退出码: ${ZX_EXIT_CODE}"

      # 根据检查结果和忽略标记决定任务是否失败
      if [ $ZX_EXIT_CODE -eq 0 ]; then
        echo "✅ TypeScript 错误基线检查通过"
      else
        if [[ "${IGNORE_TS_ERRORS}" == "true" ]]; then
          echo "⚠️ 警告: TypeScript 错误基线检查失败，但已设置忽略标记，继续执行"
        else
          echo "❌ TypeScript 错误基线检查失败"
          exit $ZX_EXIT_CODE
        fi
      fi

  # 使用 GitLab 内置的测试报告功能
  artifacts:
    when: always
    reports:
      junit:
        - ts_error_report.xml # 基准分支对比报告
        - current_ts_error_report.xml # 分支历史对比报告
    paths:
      - ts_error_report.txt # 详细文本报告
      - ts_errors.json # 当前错误数据文件
      - baseline_reference_ts_errors.json # 基准分支基线文件
      - baseline_current_ts_errors.json # 当前分支基线文件

  # 触发条件配置
  rules:
    # 当向 main 分支提交 MR 时触发
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"
      when: always

    # 当向任何 release/* 分支提交 MR 时触发
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release\//
      when: always

    # 当在相关 MR 上提交代码时触发（针对 main 分支）
    - if: $CI_COMMIT_REF_NAME == "main"
      when: always

    # 当在相关 MR 上提交代码时触发（针对 release/* 分支）
    - if: $CI_COMMIT_REF_NAME =~ /^release\//
      when: always

    # 当提交消息包含 [ts-check] 标记时触发（支持在任意分支上使用）
    - if: $CI_COMMIT_MESSAGE =~ /\[ts-check\]/
      when: always

    # 允许手动触发（通过 GitLab UI）
    - if: $CI_PIPELINE_SOURCE == "web"
      when: always

  # 作业级别重试，仅针对系统或网络故障
  retry:
    max: 2
    when:
      - runner_system_failure
      - api_failure

  tags:
    - mobile
    - online
    - linux
