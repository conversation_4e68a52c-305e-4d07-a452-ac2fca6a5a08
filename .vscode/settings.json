{"[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "editor.formatOnType": true, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "cSpell.words": ["livephoto"], "typescript.tsdk": "node_modules/typescript/lib", "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}