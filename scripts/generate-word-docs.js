#!/usr/bin/env node

/**
 * 使用docx库生成标准格式的Word文档
 * 解决换行和字号统一问题
 */

const fs = require('fs');
const path = require('path');
const { Document, Packer, Paragraph, TextRun, HeadingLevel } = require('docx');

// 常量定义
const MAX_PAGES = 80;
const LINES_PER_PAGE = 51;
const MAX_LINES = MAX_PAGES * LINES_PER_PAGE; // 4080行
const BUSINESS_MIN_LINES = 2500; // 业务代码最少2500行
const BUSINESS_MAX_LINES = 3400; // 业务代码最多3400行
const NATIVE_MIN_LINES = MAX_LINES - BUSINESS_MAX_LINES; // 原生代码最少680行
const NATIVE_MAX_LINES = MAX_LINES - BUSINESS_MIN_LINES; // 原生代码最多1580行

// 文件信息缓存
const fileInfoCache = new Map();

// 已使用的原生文件缓存（避免重复使用）
const usedNativeFiles = new Set();

// 读取配置文件
function loadConfig() {
  const configPath = path.resolve(__dirname, 'software-copyright-config.json');
  if (!fs.existsSync(configPath)) {
    throw new Error(`配置文件不存在: ${configPath}`);
  }

  try {
    const configContent = fs.readFileSync(configPath, 'utf-8');
    return JSON.parse(configContent);
  } catch (error) {
    throw new Error(`配置文件解析失败: ${error.message}`);
  }
}

/**
 * 根据文件扩展名推断编程语言
 */
function getLanguageFromExtension(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const languageMap = {
    '.ts': 'TypeScript',
    '.tsx': 'TypeScript',
    '.js': 'JavaScript', 
    '.jsx': 'JavaScript',
    '.swift': 'Swift',
    '.kt': 'Kotlin',
    '.java': 'Java',
    '.m': 'Objective-C',
    '.mm': 'Objective-C',
    '.h': 'Objective-C',
    '.rb': 'Ruby',
    '.gradle': 'Gradle',
    '.plist': 'XML',
    '.json': 'JSON'
  };
  
  return languageMap[ext] || 'Unknown';
}

/**
 * 获取文件完整路径
 */
function getFullPath(filePath) {
  if (path.isAbsolute(filePath)) {
    return filePath;
  } else {
    return path.resolve(process.cwd(), filePath);
  }
}

/**
 * 使用wc命令快速计算文件行数
 */
function countLinesWithWc(fullPath) {
  try {
    const { execSync } = require('child_process');
    const result = execSync(`wc -l "${fullPath}"`, { encoding: 'utf-8' });
    const lines = parseInt(result.trim().split(/\s+/)[0]);
    return lines || 0;
  } catch (error) {
    // 如果wc命令失败，回退到读取文件方式
    try {
      const content = fs.readFileSync(fullPath, 'utf-8');
      return content.split('\n').length;
    } catch (readError) {
      return 0;
    }
  }
}

/**
 * 获取文件信息（带缓存）
 */
function getFileInfo(filePath) {
  if (fileInfoCache.has(filePath)) {
    console.log(`  📁 缓存命中: ${filePath}`);
    return fileInfoCache.get(filePath);
  }

  console.log(`  📄 读取文件: ${filePath}`);
  try {
    const fullPath = getFullPath(filePath);

    if (!fs.existsSync(fullPath)) {
      console.log(`  ⚠️  文件不存在: ${fullPath}`);
      const info = { lines: 0, language: 'Unknown', exists: false };
      fileInfoCache.set(filePath, info);
      return info;
    }

    // 使用wc命令快速计算行数
    const lines = countLinesWithWc(fullPath);
    const language = getLanguageFromExtension(filePath);
    
    console.log(`  ✅ 文件信息获取成功: ${filePath} (${lines}行, ${language})`);
    const info = { lines, language, exists: true };
    fileInfoCache.set(filePath, info);
    return info;
  } catch (error) {
    console.error(`  ❌ 获取文件信息失败 ${filePath}: ${error.message}`);
    const info = { lines: 0, language: 'Unknown', exists: false };
    fileInfoCache.set(filePath, info);
    return info;
  }
}

/**
 * 批量获取文件信息（并行处理）
 */
async function batchGetFileInfo(files) {
  console.log(`🔍 开始批量获取 ${files.length} 个文件信息...`);
  const startTime = Date.now();
  
  const promises = files.map((filePath, index) => {
    return new Promise((resolve) => {
      console.log(`[${index + 1}/${files.length}] 处理文件: ${filePath}`);
      const info = getFileInfo(filePath);
      resolve({ path: filePath, ...info });
    });
  });

  const results = await Promise.all(promises);
  const endTime = Date.now();
  console.log(`✅ 批量获取文件信息完成，耗时: ${endTime - startTime}ms`);
  
  return results;
}

/**
 * 随机选择原生代码文件（带去重逻辑）
 */
async function selectRandomNativeFiles(nativeFiles, platform, targetLines) {
  console.log(`🎲 随机选择${platform}原生代码，目标行数: ${targetLines}`);
  
  const platformFiles = nativeFiles[platform] || [];
  if (platformFiles.length === 0) {
    console.log(`⚠️  ${platform}平台没有原生代码文件`);
    return { selectedFiles: [], removedFiles: [], totalLines: 0 };
  }

  // 获取所有文件信息
  const fileInfos = await batchGetFileInfo(platformFiles);
  const validFiles = fileInfos.filter(info => info.exists && info.lines > 0);

  if (validFiles.length === 0) {
    console.log(`⚠️  ${platform}平台没有有效的原生代码文件`);
    return { selectedFiles: [], removedFiles: [], totalLines: 0 };
  }

  // 过滤已使用的文件
  const unusedFiles = validFiles.filter(info => !usedNativeFiles.has(info.path));
  
  if (unusedFiles.length === 0) {
    console.log(`⚠️  警告: ${platform}平台原生文件不足，所有文件已被使用，将重复使用部分文件`);
    // 如果所有文件都已使用，则重新使用所有文件
    unusedFiles.push(...validFiles);
  }

  // 随机打乱文件顺序
  const shuffledFiles = [...unusedFiles].sort(() => Math.random() - 0.5);
  
  const selectedFiles = [];
  const removedFiles = [];
  let currentLines = 0;

  // 随机选择文件直到达到行数目标（严格控制不超过）
  for (const fileInfo of shuffledFiles) {
    if (currentLines + fileInfo.lines <= targetLines) {
      selectedFiles.push(fileInfo);
      currentLines += fileInfo.lines;
      usedNativeFiles.add(fileInfo.path); // 标记为已使用
      console.log(`  ✅ 随机选择: ${fileInfo.path} (${fileInfo.lines}行), 累计: ${currentLines}行`);
    } else {
      removedFiles.push(fileInfo);
      console.log(`  ❌ 跳过: ${fileInfo.path} (${fileInfo.lines}行, 会超出限制)`);
    }
  }
  
  // 将剩余文件添加到移除列表
  const remainingFiles = shuffledFiles.slice(selectedFiles.length);
  removedFiles.push(...remainingFiles);

  console.log(`🎲 随机选择完成，${platform}平台选中 ${selectedFiles.length} 个文件，总行数: ${currentLines}`);
  
  return { selectedFiles, removedFiles, totalLines: currentLines };
}

/**
 * 业务逻辑文件选择算法：从第一个文件开始添加，直到达到目标行数范围
 */
async function selectBusinessLogicFiles(files, minLines, maxLines) {
  console.log(`📋 业务逻辑文件选择，目标范围: ${minLines}-${maxLines}行`);
  
  // 并行获取所有文件信息
  const fileInfos = await batchGetFileInfo(files);
  const validFiles = fileInfos.filter(info => info.exists && info.lines > 0);

  console.log(`📊 有效文件数量: ${validFiles.length}/${files.length}`);

  if (validFiles.length === 0) {
    console.log(`⚠️  没有有效文件，跳过选择`);
    return { selectedFiles: [], removedFiles: [], totalLines: 0 };
  }

  const selectedFiles = [];
  const removedFiles = [];
  let currentLines = 0;

  // 从第一个文件开始逐个添加
  for (const fileInfo of validFiles) {
    if (currentLines < minLines || (currentLines + fileInfo.lines <= maxLines)) {
      selectedFiles.push(fileInfo);
      currentLines += fileInfo.lines;
      console.log(`  ✅ 选择: ${fileInfo.path} (${fileInfo.lines}行), 累计: ${currentLines}行`);
      
      // 如果已达到最小要求且再加下一个会超过最大值，停止
      if (currentLines >= minLines) {
        const remainingFiles = validFiles.slice(selectedFiles.length);
        const nextFile = remainingFiles[0];
        if (nextFile && currentLines + nextFile.lines > maxLines) {
          break;
        }
      }
    } else {
      removedFiles.push(fileInfo);
      console.log(`  ❌ 跳过: ${fileInfo.path} (${fileInfo.lines}行, 会超出限制)`);
    }
  }

  // 添加剩余文件到移除列表
  const remainingFiles = validFiles.slice(selectedFiles.length);
  removedFiles.push(...remainingFiles);

  console.log(`📊 业务逻辑选择完成，共选择 ${selectedFiles.length} 个文件，总行数: ${currentLines}`);
  console.log(`🗑️  移除 ${removedFiles.length} 个文件`);

  return {
    selectedFiles,
    removedFiles, 
    totalLines: currentLines
  };
}

/**
 * 简化的文件选择算法：只需要检查是否超过限制
 */
async function selectFiles(files, maxLines) {
  console.log(`⚖️  开始文件选择，目标最大行数: ${maxLines}`);
  
  // 并行获取所有文件信息
  const fileInfos = await batchGetFileInfo(files);
  const validFiles = fileInfos.filter(info => info.exists && info.lines > 0);

  console.log(`📊 有效文件数量: ${validFiles.length}/${files.length}`);

  if (validFiles.length === 0) {
    console.log(`⚠️  没有有效文件，跳过选择`);
    return { selectedFiles: [], removedFiles: [], totalLines: 0 };
  }

  // 计算总行数
  const totalLines = validFiles.reduce((sum, info) => sum + info.lines, 0);
  console.log(`📈 总行数: ${totalLines}, 限制: ${maxLines}`);
  
  if (totalLines <= maxLines) {
    console.log(`✅ 总行数未超限，选择所有文件`);
    return {
      selectedFiles: validFiles,
      removedFiles: [],
      totalLines
    };
  }

  console.log(`🎯 超出限制，需要截取文件...`);
  
  // 简单策略：从前往后选择文件，直到接近限制
  const selectedFiles = [];
  const removedFiles = [];
  let currentLines = 0;

  for (const fileInfo of validFiles) {
    if (currentLines + fileInfo.lines <= maxLines) {
      selectedFiles.push(fileInfo);
      currentLines += fileInfo.lines;
      console.log(`  ✅ 选择: ${fileInfo.path} (${fileInfo.lines}行), 累计: ${currentLines}行`);
    } else {
      removedFiles.push(fileInfo);
      console.log(`  ❌ 跳过: ${fileInfo.path} (${fileInfo.lines}行, 会超出限制)`);
    }
  }

  console.log(`📊 选择完成，共选择 ${selectedFiles.length} 个文件，总行数: ${currentLines}`);
  console.log(`🗑️  移除 ${removedFiles.length} 个文件`);

  return {
    selectedFiles,
    removedFiles, 
    totalLines: currentLines
  };
}

/**
 * 读取文件内容（支持绝对路径和相对路径）
 */
function readFileContent(filePath) {
  try {
    let fullPath;

    // 判断是否为绝对路径
    if (path.isAbsolute(filePath)) {
      fullPath = filePath;
    } else {
      fullPath = path.resolve(process.cwd(), filePath);
    }

    if (!fs.existsSync(fullPath)) {
      console.warn(`文件不存在: ${fullPath}`);
      return '';
    }

    const content = fs.readFileSync(fullPath, 'utf-8');
    return content;
  } catch (error) {
    console.error(`读取文件失败 ${filePath}: ${error.message}`);
    return '';
  }
}

/**
 * 为单个玩法生成Word文档（新架构：业务逻辑+原生代码）
 */
async function generateGameWordDoc(gameName, platform, businessFiles, nativeFiles) {
  const paragraphs = [];

  console.log(`📝 开始生成 ${gameName} - ${platform}端文档...`);

  // 第一部分：选择业务逻辑代码（从第一个文件开始添加）
  console.log(`📚 第一部分：业务逻辑代码（${BUSINESS_MIN_LINES}-${BUSINESS_MAX_LINES}行）`);
  const { selectedFiles: businessSelected, totalLines: businessLines } = await selectBusinessLogicFiles(businessFiles, BUSINESS_MIN_LINES, BUSINESS_MAX_LINES);

  // 计算原生代码需要的行数（严格控制在总行数以内）
  const remainingLines = MAX_LINES - businessLines;
  const targetNativeLines = Math.min(remainingLines, NATIVE_MAX_LINES);
  
  console.log(`📈 业务逻辑已使用 ${businessLines} 行，剩余 ${remainingLines} 行用于原生代码`);
  
  // 第二部分：随机选择原生代码
  console.log(`🔧 第二部分：原生插桩代码（目标${targetNativeLines}行）`);
  const { selectedFiles: nativeSelected } = await selectRandomNativeFiles(nativeFiles, platform, targetNativeLines);

  // 合并所有选中的文件
  const allSelectedFiles = [...businessSelected, ...nativeSelected];
  
  console.log(`📊 总计选中文件: ${allSelectedFiles.length} 个`);
  console.log(`   - 业务逻辑: ${businessSelected.length} 个文件`);
  console.log(`   - 原生代码: ${nativeSelected.length} 个文件`);

  // 处理选中的文件内容，只有代码，没有其他内容
  for (const fileInfo of allSelectedFiles) {
    const filePath = fileInfo.path;
    const content = readFileContent(filePath);
    if (content.trim()) {
      // 将内容按行分割并添加到文档
      const lines = content.split('\n');
      for (const line of lines) {
        paragraphs.push(
          new Paragraph({
            children: [
              new TextRun({
                text: line,
                font: '等线',
                size: 18 // 增大字号
              })
            ],
            spacing: {
              line: 200 // 设置行高为1.5倍行距
            }
          })
        );
      }

      // 文件间空行
      paragraphs.push(new Paragraph({}));
      paragraphs.push(new Paragraph({}));
    }
  }

  // 如果没有内容，返回null
  if (paragraphs.length === 0) {
    console.log(`⚠️  ${gameName} - ${platform}端 没有有效内容`);
    return { doc: null, businessSelected, nativeSelected, totalLines: 0 };
  }

  // 创建文档
  const doc = new Document({
    sections: [
      {
        properties: {},
        children: paragraphs
      }
    ]
  });

  const actualBusinessLines = businessSelected.reduce((sum, f) => sum + f.lines, 0);
  const actualNativeLines = nativeSelected.reduce((sum, f) => sum + f.lines, 0);
  const totalLines = actualBusinessLines + actualNativeLines;

  console.log(`✅ ${gameName} - ${platform}端 文档生成完成，总行数: ${totalLines}`);
  console.log(`  📚 业务逻辑: ${actualBusinessLines}行, 🔧 原生代码: ${actualNativeLines}行`);
  
  // 检查是否超过限制
  if (totalLines > MAX_LINES) {
    console.log(`⚠️  警告: 总行数 ${totalLines} 超过限制 ${MAX_LINES} 行！`);
  }
  
  return { doc, businessSelected, nativeSelected, totalLines };
}

/**
 * 主函数
 */
async function main() {
  console.log('开始生成标准格式的Word文档...');

  try {
    // 读取配置文件
    const config = loadConfig();
    console.log(`✅ 已加载配置文件，共 ${config.games.length} 个游戏`);

    const outputDir = path.resolve(process.cwd(), '软著Word文档');
    const reportData = {
      generatedAt: new Date().toISOString(),
      maxPages: MAX_PAGES,
      linesPerPage: LINES_PER_PAGE,
      maxLines: MAX_LINES,
      games: []
    };

    // 清理旧文件
    if (fs.existsSync(outputDir)) {
      console.log('清理之前生成的文件...');
      fs.rmSync(outputDir, { recursive: true, force: true });
    }

    // 创建输出目录
    fs.mkdirSync(outputDir, { recursive: true });

    let fileIndex = 1;

    // 为每个游戏的每个平台生成Word文档
    for (const game of config.games) {
      const platforms = ['iOS', 'Android']; // 移除小程序
      const gameReport = {
        name: game.name,
        platforms: {}
      };

      const businessFiles = game.businessLogic || [];
      if (businessFiles.length === 0) {
        console.log(`⏭️  ${game.name} 未配置业务逻辑文件，跳过`);
        continue;
      }

      for (const platform of platforms) {
        try {
          console.log(`\n🎮 正在生成 ${game.name} - ${platform}端...`);
          console.log(`📁 业务逻辑文件数量: ${businessFiles.length}`);

          const startTime = Date.now();
          
          // 使用新的生成函数
          const result = await generateGameWordDoc(
            game.name,
            platform,
            businessFiles,
            config.nativeCode
          );
          
          const endTime = Date.now();
          console.log(`⏱️  文档生成完成，耗时: ${endTime - startTime}ms`);
          
          const { doc, businessSelected, nativeSelected, totalLines } = result;

          // 统计编程语言
          const allSelected = [...businessSelected, ...nativeSelected];
          const languages = {};
          allSelected.forEach(fileInfo => {
            const lang = fileInfo.language;
            languages[lang] = (languages[lang] || 0) + 1;
          });

          // 计算推定页数
          const estimatedPages = Math.ceil(totalLines / LINES_PER_PAGE);

          // 记录平台报告
          gameReport.platforms[platform] = {
            totalLines,
            estimatedPages,
            businessLogicFiles: businessSelected.length,
            nativeCodeFiles: nativeSelected.length,
            businessLogicLines: businessSelected.reduce((sum, f) => sum + f.lines, 0),
            nativeCodeLines: nativeSelected.reduce((sum, f) => sum + f.lines, 0),
            selectedFiles: allSelected.map(f => ({
              path: f.path, 
              lines: f.lines,
              language: f.language,
              type: businessSelected.includes(f) ? 'business' : 'native'
            })),
            languages,
            generated: !!doc
          };

          if (doc) {
            const buffer = await Packer.toBuffer(doc);

            const fileName = `${String(fileIndex).padStart(2, '0')}-${game.name.replace(/[/\\:*?"<>|]/g, '-')}-${platform}端代码.docx`;
            const outputPath = path.join(outputDir, fileName);

            fs.writeFileSync(outputPath, buffer);
            console.log(`✅ ${game.name} - ${platform}端 Word文档已生成 (${totalLines}行, ~${estimatedPages}页)`);
            console.log(`   📚 业务逻辑: ${businessSelected.length}个文件, ${gameReport.platforms[platform].businessLogicLines}行`)
            console.log(`   🔧 原生代码: ${nativeSelected.length}个文件, ${gameReport.platforms[platform].nativeCodeLines}行`)
            fileIndex++;
          } else {
            console.log(
              `⚠️  ${game.name} - ${platform}端 没有有效内容，跳过生成`
            );
          }
        } catch (error) {
          console.error(
            `❌ 生成 ${game.name} - ${platform}端 失败:`,
            error.message
          );
          
          gameReport.platforms[platform] = {
            error: error.message,
            generated: false
          };
        }
      }

      reportData.games.push(gameReport);
    }

    // 生成JSON报告
    const reportPath = path.join(outputDir, 'generation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2), 'utf-8');

    console.log(`\n📁 所有Word文档已生成到目录: ${outputDir}`);
    console.log(`📊 生成报告已保存: ${reportPath}`);
    console.log(`📝 文档特点:`);
    console.log(`   - 统一字号: 18pt (调整后)`);
    console.log(`   - 等线字体`);
    console.log(`   - 行高: 1.5倍行距`);
    console.log(`   - 每页精确 ${LINES_PER_PAGE} 行`);
    console.log(`   - 最多 ${MAX_PAGES} 页 (${MAX_LINES} 行)`);
    console.log(`   - 自动文件选择算法`);
    console.log(`   - 保持文件完整性`);
    console.log(`   - 包含详细生成报告`);
  } catch (error) {
    console.error(`❌ 生成失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { generateGameWordDoc, loadConfig };
