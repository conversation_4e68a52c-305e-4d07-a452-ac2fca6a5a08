// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file recsys/aigcrec/aigcrec.proto (package step.recsys.aigcrec, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message step.recsys.aigcrec.SendKafkaMsgRequest
 */
export declare class SendKafkaMsgRequest extends Message<SendKafkaMsgRequest> {
  /**
   * @generated from field: string topic = 1;
   */
  topic: string;

  /**
   * @generated from field: string msg = 2;
   */
  msg: string;

  constructor(data?: PartialMessage<SendKafkaMsgRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.recsys.aigcrec.SendKafkaMsgRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendKafkaMsgRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendKafkaMsgRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendKafkaMsgRequest;

  static equals(a: SendKafkaMsgRequest | PlainMessage<SendKafkaMsgRequest> | undefined, b: SendKafkaMsgRequest | PlainMessage<SendKafkaMsgRequest> | undefined): boolean;
}

/**
 * @generated from message step.recsys.aigcrec.SendKafkaMsgResponse
 */
export declare class SendKafkaMsgResponse extends Message<SendKafkaMsgResponse> {
  constructor(data?: PartialMessage<SendKafkaMsgResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.recsys.aigcrec.SendKafkaMsgResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendKafkaMsgResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendKafkaMsgResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendKafkaMsgResponse;

  static equals(a: SendKafkaMsgResponse | PlainMessage<SendKafkaMsgResponse> | undefined, b: SendKafkaMsgResponse | PlainMessage<SendKafkaMsgResponse> | undefined): boolean;
}

