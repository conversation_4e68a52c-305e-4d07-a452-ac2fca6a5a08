// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file recsys/aigcrec/aigcrec.proto (package step.recsys.aigcrec, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { RecRequest, RecResponse } from "../common/common_pb.js";
import { MethodKind } from "@bufbuild/protobuf";
import { SendKafkaMsgRequest, SendKafkaMsgResponse } from "./aigcrec_pb.js";

/**
 * RecService handles
 *
 * @generated from service step.recsys.aigcrec.RecService
 */
export declare const RecService: {
  readonly typeName: "step.recsys.aigcrec.RecService",
  readonly methods: {
    /**
     * @generated from rpc step.recsys.aigcrec.RecService.Rec
     */
    readonly rec: {
      readonly name: "<PERSON><PERSON>",
      readonly I: typeof RecRequest,
      readonly O: typeof RecResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.recsys.aigcrec.RecService.SendKafkaMsg
     */
    readonly sendKafkaMsg: {
      readonly name: "SendKafkaMsg",
      readonly I: typeof SendKafkaMsgRequest,
      readonly O: typeof SendKafkaMsgResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

