// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file recsys/abtest/abtest.proto (package step.recsys.abtest, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * 分配全场景变体请求
 *
 * @generated from message step.recsys.abtest.AssignAllVariantRequest
 */
export declare class AssignAllVariantRequest extends Message<AssignAllVariantRequest> {
  /**
   * 用户信息，如果不传会尝试从Header获取用户信息，oasis-XXX
   *
   * @generated from field: optional step.recsys.abtest.User user = 1;
   */
  user?: User;

  /**
   * 实验类型：1-服务端，2-算法，3-客户端，不传时默认为客户端
   *
   * @generated from field: optional int32 expType = 2;
   */
  expType?: number;

  constructor(data?: PartialMessage<AssignAllVariantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.recsys.abtest.AssignAllVariantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AssignAllVariantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AssignAllVariantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AssignAllVariantRequest;

  static equals(a: AssignAllVariantRequest | PlainMessage<AssignAllVariantRequest> | undefined, b: AssignAllVariantRequest | PlainMessage<AssignAllVariantRequest> | undefined): boolean;
}

/**
 * 分配全场景变体响应
 *
 * @generated from message step.recsys.abtest.AssignAllVariantResponse
 */
export declare class AssignAllVariantResponse extends Message<AssignAllVariantResponse> {
  /**
   * 全场景变体, key:{场景标识}_{变体名}，value:变体值
   *
   * @generated from field: map<string, step.recsys.abtest.Variant> variants = 1;
   */
  variants: { [key: string]: Variant };

  /**
   * 错误提示信息
   *
   * @generated from field: string reason = 2;
   */
  reason: string;

  constructor(data?: PartialMessage<AssignAllVariantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.recsys.abtest.AssignAllVariantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AssignAllVariantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AssignAllVariantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AssignAllVariantResponse;

  static equals(a: AssignAllVariantResponse | PlainMessage<AssignAllVariantResponse> | undefined, b: AssignAllVariantResponse | PlainMessage<AssignAllVariantResponse> | undefined): boolean;
}

/**
 * 分配单场景变体请求
 *
 * @generated from message step.recsys.abtest.AssignVariantRequest
 */
export declare class AssignVariantRequest extends Message<AssignVariantRequest> {
  /**
   * 用户信息
   *
   * @generated from field: step.recsys.abtest.User user = 1;
   */
  user?: User;

  /**
   * 场景标识，例如 homeRecTab-首页推荐Tab
   *
   * @generated from field: string sceneId = 2;
   */
  sceneId: string;

  /**
   * 实验类型：1-服务端，2-算法，3-客户端，不传时默认为全部
   *
   * @generated from field: optional int32 expType = 3;
   */
  expType?: number;

  /**
   * 调用链ID，主要用于关联客户端和服务端埋点日志
   *
   * @generated from field: string traceId = 4;
   */
  traceId: string;

  constructor(data?: PartialMessage<AssignVariantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.recsys.abtest.AssignVariantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AssignVariantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AssignVariantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AssignVariantRequest;

  static equals(a: AssignVariantRequest | PlainMessage<AssignVariantRequest> | undefined, b: AssignVariantRequest | PlainMessage<AssignVariantRequest> | undefined): boolean;
}

/**
 * 分配单场景变体响应
 *
 * @generated from message step.recsys.abtest.AssignVariantResponse
 */
export declare class AssignVariantResponse extends Message<AssignVariantResponse> {
  /**
   * 单场景变体, key:{变体名}，value:变体值
   *
   * @generated from field: map<string, step.recsys.abtest.Variant> variants = 1;
   */
  variants: { [key: string]: Variant };

  /**
   * 错误提示信息
   *
   * @generated from field: string reason = 2;
   */
  reason: string;

  constructor(data?: PartialMessage<AssignVariantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.recsys.abtest.AssignVariantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AssignVariantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AssignVariantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AssignVariantResponse;

  static equals(a: AssignVariantResponse | PlainMessage<AssignVariantResponse> | undefined, b: AssignVariantResponse | PlainMessage<AssignVariantResponse> | undefined): boolean;
}

/**
 * 变体值
 *
 * @generated from message step.recsys.abtest.Variant
 */
export declare class Variant extends Message<Variant> {
  /**
   * 变体值
   *
   * @generated from field: string value = 1;
   */
  value: string;

  /**
   * 实验ID
   *
   * @generated from field: string expId = 2;
   */
  expId: string;

  /**
   * 分组ID
   *
   * @generated from field: string groupId = 3;
   */
  groupId: string;

  /**
   * 是否为控制组（对照组）
   *
   * @generated from field: bool isControlGroup = 4;
   */
  isControlGroup: boolean;

  /**
   * 分流字段：uid/did/extraDid/buvid
   *
   * @generated from field: string bucketKey = 5;
   */
  bucketKey: string;

  constructor(data?: PartialMessage<Variant>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.recsys.abtest.Variant";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Variant;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Variant;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Variant;

  static equals(a: Variant | PlainMessage<Variant> | undefined, b: Variant | PlainMessage<Variant> | undefined): boolean;
}

/**
 * 用户信息
 *
 * @generated from message step.recsys.abtest.User
 */
export declare class User extends Message<User> {
  /**
   * 用户ID，需要和token对应的用户ID一致
   *
   * @generated from field: string uid = 1;
   */
  uid: string;

  /**
   * 设备号，Web端为Webid
   *
   * @generated from field: string did = 2;
   */
  did: string;

  /**
   * 平台：web, andriod, ios
   *
   * @generated from field: string platform = 3;
   */
  platform: string;

  /**
   * 系统名称：android, ios, mac
   *
   * @generated from field: string osName = 4;
   */
  osName: string;

  /**
   * 系统版本：android7, ios16
   *
   * @generated from field: string osVersion = 5;
   */
  osVersion: string;

  /**
   * APP ID：10100-冒泡鸭, 10400-AIGC 参考 https://wvixbzgc0u7.feishu.cn/wiki/B8Wrw1CEaiZYKVkmfZ5cOJ61nhJ
   *
   * @generated from field: string appId = 6;
   */
  appId: string;

  /**
   * APP版本：1.0.0
   *
   * @generated from field: string appVersion = 7;
   */
  appVersion: string;

  /**
   * 埋点设备ID，如火山设备ID、阿里QT设备ID
   *
   * @generated from field: string extraDid = 8;
   */
  extraDid: string;

  /**
   * 下载渠道：应用包、华为应用市场、官网、android
   *
   * @generated from field: string channel = 9;
   */
  channel: string;

  /**
   * 手机品牌：华为、小米、Apple、Google
   *
   * @generated from field: string deviceBrand = 10;
   */
  deviceBrand: string;

  /**
   * 手机型号：Pixel 4
   *
   * @generated from field: string deviceModel = 11;
   */
  deviceModel: string;

  /**
   * 语言：zh
   *
   * @generated from field: string language = 12;
   */
  language: string;

  /**
   * 用户中心Token，服务间内部调用可不传
   *
   * @generated from field: string token = 13;
   */
  token: string;

  /**
   * 是否登录
   *
   * @generated from field: bool logined = 14;
   */
  logined: boolean;

  /**
   * APP内部版本
   *
   * @generated from field: string versionCode = 15;
   */
  versionCode: string;

  /**
   * Sim卡地区
   *
   * @generated from field: string simRegion = 16;
   */
  simRegion: string;

  /**
   * Sim卡运营商
   *
   * @generated from field: string carrier = 17;
   */
  carrier: string;

  /**
   * 国家区域：CN, US
   *
   * @generated from field: string region = 18;
   */
  region: string;

  /**
   * 时区：America/New_York;UTC-05:00
   *
   * @generated from field: string timezone = 19;
   */
  timezone: string;

  /**
   * 增长系统跨App全局设备ID
   *
   * @generated from field: string buvid = 20;
   */
  buvid: string;

  /**
   * 广告SDK设备ID，如Adjust设备ID
   *
   * @generated from field: string adDid = 21;
   */
  adDid: string;

  constructor(data?: PartialMessage<User>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.recsys.abtest.User";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): User;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): User;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): User;

  static equals(a: User | PlainMessage<User> | undefined, b: User | PlainMessage<User> | undefined): boolean;
}

