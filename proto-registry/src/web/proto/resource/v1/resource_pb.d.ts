// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file proto/resource/v1/resource.proto (package proto.resource.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message proto.resource.v1.Resource
 */
export declare class Resource extends Message<Resource> {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: string url = 2;
   */
  url: string;

  /**
   * @generated from oneof proto.resource.v1.Resource.meta
   */
  meta: {
    /**
     * @generated from field: proto.resource.v1.Image image = 3;
     */
    value: Image;
    case: "image";
  } | {
    /**
     * @generated from field: proto.resource.v1.Audio audio = 4;
     */
    value: Audio;
    case: "audio";
  } | {
    /**
     * @generated from field: proto.resource.v1.File file = 5;
     */
    value: File;
    case: "file";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: string pub_url = 99;
   */
  pubUrl: string;

  constructor(data?: PartialMessage<Resource>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.resource.v1.Resource";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Resource;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Resource;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Resource;

  static equals(a: Resource | PlainMessage<Resource> | undefined, b: Resource | PlainMessage<Resource> | undefined): boolean;
}

/**
 * @generated from message proto.resource.v1.Image
 */
export declare class Image extends Message<Image> {
  /**
   * @generated from field: string format = 1;
   */
  format: string;

  /**
   * @generated from field: int64 size = 2;
   */
  size: bigint;

  /**
   * @generated from field: int32 height = 3;
   */
  height: number;

  /**
   * @generated from field: int32 width = 4;
   */
  width: number;

  /**
   * @generated from field: string rgb = 5;
   */
  rgb: string;

  constructor(data?: PartialMessage<Image>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.resource.v1.Image";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Image;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Image;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Image;

  static equals(a: Image | PlainMessage<Image> | undefined, b: Image | PlainMessage<Image> | undefined): boolean;
}

/**
 * @generated from message proto.resource.v1.Audio
 */
export declare class Audio extends Message<Audio> {
  /**
   * @generated from field: string format = 1;
   */
  format: string;

  /**
   * @generated from field: int64 size = 2;
   */
  size: bigint;

  /**
   * @generated from field: int64 duration = 3;
   */
  duration: bigint;

  constructor(data?: PartialMessage<Audio>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.resource.v1.Audio";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Audio;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Audio;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Audio;

  static equals(a: Audio | PlainMessage<Audio> | undefined, b: Audio | PlainMessage<Audio> | undefined): boolean;
}

/**
 * @generated from message proto.resource.v1.File
 */
export declare class File extends Message<File> {
  /**
   * @generated from field: string format = 1;
   */
  format: string;

  /**
   * @generated from field: int64 size = 2;
   */
  size: bigint;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  constructor(data?: PartialMessage<File>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.resource.v1.File";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): File;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): File;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): File;

  static equals(a: File | PlainMessage<File> | undefined, b: File | PlainMessage<File> | undefined): boolean;
}

