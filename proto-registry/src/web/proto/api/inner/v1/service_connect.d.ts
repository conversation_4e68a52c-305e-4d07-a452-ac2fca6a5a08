// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file proto/api/inner/v1/service.proto (package proto.api.inner.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { ActivateAccountRequest, ActivateAccountResponse, BanAccountRequest, BanAccountResponse, DeactivateAccountRequest, DeactivateAccountResponse, GetAccountEmailRequest, GetAccountEmailResponse, MGetAccountBindingRequest, MGetAccountBindingResponse, MGetAccountByMobileRequest, MGetAccountByMobileResponse, MGetAccountRequest, MGetAccountResponse, SignOutRequest, SignOutResponse, UnBanAccountRequest, UnBanAccountResponse } from "./service_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service proto.api.inner.v1.PassportInnerService
 */
export declare const PassportInnerService: {
  readonly typeName: "proto.api.inner.v1.PassportInnerService",
  readonly methods: {
    /**
     * @generated from rpc proto.api.inner.v1.PassportInnerService.SignOut
     */
    readonly signOut: {
      readonly name: "SignOut",
      readonly I: typeof SignOutRequest,
      readonly O: typeof SignOutResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.inner.v1.PassportInnerService.MGetAccount
     */
    readonly mGetAccount: {
      readonly name: "MGetAccount",
      readonly I: typeof MGetAccountRequest,
      readonly O: typeof MGetAccountResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.inner.v1.PassportInnerService.MGetAccountBinding
     */
    readonly mGetAccountBinding: {
      readonly name: "MGetAccountBinding",
      readonly I: typeof MGetAccountBindingRequest,
      readonly O: typeof MGetAccountBindingResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.inner.v1.PassportInnerService.MGetAccountByMobile
     */
    readonly mGetAccountByMobile: {
      readonly name: "MGetAccountByMobile",
      readonly I: typeof MGetAccountByMobileRequest,
      readonly O: typeof MGetAccountByMobileResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.inner.v1.PassportInnerService.ActivateAccount
     */
    readonly activateAccount: {
      readonly name: "ActivateAccount",
      readonly I: typeof ActivateAccountRequest,
      readonly O: typeof ActivateAccountResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.inner.v1.PassportInnerService.DeactivateAccount
     */
    readonly deactivateAccount: {
      readonly name: "DeactivateAccount",
      readonly I: typeof DeactivateAccountRequest,
      readonly O: typeof DeactivateAccountResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.inner.v1.PassportInnerService.BanAccount
     */
    readonly banAccount: {
      readonly name: "BanAccount",
      readonly I: typeof BanAccountRequest,
      readonly O: typeof BanAccountResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.inner.v1.PassportInnerService.UnBanAccount
     */
    readonly unBanAccount: {
      readonly name: "UnBanAccount",
      readonly I: typeof UnBanAccountRequest,
      readonly O: typeof UnBanAccountResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.inner.v1.PassportInnerService.GetAccountEmail
     */
    readonly getAccountEmail: {
      readonly name: "GetAccountEmail",
      readonly I: typeof GetAccountEmailRequest,
      readonly O: typeof GetAccountEmailResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

