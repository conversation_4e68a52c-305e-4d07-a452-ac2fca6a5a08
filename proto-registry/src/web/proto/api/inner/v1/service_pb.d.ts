// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file proto/api/inner/v1/service.proto (package proto.api.inner.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Timestamp } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum proto.api.inner.v1.Mode
 */
export declare enum Mode {
  /**
   * @generated from enum value: MODE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 匿名态
   *
   * @generated from enum value: MODE_ANONYMOUS = 1;
   */
  ANONYMOUS = 1,

  /**
   * 登录态
   *
   * @generated from enum value: MODE_SIGN_IN = 2;
   */
  SIGN_IN = 2,

  /**
   * 注销态
   *
   * @generated from enum value: MODE_SIGN_OUT = 3;
   */
  SIGN_OUT = 3,
}

/**
 * @generated from message proto.api.inner.v1.Account
 */
export declare class Account extends Message<Account> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: int64 oasis_id = 2;
   */
  oasisId: bigint;

  /**
   * @generated from field: proto.api.inner.v1.Mode mode = 3;
   */
  mode: Mode;

  /**
   * @generated from field: bool activated = 4;
   */
  activated: boolean;

  /**
   * @generated from field: bool baned = 5;
   */
  baned: boolean;

  /**
   * @generated from field: google.protobuf.Timestamp activated_time = 6;
   */
  activatedTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp signup_time = 7;
   */
  signupTime?: Timestamp;

  constructor(data?: PartialMessage<Account>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.Account";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Account;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Account;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Account;

  static equals(a: Account | PlainMessage<Account> | undefined, b: Account | PlainMessage<Account> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.Device
 */
export declare class Device extends Message<Device> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: string device_id = 2;
   */
  deviceId: string;

  /**
   * @generated from field: int64 oasis_id = 3;
   */
  oasisId: bigint;

  /**
   * @generated from field: string platform = 4;
   */
  platform: string;

  constructor(data?: PartialMessage<Device>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.Device";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Device;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Device;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Device;

  static equals(a: Device | PlainMessage<Device> | undefined, b: Device | PlainMessage<Device> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.Mobile
 */
export declare class Mobile extends Message<Mobile> {
  /**
   * @generated from field: string cc = 1;
   */
  cc: string;

  /**
   * 默认脱敏
   *
   * @generated from field: string number = 2;
   */
  number: string;

  constructor(data?: PartialMessage<Mobile>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.Mobile";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Mobile;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Mobile;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Mobile;

  static equals(a: Mobile | PlainMessage<Mobile> | undefined, b: Mobile | PlainMessage<Mobile> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.Binding
 */
export declare class Binding extends Message<Binding> {
  /**
   * @generated from field: proto.api.inner.v1.Mobile mobile = 1;
   */
  mobile?: Mobile;

  constructor(data?: PartialMessage<Binding>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.Binding";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Binding;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Binding;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Binding;

  static equals(a: Binding | PlainMessage<Binding> | undefined, b: Binding | PlainMessage<Binding> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.MGetAccountBindingRequest
 */
export declare class MGetAccountBindingRequest extends Message<MGetAccountBindingRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: repeated int64 oasis_ids = 2;
   */
  oasisIds: bigint[];

  constructor(data?: PartialMessage<MGetAccountBindingRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.MGetAccountBindingRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MGetAccountBindingRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MGetAccountBindingRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MGetAccountBindingRequest;

  static equals(a: MGetAccountBindingRequest | PlainMessage<MGetAccountBindingRequest> | undefined, b: MGetAccountBindingRequest | PlainMessage<MGetAccountBindingRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.MGetAccountBindingResponse
 */
export declare class MGetAccountBindingResponse extends Message<MGetAccountBindingResponse> {
  /**
   * oasisID → Binding
   *
   * @generated from field: map<int64, proto.api.inner.v1.Binding> bindings = 1;
   */
  bindings: { [key: string]: Binding };

  constructor(data?: PartialMessage<MGetAccountBindingResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.MGetAccountBindingResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MGetAccountBindingResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MGetAccountBindingResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MGetAccountBindingResponse;

  static equals(a: MGetAccountBindingResponse | PlainMessage<MGetAccountBindingResponse> | undefined, b: MGetAccountBindingResponse | PlainMessage<MGetAccountBindingResponse> | undefined): boolean;
}

/**
 * =========================================== Account API ===========================================
 *
 * @generated from message proto.api.inner.v1.SignOutRequest
 */
export declare class SignOutRequest extends Message<SignOutRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: int64 oasis_id = 2;
   */
  oasisId: bigint;

  /**
   * web, ios, android
   *
   * @generated from field: string platform = 3;
   */
  platform: string;

  /**
   * webID or did
   *
   * @generated from field: string device_id = 4;
   */
  deviceId: string;

  constructor(data?: PartialMessage<SignOutRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.SignOutRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignOutRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignOutRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignOutRequest;

  static equals(a: SignOutRequest | PlainMessage<SignOutRequest> | undefined, b: SignOutRequest | PlainMessage<SignOutRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.SignOutResponse
 */
export declare class SignOutResponse extends Message<SignOutResponse> {
  /**
   * @generated from field: string token = 1;
   */
  token: string;

  constructor(data?: PartialMessage<SignOutResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.SignOutResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignOutResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignOutResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignOutResponse;

  static equals(a: SignOutResponse | PlainMessage<SignOutResponse> | undefined, b: SignOutResponse | PlainMessage<SignOutResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.MGetAccountRequest
 */
export declare class MGetAccountRequest extends Message<MGetAccountRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: repeated int64 oasis_ids = 2;
   */
  oasisIds: bigint[];

  /**
   * 是否需要获取 Binding 信息
   *
   * @generated from field: bool need_binding = 3;
   */
  needBinding: boolean;

  constructor(data?: PartialMessage<MGetAccountRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.MGetAccountRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MGetAccountRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MGetAccountRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MGetAccountRequest;

  static equals(a: MGetAccountRequest | PlainMessage<MGetAccountRequest> | undefined, b: MGetAccountRequest | PlainMessage<MGetAccountRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.MGetAccountResponse
 */
export declare class MGetAccountResponse extends Message<MGetAccountResponse> {
  /**
   * oasisID → account
   *
   * @generated from field: map<int64, proto.api.inner.v1.Account> accounts = 1;
   */
  accounts: { [key: string]: Account };

  /**
   * oasisID → Binding
   *
   * @generated from field: map<int64, proto.api.inner.v1.Binding> bindings = 2;
   */
  bindings: { [key: string]: Binding };

  constructor(data?: PartialMessage<MGetAccountResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.MGetAccountResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MGetAccountResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MGetAccountResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MGetAccountResponse;

  static equals(a: MGetAccountResponse | PlainMessage<MGetAccountResponse> | undefined, b: MGetAccountResponse | PlainMessage<MGetAccountResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.MGetAccountByMobileRequest
 */
export declare class MGetAccountByMobileRequest extends Message<MGetAccountByMobileRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: repeated proto.api.inner.v1.Mobile mobiles = 2;
   */
  mobiles: Mobile[];

  constructor(data?: PartialMessage<MGetAccountByMobileRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.MGetAccountByMobileRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MGetAccountByMobileRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MGetAccountByMobileRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MGetAccountByMobileRequest;

  static equals(a: MGetAccountByMobileRequest | PlainMessage<MGetAccountByMobileRequest> | undefined, b: MGetAccountByMobileRequest | PlainMessage<MGetAccountByMobileRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.MGetAccountByMobileResponse
 */
export declare class MGetAccountByMobileResponse extends Message<MGetAccountByMobileResponse> {
  /**
   * mobile(cc+number) → account
   *
   * @generated from field: map<string, proto.api.inner.v1.Account> accounts = 1;
   */
  accounts: { [key: string]: Account };

  constructor(data?: PartialMessage<MGetAccountByMobileResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.MGetAccountByMobileResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MGetAccountByMobileResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MGetAccountByMobileResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MGetAccountByMobileResponse;

  static equals(a: MGetAccountByMobileResponse | PlainMessage<MGetAccountByMobileResponse> | undefined, b: MGetAccountByMobileResponse | PlainMessage<MGetAccountByMobileResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.ActivateAccountRequest
 */
export declare class ActivateAccountRequest extends Message<ActivateAccountRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: int64 oasis_id = 2;
   */
  oasisId: bigint;

  constructor(data?: PartialMessage<ActivateAccountRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.ActivateAccountRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ActivateAccountRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ActivateAccountRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ActivateAccountRequest;

  static equals(a: ActivateAccountRequest | PlainMessage<ActivateAccountRequest> | undefined, b: ActivateAccountRequest | PlainMessage<ActivateAccountRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.ActivateAccountResponse
 */
export declare class ActivateAccountResponse extends Message<ActivateAccountResponse> {
  constructor(data?: PartialMessage<ActivateAccountResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.ActivateAccountResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ActivateAccountResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ActivateAccountResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ActivateAccountResponse;

  static equals(a: ActivateAccountResponse | PlainMessage<ActivateAccountResponse> | undefined, b: ActivateAccountResponse | PlainMessage<ActivateAccountResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.DeactivateAccountRequest
 */
export declare class DeactivateAccountRequest extends Message<DeactivateAccountRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: int64 oasis_id = 2;
   */
  oasisId: bigint;

  constructor(data?: PartialMessage<DeactivateAccountRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.DeactivateAccountRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeactivateAccountRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeactivateAccountRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeactivateAccountRequest;

  static equals(a: DeactivateAccountRequest | PlainMessage<DeactivateAccountRequest> | undefined, b: DeactivateAccountRequest | PlainMessage<DeactivateAccountRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.DeactivateAccountResponse
 */
export declare class DeactivateAccountResponse extends Message<DeactivateAccountResponse> {
  constructor(data?: PartialMessage<DeactivateAccountResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.DeactivateAccountResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeactivateAccountResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeactivateAccountResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeactivateAccountResponse;

  static equals(a: DeactivateAccountResponse | PlainMessage<DeactivateAccountResponse> | undefined, b: DeactivateAccountResponse | PlainMessage<DeactivateAccountResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.BanAccountRequest
 */
export declare class BanAccountRequest extends Message<BanAccountRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: int64 oasis_id = 2;
   */
  oasisId: bigint;

  constructor(data?: PartialMessage<BanAccountRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.BanAccountRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BanAccountRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BanAccountRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BanAccountRequest;

  static equals(a: BanAccountRequest | PlainMessage<BanAccountRequest> | undefined, b: BanAccountRequest | PlainMessage<BanAccountRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.BanAccountResponse
 */
export declare class BanAccountResponse extends Message<BanAccountResponse> {
  constructor(data?: PartialMessage<BanAccountResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.BanAccountResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BanAccountResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BanAccountResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BanAccountResponse;

  static equals(a: BanAccountResponse | PlainMessage<BanAccountResponse> | undefined, b: BanAccountResponse | PlainMessage<BanAccountResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.UnBanAccountRequest
 */
export declare class UnBanAccountRequest extends Message<UnBanAccountRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: int64 oasis_id = 2;
   */
  oasisId: bigint;

  constructor(data?: PartialMessage<UnBanAccountRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.UnBanAccountRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnBanAccountRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnBanAccountRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnBanAccountRequest;

  static equals(a: UnBanAccountRequest | PlainMessage<UnBanAccountRequest> | undefined, b: UnBanAccountRequest | PlainMessage<UnBanAccountRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.UnBanAccountResponse
 */
export declare class UnBanAccountResponse extends Message<UnBanAccountResponse> {
  constructor(data?: PartialMessage<UnBanAccountResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.UnBanAccountResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnBanAccountResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnBanAccountResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnBanAccountResponse;

  static equals(a: UnBanAccountResponse | PlainMessage<UnBanAccountResponse> | undefined, b: UnBanAccountResponse | PlainMessage<UnBanAccountResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.GetAccountEmailRequest
 */
export declare class GetAccountEmailRequest extends Message<GetAccountEmailRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: int64 oasis_id = 2;
   */
  oasisId: bigint;

  /**
   * @generated from field: bool apple_support = 3;
   */
  appleSupport: boolean;

  constructor(data?: PartialMessage<GetAccountEmailRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.GetAccountEmailRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAccountEmailRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAccountEmailRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAccountEmailRequest;

  static equals(a: GetAccountEmailRequest | PlainMessage<GetAccountEmailRequest> | undefined, b: GetAccountEmailRequest | PlainMessage<GetAccountEmailRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.inner.v1.GetAccountEmailResponse
 */
export declare class GetAccountEmailResponse extends Message<GetAccountEmailResponse> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: int64 oasis_id = 2;
   */
  oasisId: bigint;

  /**
   * @generated from field: string plain_email = 3;
   */
  plainEmail: string;

  /**
   * @generated from field: string email_from = 4;
   */
  emailFrom: string;

  /**
   * @generated from field: bool email_verified = 5;
   */
  emailVerified: boolean;

  constructor(data?: PartialMessage<GetAccountEmailResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.inner.v1.GetAccountEmailResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAccountEmailResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAccountEmailResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAccountEmailResponse;

  static equals(a: GetAccountEmailResponse | PlainMessage<GetAccountEmailResponse> | undefined, b: GetAccountEmailResponse | PlainMessage<GetAccountEmailResponse> | undefined): boolean;
}

