// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file proto/api/user/v1/service.proto (package proto.api.user.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Resource } from "../../../resource/v1/resource_pb.js";
import type { Result } from "../../../risk/v1/risk_pb.js";
import type { Binding } from "../../../base/v1/base_pb.js";

/**
 * =========================================== Base ===========================================
 *
 * @generated from enum proto.api.user.v1.Gender
 */
export declare enum Gender {
  /**
   * @generated from enum value: GENDER_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: GENDER_MALE = 1;
   */
  MALE = 1,

  /**
   * @generated from enum value: GENDER_FEMALE = 2;
   */
  FEMALE = 2,

  /**
   * @generated from enum value: GENDER_OTHER = 3;
   */
  OTHER = 3,
}

/**
 * @generated from message proto.api.user.v1.User
 */
export declare class User extends Message<User> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string nickname = 2;
   */
  nickname: string;

  /**
   * @generated from field: proto.resource.v1.Resource avatar = 3;
   */
  avatar?: Resource;

  /**
   * @generated from field: string introduction = 4;
   */
  introduction: string;

  /**
   * XX 号
   *
   * @generated from field: string hao = 5;
   */
  hao: string;

  /**
   * @generated from field: proto.api.user.v1.Gender gender = 6;
   */
  gender: Gender;

  constructor(data?: PartialMessage<User>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.User";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): User;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): User;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): User;

  static equals(a: User | PlainMessage<User> | undefined, b: User | PlainMessage<User> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.GetUserRequest
 */
export declare class GetUserRequest extends Message<GetUserRequest> {
  constructor(data?: PartialMessage<GetUserRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.GetUserRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserRequest;

  static equals(a: GetUserRequest | PlainMessage<GetUserRequest> | undefined, b: GetUserRequest | PlainMessage<GetUserRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.GetUserResponse
 */
export declare class GetUserResponse extends Message<GetUserResponse> {
  /**
   * @generated from field: proto.api.user.v1.User user = 1;
   */
  user?: User;

  constructor(data?: PartialMessage<GetUserResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.GetUserResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserResponse;

  static equals(a: GetUserResponse | PlainMessage<GetUserResponse> | undefined, b: GetUserResponse | PlainMessage<GetUserResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.VisitUserRequest
 */
export declare class VisitUserRequest extends Message<VisitUserRequest> {
  /**
   * @generated from oneof proto.api.user.v1.VisitUserRequest.host
   */
  host: {
    /**
     * @generated from field: string oasis_id = 1;
     */
    value: string;
    case: "oasisId";
  } | {
    /**
     * XX 号
     *
     * @generated from field: string hao = 2;
     */
    value: string;
    case: "hao";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<VisitUserRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.VisitUserRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VisitUserRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VisitUserRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VisitUserRequest;

  static equals(a: VisitUserRequest | PlainMessage<VisitUserRequest> | undefined, b: VisitUserRequest | PlainMessage<VisitUserRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.VisitUserResponse
 */
export declare class VisitUserResponse extends Message<VisitUserResponse> {
  /**
   * @generated from field: proto.api.user.v1.User user = 1;
   */
  user?: User;

  constructor(data?: PartialMessage<VisitUserResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.VisitUserResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VisitUserResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VisitUserResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VisitUserResponse;

  static equals(a: VisitUserResponse | PlainMessage<VisitUserResponse> | undefined, b: VisitUserResponse | PlainMessage<VisitUserResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.UploadAvatarRequest
 */
export declare class UploadAvatarRequest extends Message<UploadAvatarRequest> {
  constructor(data?: PartialMessage<UploadAvatarRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.UploadAvatarRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UploadAvatarRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UploadAvatarRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UploadAvatarRequest;

  static equals(a: UploadAvatarRequest | PlainMessage<UploadAvatarRequest> | undefined, b: UploadAvatarRequest | PlainMessage<UploadAvatarRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.UploadAvatarResponse
 */
export declare class UploadAvatarResponse extends Message<UploadAvatarResponse> {
  /**
   * @generated from field: string object_id = 1;
   */
  objectId: string;

  constructor(data?: PartialMessage<UploadAvatarResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.UploadAvatarResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UploadAvatarResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UploadAvatarResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UploadAvatarResponse;

  static equals(a: UploadAvatarResponse | PlainMessage<UploadAvatarResponse> | undefined, b: UploadAvatarResponse | PlainMessage<UploadAvatarResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.SetUserGenderRequest
 */
export declare class SetUserGenderRequest extends Message<SetUserGenderRequest> {
  /**
   * @generated from field: proto.api.user.v1.Gender gender = 1;
   */
  gender: Gender;

  constructor(data?: PartialMessage<SetUserGenderRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.SetUserGenderRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetUserGenderRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetUserGenderRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetUserGenderRequest;

  static equals(a: SetUserGenderRequest | PlainMessage<SetUserGenderRequest> | undefined, b: SetUserGenderRequest | PlainMessage<SetUserGenderRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.SetUserGenderResponse
 */
export declare class SetUserGenderResponse extends Message<SetUserGenderResponse> {
  constructor(data?: PartialMessage<SetUserGenderResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.SetUserGenderResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetUserGenderResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetUserGenderResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetUserGenderResponse;

  static equals(a: SetUserGenderResponse | PlainMessage<SetUserGenderResponse> | undefined, b: SetUserGenderResponse | PlainMessage<SetUserGenderResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.UpdatePersonalInfoRequest
 */
export declare class UpdatePersonalInfoRequest extends Message<UpdatePersonalInfoRequest> {
  /**
   * @generated from field: optional string nickname = 1;
   */
  nickname?: string;

  /**
   * @generated from field: optional string avatar = 2;
   */
  avatar?: string;

  /**
   * @generated from field: optional string introduction = 3;
   */
  introduction?: string;

  constructor(data?: PartialMessage<UpdatePersonalInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.UpdatePersonalInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdatePersonalInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdatePersonalInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdatePersonalInfoRequest;

  static equals(a: UpdatePersonalInfoRequest | PlainMessage<UpdatePersonalInfoRequest> | undefined, b: UpdatePersonalInfoRequest | PlainMessage<UpdatePersonalInfoRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.UpdatePersonalInfoResponse
 */
export declare class UpdatePersonalInfoResponse extends Message<UpdatePersonalInfoResponse> {
  /**
   * 快审结果
   *
   * @generated from field: proto.risk.v1.Result risk_status = 1;
   */
  riskStatus?: Result;

  constructor(data?: PartialMessage<UpdatePersonalInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.UpdatePersonalInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdatePersonalInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdatePersonalInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdatePersonalInfoResponse;

  static equals(a: UpdatePersonalInfoResponse | PlainMessage<UpdatePersonalInfoResponse> | undefined, b: UpdatePersonalInfoResponse | PlainMessage<UpdatePersonalInfoResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.GetAccountBindingRequest
 */
export declare class GetAccountBindingRequest extends Message<GetAccountBindingRequest> {
  constructor(data?: PartialMessage<GetAccountBindingRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.GetAccountBindingRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAccountBindingRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAccountBindingRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAccountBindingRequest;

  static equals(a: GetAccountBindingRequest | PlainMessage<GetAccountBindingRequest> | undefined, b: GetAccountBindingRequest | PlainMessage<GetAccountBindingRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.user.v1.GetAccountBindingResponse
 */
export declare class GetAccountBindingResponse extends Message<GetAccountBindingResponse> {
  /**
   * 用户账号绑定信息（包含：手机号、邮箱、微信等等）
   *
   * @generated from field: proto.base.v1.Binding binding = 1;
   */
  binding?: Binding;

  constructor(data?: PartialMessage<GetAccountBindingResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.user.v1.GetAccountBindingResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAccountBindingResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAccountBindingResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAccountBindingResponse;

  static equals(a: GetAccountBindingResponse | PlainMessage<GetAccountBindingResponse> | undefined, b: GetAccountBindingResponse | PlainMessage<GetAccountBindingResponse> | undefined): boolean;
}

