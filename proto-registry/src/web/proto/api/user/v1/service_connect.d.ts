// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file proto/api/user/v1/service.proto (package proto.api.user.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GetAccountBindingRequest, GetAccountBindingResponse, GetUserRequest, GetUserResponse, SetUserGenderRequest, SetUserGenderResponse, UpdatePersonalInfoRequest, UpdatePersonalInfoResponse, VisitUserRequest, VisitUserResponse } from "./service_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service proto.api.user.v1.UserService
 */
export declare const UserService: {
  readonly typeName: "proto.api.user.v1.UserService",
  readonly methods: {
    /**
     * @generated from rpc proto.api.user.v1.UserService.GetUser
     */
    readonly getUser: {
      readonly name: "<PERSON><PERSON><PERSON>",
      readonly I: typeof GetUserRequest,
      readonly O: typeof GetUserResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.user.v1.UserService.VisitUser
     */
    readonly visitUser: {
      readonly name: "VisitUser",
      readonly I: typeof VisitUserRequest,
      readonly O: typeof VisitUserResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.user.v1.UserService.SetUserGender
     */
    readonly setUserGender: {
      readonly name: "SetUserGender",
      readonly I: typeof SetUserGenderRequest,
      readonly O: typeof SetUserGenderResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.user.v1.UserService.UpdatePersonalInfo
     */
    readonly updatePersonalInfo: {
      readonly name: "UpdatePersonalInfo",
      readonly I: typeof UpdatePersonalInfoRequest,
      readonly O: typeof UpdatePersonalInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.user.v1.UserService.GetAccountBinding
     */
    readonly getAccountBinding: {
      readonly name: "GetAccountBinding",
      readonly I: typeof GetAccountBindingRequest,
      readonly O: typeof GetAccountBindingResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

