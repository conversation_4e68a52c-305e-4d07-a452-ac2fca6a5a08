// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file proto/api/passport/v1/global.proto (package proto.api.passport.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { AccessToken, RefreshToken } from "./service_pb.js";

/**
 * @generated from message proto.api.passport.v1.PingRequest
 */
export declare class PingRequest extends Message<PingRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  constructor(data?: PartialMessage<PingRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.PingRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PingRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PingRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PingRequest;

  static equals(a: PingRequest | PlainMessage<PingRequest> | undefined, b: PingRequest | PlainMessage<PingRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.PingResponse
 */
export declare class PingResponse extends Message<PingResponse> {
  constructor(data?: PartialMessage<PingResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.PingResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PingResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PingResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PingResponse;

  static equals(a: PingResponse | PlainMessage<PingResponse> | undefined, b: PingResponse | PlainMessage<PingResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SignInByEmailRequest
 */
export declare class SignInByEmailRequest extends Message<SignInByEmailRequest> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: string auth_code = 2;
   */
  authCode: string;

  /**
   * @generated from field: string email_address = 3;
   */
  emailAddress: string;

  constructor(data?: PartialMessage<SignInByEmailRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignInByEmailRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignInByEmailRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignInByEmailRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignInByEmailRequest;

  static equals(a: SignInByEmailRequest | PlainMessage<SignInByEmailRequest> | undefined, b: SignInByEmailRequest | PlainMessage<SignInByEmailRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SignInByEmailResponse
 */
export declare class SignInByEmailResponse extends Message<SignInByEmailResponse> {
  /**
   * @generated from field: proto.api.passport.v1.AccessToken access_token = 1;
   */
  accessToken?: AccessToken;

  /**
   * @generated from field: proto.api.passport.v1.RefreshToken refresh_token = 2;
   */
  refreshToken?: RefreshToken;

  /**
   * @generated from field: bool is_sign_up = 3;
   */
  isSignUp: boolean;

  /**
   * @generated from field: bool is_redid = 4;
   */
  isRedid: boolean;

  constructor(data?: PartialMessage<SignInByEmailResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignInByEmailResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignInByEmailResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignInByEmailResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignInByEmailResponse;

  static equals(a: SignInByEmailResponse | PlainMessage<SignInByEmailResponse> | undefined, b: SignInByEmailResponse | PlainMessage<SignInByEmailResponse> | undefined): boolean;
}

