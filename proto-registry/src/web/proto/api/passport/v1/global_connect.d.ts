// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file proto/api/passport/v1/global.proto (package proto.api.passport.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { PingRequest, PingResponse, SignInByEmailRequest, SignInByEmailResponse } from "./global_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service proto.api.passport.v1.GlobalPassportService
 */
export declare const GlobalPassportService: {
  readonly typeName: "proto.api.passport.v1.GlobalPassportService",
  readonly methods: {
    /**
     * @generated from rpc proto.api.passport.v1.GlobalPassportService.Ping
     */
    readonly ping: {
      readonly name: "<PERSON>",
      readonly I: typeof PingRequest,
      readonly O: typeof PingResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 账密登录
     *
     * @generated from rpc proto.api.passport.v1.GlobalPassportService.SignInByEmail
     */
    readonly signInByEmail: {
      readonly name: "SignInByEmail",
      readonly I: typeof SignInByEmailRequest,
      readonly O: typeof SignInByEmailResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

