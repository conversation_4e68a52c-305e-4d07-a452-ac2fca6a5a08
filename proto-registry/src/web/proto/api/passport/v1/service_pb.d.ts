// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file proto/api/passport/v1/service.proto (package proto.api.passport.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Mobile, QQ, WX, WxAppInfo } from "../../../base/v1/base_pb.js";

/**
 * @generated from enum proto.api.passport.v1.Mode
 */
export declare enum Mode {
  /**
   * @generated from enum value: MODE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 匿名态
   *
   * @generated from enum value: MODE_ANONYMOUS = 1;
   */
  ANONYMOUS = 1,

  /**
   * 登录态
   *
   * @generated from enum value: MODE_SIGN_IN = 2;
   */
  SIGN_IN = 2,
}

/**
 * @generated from enum proto.api.passport.v1.AuthCodeUsage
 */
export declare enum AuthCodeUsage {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 登录注册
   *
   * @generated from enum value: SIGN_IN = 1;
   */
  SIGN_IN = 1,

  /**
   * 设置（重置）密码
   *
   * @generated from enum value: SET_PASSWORD = 2;
   */
  SET_PASSWORD = 2,

  /**
   * 找回密码（非登录态）
   *
   * @generated from enum value: RETRIEVE_PASSWORD = 3;
   */
  RETRIEVE_PASSWORD = 3,

  /**
   * 验证当前账户的手机号/邮箱
   *
   * @generated from enum value: VERIFY = 4;
   */
  VERIFY = 4,

  /**
   * 为当前账户绑定手机号/邮箱（国内应用基本会先经过手机号登录，暂不涉及后绑定手机号情况）
   *
   * @generated from enum value: BIND = 5;
   */
  BIND = 5,

  /**
   * 为当前账号换绑手机号/邮箱
   *
   * @generated from enum value: REBIND = 6;
   */
  REBIND = 6,
}

/**
 * @generated from message proto.api.passport.v1.Device
 */
export declare class Device extends Message<Device> {
  /**
   * @generated from field: string platform = 1;
   */
  platform: string;

  /**
   * 账号中心在使用设备唯一标识
   *
   * @generated from field: string deviceID = 2;
   */
  deviceID: string;

  /**
   * @generated from field: string buvid = 3;
   */
  buvid: string;

  constructor(data?: PartialMessage<Device>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.Device";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Device;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Device;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Device;

  static equals(a: Device | PlainMessage<Device> | undefined, b: Device | PlainMessage<Device> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.Registry
 */
export declare class Registry extends Message<Registry> {
  /**
   * @generated from field: int32 app_id = 1;
   */
  appId: number;

  /**
   * @generated from field: string oasis_id = 2;
   */
  oasisId: string;

  constructor(data?: PartialMessage<Registry>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.Registry";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Registry;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Registry;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Registry;

  static equals(a: Registry | PlainMessage<Registry> | undefined, b: Registry | PlainMessage<Registry> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.AccessToken
 */
export declare class AccessToken extends Message<AccessToken> {
  /**
   * @generated from field: string raw = 1;
   */
  raw: string;

  /**
   * 剩余有效时长（秒）
   *
   * @generated from field: int32 duration = 2;
   */
  duration: number;

  /**
   * @generated from field: proto.api.passport.v1.Mode mode = 3;
   */
  mode: Mode;

  constructor(data?: PartialMessage<AccessToken>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.AccessToken";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AccessToken;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AccessToken;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AccessToken;

  static equals(a: AccessToken | PlainMessage<AccessToken> | undefined, b: AccessToken | PlainMessage<AccessToken> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RefreshToken
 */
export declare class RefreshToken extends Message<RefreshToken> {
  /**
   * @generated from field: string raw = 1;
   */
  raw: string;

  constructor(data?: PartialMessage<RefreshToken>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RefreshToken";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshToken;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshToken;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshToken;

  static equals(a: RefreshToken | PlainMessage<RefreshToken> | undefined, b: RefreshToken | PlainMessage<RefreshToken> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RegisterDeviceRequest
 */
export declare class RegisterDeviceRequest extends Message<RegisterDeviceRequest> {
  /**
   * @generated from field: string encoded_device_info = 1;
   */
  encodedDeviceInfo: string;

  /**
   * 客户端请求时间戳Ms
   *
   * @generated from field: string deviceReqTimeMs = 2;
   */
  deviceReqTimeMs: string;

  constructor(data?: PartialMessage<RegisterDeviceRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RegisterDeviceRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegisterDeviceRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegisterDeviceRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegisterDeviceRequest;

  static equals(a: RegisterDeviceRequest | PlainMessage<RegisterDeviceRequest> | undefined, b: RegisterDeviceRequest | PlainMessage<RegisterDeviceRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RegisterDeviceResponse
 */
export declare class RegisterDeviceResponse extends Message<RegisterDeviceResponse> {
  /**
   * @generated from field: proto.api.passport.v1.AccessToken access_token = 1;
   */
  accessToken?: AccessToken;

  /**
   * @generated from field: proto.api.passport.v1.RefreshToken refresh_token = 2;
   */
  refreshToken?: RefreshToken;

  /**
   * @generated from field: proto.api.passport.v1.Device device = 3;
   */
  device?: Device;

  /**
   * @generated from field: proto.api.passport.v1.Registry registry = 4;
   */
  registry?: Registry;

  /**
   * buvid 是否首次创建
   *
   * @generated from field: bool buvid_created = 5;
   */
  buvidCreated: boolean;

  /**
   * buvid 在当前app 创建时间
   *
   * @generated from field: string buvid_createtime_ms = 6;
   */
  buvidCreatetimeMs: string;

  constructor(data?: PartialMessage<RegisterDeviceResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RegisterDeviceResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegisterDeviceResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegisterDeviceResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegisterDeviceResponse;

  static equals(a: RegisterDeviceResponse | PlainMessage<RegisterDeviceResponse> | undefined, b: RegisterDeviceResponse | PlainMessage<RegisterDeviceResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SyncDeviceActivationRequest
 */
export declare class SyncDeviceActivationRequest extends Message<SyncDeviceActivationRequest> {
  constructor(data?: PartialMessage<SyncDeviceActivationRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SyncDeviceActivationRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SyncDeviceActivationRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SyncDeviceActivationRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SyncDeviceActivationRequest;

  static equals(a: SyncDeviceActivationRequest | PlainMessage<SyncDeviceActivationRequest> | undefined, b: SyncDeviceActivationRequest | PlainMessage<SyncDeviceActivationRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SyncDeviceActivationResponse
 */
export declare class SyncDeviceActivationResponse extends Message<SyncDeviceActivationResponse> {
  constructor(data?: PartialMessage<SyncDeviceActivationResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SyncDeviceActivationResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SyncDeviceActivationResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SyncDeviceActivationResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SyncDeviceActivationResponse;

  static equals(a: SyncDeviceActivationResponse | PlainMessage<SyncDeviceActivationResponse> | undefined, b: SyncDeviceActivationResponse | PlainMessage<SyncDeviceActivationResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RefreshTokenRequest
 */
export declare class RefreshTokenRequest extends Message<RefreshTokenRequest> {
  /**
   * base.v1.Journal
   *
   * @generated from field: string journal_b64 = 1;
   */
  journalB64: string;

  /**
   * @generated from field: string encoded_device_info = 2;
   */
  encodedDeviceInfo: string;

  /**
   * 客户端请求时间戳Ms
   *
   * @generated from field: string deviceReqTimeMs = 3;
   */
  deviceReqTimeMs: string;

  constructor(data?: PartialMessage<RefreshTokenRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RefreshTokenRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshTokenRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshTokenRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshTokenRequest;

  static equals(a: RefreshTokenRequest | PlainMessage<RefreshTokenRequest> | undefined, b: RefreshTokenRequest | PlainMessage<RefreshTokenRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RefreshTokenResponse
 */
export declare class RefreshTokenResponse extends Message<RefreshTokenResponse> {
  /**
   * @generated from field: proto.api.passport.v1.AccessToken access_token = 1;
   */
  accessToken?: AccessToken;

  /**
   * @generated from field: proto.api.passport.v1.RefreshToken refresh_token = 2;
   */
  refreshToken?: RefreshToken;

  constructor(data?: PartialMessage<RefreshTokenResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RefreshTokenResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshTokenResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshTokenResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshTokenResponse;

  static equals(a: RefreshTokenResponse | PlainMessage<RefreshTokenResponse> | undefined, b: RefreshTokenResponse | PlainMessage<RefreshTokenResponse> | undefined): boolean;
}

/**
 * 登录方式：
 * 1. 手机号+验证码登录（注册）：auth_code、mobile_cc、mobile_num
 * 2. 手机号一键登录（注册）：client_auth_token
 * 3. 微信小程序授权手机号登录（注册）: wxa_auth_code
 *
 * @generated from message proto.api.passport.v1.SignInRequest
 */
export declare class SignInRequest extends Message<SignInRequest> {
  /**
   * 登录注册验证码
   *
   * @generated from field: string auth_code = 1;
   */
  authCode: string;

  /**
   * （86为默认）、中国香港（852）；中国澳门（853）；中国台湾（886）
   *
   * @generated from field: string mobile_cc = 2;
   */
  mobileCc: string;

  /**
   * @generated from field: string mobile_num = 3;
   */
  mobileNum: string;

  /**
   * 手机号一键登录，App 端 SDK 获取的登录 Token。
   *
   * @generated from field: string client_auth_token = 4;
   */
  clientAuthToken: string;

  /**
   * 微信小程序授权手机号登录，客户端获取到的凭证
   *
   * @generated from field: string wxa_auth_code = 5;
   */
  wxaAuthCode: string;

  /**
   * @generated from field: proto.base.v1.WxAppInfo wx_info = 6;
   */
  wxInfo?: WxAppInfo;

  constructor(data?: PartialMessage<SignInRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignInRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignInRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignInRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignInRequest;

  static equals(a: SignInRequest | PlainMessage<SignInRequest> | undefined, b: SignInRequest | PlainMessage<SignInRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SignInResponse
 */
export declare class SignInResponse extends Message<SignInResponse> {
  /**
   * @generated from field: proto.api.passport.v1.AccessToken access_token = 1;
   */
  accessToken?: AccessToken;

  /**
   * @generated from field: proto.api.passport.v1.RefreshToken refresh_token = 2;
   */
  refreshToken?: RefreshToken;

  /**
   * @generated from field: bool is_sign_up = 3;
   */
  isSignUp: boolean;

  /**
   * 登录行为是一个重复操作（通常发生在在登录态下重复调用 SignIn 接口）
   *
   * @generated from field: bool is_redid = 4;
   */
  isRedid: boolean;

  constructor(data?: PartialMessage<SignInResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignInResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignInResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignInResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignInResponse;

  static equals(a: SignInResponse | PlainMessage<SignInResponse> | undefined, b: SignInResponse | PlainMessage<SignInResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SignOffRequest
 */
export declare class SignOffRequest extends Message<SignOffRequest> {
  constructor(data?: PartialMessage<SignOffRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignOffRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignOffRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignOffRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignOffRequest;

  static equals(a: SignOffRequest | PlainMessage<SignOffRequest> | undefined, b: SignOffRequest | PlainMessage<SignOffRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SignOffResponse
 */
export declare class SignOffResponse extends Message<SignOffResponse> {
  /**
   * @generated from field: proto.api.passport.v1.AccessToken access_token = 1;
   */
  accessToken?: AccessToken;

  /**
   * @generated from field: proto.api.passport.v1.RefreshToken refresh_token = 2;
   */
  refreshToken?: RefreshToken;

  constructor(data?: PartialMessage<SignOffResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignOffResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignOffResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignOffResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignOffResponse;

  static equals(a: SignOffResponse | PlainMessage<SignOffResponse> | undefined, b: SignOffResponse | PlainMessage<SignOffResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SignOutRequest
 */
export declare class SignOutRequest extends Message<SignOutRequest> {
  constructor(data?: PartialMessage<SignOutRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignOutRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignOutRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignOutRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignOutRequest;

  static equals(a: SignOutRequest | PlainMessage<SignOutRequest> | undefined, b: SignOutRequest | PlainMessage<SignOutRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SignOutResponse
 */
export declare class SignOutResponse extends Message<SignOutResponse> {
  /**
   * @generated from field: proto.api.passport.v1.AccessToken access_token = 1;
   */
  accessToken?: AccessToken;

  /**
   * @generated from field: proto.api.passport.v1.RefreshToken refresh_token = 2;
   */
  refreshToken?: RefreshToken;

  constructor(data?: PartialMessage<SignOutResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignOutResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignOutResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignOutResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignOutResponse;

  static equals(a: SignOutResponse | PlainMessage<SignOutResponse> | undefined, b: SignOutResponse | PlainMessage<SignOutResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SendVerifyCodeRequest
 */
export declare class SendVerifyCodeRequest extends Message<SendVerifyCodeRequest> {
  /**
   * （86为默认）、中国香港（852）；中国澳门（853）；中国台湾（886）
   *
   * @generated from field: string mobile_cc = 1;
   */
  mobileCc: string;

  /**
   * @generated from field: string mobile_num = 2;
   */
  mobileNum: string;

  constructor(data?: PartialMessage<SendVerifyCodeRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SendVerifyCodeRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendVerifyCodeRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendVerifyCodeRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendVerifyCodeRequest;

  static equals(a: SendVerifyCodeRequest | PlainMessage<SendVerifyCodeRequest> | undefined, b: SendVerifyCodeRequest | PlainMessage<SendVerifyCodeRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SendVerifyCodeResponse
 */
export declare class SendVerifyCodeResponse extends Message<SendVerifyCodeResponse> {
  constructor(data?: PartialMessage<SendVerifyCodeResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SendVerifyCodeResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendVerifyCodeResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendVerifyCodeResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendVerifyCodeResponse;

  static equals(a: SendVerifyCodeResponse | PlainMessage<SendVerifyCodeResponse> | undefined, b: SendVerifyCodeResponse | PlainMessage<SendVerifyCodeResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SMSCountry
 */
export declare class SMSCountry extends Message<SMSCountry> {
  /**
   * Afghanistan
   *
   * @generated from field: string country = 1;
   */
  country: string;

  /**
   * AF
   *
   * @generated from field: string country_code = 2;
   */
  countryCode: string;

  /**
   * 93
   *
   * @generated from field: string mobile_code = 3;
   */
  mobileCode: string;

  /**
   * A
   *
   * @generated from field: string group_index = 4;
   */
  groupIndex: string;

  /**
   * 号码最小长度（不含空格）
   *
   * @generated from field: int32 min_len = 5;
   */
  minLen: number;

  /**
   * 号码最大长度（不含空格）
   *
   * @generated from field: int32 max_len = 6;
   */
  maxLen: number;

  /**
   * @generated from field: string region = 7;
   */
  region: string;

  constructor(data?: PartialMessage<SMSCountry>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SMSCountry";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SMSCountry;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SMSCountry;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SMSCountry;

  static equals(a: SMSCountry | PlainMessage<SMSCountry> | undefined, b: SMSCountry | PlainMessage<SMSCountry> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SMSRegion
 */
export declare class SMSRegion extends Message<SMSRegion> {
  /**
   * @generated from field: string region = 1;
   */
  region: string;

  /**
   * AF
   *
   * @generated from field: string region_code = 2;
   */
  regionCode: string;

  /**
   * 93
   *
   * @generated from field: string mobile_code = 3;
   */
  mobileCode: string;

  /**
   * A
   *
   * @generated from field: string group_index = 4;
   */
  groupIndex: string;

  /**
   * 号码最小长度（不含空格）
   *
   * @generated from field: int32 min_len = 5;
   */
  minLen: number;

  /**
   * 号码最大长度（不含空格）
   *
   * @generated from field: int32 max_len = 6;
   */
  maxLen: number;

  constructor(data?: PartialMessage<SMSRegion>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SMSRegion";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SMSRegion;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SMSRegion;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SMSRegion;

  static equals(a: SMSRegion | PlainMessage<SMSRegion> | undefined, b: SMSRegion | PlainMessage<SMSRegion> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.ListSupportedCountryRequest
 */
export declare class ListSupportedCountryRequest extends Message<ListSupportedCountryRequest> {
  constructor(data?: PartialMessage<ListSupportedCountryRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.ListSupportedCountryRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSupportedCountryRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSupportedCountryRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSupportedCountryRequest;

  static equals(a: ListSupportedCountryRequest | PlainMessage<ListSupportedCountryRequest> | undefined, b: ListSupportedCountryRequest | PlainMessage<ListSupportedCountryRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.ListSupportedCountryResponse
 */
export declare class ListSupportedCountryResponse extends Message<ListSupportedCountryResponse> {
  /**
   * @generated from field: repeated proto.api.passport.v1.SMSCountry countries = 1;
   */
  countries: SMSCountry[];

  /**
   * @generated from field: string comment = 2;
   */
  comment: string;

  constructor(data?: PartialMessage<ListSupportedCountryResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.ListSupportedCountryResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSupportedCountryResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSupportedCountryResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSupportedCountryResponse;

  static equals(a: ListSupportedCountryResponse | PlainMessage<ListSupportedCountryResponse> | undefined, b: ListSupportedCountryResponse | PlainMessage<ListSupportedCountryResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.ListSupportedRegionRequest
 */
export declare class ListSupportedRegionRequest extends Message<ListSupportedRegionRequest> {
  constructor(data?: PartialMessage<ListSupportedRegionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.ListSupportedRegionRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSupportedRegionRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSupportedRegionRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSupportedRegionRequest;

  static equals(a: ListSupportedRegionRequest | PlainMessage<ListSupportedRegionRequest> | undefined, b: ListSupportedRegionRequest | PlainMessage<ListSupportedRegionRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.ListSupportedRegionResponse
 */
export declare class ListSupportedRegionResponse extends Message<ListSupportedRegionResponse> {
  /**
   * @generated from field: repeated proto.api.passport.v1.SMSRegion regions = 1;
   */
  regions: SMSRegion[];

  constructor(data?: PartialMessage<ListSupportedRegionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.ListSupportedRegionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSupportedRegionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSupportedRegionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSupportedRegionResponse;

  static equals(a: ListSupportedRegionResponse | PlainMessage<ListSupportedRegionResponse> | undefined, b: ListSupportedRegionResponse | PlainMessage<ListSupportedRegionResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SendSMSAuthCodeRequest
 */
export declare class SendSMSAuthCodeRequest extends Message<SendSMSAuthCodeRequest> {
  /**
   * @generated from field: proto.api.passport.v1.AuthCodeUsage usage = 1;
   */
  usage: AuthCodeUsage;

  /**
   * Usage=VERIFY 时，不需要该参数，系统默认使用账号当前绑定的手机号
   *
   * @generated from field: proto.base.v1.Mobile mobile = 2;
   */
  mobile?: Mobile;

  constructor(data?: PartialMessage<SendSMSAuthCodeRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SendSMSAuthCodeRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendSMSAuthCodeRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendSMSAuthCodeRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendSMSAuthCodeRequest;

  static equals(a: SendSMSAuthCodeRequest | PlainMessage<SendSMSAuthCodeRequest> | undefined, b: SendSMSAuthCodeRequest | PlainMessage<SendSMSAuthCodeRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SendSMSAuthCodeResponse
 */
export declare class SendSMSAuthCodeResponse extends Message<SendSMSAuthCodeResponse> {
  constructor(data?: PartialMessage<SendSMSAuthCodeResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SendSMSAuthCodeResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendSMSAuthCodeResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendSMSAuthCodeResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendSMSAuthCodeResponse;

  static equals(a: SendSMSAuthCodeResponse | PlainMessage<SendSMSAuthCodeResponse> | undefined, b: SendSMSAuthCodeResponse | PlainMessage<SendSMSAuthCodeResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.VerifySMSAuthCodeRequest
 */
export declare class VerifySMSAuthCodeRequest extends Message<VerifySMSAuthCodeRequest> {
  /**
   * @generated from field: proto.api.passport.v1.AuthCodeUsage usage = 1;
   */
  usage: AuthCodeUsage;

  /**
   * @generated from field: string auth_code = 2;
   */
  authCode: string;

  constructor(data?: PartialMessage<VerifySMSAuthCodeRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.VerifySMSAuthCodeRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VerifySMSAuthCodeRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VerifySMSAuthCodeRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VerifySMSAuthCodeRequest;

  static equals(a: VerifySMSAuthCodeRequest | PlainMessage<VerifySMSAuthCodeRequest> | undefined, b: VerifySMSAuthCodeRequest | PlainMessage<VerifySMSAuthCodeRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.VerifySMSAuthCodeResponse
 */
export declare class VerifySMSAuthCodeResponse extends Message<VerifySMSAuthCodeResponse> {
  /**
   * 例如：换绑时需要两步验证码绑定手机号的应用使用
   *
   * @generated from field: string token = 1;
   */
  token: string;

  constructor(data?: PartialMessage<VerifySMSAuthCodeResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.VerifySMSAuthCodeResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VerifySMSAuthCodeResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VerifySMSAuthCodeResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VerifySMSAuthCodeResponse;

  static equals(a: VerifySMSAuthCodeResponse | PlainMessage<VerifySMSAuthCodeResponse> | undefined, b: VerifySMSAuthCodeResponse | PlainMessage<VerifySMSAuthCodeResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.BindMobileRequest
 */
export declare class BindMobileRequest extends Message<BindMobileRequest> {
  /**
   * @generated from field: string auth_code = 1;
   */
  authCode: string;

  /**
   * @generated from field: proto.base.v1.Mobile mobile = 2;
   */
  mobile?: Mobile;

  constructor(data?: PartialMessage<BindMobileRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.BindMobileRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BindMobileRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BindMobileRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BindMobileRequest;

  static equals(a: BindMobileRequest | PlainMessage<BindMobileRequest> | undefined, b: BindMobileRequest | PlainMessage<BindMobileRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.BindMobileResponse
 */
export declare class BindMobileResponse extends Message<BindMobileResponse> {
  constructor(data?: PartialMessage<BindMobileResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.BindMobileResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BindMobileResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BindMobileResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BindMobileResponse;

  static equals(a: BindMobileResponse | PlainMessage<BindMobileResponse> | undefined, b: BindMobileResponse | PlainMessage<BindMobileResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RebindMobileRequest
 */
export declare class RebindMobileRequest extends Message<RebindMobileRequest> {
  /**
   * @generated from field: string auth_code = 1;
   */
  authCode: string;

  /**
   * @generated from field: string token = 2;
   */
  token: string;

  /**
   * @generated from field: proto.base.v1.Mobile mobile = 3;
   */
  mobile?: Mobile;

  constructor(data?: PartialMessage<RebindMobileRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RebindMobileRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RebindMobileRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RebindMobileRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RebindMobileRequest;

  static equals(a: RebindMobileRequest | PlainMessage<RebindMobileRequest> | undefined, b: RebindMobileRequest | PlainMessage<RebindMobileRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RebindMobileResponse
 */
export declare class RebindMobileResponse extends Message<RebindMobileResponse> {
  constructor(data?: PartialMessage<RebindMobileResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RebindMobileResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RebindMobileResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RebindMobileResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RebindMobileResponse;

  static equals(a: RebindMobileResponse | PlainMessage<RebindMobileResponse> | undefined, b: RebindMobileResponse | PlainMessage<RebindMobileResponse> | undefined): boolean;
}

/**
 * =========================================== Email API ===========================================
 *
 * @generated from message proto.api.passport.v1.SendEmailAuthCodeRequest
 */
export declare class SendEmailAuthCodeRequest extends Message<SendEmailAuthCodeRequest> {
  /**
   * @generated from field: proto.api.passport.v1.AuthCodeUsage usage = 1;
   */
  usage: AuthCodeUsage;

  /**
   * @generated from field: string email_address = 2;
   */
  emailAddress: string;

  constructor(data?: PartialMessage<SendEmailAuthCodeRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SendEmailAuthCodeRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendEmailAuthCodeRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendEmailAuthCodeRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendEmailAuthCodeRequest;

  static equals(a: SendEmailAuthCodeRequest | PlainMessage<SendEmailAuthCodeRequest> | undefined, b: SendEmailAuthCodeRequest | PlainMessage<SendEmailAuthCodeRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SendEmailAuthCodeResponse
 */
export declare class SendEmailAuthCodeResponse extends Message<SendEmailAuthCodeResponse> {
  constructor(data?: PartialMessage<SendEmailAuthCodeResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SendEmailAuthCodeResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendEmailAuthCodeResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendEmailAuthCodeResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendEmailAuthCodeResponse;

  static equals(a: SendEmailAuthCodeResponse | PlainMessage<SendEmailAuthCodeResponse> | undefined, b: SendEmailAuthCodeResponse | PlainMessage<SendEmailAuthCodeResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.VerifyEmailAuthCodeRequest
 */
export declare class VerifyEmailAuthCodeRequest extends Message<VerifyEmailAuthCodeRequest> {
  /**
   * @generated from field: proto.api.passport.v1.AuthCodeUsage usage = 1;
   */
  usage: AuthCodeUsage;

  /**
   * @generated from field: string auth_code = 2;
   */
  authCode: string;

  constructor(data?: PartialMessage<VerifyEmailAuthCodeRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.VerifyEmailAuthCodeRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VerifyEmailAuthCodeRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VerifyEmailAuthCodeRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VerifyEmailAuthCodeRequest;

  static equals(a: VerifyEmailAuthCodeRequest | PlainMessage<VerifyEmailAuthCodeRequest> | undefined, b: VerifyEmailAuthCodeRequest | PlainMessage<VerifyEmailAuthCodeRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.VerifyEmailAuthCodeResponse
 */
export declare class VerifyEmailAuthCodeResponse extends Message<VerifyEmailAuthCodeResponse> {
  /**
   * 例如：换绑时需要两步验证码绑定邮箱的应用使用
   *
   * @generated from field: string token = 1;
   */
  token: string;

  constructor(data?: PartialMessage<VerifyEmailAuthCodeResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.VerifyEmailAuthCodeResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VerifyEmailAuthCodeResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VerifyEmailAuthCodeResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VerifyEmailAuthCodeResponse;

  static equals(a: VerifyEmailAuthCodeResponse | PlainMessage<VerifyEmailAuthCodeResponse> | undefined, b: VerifyEmailAuthCodeResponse | PlainMessage<VerifyEmailAuthCodeResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.BindEmailRequest
 */
export declare class BindEmailRequest extends Message<BindEmailRequest> {
  /**
   * @generated from field: string auth_code = 1;
   */
  authCode: string;

  /**
   * @generated from field: string email_address = 2;
   */
  emailAddress: string;

  constructor(data?: PartialMessage<BindEmailRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.BindEmailRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BindEmailRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BindEmailRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BindEmailRequest;

  static equals(a: BindEmailRequest | PlainMessage<BindEmailRequest> | undefined, b: BindEmailRequest | PlainMessage<BindEmailRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.BindEmailResponse
 */
export declare class BindEmailResponse extends Message<BindEmailResponse> {
  constructor(data?: PartialMessage<BindEmailResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.BindEmailResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BindEmailResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BindEmailResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BindEmailResponse;

  static equals(a: BindEmailResponse | PlainMessage<BindEmailResponse> | undefined, b: BindEmailResponse | PlainMessage<BindEmailResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RebindEmailRequest
 */
export declare class RebindEmailRequest extends Message<RebindEmailRequest> {
  /**
   * @generated from field: string auth_code = 1;
   */
  authCode: string;

  /**
   * @generated from field: string token = 2;
   */
  token: string;

  /**
   * @generated from field: string email_address = 3;
   */
  emailAddress: string;

  constructor(data?: PartialMessage<RebindEmailRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RebindEmailRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RebindEmailRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RebindEmailRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RebindEmailRequest;

  static equals(a: RebindEmailRequest | PlainMessage<RebindEmailRequest> | undefined, b: RebindEmailRequest | PlainMessage<RebindEmailRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RebindEmailResponse
 */
export declare class RebindEmailResponse extends Message<RebindEmailResponse> {
  constructor(data?: PartialMessage<RebindEmailResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RebindEmailResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RebindEmailResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RebindEmailResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RebindEmailResponse;

  static equals(a: RebindEmailResponse | PlainMessage<RebindEmailResponse> | undefined, b: RebindEmailResponse | PlainMessage<RebindEmailResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SignInByPasswordRequest
 */
export declare class SignInByPasswordRequest extends Message<SignInByPasswordRequest> {
  /**
   * @generated from field: string username = 1;
   */
  username: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;

  constructor(data?: PartialMessage<SignInByPasswordRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignInByPasswordRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignInByPasswordRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignInByPasswordRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignInByPasswordRequest;

  static equals(a: SignInByPasswordRequest | PlainMessage<SignInByPasswordRequest> | undefined, b: SignInByPasswordRequest | PlainMessage<SignInByPasswordRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SignInByPasswordResponse
 */
export declare class SignInByPasswordResponse extends Message<SignInByPasswordResponse> {
  /**
   * @generated from field: proto.api.passport.v1.AccessToken access_token = 1;
   */
  accessToken?: AccessToken;

  /**
   * @generated from field: proto.api.passport.v1.RefreshToken refresh_token = 2;
   */
  refreshToken?: RefreshToken;

  constructor(data?: PartialMessage<SignInByPasswordResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SignInByPasswordResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignInByPasswordResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignInByPasswordResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignInByPasswordResponse;

  static equals(a: SignInByPasswordResponse | PlainMessage<SignInByPasswordResponse> | undefined, b: SignInByPasswordResponse | PlainMessage<SignInByPasswordResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SetPasswordRequest
 */
export declare class SetPasswordRequest extends Message<SetPasswordRequest> {
  /**
   * @generated from field: repeated string passwords = 1;
   */
  passwords: string[];

  /**
   * @generated from field: string auth_code = 2;
   */
  authCode: string;

  constructor(data?: PartialMessage<SetPasswordRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SetPasswordRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetPasswordRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetPasswordRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetPasswordRequest;

  static equals(a: SetPasswordRequest | PlainMessage<SetPasswordRequest> | undefined, b: SetPasswordRequest | PlainMessage<SetPasswordRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.SetPasswordResponse
 */
export declare class SetPasswordResponse extends Message<SetPasswordResponse> {
  constructor(data?: PartialMessage<SetPasswordResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.SetPasswordResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetPasswordResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetPasswordResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetPasswordResponse;

  static equals(a: SetPasswordResponse | PlainMessage<SetPasswordResponse> | undefined, b: SetPasswordResponse | PlainMessage<SetPasswordResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RetrievePasswordRequest
 */
export declare class RetrievePasswordRequest extends Message<RetrievePasswordRequest> {
  /**
   * @generated from field: repeated string passwords = 1;
   */
  passwords: string[];

  /**
   * @generated from field: string auth_code = 2;
   */
  authCode: string;

  /**
   * @generated from field: proto.base.v1.Mobile mobile = 3;
   */
  mobile?: Mobile;

  constructor(data?: PartialMessage<RetrievePasswordRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RetrievePasswordRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RetrievePasswordRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RetrievePasswordRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RetrievePasswordRequest;

  static equals(a: RetrievePasswordRequest | PlainMessage<RetrievePasswordRequest> | undefined, b: RetrievePasswordRequest | PlainMessage<RetrievePasswordRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.RetrievePasswordResponse
 */
export declare class RetrievePasswordResponse extends Message<RetrievePasswordResponse> {
  constructor(data?: PartialMessage<RetrievePasswordResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.RetrievePasswordResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RetrievePasswordResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RetrievePasswordResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RetrievePasswordResponse;

  static equals(a: RetrievePasswordResponse | PlainMessage<RetrievePasswordResponse> | undefined, b: RetrievePasswordResponse | PlainMessage<RetrievePasswordResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.GetWXSessionRequest
 */
export declare class GetWXSessionRequest extends Message<GetWXSessionRequest> {
  /**
   * @generated from field: string code = 1;
   */
  code: string;

  /**
   * @generated from field: proto.base.v1.WxAppInfo wx_info = 2;
   */
  wxInfo?: WxAppInfo;

  constructor(data?: PartialMessage<GetWXSessionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.GetWXSessionRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetWXSessionRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetWXSessionRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetWXSessionRequest;

  static equals(a: GetWXSessionRequest | PlainMessage<GetWXSessionRequest> | undefined, b: GetWXSessionRequest | PlainMessage<GetWXSessionRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.GetWXSessionResponse
 */
export declare class GetWXSessionResponse extends Message<GetWXSessionResponse> {
  /**
   * @generated from field: proto.base.v1.WX wx = 2;
   */
  wx?: WX;

  constructor(data?: PartialMessage<GetWXSessionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.GetWXSessionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetWXSessionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetWXSessionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetWXSessionResponse;

  static equals(a: GetWXSessionResponse | PlainMessage<GetWXSessionResponse> | undefined, b: GetWXSessionResponse | PlainMessage<GetWXSessionResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.GetQQSessionRequest
 */
export declare class GetQQSessionRequest extends Message<GetQQSessionRequest> {
  /**
   * @generated from field: string code = 1;
   */
  code: string;

  constructor(data?: PartialMessage<GetQQSessionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.GetQQSessionRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetQQSessionRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetQQSessionRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetQQSessionRequest;

  static equals(a: GetQQSessionRequest | PlainMessage<GetQQSessionRequest> | undefined, b: GetQQSessionRequest | PlainMessage<GetQQSessionRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.GetQQSessionResponse
 */
export declare class GetQQSessionResponse extends Message<GetQQSessionResponse> {
  /**
   * @generated from field: proto.base.v1.QQ qq = 1;
   */
  qq?: QQ;

  constructor(data?: PartialMessage<GetQQSessionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.GetQQSessionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetQQSessionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetQQSessionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetQQSessionResponse;

  static equals(a: GetQQSessionResponse | PlainMessage<GetQQSessionResponse> | undefined, b: GetQQSessionResponse | PlainMessage<GetQQSessionResponse> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.OAuthStateRequest
 */
export declare class OAuthStateRequest extends Message<OAuthStateRequest> {
  constructor(data?: PartialMessage<OAuthStateRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.OAuthStateRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OAuthStateRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OAuthStateRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OAuthStateRequest;

  static equals(a: OAuthStateRequest | PlainMessage<OAuthStateRequest> | undefined, b: OAuthStateRequest | PlainMessage<OAuthStateRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.OAuthStateResponse
 */
export declare class OAuthStateResponse extends Message<OAuthStateResponse> {
  /**
   * 不用存储，即用即毁；
   *
   * @generated from field: string nonce = 1;
   */
  nonce: string;

  constructor(data?: PartialMessage<OAuthStateResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.OAuthStateResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OAuthStateResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OAuthStateResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OAuthStateResponse;

  static equals(a: OAuthStateResponse | PlainMessage<OAuthStateResponse> | undefined, b: OAuthStateResponse | PlainMessage<OAuthStateResponse> | undefined): boolean;
}

/**
 * OAuth2.0 授权码模式
 * google: https://developers.google.com/identity/protocols/oauth2/native-app?hl=zh-cn
 * apple: https://developer.apple.com/documentation/sign_in_with_apple/sign_in_with_apple_rest_api/authenticating_users_with_sign_in_with_apple
 * meta: https://developers.facebook.com/docs/facebook-login/guides/advanced/manual-flow
 *
 * @generated from message proto.api.passport.v1.OAuthRequest
 */
export declare class OAuthRequest extends Message<OAuthRequest> {
  /**
   * Google 为 nonce；Apple 为 idToken
   *
   * @generated from field: string state = 1;
   */
  state: string;

  /**
   * @generated from field: string google_auth_code = 2;
   */
  googleAuthCode: string;

  /**
   * @generated from field: string apple_auth_code = 3;
   */
  appleAuthCode: string;

  /**
   * @generated from field: string redirect_uri = 4;
   */
  redirectUri: string;

  /**
   * @generated from field: string meta_access_token = 5;
   */
  metaAccessToken: string;

  /**
   * @generated from field: string discord_auth_code = 6;
   */
  discordAuthCode: string;

  /**
   * @generated from field: bool auth_binding_email = 7;
   */
  authBindingEmail: boolean;

  /**
   * @generated from field: string meta_auth_code = 8;
   */
  metaAuthCode: string;

  constructor(data?: PartialMessage<OAuthRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.OAuthRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OAuthRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OAuthRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OAuthRequest;

  static equals(a: OAuthRequest | PlainMessage<OAuthRequest> | undefined, b: OAuthRequest | PlainMessage<OAuthRequest> | undefined): boolean;
}

/**
 * @generated from message proto.api.passport.v1.OAuthResponse
 */
export declare class OAuthResponse extends Message<OAuthResponse> {
  /**
   * @generated from field: proto.api.passport.v1.AccessToken access_token = 1;
   */
  accessToken?: AccessToken;

  /**
   * @generated from field: proto.api.passport.v1.RefreshToken refresh_token = 2;
   */
  refreshToken?: RefreshToken;

  /**
   * @generated from field: bool is_sign_up = 3;
   */
  isSignUp: boolean;

  constructor(data?: PartialMessage<OAuthResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.api.passport.v1.OAuthResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OAuthResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OAuthResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OAuthResponse;

  static equals(a: OAuthResponse | PlainMessage<OAuthResponse> | undefined, b: OAuthResponse | PlainMessage<OAuthResponse> | undefined): boolean;
}

