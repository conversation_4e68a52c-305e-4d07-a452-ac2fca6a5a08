// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file proto/api/passport/v1/service.proto (package proto.api.passport.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { BindEmailRequest, BindEmailResponse, BindMobileRequest, BindMobileResponse, GetQQSessionRequest, GetQQSessionResponse, GetWXSessionRequest, GetWXSessionResponse, ListSupportedCountryRequest, ListSupportedCountryResponse, ListSupportedRegionRequest, ListSupportedRegionResponse, OAuthRequest, OAuthResponse, OAuthStateRequest, OAuthStateResponse, RebindEmailRequest, RebindEmailResponse, RebindMobileRequest, RebindMobileResponse, RefreshTokenRequest, RefreshTokenResponse, RegisterDeviceRequest, RegisterDeviceResponse, RetrievePasswordRequest, RetrievePasswordResponse, SendEmailAuthCodeRequest, SendEmailAuthCodeResponse, SendSMSAuthCodeRequest, SendSMSAuthCodeResponse, SendVerifyCodeRequest, SendVerifyCodeResponse, SetPasswordRequest, SetPasswordResponse, SignInByPasswordRequest, SignInByPasswordResponse, SignInRequest, SignInResponse, SignOffRequest, SignOffResponse, SignOutRequest, SignOutResponse, SyncDeviceActivationRequest, SyncDeviceActivationResponse, VerifyEmailAuthCodeRequest, VerifyEmailAuthCodeResponse, VerifySMSAuthCodeRequest, VerifySMSAuthCodeResponse } from "./service_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service proto.api.passport.v1.PassportService
 */
export declare const PassportService: {
  readonly typeName: "proto.api.passport.v1.PassportService",
  readonly methods: {
    /**
     * @generated from rpc proto.api.passport.v1.PassportService.RegisterDevice
     */
    readonly registerDevice: {
      readonly name: "RegisterDevice",
      readonly I: typeof RegisterDeviceRequest,
      readonly O: typeof RegisterDeviceResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.passport.v1.PassportService.SyncDeviceActivation
     */
    readonly syncDeviceActivation: {
      readonly name: "SyncDeviceActivation",
      readonly I: typeof SyncDeviceActivationRequest,
      readonly O: typeof SyncDeviceActivationResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.passport.v1.PassportService.RefreshToken
     */
    readonly refreshToken: {
      readonly name: "RefreshToken",
      readonly I: typeof RefreshTokenRequest,
      readonly O: typeof RefreshTokenResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 登录&注册
     *
     * @generated from rpc proto.api.passport.v1.PassportService.SignIn
     */
    readonly signIn: {
      readonly name: "SignIn",
      readonly I: typeof SignInRequest,
      readonly O: typeof SignInResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 退登
     *
     * @generated from rpc proto.api.passport.v1.PassportService.SignOff
     */
    readonly signOff: {
      readonly name: "SignOff",
      readonly I: typeof SignOffRequest,
      readonly O: typeof SignOffResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 注销
     *
     * @generated from rpc proto.api.passport.v1.PassportService.SignOut
     */
    readonly signOut: {
      readonly name: "SignOut",
      readonly I: typeof SignOutRequest,
      readonly O: typeof SignOutResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 短信服务支持的国家
     *
     * @generated from rpc proto.api.passport.v1.PassportService.ListSupportedCountry
     */
    readonly listSupportedCountry: {
      readonly name: "ListSupportedCountry",
      readonly I: typeof ListSupportedCountryRequest,
      readonly O: typeof ListSupportedCountryResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 短信服务支持的Region 代替 ListSupportedCountry
     *
     * @generated from rpc proto.api.passport.v1.PassportService.ListSupportedRegion
     */
    readonly listSupportedRegion: {
      readonly name: "ListSupportedRegion",
      readonly I: typeof ListSupportedRegionRequest,
      readonly O: typeof ListSupportedRegionResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 发送手机登录验证码
     *
     * @generated from rpc proto.api.passport.v1.PassportService.SendVerifyCode
     */
    readonly sendVerifyCode: {
      readonly name: "SendVerifyCode",
      readonly I: typeof SendVerifyCodeRequest,
      readonly O: typeof SendVerifyCodeResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * >>> 手机号
     *
     * 发送手机号验证码（登录态下使用，不支持 SignIn 场景）
     *
     * @generated from rpc proto.api.passport.v1.PassportService.SendSMSAuthCode
     */
    readonly sendSMSAuthCode: {
      readonly name: "SendSMSAuthCode",
      readonly I: typeof SendSMSAuthCodeRequest,
      readonly O: typeof SendSMSAuthCodeResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 验证验证码
     *
     * @generated from rpc proto.api.passport.v1.PassportService.VerifySMSAuthCode
     */
    readonly verifySMSAuthCode: {
      readonly name: "VerifySMSAuthCode",
      readonly I: typeof VerifySMSAuthCodeRequest,
      readonly O: typeof VerifySMSAuthCodeResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 绑定手机号（默认仅能绑定一个手机号，需要校验当前账号未绑定手机号，换绑请调用 RebindMobile）
     *
     * @generated from rpc proto.api.passport.v1.PassportService.BindMobile
     */
    readonly bindMobile: {
      readonly name: "BindMobile",
      readonly I: typeof BindMobileRequest,
      readonly O: typeof BindMobileResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 重新（换）绑定手机号（短信验证原手机号，获取临时token → 携带临时token验证码新手机号 → 确认新手机号为全新号码，完成换绑；第一步为应用可选配置）
     *
     * @generated from rpc proto.api.passport.v1.PassportService.RebindMobile
     */
    readonly rebindMobile: {
      readonly name: "RebindMobile",
      readonly I: typeof RebindMobileRequest,
      readonly O: typeof RebindMobileResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * >>> 邮箱
     *
     * 发送邮件验证码
     *
     * @generated from rpc proto.api.passport.v1.PassportService.SendEmailAuthCode
     */
    readonly sendEmailAuthCode: {
      readonly name: "SendEmailAuthCode",
      readonly I: typeof SendEmailAuthCodeRequest,
      readonly O: typeof SendEmailAuthCodeResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc proto.api.passport.v1.PassportService.VerifyEmailAuthCode
     */
    readonly verifyEmailAuthCode: {
      readonly name: "VerifyEmailAuthCode",
      readonly I: typeof VerifyEmailAuthCodeRequest,
      readonly O: typeof VerifyEmailAuthCodeResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 绑定邮箱（默认仅能绑定一个邮箱）
     *
     * @generated from rpc proto.api.passport.v1.PassportService.BindEmail
     */
    readonly bindEmail: {
      readonly name: "BindEmail",
      readonly I: typeof BindEmailRequest,
      readonly O: typeof BindEmailResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 重新（换）绑定邮箱
     *
     * @generated from rpc proto.api.passport.v1.PassportService.RebindEmail
     */
    readonly rebindEmail: {
      readonly name: "RebindEmail",
      readonly I: typeof RebindEmailRequest,
      readonly O: typeof RebindEmailResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * >>> 账密
     *
     * 账密登录
     *
     * @generated from rpc proto.api.passport.v1.PassportService.SignInByPassword
     */
    readonly signInByPassword: {
      readonly name: "SignInByPassword",
      readonly I: typeof SignInByPasswordRequest,
      readonly O: typeof SignInByPasswordResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 设置密码
     *
     * @generated from rpc proto.api.passport.v1.PassportService.SetPassword
     */
    readonly setPassword: {
      readonly name: "SetPassword",
      readonly I: typeof SetPasswordRequest,
      readonly O: typeof SetPasswordResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 找回密码
     *
     * @generated from rpc proto.api.passport.v1.PassportService.RetrievePassword
     */
    readonly retrievePassword: {
      readonly name: "RetrievePassword",
      readonly I: typeof RetrievePasswordRequest,
      readonly O: typeof RetrievePasswordResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * >>> 微信
     *
     *  https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
     *
     * @generated from rpc proto.api.passport.v1.PassportService.GetWXSession
     */
    readonly getWXSession: {
      readonly name: "GetWXSession",
      readonly I: typeof GetWXSessionRequest,
      readonly O: typeof GetWXSessionResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * >>> QQ
     *
     * https://q.qq.com/wiki/develop/miniprogram/server/open_port/port_login.html#code2session
     *
     * @generated from rpc proto.api.passport.v1.PassportService.GetQQSession
     */
    readonly getQQSession: {
      readonly name: "GetQQSession",
      readonly I: typeof GetQQSessionRequest,
      readonly O: typeof GetQQSessionResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * >>> 三方
     *
     * 客户端三方授权临时身份
     *
     * @generated from rpc proto.api.passport.v1.PassportService.OAuthState
     */
    readonly oAuthState: {
      readonly name: "OAuthState",
      readonly I: typeof OAuthStateRequest,
      readonly O: typeof OAuthStateResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 三方（OAuth2.0）授权登录
     *
     * @generated from rpc proto.api.passport.v1.PassportService.OAuth
     */
    readonly oAuth: {
      readonly name: "OAuth",
      readonly I: typeof OAuthRequest,
      readonly O: typeof OAuthResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

