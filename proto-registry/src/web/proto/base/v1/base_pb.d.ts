// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file proto/base/v1/base.proto (package proto.base.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Timestamp } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message proto.base.v1.Mobile
 */
export declare class Mobile extends Message<Mobile> {
  /**
   * @generated from field: string cc = 1;
   */
  cc: string;

  /**
   * 默认脱敏
   *
   * @generated from field: string number = 2;
   */
  number: string;

  constructor(data?: PartialMessage<Mobile>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.base.v1.Mobile";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Mobile;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Mobile;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Mobile;

  static equals(a: Mobile | PlainMessage<Mobile> | undefined, b: Mobile | PlainMessage<Mobile> | undefined): boolean;
}

/**
 * @generated from message proto.base.v1.WX
 */
export declare class WX extends Message<WX> {
  /**
   * @generated from field: string open_id = 1;
   */
  openId: string;

  /**
   * @generated from field: string union_id = 2;
   */
  unionId: string;

  constructor(data?: PartialMessage<WX>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.base.v1.WX";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WX;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WX;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WX;

  static equals(a: WX | PlainMessage<WX> | undefined, b: WX | PlainMessage<WX> | undefined): boolean;
}

/**
 * @generated from message proto.base.v1.QQ
 */
export declare class QQ extends Message<QQ> {
  /**
   * @generated from field: string open_id = 1;
   */
  openId: string;

  /**
   * @generated from field: string union_id = 2;
   */
  unionId: string;

  constructor(data?: PartialMessage<QQ>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.base.v1.QQ";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QQ;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QQ;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QQ;

  static equals(a: QQ | PlainMessage<QQ> | undefined, b: QQ | PlainMessage<QQ> | undefined): boolean;
}

/**
 * @generated from message proto.base.v1.Apple
 */
export declare class Apple extends Message<Apple> {
  /**
   * @generated from field: string open_id = 1;
   */
  openId: string;

  constructor(data?: PartialMessage<Apple>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.base.v1.Apple";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Apple;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Apple;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Apple;

  static equals(a: Apple | PlainMessage<Apple> | undefined, b: Apple | PlainMessage<Apple> | undefined): boolean;
}

/**
 * @generated from message proto.base.v1.Google
 */
export declare class Google extends Message<Google> {
  /**
   * @generated from field: string open_id = 1;
   */
  openId: string;

  constructor(data?: PartialMessage<Google>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.base.v1.Google";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Google;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Google;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Google;

  static equals(a: Google | PlainMessage<Google> | undefined, b: Google | PlainMessage<Google> | undefined): boolean;
}

/**
 * @generated from message proto.base.v1.Email
 */
export declare class Email extends Message<Email> {
  /**
   * @generated from field: string address = 1;
   */
  address: string;

  constructor(data?: PartialMessage<Email>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.base.v1.Email";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Email;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Email;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Email;

  static equals(a: Email | PlainMessage<Email> | undefined, b: Email | PlainMessage<Email> | undefined): boolean;
}

/**
 * @generated from message proto.base.v1.Binding
 */
export declare class Binding extends Message<Binding> {
  /**
   * @generated from field: proto.base.v1.Mobile mobile = 1;
   */
  mobile?: Mobile;

  /**
   * @generated from field: proto.base.v1.Apple apple = 2;
   */
  apple?: Apple;

  /**
   * @generated from field: proto.base.v1.Google google = 3;
   */
  google?: Google;

  /**
   * @generated from field: proto.base.v1.Email email = 4;
   */
  email?: Email;

  constructor(data?: PartialMessage<Binding>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.base.v1.Binding";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Binding;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Binding;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Binding;

  static equals(a: Binding | PlainMessage<Binding> | undefined, b: Binding | PlainMessage<Binding> | undefined): boolean;
}

/**
 * @generated from message proto.base.v1.Journal
 */
export declare class Journal extends Message<Journal> {
  /**
   * @generated from field: proto.base.v1.Journal.Type type = 1;
   */
  type: Journal_Type;

  /**
   * @generated from field: google.protobuf.Timestamp report_time = 2;
   */
  reportTime?: Timestamp;

  /**
   * @generated from field: string data = 3;
   */
  data: string;

  constructor(data?: PartialMessage<Journal>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.base.v1.Journal";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Journal;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Journal;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Journal;

  static equals(a: Journal | PlainMessage<Journal> | undefined, b: Journal | PlainMessage<Journal> | undefined): boolean;
}

/**
 * @generated from enum proto.base.v1.Journal.Type
 */
export declare enum Journal_Type {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: CAID = 1;
   */
  CAID = 1,
}

/**
 * @generated from message proto.base.v1.WxAppInfo
 */
export declare class WxAppInfo extends Message<WxAppInfo> {
  /**
   * @generated from field: string req_time_ms = 1;
   */
  reqTimeMs: string;

  /**
   * @generated from field: map<string, string> query_info = 2;
   */
  queryInfo: { [key: string]: string };

  constructor(data?: PartialMessage<WxAppInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.base.v1.WxAppInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WxAppInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WxAppInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WxAppInfo;

  static equals(a: WxAppInfo | PlainMessage<WxAppInfo> | undefined, b: WxAppInfo | PlainMessage<WxAppInfo> | undefined): boolean;
}

