// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file proto/error/v1/error.proto (package proto.error.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * code format
 * | module | err_code |
 * | xxx    | xxx      |
 *
 * @generated from enum proto.error.v1.Code
 */
export declare enum Code {
  /**
   * @generated from enum value: CODE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * token: 100
   *
   * token is missing，httpCode = 400 CodeInvalidArgument
   *
   * @generated from enum value: CODE_TOKEN_MISSING = 100001;
   */
  TOKEN_MISSING = 100001,

  /**
   * token is illegal, httpCode = 401 CodeUnauthenticated，通常为 token 解析或校验失败
   *
   * @generated from enum value: CODE_TOKEN_ILLEGAL = 100002;
   */
  TOKEN_ILLEGAL = 100002,

  /**
   * token is expired, httpCode = 401 CodeUnauthenticated
   *
   * @generated from enum value: CODE_TOKEN_EXPIRED = 100003;
   */
  TOKEN_EXPIRED = 100003,

  /**
   * account: 101
   *
   * account is illegal, httpCode = 400 CodeInvalidArgument，
   *
   * @generated from enum value: CODE_ACCOUNT_ILLEGAL = 101001;
   */
  ACCOUNT_ILLEGAL = 101001,

  /**
   * 验证码错误 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_AUTH_CODE_INVALID = 101002;
   */
  ACCOUNT_AUTH_CODE_INVALID = 101002,

  /**
   * 验证码已失效 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_AUTH_CODE_EXPIRED = 101003;
   */
  ACCOUNT_AUTH_CODE_EXPIRED = 101003,

  /**
   * 验证码发送过于频繁 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_AUTH_CODE_SEND_TOO_OFTEN = 101004;
   */
  ACCOUNT_AUTH_CODE_SEND_TOO_OFTEN = 101004,

  /**
   * 账号登录操作过于频繁 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_SIGN_IN_TOO_OFTEN = 101005;
   */
  ACCOUNT_SIGN_IN_TOO_OFTEN = 101005,

  /**
   * 账号注册操作过于频繁 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_SIGN_UP_TOO_OFTEN = 101006;
   */
  ACCOUNT_SIGN_UP_TOO_OFTEN = 101006,

  /**
   * 账号注销操作过于频繁 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_SIGN_OUT_TOO_OFTEN = 101007;
   */
  ACCOUNT_SIGN_OUT_TOO_OFTEN = 101007,

  /**
   * 账号被封禁（baned=true，通常为手动封禁） httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_HAS_BEEN_BANED = 101008;
   */
  ACCOUNT_HAS_BEEN_BANED = 101008,

  /**
   * 账号被安全封禁（baned可能为false，被安全审核封禁）httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_HAS_BEEN_RISK_BANED = 101009;
   */
  ACCOUNT_HAS_BEEN_RISK_BANED = 101009,

  /**
   * 账号不存在，通常为账号已注销，使用了错误环境的 token 而导致的账号不存在问题 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_NOT_EXISTING = 101010;
   */
  ACCOUNT_NOT_EXISTING = 101010,

  /**
   * 一键登录，通过客户端 token 获取手机号失败 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_GET_MOBILE_BY_CLIENT_AUTH_TOKEN_FAILED = 101011;
   */
  ACCOUNT_GET_MOBILE_BY_CLIENT_AUTH_TOKEN_FAILED = 101011,

  /**
   * 微信小程序授权获取手机号失败 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_GET_MOBILE_BY_WXA_CODE_FAILED = 101012;
   */
  ACCOUNT_GET_MOBILE_BY_WXA_CODE_FAILED = 101012,

  /**
   * OAuth 授权失败 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_OAUTH_FAILED = 101013;
   */
  ACCOUNT_OAUTH_FAILED = 101013,

  /**
   * 核查验证码票据结果不通过 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_CAPTCHA_RESULT_NO_PASS = 101014;
   */
  ACCOUNT_CAPTCHA_RESULT_NO_PASS = 101014,

  /**
   * 账号换绑定信息（手机号、邮箱等）时，临时令牌不可用 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_REBIND_TOKEN_INVALID = 101015;
   */
  ACCOUNT_REBIND_TOKEN_INVALID = 101015,

  /**
   * 手机号已被绑定 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_MOBILE_HAS_BEEN_BOUND = 101016;
   */
  ACCOUNT_MOBILE_HAS_BEEN_BOUND = 101016,

  /**
   * 邮箱已被绑定 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_EMAIL_HAS_BEEN_BOUND = 101017;
   */
  ACCOUNT_EMAIL_HAS_BEEN_BOUND = 101017,

  /**
   * 密码错误 httpCode=400
   *
   * @generated from enum value: CODE_ACCOUNT_PASSWORD_IS_WRONG = 101018;
   */
  ACCOUNT_PASSWORD_IS_WRONG = 101018,

  /**
   * 密码错误 httpCode=400  对于仅有 email 的用户， 不允许换绑
   *
   * @generated from enum value: CODE_ACCOUNT_FORBID_EDIT_EMAIL = 101019;
   */
  ACCOUNT_FORBID_EDIT_EMAIL = 101019,

  /**
   * user: 201
   *
   * 用户头像图片过大，httpCode = 400 CodeInvalidArgument
   *
   * @generated from enum value: CODE_USER_AVATAR_FILE_TOO_LARGE = 201001;
   */
  USER_AVATAR_FILE_TOO_LARGE = 201001,

  /**
   * 用户所在账号已注销, httpCode = 403 CodePermissionDenied
   *
   * @generated from enum value: CODE_USER_ACCOUNT_HAS_SIGN_OUT = 201002;
   */
  USER_ACCOUNT_HAS_SIGN_OUT = 201002,

  /**
   * 全局管控，禁止编辑用户个人资料，httpCode=403 CodePermissionDenied
   *
   * @generated from enum value: CODE_USER_FORBID_EDIT_PERSONAL_INFO = 201003;
   */
  USER_FORBID_EDIT_PERSONAL_INFO = 201003,

  /**
   * 目前为编辑用户个人资料次数受限（已达上限），httpCode=403 CodePermissionDenied
   *
   * @generated from enum value: CODE_USER_RESTRICT_EDIT_PERSONAL_INFO = 201004;
   */
  USER_RESTRICT_EDIT_PERSONAL_INFO = 201004,
}

/**
 * @generated from message proto.error.v1.Error
 */
export declare class Error extends Message<Error> {
  /**
   * @generated from field: proto.error.v1.Code code = 1;
   */
  code: Code;

  constructor(data?: PartialMessage<Error>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.error.v1.Error";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Error;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Error;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Error;

  static equals(a: Error | PlainMessage<Error> | undefined, b: Error | PlainMessage<Error> | undefined): boolean;
}

/**
 * @generated from message proto.error.v1.ErrorWithTraceID
 */
export declare class ErrorWithTraceID extends Message<ErrorWithTraceID> {
  /**
   * @generated from field: string trace_id = 1;
   */
  traceId: string;

  constructor(data?: PartialMessage<ErrorWithTraceID>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.error.v1.ErrorWithTraceID";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ErrorWithTraceID;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ErrorWithTraceID;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ErrorWithTraceID;

  static equals(a: ErrorWithTraceID | PlainMessage<ErrorWithTraceID> | undefined, b: ErrorWithTraceID | PlainMessage<ErrorWithTraceID> | undefined): boolean;
}

