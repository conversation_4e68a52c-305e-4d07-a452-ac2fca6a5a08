// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file proto/risk/v1/risk.proto (package proto.risk.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum proto.risk.v1.State
 */
export declare enum State {
  /**
   * @generated from enum value: STATE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 审核中
   *
   * @generated from enum value: STATE_REVIEW = 1;
   */
  REVIEW = 1,

  /**
   * 审核通过
   *
   * @generated from enum value: STATE_PASS = 2;
   */
  PASS = 2,

  /**
   * 审核不通过
   *
   * @generated from enum value: STATE_BLOCK = 3;
   */
  BLOCK = 3,
}

/**
 * @generated from message proto.risk.v1.Detail
 */
export declare class Detail extends Message<Detail> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: proto.risk.v1.State state = 2;
   */
  state: State;

  /**
   * @generated from field: string type = 3;
   */
  type: string;

  constructor(data?: PartialMessage<Detail>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.risk.v1.Detail";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Detail;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Detail;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Detail;

  static equals(a: Detail | PlainMessage<Detail> | undefined, b: Detail | PlainMessage<Detail> | undefined): boolean;
}

/**
 * @generated from message proto.risk.v1.Result
 */
export declare class Result extends Message<Result> {
  /**
   * @generated from field: proto.risk.v1.State state = 1;
   */
  state: State;

  /**
   * @generated from field: repeated proto.risk.v1.Detail details = 2;
   */
  details: Detail[];

  constructor(data?: PartialMessage<Result>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "proto.risk.v1.Result";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Result;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Result;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Result;

  static equals(a: Result | PlainMessage<Result> | undefined, b: Result | PlainMessage<Result> | undefined): boolean;
}

