// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/chatbot/chatbot.proto (package step.raccoon.chatbot, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { MessageType } from "./chatbot_common_pb.js";

/**
 * @generated from enum step.raccoon.chatbot.SuggestionType
 */
export declare enum SuggestionType {
  /**
   * @generated from enum value: SuggestionType_UNKNOWN = 0;
   */
  SuggestionType_UNKNOWN = 0,

  /**
   * @generated from enum value: SuggestionType_CHAT = 1;
   */
  SuggestionType_CHAT = 1,

  /**
   * @generated from enum value: SuggestionType_INDEX_BUBBLE = 2;
   */
  SuggestionType_INDEX_BUBBLE = 2,

  /**
   * @generated from enum value: SuggestionType_SEARCH_BUBBLE = 3;
   */
  SuggestionType_SEARCH_BUBBLE = 3,
}

/**
 * @generated from message step.raccoon.chatbot.ChatMessageContent
 */
export declare class ChatMessageContent extends Message<ChatMessageContent> {
  /**
   * @generated from field: string content = 1;
   */
  content: string;

  /**
   * @generated from field: string think_content = 2;
   */
  thinkContent: string;

  constructor(data?: PartialMessage<ChatMessageContent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ChatMessageContent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatMessageContent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatMessageContent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatMessageContent;

  static equals(a: ChatMessageContent | PlainMessage<ChatMessageContent> | undefined, b: ChatMessageContent | PlainMessage<ChatMessageContent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.TTSContent
 */
export declare class TTSContent extends Message<TTSContent> {
  /**
   * @generated from field: repeated step.raccoon.chatbot.TTSContent.Chunk chunks = 1;
   */
  chunks: TTSContent_Chunk[];

  constructor(data?: PartialMessage<TTSContent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.TTSContent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TTSContent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TTSContent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TTSContent;

  static equals(a: TTSContent | PlainMessage<TTSContent> | undefined, b: TTSContent | PlainMessage<TTSContent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.TTSContent.Chunk
 */
export declare class TTSContent_Chunk extends Message<TTSContent_Chunk> {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  /**
   * int64 ms
   *
   * @generated from field: string duration = 2;
   */
  duration: string;

  constructor(data?: PartialMessage<TTSContent_Chunk>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.TTSContent.Chunk";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TTSContent_Chunk;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TTSContent_Chunk;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TTSContent_Chunk;

  static equals(a: TTSContent_Chunk | PlainMessage<TTSContent_Chunk> | undefined, b: TTSContent_Chunk | PlainMessage<TTSContent_Chunk> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ChatMessage
 */
export declare class ChatMessage extends Message<ChatMessage> {
  /**
   * 消息id
   *
   * @generated from field: string msg_id = 1;
   */
  msgId: string;

  /**
   * 会话id
   *
   * @generated from field: string session_id = 2;
   */
  sessionId: string;

  /**
   * int64
   *
   * @generated from field: string uid = 3;
   */
  uid: string;

  /**
   * 为0是机器人发送
   *
   * @generated from field: string sender_uid = 4;
   */
  senderUid: string;

  /**
   * 消息类型：普通消息或其他控制类消息
   *
   * @generated from field: step.raccoon.chatbot.MessageType message_type = 5;
   */
  messageType: MessageType;

  /**
   * 控制类消息可能没有content
   *
   * @generated from field: step.raccoon.chatbot.ChatMessageContent content = 6;
   */
  content?: ChatMessageContent;

  /**
   * tts语音片段。可能为空
   *
   * @generated from field: step.raccoon.chatbot.TTSContent tts_content = 7;
   */
  ttsContent?: TTSContent;

  /**
   * 是否已读
   *
   * @generated from field: bool read = 8;
   */
  read: boolean;

  /**
   * 创建时间
   *
   * @generated from field: string created_at = 9;
   */
  createdAt: string;

  constructor(data?: PartialMessage<ChatMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ChatMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatMessage;

  static equals(a: ChatMessage | PlainMessage<ChatMessage> | undefined, b: ChatMessage | PlainMessage<ChatMessage> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.FetchMessagesRequest
 */
export declare class FetchMessagesRequest extends Message<FetchMessagesRequest> {
  /**
   * @generated from field: string next_page_token = 1;
   */
  nextPageToken: string;

  constructor(data?: PartialMessage<FetchMessagesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.FetchMessagesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FetchMessagesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FetchMessagesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FetchMessagesRequest;

  static equals(a: FetchMessagesRequest | PlainMessage<FetchMessagesRequest> | undefined, b: FetchMessagesRequest | PlainMessage<FetchMessagesRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.FetchMessagesResponse
 */
export declare class FetchMessagesResponse extends Message<FetchMessagesResponse> {
  /**
   * @generated from field: repeated step.raccoon.chatbot.ChatMessage messages = 1;
   */
  messages: ChatMessage[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  constructor(data?: PartialMessage<FetchMessagesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.FetchMessagesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FetchMessagesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FetchMessagesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FetchMessagesResponse;

  static equals(a: FetchMessagesResponse | PlainMessage<FetchMessagesResponse> | undefined, b: FetchMessagesResponse | PlainMessage<FetchMessagesResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.MarkMessagesRequest
 */
export declare class MarkMessagesRequest extends Message<MarkMessagesRequest> {
  /**
   * @generated from field: repeated string msg_ids = 1;
   */
  msgIds: string[];

  /**
   * 只允许true标记，忽略false
   *
   * TODO: 扩展更多标记
   *
   * @generated from field: bool read = 2;
   */
  read: boolean;

  constructor(data?: PartialMessage<MarkMessagesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.MarkMessagesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MarkMessagesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MarkMessagesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MarkMessagesRequest;

  static equals(a: MarkMessagesRequest | PlainMessage<MarkMessagesRequest> | undefined, b: MarkMessagesRequest | PlainMessage<MarkMessagesRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.MarkMessagesResponse
 */
export declare class MarkMessagesResponse extends Message<MarkMessagesResponse> {
  constructor(data?: PartialMessage<MarkMessagesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.MarkMessagesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MarkMessagesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MarkMessagesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MarkMessagesResponse;

  static equals(a: MarkMessagesResponse | PlainMessage<MarkMessagesResponse> | undefined, b: MarkMessagesResponse | PlainMessage<MarkMessagesResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.InterruptRequest
 */
export declare class InterruptRequest extends Message<InterruptRequest> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  constructor(data?: PartialMessage<InterruptRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.InterruptRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InterruptRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InterruptRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InterruptRequest;

  static equals(a: InterruptRequest | PlainMessage<InterruptRequest> | undefined, b: InterruptRequest | PlainMessage<InterruptRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.InterruptResponse
 */
export declare class InterruptResponse extends Message<InterruptResponse> {
  constructor(data?: PartialMessage<InterruptResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.InterruptResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InterruptResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InterruptResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InterruptResponse;

  static equals(a: InterruptResponse | PlainMessage<InterruptResponse> | undefined, b: InterruptResponse | PlainMessage<InterruptResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.BotStatusRequest
 */
export declare class BotStatusRequest extends Message<BotStatusRequest> {
  constructor(data?: PartialMessage<BotStatusRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.BotStatusRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BotStatusRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BotStatusRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BotStatusRequest;

  static equals(a: BotStatusRequest | PlainMessage<BotStatusRequest> | undefined, b: BotStatusRequest | PlainMessage<BotStatusRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.BotStatusResponse
 */
export declare class BotStatusResponse extends Message<BotStatusResponse> {
  /**
   * @generated from field: string current_responding_task_id = 1;
   */
  currentRespondingTaskId: string;

  /**
   * @generated from field: string user_message = 2;
   */
  userMessage: string;

  constructor(data?: PartialMessage<BotStatusResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.BotStatusResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BotStatusResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BotStatusResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BotStatusResponse;

  static equals(a: BotStatusResponse | PlainMessage<BotStatusResponse> | undefined, b: BotStatusResponse | PlainMessage<BotStatusResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageRequest
 */
export declare class ListenRespondingMessageRequest extends Message<ListenRespondingMessageRequest> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  constructor(data?: PartialMessage<ListenRespondingMessageRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageRequest;

  static equals(a: ListenRespondingMessageRequest | PlainMessage<ListenRespondingMessageRequest> | undefined, b: ListenRespondingMessageRequest | PlainMessage<ListenRespondingMessageRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageResponse
 */
export declare class ListenRespondingMessageResponse extends Message<ListenRespondingMessageResponse> {
  /**
   * @generated from oneof step.raccoon.chatbot.ListenRespondingMessageResponse.event_oneof
   */
  eventOneof: {
    /**
     * @generated from field: step.raccoon.chatbot.ListenRespondingMessageResponse.MessageChunk message_chunk = 101;
     */
    value: ListenRespondingMessageResponse_MessageChunk;
    case: "messageChunk";
  } | {
    /**
     * @generated from field: step.raccoon.chatbot.ListenRespondingMessageResponse.TTSChunk tts = 102;
     */
    value: ListenRespondingMessageResponse_TTSChunk;
    case: "tts";
  } | {
    /**
     * @generated from field: step.raccoon.chatbot.ListenRespondingMessageResponse.EOF eof = 1001;
     */
    value: ListenRespondingMessageResponse_EOF;
    case: "eof";
  } | {
    /**
     * @generated from field: step.raccoon.chatbot.ListenRespondingMessageResponse.Clear clear = 1002;
     */
    value: ListenRespondingMessageResponse_Clear;
    case: "clear";
  } | {
    /**
     * @generated from field: step.raccoon.chatbot.ListenRespondingMessageResponse.Error error = 1003;
     */
    value: ListenRespondingMessageResponse_Error;
    case: "error";
  } | {
    /**
     * @generated from field: step.raccoon.chatbot.ListenRespondingMessageResponse.TextFinish text_finish = 1011;
     */
    value: ListenRespondingMessageResponse_TextFinish;
    case: "textFinish";
  } | {
    /**
     * @generated from field: step.raccoon.chatbot.ListenRespondingMessageResponse.TTSFinish tts_finish = 1012;
     */
    value: ListenRespondingMessageResponse_TTSFinish;
    case: "ttsFinish";
  } | {
    /**
     * @generated from field: step.raccoon.chatbot.ListenRespondingMessageResponse.ThinkFinish think_finish = 1013;
     */
    value: ListenRespondingMessageResponse_ThinkFinish;
    case: "thinkFinish";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<ListenRespondingMessageResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse;

  static equals(a: ListenRespondingMessageResponse | PlainMessage<ListenRespondingMessageResponse> | undefined, b: ListenRespondingMessageResponse | PlainMessage<ListenRespondingMessageResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageResponse.MessageChunk
 */
export declare class ListenRespondingMessageResponse_MessageChunk extends Message<ListenRespondingMessageResponse_MessageChunk> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * @generated from field: string msg = 2;
   */
  msg: string;

  /**
   * 思考数据
   *
   * @generated from field: string reason_msg = 3;
   */
  reasonMsg: string;

  constructor(data?: PartialMessage<ListenRespondingMessageResponse_MessageChunk>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageResponse.MessageChunk";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageResponse_MessageChunk;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_MessageChunk;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_MessageChunk;

  static equals(a: ListenRespondingMessageResponse_MessageChunk | PlainMessage<ListenRespondingMessageResponse_MessageChunk> | undefined, b: ListenRespondingMessageResponse_MessageChunk | PlainMessage<ListenRespondingMessageResponse_MessageChunk> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageResponse.ThinkFinish
 */
export declare class ListenRespondingMessageResponse_ThinkFinish extends Message<ListenRespondingMessageResponse_ThinkFinish> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * int64 ms
   *
   * @generated from field: string duration = 2;
   */
  duration: string;

  constructor(data?: PartialMessage<ListenRespondingMessageResponse_ThinkFinish>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageResponse.ThinkFinish";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageResponse_ThinkFinish;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_ThinkFinish;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_ThinkFinish;

  static equals(a: ListenRespondingMessageResponse_ThinkFinish | PlainMessage<ListenRespondingMessageResponse_ThinkFinish> | undefined, b: ListenRespondingMessageResponse_ThinkFinish | PlainMessage<ListenRespondingMessageResponse_ThinkFinish> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageResponse.Clear
 */
export declare class ListenRespondingMessageResponse_Clear extends Message<ListenRespondingMessageResponse_Clear> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * @generated from field: int32 code = 2;
   */
  code: number;

  constructor(data?: PartialMessage<ListenRespondingMessageResponse_Clear>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageResponse.Clear";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageResponse_Clear;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_Clear;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_Clear;

  static equals(a: ListenRespondingMessageResponse_Clear | PlainMessage<ListenRespondingMessageResponse_Clear> | undefined, b: ListenRespondingMessageResponse_Clear | PlainMessage<ListenRespondingMessageResponse_Clear> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageResponse.TextFinish
 */
export declare class ListenRespondingMessageResponse_TextFinish extends Message<ListenRespondingMessageResponse_TextFinish> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * @generated from field: int32 code = 2;
   */
  code: number;

  constructor(data?: PartialMessage<ListenRespondingMessageResponse_TextFinish>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageResponse.TextFinish";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageResponse_TextFinish;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_TextFinish;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_TextFinish;

  static equals(a: ListenRespondingMessageResponse_TextFinish | PlainMessage<ListenRespondingMessageResponse_TextFinish> | undefined, b: ListenRespondingMessageResponse_TextFinish | PlainMessage<ListenRespondingMessageResponse_TextFinish> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageResponse.TTSFinish
 */
export declare class ListenRespondingMessageResponse_TTSFinish extends Message<ListenRespondingMessageResponse_TTSFinish> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * @generated from field: int32 code = 2;
   */
  code: number;

  constructor(data?: PartialMessage<ListenRespondingMessageResponse_TTSFinish>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageResponse.TTSFinish";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageResponse_TTSFinish;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_TTSFinish;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_TTSFinish;

  static equals(a: ListenRespondingMessageResponse_TTSFinish | PlainMessage<ListenRespondingMessageResponse_TTSFinish> | undefined, b: ListenRespondingMessageResponse_TTSFinish | PlainMessage<ListenRespondingMessageResponse_TTSFinish> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageResponse.EOF
 */
export declare class ListenRespondingMessageResponse_EOF extends Message<ListenRespondingMessageResponse_EOF> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * @generated from field: int32 code = 2;
   */
  code: number;

  constructor(data?: PartialMessage<ListenRespondingMessageResponse_EOF>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageResponse.EOF";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageResponse_EOF;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_EOF;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_EOF;

  static equals(a: ListenRespondingMessageResponse_EOF | PlainMessage<ListenRespondingMessageResponse_EOF> | undefined, b: ListenRespondingMessageResponse_EOF | PlainMessage<ListenRespondingMessageResponse_EOF> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageResponse.Error
 */
export declare class ListenRespondingMessageResponse_Error extends Message<ListenRespondingMessageResponse_Error> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * @generated from field: int32 code = 2;
   */
  code: number;

  constructor(data?: PartialMessage<ListenRespondingMessageResponse_Error>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageResponse.Error";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageResponse_Error;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_Error;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_Error;

  static equals(a: ListenRespondingMessageResponse_Error | PlainMessage<ListenRespondingMessageResponse_Error> | undefined, b: ListenRespondingMessageResponse_Error | PlainMessage<ListenRespondingMessageResponse_Error> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.ListenRespondingMessageResponse.TTSChunk
 */
export declare class ListenRespondingMessageResponse_TTSChunk extends Message<ListenRespondingMessageResponse_TTSChunk> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * @generated from field: string url = 2;
   */
  url: string;

  /**
   * int64 ms
   *
   * @generated from field: string duration = 3;
   */
  duration: string;

  constructor(data?: PartialMessage<ListenRespondingMessageResponse_TTSChunk>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.ListenRespondingMessageResponse.TTSChunk";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListenRespondingMessageResponse_TTSChunk;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_TTSChunk;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListenRespondingMessageResponse_TTSChunk;

  static equals(a: ListenRespondingMessageResponse_TTSChunk | PlainMessage<ListenRespondingMessageResponse_TTSChunk> | undefined, b: ListenRespondingMessageResponse_TTSChunk | PlainMessage<ListenRespondingMessageResponse_TTSChunk> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.SendMessageRequest
 */
export declare class SendMessageRequest extends Message<SendMessageRequest> {
  /**
   * @generated from field: string msg = 1;
   */
  msg: string;

  /**
   * @generated from field: bool new_session = 2;
   */
  newSession: boolean;

  /**
   * 是否使用deepseek r1
   *
   * @generated from field: bool use_ds = 3;
   */
  useDs: boolean;

  constructor(data?: PartialMessage<SendMessageRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.SendMessageRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendMessageRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendMessageRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendMessageRequest;

  static equals(a: SendMessageRequest | PlainMessage<SendMessageRequest> | undefined, b: SendMessageRequest | PlainMessage<SendMessageRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.SendMessageResponse
 */
export declare class SendMessageResponse extends Message<SendMessageResponse> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  constructor(data?: PartialMessage<SendMessageResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.SendMessageResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendMessageResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendMessageResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendMessageResponse;

  static equals(a: SendMessageResponse | PlainMessage<SendMessageResponse> | undefined, b: SendMessageResponse | PlainMessage<SendMessageResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.GetSuggestionsRequest
 */
export declare class GetSuggestionsRequest extends Message<GetSuggestionsRequest> {
  /**
   * @generated from field: step.raccoon.chatbot.SuggestionType type = 1;
   */
  type: SuggestionType;

  constructor(data?: PartialMessage<GetSuggestionsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.GetSuggestionsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSuggestionsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSuggestionsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSuggestionsRequest;

  static equals(a: GetSuggestionsRequest | PlainMessage<GetSuggestionsRequest> | undefined, b: GetSuggestionsRequest | PlainMessage<GetSuggestionsRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.chatbot.GetSuggestionsResponse
 */
export declare class GetSuggestionsResponse extends Message<GetSuggestionsResponse> {
  /**
   * @generated from field: repeated string suggestions = 1;
   */
  suggestions: string[];

  constructor(data?: PartialMessage<GetSuggestionsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.chatbot.GetSuggestionsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSuggestionsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSuggestionsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSuggestionsResponse;

  static equals(a: GetSuggestionsResponse | PlainMessage<GetSuggestionsResponse> | undefined, b: GetSuggestionsResponse | PlainMessage<GetSuggestionsResponse> | undefined): boolean;
}

