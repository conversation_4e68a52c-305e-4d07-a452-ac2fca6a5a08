// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/chatbot/chatbot_common.proto (package step.raccoon.chatbot, syntax proto3)
/* eslint-disable */
// @ts-nocheck

/**
 * @generated from enum step.raccoon.chatbot.MessageType
 */
export declare enum MessageType {
  /**
   * @generated from enum value: MessageTypeNormal = 0;
   */
  MessageTypeNormal = 0,

  /**
   * 开启新会话
   *
   * @generated from enum value: MessageTypeNewSession = 1;
   */
  MessageTypeNewSession = 1,
}

