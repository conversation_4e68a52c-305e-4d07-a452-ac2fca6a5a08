// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/chatbot/chatbot.proto (package step.raccoon.chatbot, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { BotStatusRequest, BotStatusResponse, FetchMessagesRequest, FetchMessagesResponse, GetSuggestionsRequest, GetSuggestionsResponse, InterruptRequest, InterruptResponse, ListenRespondingMessageRequest, ListenRespondingMessageResponse, MarkMessagesRequest, MarkMessagesResponse, SendMessageRequest, SendMessageResponse } from "./chatbot_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 *  handles
 *
 * @generated from service step.raccoon.chatbot.ChatBot
 */
export declare const ChatBot: {
  readonly typeName: "step.raccoon.chatbot.ChatBot",
  readonly methods: {
    /**
     * 拉取历史消息（已持久化）
     *
     * @generated from rpc step.raccoon.chatbot.ChatBot.FetchMessages
     */
    readonly fetchMessages: {
      readonly name: "FetchMessages",
      readonly I: typeof FetchMessagesRequest,
      readonly O: typeof FetchMessagesResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 标记已读
     *
     * @generated from rpc step.raccoon.chatbot.ChatBot.MarkMessages
     */
    readonly markMessages: {
      readonly name: "MarkMessages",
      readonly I: typeof MarkMessagesRequest,
      readonly O: typeof MarkMessagesResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 终止推理输出
     *
     * @generated from rpc step.raccoon.chatbot.ChatBot.Interrupt
     */
    readonly interrupt: {
      readonly name: "Interrupt",
      readonly I: typeof InterruptRequest,
      readonly O: typeof InterruptResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询推理状态
     *
     * @generated from rpc step.raccoon.chatbot.ChatBot.BotStatus
     */
    readonly botStatus: {
      readonly name: "BotStatus",
      readonly I: typeof BotStatusRequest,
      readonly O: typeof BotStatusResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 断线重连
     *
     * @generated from rpc step.raccoon.chatbot.ChatBot.ListenRespondingMessage
     */
    readonly listenRespondingMessage: {
      readonly name: "ListenRespondingMessage",
      readonly I: typeof ListenRespondingMessageRequest,
      readonly O: typeof ListenRespondingMessageResponse,
      readonly kind: MethodKind.ServerStreaming,
    },
    /**
     * 发送消息
     *
     * @generated from rpc step.raccoon.chatbot.ChatBot.SendMessage
     */
    readonly sendMessage: {
      readonly name: "SendMessage",
      readonly I: typeof SendMessageRequest,
      readonly O: typeof SendMessageResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.chatbot.ChatBot.GetSuggestions
     */
    readonly getSuggestions: {
      readonly name: "GetSuggestions",
      readonly I: typeof GetSuggestionsRequest,
      readonly O: typeof GetSuggestionsResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

