// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/topic/topic_admin.proto (package step.raccoon.topic, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { TopicDetail } from "../common/topic_pb.js";
import type { ActivityConf } from "../common/showcase_pb.js";
import type { Pagination } from "../common/utils_pb.js";
import type { GameType } from "../common/types_pb.js";

/**
 * @generated from enum step.raccoon.topic.TopicCardState
 */
export declare enum TopicCardState {
  /**
   * @generated from enum value: Unknown = 0;
   */
  Unknown = 0,

  /**
   * @generated from enum value: Valid = 1;
   */
  Valid = 1,

  /**
   * @generated from enum value: Invalid = -1;
   */
  Invalid = -1,

  /**
   * @generated from enum value: SelfView = 2;
   */
  SelfView = 2,

  /**
   * @generated from enum value: HottestBlock = 3;
   */
  HottestBlock = 3,
}

/**
 * @generated from enum step.raccoon.topic.TopicRuleType
 */
export declare enum TopicRuleType {
  /**
   * 可选
   *
   * @generated from enum value: Optional = 0;
   */
  Optional = 0,

  /**
   * 默认携带
   *
   * @generated from enum value: Default = 1;
   */
  Default = 1,
}

/**
 * 运营后台展示topic
 *
 * @generated from message step.raccoon.topic.AdminTopic
 */
export declare class AdminTopic extends Message<AdminTopic> {
  /**
   * @generated from field: step.raccoon.common.TopicDetail topicDetail = 1;
   */
  topicDetail?: TopicDetail;

  /**
   * 话题权重
   *
   * @generated from field: int32 weight = 2;
   */
  weight: number;

  /**
   * 0 表示草稿， 1表示生效， -1表示下线
   *
   * @generated from field: int32 state = 3;
   */
  state: number;

  /**
   * 0 表示草稿， 1表示生效， -1表示下线 活动状态
   *
   * @generated from field: int32 activity_state = 4;
   */
  activityState: number;

  /**
   * 创建人
   *
   * @generated from field: string creator = 5;
   */
  creator: string;

  /**
   * 创建时间
   *
   * @generated from field: string created_at = 6;
   */
  createdAt: string;

  /**
   * 更新人
   *
   * @generated from field: string updator = 7;
   */
  updator: string;

  /**
   * 更新时间
   *
   * @generated from field: string updated_at = 8;
   */
  updatedAt: string;

  /**
   * 话题附加属性
   *
   * @generated from field: step.raccoon.topic.TopicExtra topic_extra = 9;
   */
  topicExtra?: TopicExtra;

  constructor(data?: PartialMessage<AdminTopic>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.AdminTopic";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminTopic;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminTopic;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminTopic;

  static equals(a: AdminTopic | PlainMessage<AdminTopic> | undefined, b: AdminTopic | PlainMessage<AdminTopic> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.AdminTopicTemplate
 */
export declare class AdminTopicTemplate extends Message<AdminTopicTemplate> {
  /**
   * 标题
   *
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * 内容
   *
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * 背景颜色
   *
   * @generated from field: string background_color = 3;
   */
  backgroundColor: string;

  /**
   * 头像
   *
   * @generated from field: string avatar = 4;
   */
  avatar: string;

  /**
   * 活动配置
   *
   * @generated from field: step.raccoon.common.ActivityConf activity_conf = 5;
   */
  activityConf?: ActivityConf;

  /**
   *  0 表示草稿， 1表示生效， -1表示下线 活动状态
   *
   * @generated from field: int32 activity_state = 6;
   */
  activityState: number;

  /**
   * 话题权重
   *
   * @generated from field: int32 weight = 7;
   */
  weight: number;

  /**
   * 初始热度
   *
   * @generated from field: string heat = 8;
   */
  heat: string;

  /**
   * 话题附加属性
   *
   * @generated from field: step.raccoon.topic.TopicExtra topic_extra = 9;
   */
  topicExtra?: TopicExtra;

  constructor(data?: PartialMessage<AdminTopicTemplate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.AdminTopicTemplate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminTopicTemplate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminTopicTemplate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminTopicTemplate;

  static equals(a: AdminTopicTemplate | PlainMessage<AdminTopicTemplate> | undefined, b: AdminTopicTemplate | PlainMessage<AdminTopicTemplate> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.TopicExtra
 */
export declare class TopicExtra extends Message<TopicExtra> {
  /**
   * 跳转玩法类型
   *
   * @generated from field: int32 game_type = 1;
   */
  gameType: number;

  /**
   * 最低版本
   *
   * @generated from field: string min_version = 2;
   */
  minVersion: string;

  /**
   * 玩法强制带此话题
   *
   * @generated from field: repeated int32 game_types_required = 3;
   */
  gameTypesRequired: number[];

  /**
   * 玩法可选此话题
   *
   * @generated from field: repeated int32 game_types_visible = 4;
   */
  gameTypesVisible: number[];

  /**
   * 人群筛选 符合条件时可见
   *
   * @generated from field: repeated string group_keys_required = 11;
   */
  groupKeysRequired: string[];

  /**
   * 人群筛选 符合条件时可见
   *
   * @generated from field: repeated string group_keys_visible = 12;
   */
  groupKeysVisible: string[];

  /**
   * 是否在理想国（话题广场）隐藏
   *
   * @generated from field: bool topic_square_hidden = 51;
   */
  topicSquareHidden: boolean;

  constructor(data?: PartialMessage<TopicExtra>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.TopicExtra";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicExtra;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicExtra;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicExtra;

  static equals(a: TopicExtra | PlainMessage<TopicExtra> | undefined, b: TopicExtra | PlainMessage<TopicExtra> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.TopicListReq
 */
export declare class TopicListReq extends Message<TopicListReq> {
  constructor(data?: PartialMessage<TopicListReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.TopicListReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicListReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicListReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicListReq;

  static equals(a: TopicListReq | PlainMessage<TopicListReq> | undefined, b: TopicListReq | PlainMessage<TopicListReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.TopicListResp
 */
export declare class TopicListResp extends Message<TopicListResp> {
  /**
   * @generated from field: repeated step.raccoon.topic.AdminTopic data = 1;
   */
  data: AdminTopic[];

  constructor(data?: PartialMessage<TopicListResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.TopicListResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicListResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicListResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicListResp;

  static equals(a: TopicListResp | PlainMessage<TopicListResp> | undefined, b: TopicListResp | PlainMessage<TopicListResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.AddTopicReq
 */
export declare class AddTopicReq extends Message<AddTopicReq> {
  /**
   * @generated from field: step.raccoon.topic.AdminTopicTemplate template = 1;
   */
  template?: AdminTopicTemplate;

  constructor(data?: PartialMessage<AddTopicReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.AddTopicReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddTopicReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddTopicReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddTopicReq;

  static equals(a: AddTopicReq | PlainMessage<AddTopicReq> | undefined, b: AddTopicReq | PlainMessage<AddTopicReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.AddTopicResp
 */
export declare class AddTopicResp extends Message<AddTopicResp> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  constructor(data?: PartialMessage<AddTopicResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.AddTopicResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddTopicResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddTopicResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddTopicResp;

  static equals(a: AddTopicResp | PlainMessage<AddTopicResp> | undefined, b: AddTopicResp | PlainMessage<AddTopicResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.UpdateTopicReq
 */
export declare class UpdateTopicReq extends Message<UpdateTopicReq> {
  /**
   * @generated from field: string topic_id = 1;
   */
  topicId: string;

  /**
   * @generated from field: step.raccoon.topic.AdminTopicTemplate template = 2;
   */
  template?: AdminTopicTemplate;

  constructor(data?: PartialMessage<UpdateTopicReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.UpdateTopicReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTopicReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTopicReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTopicReq;

  static equals(a: UpdateTopicReq | PlainMessage<UpdateTopicReq> | undefined, b: UpdateTopicReq | PlainMessage<UpdateTopicReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.UpdateTopicResp
 */
export declare class UpdateTopicResp extends Message<UpdateTopicResp> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  constructor(data?: PartialMessage<UpdateTopicResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.UpdateTopicResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTopicResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTopicResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTopicResp;

  static equals(a: UpdateTopicResp | PlainMessage<UpdateTopicResp> | undefined, b: UpdateTopicResp | PlainMessage<UpdateTopicResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.UpdateTopicStateReq
 */
export declare class UpdateTopicStateReq extends Message<UpdateTopicStateReq> {
  /**
   * @generated from field: string topic_id = 1;
   */
  topicId: string;

  /**
   * @generated from field: int32 state = 2;
   */
  state: number;

  constructor(data?: PartialMessage<UpdateTopicStateReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.UpdateTopicStateReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTopicStateReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTopicStateReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTopicStateReq;

  static equals(a: UpdateTopicStateReq | PlainMessage<UpdateTopicStateReq> | undefined, b: UpdateTopicStateReq | PlainMessage<UpdateTopicStateReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.UpdateTopicStateResp
 */
export declare class UpdateTopicStateResp extends Message<UpdateTopicStateResp> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  constructor(data?: PartialMessage<UpdateTopicStateResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.UpdateTopicStateResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTopicStateResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTopicStateResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTopicStateResp;

  static equals(a: UpdateTopicStateResp | PlainMessage<UpdateTopicStateResp> | undefined, b: UpdateTopicStateResp | PlainMessage<UpdateTopicStateResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.AddTopicWorkReq
 */
export declare class AddTopicWorkReq extends Message<AddTopicWorkReq> {
  /**
   * @generated from field: string topic_id = 1;
   */
  topicId: string;

  /**
   * @generated from field: repeated string card_id = 2;
   */
  cardId: string[];

  constructor(data?: PartialMessage<AddTopicWorkReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.AddTopicWorkReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddTopicWorkReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddTopicWorkReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddTopicWorkReq;

  static equals(a: AddTopicWorkReq | PlainMessage<AddTopicWorkReq> | undefined, b: AddTopicWorkReq | PlainMessage<AddTopicWorkReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.AddTopicWorkResp
 */
export declare class AddTopicWorkResp extends Message<AddTopicWorkResp> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  constructor(data?: PartialMessage<AddTopicWorkResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.AddTopicWorkResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddTopicWorkResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddTopicWorkResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddTopicWorkResp;

  static equals(a: AddTopicWorkResp | PlainMessage<AddTopicWorkResp> | undefined, b: AddTopicWorkResp | PlainMessage<AddTopicWorkResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.UpdateTopicWorkStateReq
 */
export declare class UpdateTopicWorkStateReq extends Message<UpdateTopicWorkStateReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * @generated from field: repeated string topic_id = 2;
   */
  topicId: string[];

  /**
   * @generated from field: step.raccoon.topic.TopicCardState state = 3;
   */
  state: TopicCardState;

  constructor(data?: PartialMessage<UpdateTopicWorkStateReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.UpdateTopicWorkStateReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTopicWorkStateReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTopicWorkStateReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTopicWorkStateReq;

  static equals(a: UpdateTopicWorkStateReq | PlainMessage<UpdateTopicWorkStateReq> | undefined, b: UpdateTopicWorkStateReq | PlainMessage<UpdateTopicWorkStateReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.UpdateTopicWorkStateResp
 */
export declare class UpdateTopicWorkStateResp extends Message<UpdateTopicWorkStateResp> {
  constructor(data?: PartialMessage<UpdateTopicWorkStateResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.UpdateTopicWorkStateResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTopicWorkStateResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTopicWorkStateResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTopicWorkStateResp;

  static equals(a: UpdateTopicWorkStateResp | PlainMessage<UpdateTopicWorkStateResp> | undefined, b: UpdateTopicWorkStateResp | PlainMessage<UpdateTopicWorkStateResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.DeleteTopicWorkReq
 */
export declare class DeleteTopicWorkReq extends Message<DeleteTopicWorkReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * @generated from field: string topic_id = 2;
   */
  topicId: string;

  constructor(data?: PartialMessage<DeleteTopicWorkReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.DeleteTopicWorkReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteTopicWorkReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteTopicWorkReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteTopicWorkReq;

  static equals(a: DeleteTopicWorkReq | PlainMessage<DeleteTopicWorkReq> | undefined, b: DeleteTopicWorkReq | PlainMessage<DeleteTopicWorkReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.DeleteTopicWorkResp
 */
export declare class DeleteTopicWorkResp extends Message<DeleteTopicWorkResp> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  constructor(data?: PartialMessage<DeleteTopicWorkResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.DeleteTopicWorkResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteTopicWorkResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteTopicWorkResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteTopicWorkResp;

  static equals(a: DeleteTopicWorkResp | PlainMessage<DeleteTopicWorkResp> | undefined, b: DeleteTopicWorkResp | PlainMessage<DeleteTopicWorkResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.SetTopicCardHeatReq
 */
export declare class SetTopicCardHeatReq extends Message<SetTopicCardHeatReq> {
  /**
   * @generated from field: string topic_id = 1;
   */
  topicId: string;

  /**
   * @generated from field: string card_id = 2;
   */
  cardId: string;

  /**
   * @generated from field: int32 heat_admin = 21;
   */
  heatAdmin: number;

  constructor(data?: PartialMessage<SetTopicCardHeatReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.SetTopicCardHeatReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetTopicCardHeatReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetTopicCardHeatReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetTopicCardHeatReq;

  static equals(a: SetTopicCardHeatReq | PlainMessage<SetTopicCardHeatReq> | undefined, b: SetTopicCardHeatReq | PlainMessage<SetTopicCardHeatReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.SetTopicCardHeatResp
 */
export declare class SetTopicCardHeatResp extends Message<SetTopicCardHeatResp> {
  constructor(data?: PartialMessage<SetTopicCardHeatResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.SetTopicCardHeatResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetTopicCardHeatResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetTopicCardHeatResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetTopicCardHeatResp;

  static equals(a: SetTopicCardHeatResp | PlainMessage<SetTopicCardHeatResp> | undefined, b: SetTopicCardHeatResp | PlainMessage<SetTopicCardHeatResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.TopicCardListAdminReq
 */
export declare class TopicCardListAdminReq extends Message<TopicCardListAdminReq> {
  /**
   * @generated from field: step.raccoon.topic.TopicCardListAdminReq.TopicCardListAdminQueryParam search = 1;
   */
  search?: TopicCardListAdminReq_TopicCardListAdminQueryParam;

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<TopicCardListAdminReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.TopicCardListAdminReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicCardListAdminReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicCardListAdminReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicCardListAdminReq;

  static equals(a: TopicCardListAdminReq | PlainMessage<TopicCardListAdminReq> | undefined, b: TopicCardListAdminReq | PlainMessage<TopicCardListAdminReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.TopicCardListAdminReq.TopicCardListAdminQueryParam
 */
export declare class TopicCardListAdminReq_TopicCardListAdminQueryParam extends Message<TopicCardListAdminReq_TopicCardListAdminQueryParam> {
  /**
   * @generated from field: string topic_id = 1;
   */
  topicId: string;

  /**
   * @generated from field: string card_id = 2;
   */
  cardId: string;

  constructor(data?: PartialMessage<TopicCardListAdminReq_TopicCardListAdminQueryParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.TopicCardListAdminReq.TopicCardListAdminQueryParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicCardListAdminReq_TopicCardListAdminQueryParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicCardListAdminReq_TopicCardListAdminQueryParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicCardListAdminReq_TopicCardListAdminQueryParam;

  static equals(a: TopicCardListAdminReq_TopicCardListAdminQueryParam | PlainMessage<TopicCardListAdminReq_TopicCardListAdminQueryParam> | undefined, b: TopicCardListAdminReq_TopicCardListAdminQueryParam | PlainMessage<TopicCardListAdminReq_TopicCardListAdminQueryParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.TopicCardListAdminResp
 */
export declare class TopicCardListAdminResp extends Message<TopicCardListAdminResp> {
  /**
   * @generated from field: repeated step.raccoon.topic.TopicCardListAdminResp.TopicCardHeat topic_card_heats = 1;
   */
  topicCardHeats: TopicCardListAdminResp_TopicCardHeat[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<TopicCardListAdminResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.TopicCardListAdminResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicCardListAdminResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicCardListAdminResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicCardListAdminResp;

  static equals(a: TopicCardListAdminResp | PlainMessage<TopicCardListAdminResp> | undefined, b: TopicCardListAdminResp | PlainMessage<TopicCardListAdminResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.TopicCardListAdminResp.TopicCardHeat
 */
export declare class TopicCardListAdminResp_TopicCardHeat extends Message<TopicCardListAdminResp_TopicCardHeat> {
  /**
   * @generated from field: string topic_id = 1;
   */
  topicId: string;

  /**
   * @generated from field: string card_id = 2;
   */
  cardId: string;

  /**
   * @generated from field: int32 heat = 3;
   */
  heat: number;

  /**
   * @generated from field: int32 heat_on_manual = 4;
   */
  heatOnManual: number;

  /**
   * @generated from field: int32 heat_admin = 5;
   */
  heatAdmin: number;

  constructor(data?: PartialMessage<TopicCardListAdminResp_TopicCardHeat>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.TopicCardListAdminResp.TopicCardHeat";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicCardListAdminResp_TopicCardHeat;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicCardListAdminResp_TopicCardHeat;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicCardListAdminResp_TopicCardHeat;

  static equals(a: TopicCardListAdminResp_TopicCardHeat | PlainMessage<TopicCardListAdminResp_TopicCardHeat> | undefined, b: TopicCardListAdminResp_TopicCardHeat | PlainMessage<TopicCardListAdminResp_TopicCardHeat> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.ListTopicRuleReq
 */
export declare class ListTopicRuleReq extends Message<ListTopicRuleReq> {
  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<ListTopicRuleReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.ListTopicRuleReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTopicRuleReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTopicRuleReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTopicRuleReq;

  static equals(a: ListTopicRuleReq | PlainMessage<ListTopicRuleReq> | undefined, b: ListTopicRuleReq | PlainMessage<ListTopicRuleReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.TopicRuleDetail
 */
export declare class TopicRuleDetail extends Message<TopicRuleDetail> {
  /**
   * @generated from field: repeated step.raccoon.common.GameType game_types = 1;
   */
  gameTypes: GameType[];

  /**
   * @generated from field: repeated string group_keys = 2;
   */
  groupKeys: string[];

  /**
   * @generated from field: string prompt = 3;
   */
  prompt: string;

  /**
   * @generated from field: repeated int32 brands = 4;
   */
  brands: number[];

  /**
   * @generated from field: repeated string role_ids = 5;
   */
  roleIds: string[];

  /**
   * @generated from field: repeated string style = 6;
   */
  style: string[];

  constructor(data?: PartialMessage<TopicRuleDetail>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.TopicRuleDetail";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicRuleDetail;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicRuleDetail;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicRuleDetail;

  static equals(a: TopicRuleDetail | PlainMessage<TopicRuleDetail> | undefined, b: TopicRuleDetail | PlainMessage<TopicRuleDetail> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.TopicRule
 */
export declare class TopicRule extends Message<TopicRule> {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: string topic_id = 2;
   */
  topicId: string;

  /**
   * @generated from field: step.raccoon.topic.TopicRuleDetail detail = 3;
   */
  detail?: TopicRuleDetail;

  /**
   * @generated from field: step.raccoon.topic.TopicRuleType type = 4;
   */
  type: TopicRuleType;

  /**
   * @generated from field: string start_at = 5;
   */
  startAt: string;

  /**
   * @generated from field: string end_at = 6;
   */
  endAt: string;

  constructor(data?: PartialMessage<TopicRule>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.TopicRule";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicRule;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicRule;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicRule;

  static equals(a: TopicRule | PlainMessage<TopicRule> | undefined, b: TopicRule | PlainMessage<TopicRule> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.ListTopicRuleResp
 */
export declare class ListTopicRuleResp extends Message<ListTopicRuleResp> {
  /**
   * @generated from field: repeated step.raccoon.topic.TopicRule topic_rules = 1;
   */
  topicRules: TopicRule[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<ListTopicRuleResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.ListTopicRuleResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTopicRuleResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTopicRuleResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTopicRuleResp;

  static equals(a: ListTopicRuleResp | PlainMessage<ListTopicRuleResp> | undefined, b: ListTopicRuleResp | PlainMessage<ListTopicRuleResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.AddTopicRuleReq
 */
export declare class AddTopicRuleReq extends Message<AddTopicRuleReq> {
  /**
   * @generated from field: step.raccoon.topic.TopicRule rule = 1;
   */
  rule?: TopicRule;

  constructor(data?: PartialMessage<AddTopicRuleReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.AddTopicRuleReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddTopicRuleReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddTopicRuleReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddTopicRuleReq;

  static equals(a: AddTopicRuleReq | PlainMessage<AddTopicRuleReq> | undefined, b: AddTopicRuleReq | PlainMessage<AddTopicRuleReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.AddTopicRuleResp
 */
export declare class AddTopicRuleResp extends Message<AddTopicRuleResp> {
  constructor(data?: PartialMessage<AddTopicRuleResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.AddTopicRuleResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddTopicRuleResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddTopicRuleResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddTopicRuleResp;

  static equals(a: AddTopicRuleResp | PlainMessage<AddTopicRuleResp> | undefined, b: AddTopicRuleResp | PlainMessage<AddTopicRuleResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.UpdateTopicRuleReq
 */
export declare class UpdateTopicRuleReq extends Message<UpdateTopicRuleReq> {
  /**
   * @generated from field: step.raccoon.topic.TopicRule rule = 1;
   */
  rule?: TopicRule;

  constructor(data?: PartialMessage<UpdateTopicRuleReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.UpdateTopicRuleReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTopicRuleReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTopicRuleReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTopicRuleReq;

  static equals(a: UpdateTopicRuleReq | PlainMessage<UpdateTopicRuleReq> | undefined, b: UpdateTopicRuleReq | PlainMessage<UpdateTopicRuleReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.topic.UpdateTopicRuleResp
 */
export declare class UpdateTopicRuleResp extends Message<UpdateTopicRuleResp> {
  constructor(data?: PartialMessage<UpdateTopicRuleResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.topic.UpdateTopicRuleResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTopicRuleResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTopicRuleResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTopicRuleResp;

  static equals(a: UpdateTopicRuleResp | PlainMessage<UpdateTopicRuleResp> | undefined, b: UpdateTopicRuleResp | PlainMessage<UpdateTopicRuleResp> | undefined): boolean;
}

