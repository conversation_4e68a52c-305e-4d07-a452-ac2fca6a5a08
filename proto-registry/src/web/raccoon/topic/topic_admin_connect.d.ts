// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/topic/topic_admin.proto (package step.raccoon.topic, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AddTopicReq, AddTopicResp, AddTopicRuleReq, AddTopicRuleResp, AddTopicWorkReq, AddTopicWorkResp, DeleteTopicWorkReq, DeleteTopicWorkResp, ListTopicRuleReq, ListTopicRuleResp, SetTopicCardHeatReq, SetTopicCardHeatResp, TopicCardListAdminReq, TopicCardListAdminResp, TopicListReq, TopicListResp, UpdateTopicReq, UpdateTopicResp, UpdateTopicRuleReq, UpdateTopicRuleResp, UpdateTopicStateReq, UpdateTopicStateResp, UpdateTopicWorkStateReq, UpdateTopicWorkStateResp } from "./topic_admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.topic.AdminService
 */
export declare const AdminService: {
  readonly typeName: "step.raccoon.topic.AdminService",
  readonly methods: {
    /**
     * 话题列表
     *
     * @generated from rpc step.raccoon.topic.AdminService.TopicList
     */
    readonly topicList: {
      readonly name: "TopicList",
      readonly I: typeof TopicListReq,
      readonly O: typeof TopicListResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 新增话题
     *
     * @generated from rpc step.raccoon.topic.AdminService.AddTopic
     */
    readonly addTopic: {
      readonly name: "AddTopic",
      readonly I: typeof AddTopicReq,
      readonly O: typeof AddTopicResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新话题
     *
     * @generated from rpc step.raccoon.topic.AdminService.UpdateTopic
     */
    readonly updateTopic: {
      readonly name: "UpdateTopic",
      readonly I: typeof UpdateTopicReq,
      readonly O: typeof UpdateTopicResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 变更话题状态
     *
     * @generated from rpc step.raccoon.topic.AdminService.UpdateTopicState
     */
    readonly updateTopicState: {
      readonly name: "UpdateTopicState",
      readonly I: typeof UpdateTopicStateReq,
      readonly O: typeof UpdateTopicStateResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 新增话题作品
     *
     * @generated from rpc step.raccoon.topic.AdminService.AddTopicWork
     */
    readonly addTopicWork: {
      readonly name: "AddTopicWork",
      readonly I: typeof AddTopicWorkReq,
      readonly O: typeof AddTopicWorkResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 变更话题作品状态
     *
     * @generated from rpc step.raccoon.topic.AdminService.UpdateTopicWorkState
     */
    readonly updateTopicWorkState: {
      readonly name: "UpdateTopicWorkState",
      readonly I: typeof UpdateTopicWorkStateReq,
      readonly O: typeof UpdateTopicWorkStateResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 删除话题作品
     *
     * @generated from rpc step.raccoon.topic.AdminService.DeleteTopicWork
     */
    readonly deleteTopicWork: {
      readonly name: "DeleteTopicWork",
      readonly I: typeof DeleteTopicWorkReq,
      readonly O: typeof DeleteTopicWorkResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 手动干预热度
     *
     * @generated from rpc step.raccoon.topic.AdminService.SetTopicCardHeat
     */
    readonly setTopicCardHeat: {
      readonly name: "SetTopicCardHeat",
      readonly I: typeof SetTopicCardHeatReq,
      readonly O: typeof SetTopicCardHeatResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取话题下卡片的列表
     *
     * @generated from rpc step.raccoon.topic.AdminService.TopicCardListAdmin
     */
    readonly topicCardListAdmin: {
      readonly name: "TopicCardListAdmin",
      readonly I: typeof TopicCardListAdminReq,
      readonly O: typeof TopicCardListAdminResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取话题tag配置
     *
     * @generated from rpc step.raccoon.topic.AdminService.ListTopicRule
     */
    readonly listTopicRule: {
      readonly name: "ListTopicRule",
      readonly I: typeof ListTopicRuleReq,
      readonly O: typeof ListTopicRuleResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 新建话题tag配置
     *
     * @generated from rpc step.raccoon.topic.AdminService.AddTopicRule
     */
    readonly addTopicRule: {
      readonly name: "AddTopicRule",
      readonly I: typeof AddTopicRuleReq,
      readonly O: typeof AddTopicRuleResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新话题tag配置
     *
     * @generated from rpc step.raccoon.topic.AdminService.UpdateTopicRule
     */
    readonly updateTopicRule: {
      readonly name: "UpdateTopicRule",
      readonly I: typeof UpdateTopicRuleReq,
      readonly O: typeof UpdateTopicRuleResp,
      readonly kind: MethodKind.Unary,
    },
  }
};

