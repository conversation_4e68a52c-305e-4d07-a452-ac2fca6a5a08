// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/world/admin.proto (package step.raccoon.world, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CreateRootWorldInputTaskReq, CreateRootWorldInputTaskRsp, CreateSubWorldInputTaskReq, CreateSubWorldInputTaskRsp, DeleteAdminActReq, DeleteAdminActRsp, DeleteCreateTaskReq, DeleteCreateTaskRsp, DelRootWorldRequest, DelRootWorldResponse, GenAdminActImageDescReq, GenAdminActImageDescRsp, GenAdminActImageReq, GenAdminActImageRsp, InsertAdminActReq, InsertAdminActRsp, MainWorldExportReq, MainWorldExportRsp, PublishAdminWorldReq, PublishAdminWorldRsp, QueryAdminWorldListReq, QueryAdminWorldListRsp, QueryAdminWorldReq, QueryAdminWorldRsp, QueryCreateTaskInfoReq, QueryCreateTaskInfoRsp, QueryCreateUserListReq, QueryCreateUserListRsp, QueryRoleDialogRequest, QueryRoleDialogResponse, QueryRootWorldsRequest, QueryRootWorldsResponse, RefreshCardAuditReq, RefreshCardAuditRsp, RefreshChoicePointReq, RefreshChoicePointRsp, RefreshTTSReq, RefreshTTSResp, RefreshWorldPreviousReq, RefreshWorldPreviousRsp, RetryTaskReq, RetryTaskRsp, UpdateAdminActReq, UpdateAdminActRsp, UpdateAdminPlotReq, UpdateAdminPlotRsp, UpdateAdminWorldReq, UpdateAdminWorldRsp, UpdateRootWorldRequest, UpdateRootWorldResponse } from "./admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.world.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.world.Admin",
  readonly methods: {
    /**
     * 查询剧本配置
     *
     * @generated from rpc step.raccoon.world.Admin.QueryRootWorlds
     */
    readonly queryRootWorlds: {
      readonly name: "QueryRootWorlds",
      readonly I: typeof QueryRootWorldsRequest,
      readonly O: typeof QueryRootWorldsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 创建或更新剧本配置
     *
     * @generated from rpc step.raccoon.world.Admin.UpdateRootWorld
     */
    readonly updateRootWorld: {
      readonly name: "UpdateRootWorld",
      readonly I: typeof UpdateRootWorldRequest,
      readonly O: typeof UpdateRootWorldResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 删除剧本配置
     *
     * @generated from rpc step.raccoon.world.Admin.DelRootWorld
     */
    readonly delRootWorld: {
      readonly name: "DelRootWorld",
      readonly I: typeof DelRootWorldRequest,
      readonly O: typeof DelRootWorldResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.QueryRoleDialog
     */
    readonly queryRoleDialog: {
      readonly name: "QueryRoleDialog",
      readonly I: typeof QueryRoleDialogRequest,
      readonly O: typeof QueryRoleDialogResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.RefreshChoicePoint
     */
    readonly refreshChoicePoint: {
      readonly name: "RefreshChoicePoint",
      readonly I: typeof RefreshChoicePointReq,
      readonly O: typeof RefreshChoicePointRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.RefreshWorldPrevious
     */
    readonly refreshWorldPrevious: {
      readonly name: "RefreshWorldPrevious",
      readonly I: typeof RefreshWorldPreviousReq,
      readonly O: typeof RefreshWorldPreviousRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.RefreshTTS
     */
    readonly refreshTTS: {
      readonly name: "RefreshTTS",
      readonly I: typeof RefreshTTSReq,
      readonly O: typeof RefreshTTSResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 运营后台-编辑器
     *
     * @generated from rpc step.raccoon.world.Admin.QueryAdminWorldList
     */
    readonly queryAdminWorldList: {
      readonly name: "QueryAdminWorldList",
      readonly I: typeof QueryAdminWorldListReq,
      readonly O: typeof QueryAdminWorldListRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.QueryAdminWorld
     */
    readonly queryAdminWorld: {
      readonly name: "QueryAdminWorld",
      readonly I: typeof QueryAdminWorldReq,
      readonly O: typeof QueryAdminWorldRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.QueryCreateUserList
     */
    readonly queryCreateUserList: {
      readonly name: "QueryCreateUserList",
      readonly I: typeof QueryCreateUserListReq,
      readonly O: typeof QueryCreateUserListRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.CreateRootWorldInputTask
     */
    readonly createRootWorldInputTask: {
      readonly name: "CreateRootWorldInputTask",
      readonly I: typeof CreateRootWorldInputTaskReq,
      readonly O: typeof CreateRootWorldInputTaskRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.CreateSubWorldInputTask
     */
    readonly createSubWorldInputTask: {
      readonly name: "CreateSubWorldInputTask",
      readonly I: typeof CreateSubWorldInputTaskReq,
      readonly O: typeof CreateSubWorldInputTaskRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.QueryCreateTaskInfo
     */
    readonly queryCreateTaskInfo: {
      readonly name: "QueryCreateTaskInfo",
      readonly I: typeof QueryCreateTaskInfoReq,
      readonly O: typeof QueryCreateTaskInfoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.DeleteCreateTask
     */
    readonly deleteCreateTask: {
      readonly name: "DeleteCreateTask",
      readonly I: typeof DeleteCreateTaskReq,
      readonly O: typeof DeleteCreateTaskRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.GenAdminActImageDesc
     */
    readonly genAdminActImageDesc: {
      readonly name: "GenAdminActImageDesc",
      readonly I: typeof GenAdminActImageDescReq,
      readonly O: typeof GenAdminActImageDescRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.GenAdminActImage
     */
    readonly genAdminActImage: {
      readonly name: "GenAdminActImage",
      readonly I: typeof GenAdminActImageReq,
      readonly O: typeof GenAdminActImageRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.UpdateAdminAct
     */
    readonly updateAdminAct: {
      readonly name: "UpdateAdminAct",
      readonly I: typeof UpdateAdminActReq,
      readonly O: typeof UpdateAdminActRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.InsertAdminAct
     */
    readonly insertAdminAct: {
      readonly name: "InsertAdminAct",
      readonly I: typeof InsertAdminActReq,
      readonly O: typeof InsertAdminActRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.DeleteAdminAct
     */
    readonly deleteAdminAct: {
      readonly name: "DeleteAdminAct",
      readonly I: typeof DeleteAdminActReq,
      readonly O: typeof DeleteAdminActRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.UpdateAdminWorld
     */
    readonly updateAdminWorld: {
      readonly name: "UpdateAdminWorld",
      readonly I: typeof UpdateAdminWorldReq,
      readonly O: typeof UpdateAdminWorldRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.PublishAdminWorld
     */
    readonly publishAdminWorld: {
      readonly name: "PublishAdminWorld",
      readonly I: typeof PublishAdminWorldReq,
      readonly O: typeof PublishAdminWorldRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.UpdateAdminPlot
     */
    readonly updateAdminPlot: {
      readonly name: "UpdateAdminPlot",
      readonly I: typeof UpdateAdminPlotReq,
      readonly O: typeof UpdateAdminPlotRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.RefreshCardAudit
     */
    readonly refreshCardAudit: {
      readonly name: "RefreshCardAudit",
      readonly I: typeof RefreshCardAuditReq,
      readonly O: typeof RefreshCardAuditRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.RetryTask
     */
    readonly retryTask: {
      readonly name: "RetryTask",
      readonly I: typeof RetryTaskReq,
      readonly O: typeof RetryTaskRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.world.Admin.MainWorldExport
     */
    readonly mainWorldExport: {
      readonly name: "MainWorldExport",
      readonly I: typeof MainWorldExportReq,
      readonly O: typeof MainWorldExportRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

