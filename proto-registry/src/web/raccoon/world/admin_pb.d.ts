// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/world/admin.proto (package step.raccoon.world, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { ConfigState, RootWorldConfig, WorldSource, WorldState } from "./common_pb.js";

/**
 * @generated from enum step.raccoon.world.InputTaskStatus
 */
export declare enum InputTaskStatus {
  /**
   * 默认占位
   *
   * @generated from enum value: UNKNOWN_TAKS_STATUS = 0;
   */
  UNKNOWN_TAKS_STATUS = 0,

  /**
   * 初始化
   *
   * @generated from enum value: INIT_TASK_STATUS = 1;
   */
  INIT_TASK_STATUS = 1,

  /**
   * 拆幕中
   *
   * @generated from enum value: WORKING_TASK_STATUS = 2;
   */
  WORKING_TASK_STATUS = 2,

  /**
   * 拆幕失败
   *
   * @generated from enum value: FAIL_TASK_STATUS = 3;
   */
  FAIL_TASK_STATUS = 3,

  /**
   * 正常结束
   *
   * @generated from enum value: FINISH_TASK_STATUS = 4;
   */
  FINISH_TASK_STATUS = 4,
}

/**
 * @generated from message step.raccoon.world.QueryRootWorldsRequest
 */
export declare class QueryRootWorldsRequest extends Message<QueryRootWorldsRequest> {
  constructor(data?: PartialMessage<QueryRootWorldsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryRootWorldsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryRootWorldsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryRootWorldsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryRootWorldsRequest;

  static equals(a: QueryRootWorldsRequest | PlainMessage<QueryRootWorldsRequest> | undefined, b: QueryRootWorldsRequest | PlainMessage<QueryRootWorldsRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryRootWorldsResponse
 */
export declare class QueryRootWorldsResponse extends Message<QueryRootWorldsResponse> {
  /**
   * @generated from field: repeated step.raccoon.world.RootWorldConfig worlds = 1;
   */
  worlds: RootWorldConfig[];

  constructor(data?: PartialMessage<QueryRootWorldsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryRootWorldsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryRootWorldsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryRootWorldsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryRootWorldsResponse;

  static equals(a: QueryRootWorldsResponse | PlainMessage<QueryRootWorldsResponse> | undefined, b: QueryRootWorldsResponse | PlainMessage<QueryRootWorldsResponse> | undefined): boolean;
}

/**
 * 创建操作：id留空，其他必填
 * 更新操作：id必填，其他按需
 *
 * @generated from message step.raccoon.world.UpdateRootWorldRequest
 */
export declare class UpdateRootWorldRequest extends Message<UpdateRootWorldRequest> {
  /**
   * 配置id
   *
   * @generated from field: optional string id = 1;
   */
  id?: string;

  /**
   * 信息流id、剧本id、根世界id、内容id
   *
   * @generated from field: optional string card_id = 2;
   */
  cardId?: string;

  /**
   * 名称
   *
   * @generated from field: optional string name = 3;
   */
  name?: string;

  /**
   * 标题
   *
   * @generated from field: optional string title = 4;
   */
  title?: string;

  /**
   * 封面
   *
   * @generated from field: optional string world_cover = 5;
   */
  worldCover?: string;

  /**
   * 所属ip
   *
   * @generated from field: optional string brand_type = 6;
   */
  brandType?: string;

  /**
   * 同ip内剧本显示顺序
   *
   * @generated from field: optional int32 display_order = 7;
   */
  displayOrder?: number;

  /**
   * 话题名
   *
   * @generated from field: optional string topic = 8;
   */
  topic?: string;

  /**
   * 话题封面
   *
   * @generated from field: optional string topic_cover = 9;
   */
  topicCover?: string;

  /**
   * 话题封面平均颜色
   *
   * @generated from field: optional string topic_cover_color = 10;
   */
  topicCoverColor?: string;

  /**
   * 剧情简介
   *
   * @generated from field: optional string description = 11;
   */
  description?: string;

  /**
   * 上墙评论id
   *
   * @generated from field: optional string top_comment_id = 12;
   */
  topCommentId?: string;

  /**
   * 状态
   *
   * @generated from field: optional step.raccoon.world.ConfigState state = 13;
   */
  state?: ConfigState;

  constructor(data?: PartialMessage<UpdateRootWorldRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.UpdateRootWorldRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRootWorldRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRootWorldRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRootWorldRequest;

  static equals(a: UpdateRootWorldRequest | PlainMessage<UpdateRootWorldRequest> | undefined, b: UpdateRootWorldRequest | PlainMessage<UpdateRootWorldRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.UpdateRootWorldResponse
 */
export declare class UpdateRootWorldResponse extends Message<UpdateRootWorldResponse> {
  constructor(data?: PartialMessage<UpdateRootWorldResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.UpdateRootWorldResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRootWorldResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRootWorldResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRootWorldResponse;

  static equals(a: UpdateRootWorldResponse | PlainMessage<UpdateRootWorldResponse> | undefined, b: UpdateRootWorldResponse | PlainMessage<UpdateRootWorldResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.DelRootWorldRequest
 */
export declare class DelRootWorldRequest extends Message<DelRootWorldRequest> {
  /**
   * 配置id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  constructor(data?: PartialMessage<DelRootWorldRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.DelRootWorldRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DelRootWorldRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DelRootWorldRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DelRootWorldRequest;

  static equals(a: DelRootWorldRequest | PlainMessage<DelRootWorldRequest> | undefined, b: DelRootWorldRequest | PlainMessage<DelRootWorldRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.DelRootWorldResponse
 */
export declare class DelRootWorldResponse extends Message<DelRootWorldResponse> {
  constructor(data?: PartialMessage<DelRootWorldResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.DelRootWorldResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DelRootWorldResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DelRootWorldResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DelRootWorldResponse;

  static equals(a: DelRootWorldResponse | PlainMessage<DelRootWorldResponse> | undefined, b: DelRootWorldResponse | PlainMessage<DelRootWorldResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryRoleDialogRequest
 */
export declare class QueryRoleDialogRequest extends Message<QueryRoleDialogRequest> {
  /**
   * @generated from field: int32 brand_type = 1;
   */
  brandType: number;

  /**
   * @generated from field: repeated string role_name = 2;
   */
  roleName: string[];

  /**
   * @generated from field: int32 dialog_cnt = 3;
   */
  dialogCnt: number;

  constructor(data?: PartialMessage<QueryRoleDialogRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryRoleDialogRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryRoleDialogRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryRoleDialogRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryRoleDialogRequest;

  static equals(a: QueryRoleDialogRequest | PlainMessage<QueryRoleDialogRequest> | undefined, b: QueryRoleDialogRequest | PlainMessage<QueryRoleDialogRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryRoleDialogResponse
 */
export declare class QueryRoleDialogResponse extends Message<QueryRoleDialogResponse> {
  /**
   * @generated from field: repeated step.raccoon.world.DialogResult dialog_list = 1;
   */
  dialogList: DialogResult[];

  constructor(data?: PartialMessage<QueryRoleDialogResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryRoleDialogResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryRoleDialogResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryRoleDialogResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryRoleDialogResponse;

  static equals(a: QueryRoleDialogResponse | PlainMessage<QueryRoleDialogResponse> | undefined, b: QueryRoleDialogResponse | PlainMessage<QueryRoleDialogResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.DialogResult
 */
export declare class DialogResult extends Message<DialogResult> {
  /**
   * @generated from field: string role = 1;
   */
  role: string;

  /**
   * @generated from field: string dialog = 2;
   */
  dialog: string;

  constructor(data?: PartialMessage<DialogResult>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.DialogResult";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DialogResult;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DialogResult;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DialogResult;

  static equals(a: DialogResult | PlainMessage<DialogResult> | undefined, b: DialogResult | PlainMessage<DialogResult> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RefreshChoicePointReq
 */
export declare class RefreshChoicePointReq extends Message<RefreshChoicePointReq> {
  /**
   * @generated from field: int64 start_card_id = 1;
   */
  startCardId: bigint;

  constructor(data?: PartialMessage<RefreshChoicePointReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RefreshChoicePointReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshChoicePointReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshChoicePointReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshChoicePointReq;

  static equals(a: RefreshChoicePointReq | PlainMessage<RefreshChoicePointReq> | undefined, b: RefreshChoicePointReq | PlainMessage<RefreshChoicePointReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RefreshChoicePointRsp
 */
export declare class RefreshChoicePointRsp extends Message<RefreshChoicePointRsp> {
  constructor(data?: PartialMessage<RefreshChoicePointRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RefreshChoicePointRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshChoicePointRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshChoicePointRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshChoicePointRsp;

  static equals(a: RefreshChoicePointRsp | PlainMessage<RefreshChoicePointRsp> | undefined, b: RefreshChoicePointRsp | PlainMessage<RefreshChoicePointRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RefreshWorldPreviousReq
 */
export declare class RefreshWorldPreviousReq extends Message<RefreshWorldPreviousReq> {
  /**
   * @generated from field: int64 start_card_id = 1;
   */
  startCardId: bigint;

  constructor(data?: PartialMessage<RefreshWorldPreviousReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RefreshWorldPreviousReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshWorldPreviousReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshWorldPreviousReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshWorldPreviousReq;

  static equals(a: RefreshWorldPreviousReq | PlainMessage<RefreshWorldPreviousReq> | undefined, b: RefreshWorldPreviousReq | PlainMessage<RefreshWorldPreviousReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RefreshWorldPreviousRsp
 */
export declare class RefreshWorldPreviousRsp extends Message<RefreshWorldPreviousRsp> {
  constructor(data?: PartialMessage<RefreshWorldPreviousRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RefreshWorldPreviousRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshWorldPreviousRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshWorldPreviousRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshWorldPreviousRsp;

  static equals(a: RefreshWorldPreviousRsp | PlainMessage<RefreshWorldPreviousRsp> | undefined, b: RefreshWorldPreviousRsp | PlainMessage<RefreshWorldPreviousRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.AdminActDialog
 */
export declare class AdminActDialog extends Message<AdminActDialog> {
  /**
   * @generated from field: string role_name = 1;
   */
  roleName: string;

  /**
   * @generated from field: string dialog = 2;
   */
  dialog: string;

  constructor(data?: PartialMessage<AdminActDialog>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.AdminActDialog";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminActDialog;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminActDialog;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminActDialog;

  static equals(a: AdminActDialog | PlainMessage<AdminActDialog> | undefined, b: AdminActDialog | PlainMessage<AdminActDialog> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.AdminActContent
 */
export declare class AdminActContent extends Message<AdminActContent> {
  /**
   * @generated from field: string act_id = 1;
   */
  actId: string;

  /**
   * 幕顺序
   *
   * @generated from field: int32 act_index = 2;
   */
  actIndex: number;

  /**
   * 时间
   *
   * @generated from field: string time = 3;
   */
  time: string;

  /**
   * 地点
   *
   * @generated from field: string location = 4;
   */
  location: string;

  /**
   * 剧情
   *
   * @generated from field: string story = 5;
   */
  story: string;

  /**
   * 台词
   *
   * @generated from field: repeated step.raccoon.world.AdminActDialog dialog_list = 6;
   */
  dialogList: AdminActDialog[];

  /**
   * 幕图片
   *
   * @generated from field: step.raccoon.world.ImageInfo act_image = 7;
   */
  actImage?: ImageInfo;

  /**
   * 生图描述
   *
   * @generated from field: string image_desc = 8;
   */
  imageDesc: string;

  constructor(data?: PartialMessage<AdminActContent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.AdminActContent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminActContent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminActContent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminActContent;

  static equals(a: AdminActContent | PlainMessage<AdminActContent> | undefined, b: AdminActContent | PlainMessage<AdminActContent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.AdminPlotContent
 */
export declare class AdminPlotContent extends Message<AdminPlotContent> {
  /**
   * 章节id
   *
   * @generated from field: string plot_id = 1;
   */
  plotId: string;

  /**
   * 章节顺序
   *
   * @generated from field: int32 plot_index = 2;
   */
  plotIndex: number;

  /**
   * 剧情
   *
   * @generated from field: string content = 3;
   */
  content: string;

  /**
   * 决策点
   *
   * @generated from field: string choice_point = 4;
   */
  choicePoint: string;

  /**
   * 选项
   *
   * @generated from field: string choice = 5;
   */
  choice: string;

  /**
   * 幕内容
   *
   * @generated from field: repeated step.raccoon.world.AdminActContent act_content_list = 6;
   */
  actContentList: AdminActContent[];

  constructor(data?: PartialMessage<AdminPlotContent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.AdminPlotContent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminPlotContent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminPlotContent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminPlotContent;

  static equals(a: AdminPlotContent | PlainMessage<AdminPlotContent> | undefined, b: AdminPlotContent | PlainMessage<AdminPlotContent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.AdminWorldOriginInfo
 */
export declare class AdminWorldOriginInfo extends Message<AdminWorldOriginInfo> {
  /**
   * 故事简介
   *
   * @generated from field: string setting_init = 1;
   */
  settingInit: string;

  /**
   * 角色信息
   *
   * @generated from field: string role_info = 2;
   */
  roleInfo: string;

  /**
   * 结局影响因素
   *
   * @generated from field: string end_factor = 3;
   */
  endFactor: string;

  /**
   * 补充注意事项
   *
   * @generated from field: string notice = 4;
   */
  notice: string;

  /**
   * 输出示例
   *
   * @generated from field: string output_demo_v2 = 5;
   */
  outputDemoV2: string;

  /**
   * 角色相互称谓
   *
   * @generated from field: string role_call = 6;
   */
  roleCall: string;

  /**
   * 标题
   *
   * @generated from field: string title = 7;
   */
  title: string;

  /**
   * bgm
   *
   * @generated from field: string bgm_url = 8;
   */
  bgmUrl: string;

  constructor(data?: PartialMessage<AdminWorldOriginInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.AdminWorldOriginInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminWorldOriginInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminWorldOriginInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminWorldOriginInfo;

  static equals(a: AdminWorldOriginInfo | PlainMessage<AdminWorldOriginInfo> | undefined, b: AdminWorldOriginInfo | PlainMessage<AdminWorldOriginInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.AdminWorldInfo
 */
export declare class AdminWorldInfo extends Message<AdminWorldInfo> {
  /**
   * 卡片id
   *
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * 标题
   *
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * 主线特有-基础信息
   *
   * @generated from field: step.raccoon.world.AdminWorldOriginInfo origin_info = 3;
   */
  originInfo?: AdminWorldOriginInfo;

  /**
   * 所属ip
   *
   * @generated from field: string brand_type = 4;
   */
  brandType: string;

  /**
   * 录入账户
   *
   * @generated from field: step.raccoon.world.WorldCreater user = 5;
   */
  user?: WorldCreater;

  /**
   * 世界线创作来源
   *
   * @generated from field: step.raccoon.world.WorldSource source = 6;
   */
  source: WorldSource;

  /**
   * 封面图
   *
   * @generated from field: step.raccoon.world.ImageInfo cover_image = 7;
   */
  coverImage?: ImageInfo;

  /**
   * 剧情内容
   *
   * @generated from field: repeated step.raccoon.world.AdminPlotContent plot_contents = 8;
   */
  plotContents: AdminPlotContent[];

  /**
   * 支线特有-引用的主线内容
   *
   * @generated from field: step.raccoon.world.SimpleWorldInfo ref_root_word = 9;
   */
  refRootWord?: SimpleWorldInfo;

  /**
   * 世界线状态
   *
   * @generated from field: step.raccoon.world.WorldState state = 10;
   */
  state: WorldState;

  constructor(data?: PartialMessage<AdminWorldInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.AdminWorldInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminWorldInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminWorldInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminWorldInfo;

  static equals(a: AdminWorldInfo | PlainMessage<AdminWorldInfo> | undefined, b: AdminWorldInfo | PlainMessage<AdminWorldInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.WorldCreater
 */
export declare class WorldCreater extends Message<WorldCreater> {
  /**
   * @generated from field: string uid = 1;
   */
  uid: string;

  /**
   * @generated from field: string avatar = 2;
   */
  avatar: string;

  /**
   * @generated from field: string user_name = 3;
   */
  userName: string;

  /**
   * @generated from field: string phone_num = 4;
   */
  phoneNum: string;

  /**
   * @generated from field: bool for_root = 5;
   */
  forRoot: boolean;

  constructor(data?: PartialMessage<WorldCreater>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.WorldCreater";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorldCreater;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorldCreater;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorldCreater;

  static equals(a: WorldCreater | PlainMessage<WorldCreater> | undefined, b: WorldCreater | PlainMessage<WorldCreater> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.CreateRootWorldInputTaskReq
 */
export declare class CreateRootWorldInputTaskReq extends Message<CreateRootWorldInputTaskReq> {
  /**
   * 主线基础信息
   *
   * @generated from field: step.raccoon.world.AdminWorldOriginInfo origin_info = 1;
   */
  originInfo?: AdminWorldOriginInfo;

  /**
   * 所属ip
   *
   * @generated from field: string brand_type = 2;
   */
  brandType: string;

  /**
   * 录入账户
   *
   * @generated from field: step.raccoon.world.WorldCreater user = 3;
   */
  user?: WorldCreater;

  /**
   * 剧情内容
   *
   * @generated from field: repeated step.raccoon.world.AdminPlotContent plot_contents = 4;
   */
  plotContents: AdminPlotContent[];

  constructor(data?: PartialMessage<CreateRootWorldInputTaskReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.CreateRootWorldInputTaskReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateRootWorldInputTaskReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateRootWorldInputTaskReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateRootWorldInputTaskReq;

  static equals(a: CreateRootWorldInputTaskReq | PlainMessage<CreateRootWorldInputTaskReq> | undefined, b: CreateRootWorldInputTaskReq | PlainMessage<CreateRootWorldInputTaskReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.CreateRootWorldInputTaskRsp
 */
export declare class CreateRootWorldInputTaskRsp extends Message<CreateRootWorldInputTaskRsp> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  constructor(data?: PartialMessage<CreateRootWorldInputTaskRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.CreateRootWorldInputTaskRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateRootWorldInputTaskRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateRootWorldInputTaskRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateRootWorldInputTaskRsp;

  static equals(a: CreateRootWorldInputTaskRsp | PlainMessage<CreateRootWorldInputTaskRsp> | undefined, b: CreateRootWorldInputTaskRsp | PlainMessage<CreateRootWorldInputTaskRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryAdminWorldReq
 */
export declare class QueryAdminWorldReq extends Message<QueryAdminWorldReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  constructor(data?: PartialMessage<QueryAdminWorldReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryAdminWorldReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryAdminWorldReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryAdminWorldReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryAdminWorldReq;

  static equals(a: QueryAdminWorldReq | PlainMessage<QueryAdminWorldReq> | undefined, b: QueryAdminWorldReq | PlainMessage<QueryAdminWorldReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryAdminWorldRsp
 */
export declare class QueryAdminWorldRsp extends Message<QueryAdminWorldRsp> {
  /**
   * @generated from field: step.raccoon.world.AdminWorldInfo world_info = 1;
   */
  worldInfo?: AdminWorldInfo;

  constructor(data?: PartialMessage<QueryAdminWorldRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryAdminWorldRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryAdminWorldRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryAdminWorldRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryAdminWorldRsp;

  static equals(a: QueryAdminWorldRsp | PlainMessage<QueryAdminWorldRsp> | undefined, b: QueryAdminWorldRsp | PlainMessage<QueryAdminWorldRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.SimpleWorldInfo
 */
export declare class SimpleWorldInfo extends Message<SimpleWorldInfo> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * 世界线创作来源
   *
   * @generated from field: step.raccoon.world.WorldSource source = 3;
   */
  source: WorldSource;

  constructor(data?: PartialMessage<SimpleWorldInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.SimpleWorldInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SimpleWorldInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SimpleWorldInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SimpleWorldInfo;

  static equals(a: SimpleWorldInfo | PlainMessage<SimpleWorldInfo> | undefined, b: SimpleWorldInfo | PlainMessage<SimpleWorldInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryAdminWorldListReq
 */
export declare class QueryAdminWorldListReq extends Message<QueryAdminWorldListReq> {
  /**
   * 所属ip
   *
   * @generated from field: string brand_type = 1;
   */
  brandType: string;

  /**
   * 世界线类型 0用户创造 1主线 2支线
   *
   * @generated from field: step.raccoon.world.WorldSource source = 2;
   */
  source: WorldSource;

  constructor(data?: PartialMessage<QueryAdminWorldListReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryAdminWorldListReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryAdminWorldListReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryAdminWorldListReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryAdminWorldListReq;

  static equals(a: QueryAdminWorldListReq | PlainMessage<QueryAdminWorldListReq> | undefined, b: QueryAdminWorldListReq | PlainMessage<QueryAdminWorldListReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryAdminWorldListRsp
 */
export declare class QueryAdminWorldListRsp extends Message<QueryAdminWorldListRsp> {
  /**
   * @generated from field: repeated step.raccoon.world.SimpleWorldInfo world_info_list = 1;
   */
  worldInfoList: SimpleWorldInfo[];

  constructor(data?: PartialMessage<QueryAdminWorldListRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryAdminWorldListRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryAdminWorldListRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryAdminWorldListRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryAdminWorldListRsp;

  static equals(a: QueryAdminWorldListRsp | PlainMessage<QueryAdminWorldListRsp> | undefined, b: QueryAdminWorldListRsp | PlainMessage<QueryAdminWorldListRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryCreateUserListReq
 */
export declare class QueryCreateUserListReq extends Message<QueryCreateUserListReq> {
  /**
   * 世界线类型 0用户创造 1主线 2支线
   *
   * @generated from field: step.raccoon.world.WorldSource source = 1;
   */
  source: WorldSource;

  constructor(data?: PartialMessage<QueryCreateUserListReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryCreateUserListReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCreateUserListReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCreateUserListReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCreateUserListReq;

  static equals(a: QueryCreateUserListReq | PlainMessage<QueryCreateUserListReq> | undefined, b: QueryCreateUserListReq | PlainMessage<QueryCreateUserListReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryCreateUserListRsp
 */
export declare class QueryCreateUserListRsp extends Message<QueryCreateUserListRsp> {
  /**
   * 账号信息
   *
   * @generated from field: repeated step.raccoon.world.WorldCreater creater_list = 1;
   */
  createrList: WorldCreater[];

  constructor(data?: PartialMessage<QueryCreateUserListRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryCreateUserListRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCreateUserListRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCreateUserListRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCreateUserListRsp;

  static equals(a: QueryCreateUserListRsp | PlainMessage<QueryCreateUserListRsp> | undefined, b: QueryCreateUserListRsp | PlainMessage<QueryCreateUserListRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.CreateSubWorldInputTaskReq
 */
export declare class CreateSubWorldInputTaskReq extends Message<CreateSubWorldInputTaskReq> {
  /**
   * 引用的主线
   *
   * @generated from field: string ref_root_card_id = 1;
   */
  refRootCardId: string;

  /**
   * 标题
   *
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * 录入账户
   *
   * @generated from field: step.raccoon.world.WorldCreater user = 3;
   */
  user?: WorldCreater;

  /**
   * 所属主线段落
   *
   * @generated from field: int32 start_plot_index = 4;
   */
  startPlotIndex: number;

  /**
   * 剧情内容
   *
   * @generated from field: repeated step.raccoon.world.AdminPlotContent plot_contents = 5;
   */
  plotContents: AdminPlotContent[];

  /**
   * 剧情走向
   *
   * @generated from field: int32 tag = 6;
   */
  tag: number;

  constructor(data?: PartialMessage<CreateSubWorldInputTaskReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.CreateSubWorldInputTaskReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateSubWorldInputTaskReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateSubWorldInputTaskReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateSubWorldInputTaskReq;

  static equals(a: CreateSubWorldInputTaskReq | PlainMessage<CreateSubWorldInputTaskReq> | undefined, b: CreateSubWorldInputTaskReq | PlainMessage<CreateSubWorldInputTaskReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.CreateSubWorldInputTaskRsp
 */
export declare class CreateSubWorldInputTaskRsp extends Message<CreateSubWorldInputTaskRsp> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  constructor(data?: PartialMessage<CreateSubWorldInputTaskRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.CreateSubWorldInputTaskRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateSubWorldInputTaskRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateSubWorldInputTaskRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateSubWorldInputTaskRsp;

  static equals(a: CreateSubWorldInputTaskRsp | PlainMessage<CreateSubWorldInputTaskRsp> | undefined, b: CreateSubWorldInputTaskRsp | PlainMessage<CreateSubWorldInputTaskRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryCreateTaskInfoReq
 */
export declare class QueryCreateTaskInfoReq extends Message<QueryCreateTaskInfoReq> {
  /**
   * 所属ip
   *
   * @generated from field: string brand_type = 1;
   */
  brandType: string;

  /**
   * @generated from field: step.raccoon.world.InputTaskStatus status = 2;
   */
  status: InputTaskStatus;

  constructor(data?: PartialMessage<QueryCreateTaskInfoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryCreateTaskInfoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCreateTaskInfoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCreateTaskInfoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCreateTaskInfoReq;

  static equals(a: QueryCreateTaskInfoReq | PlainMessage<QueryCreateTaskInfoReq> | undefined, b: QueryCreateTaskInfoReq | PlainMessage<QueryCreateTaskInfoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.InputTaskInfo
 */
export declare class InputTaskInfo extends Message<InputTaskInfo> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * @generated from field: step.raccoon.world.InputTaskStatus status = 2;
   */
  status: InputTaskStatus;

  /**
   * @generated from field: string fail_reason = 3;
   */
  failReason: string;

  /**
   * @generated from field: step.raccoon.world.SimpleWorldInfo relate_world = 4;
   */
  relateWorld?: SimpleWorldInfo;

  /**
   * @generated from field: step.raccoon.world.SimpleWorldInfo cur_world = 5;
   */
  curWorld?: SimpleWorldInfo;

  constructor(data?: PartialMessage<InputTaskInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.InputTaskInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InputTaskInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InputTaskInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InputTaskInfo;

  static equals(a: InputTaskInfo | PlainMessage<InputTaskInfo> | undefined, b: InputTaskInfo | PlainMessage<InputTaskInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.QueryCreateTaskInfoRsp
 */
export declare class QueryCreateTaskInfoRsp extends Message<QueryCreateTaskInfoRsp> {
  /**
   * @generated from field: repeated step.raccoon.world.InputTaskInfo task_info_list = 1;
   */
  taskInfoList: InputTaskInfo[];

  constructor(data?: PartialMessage<QueryCreateTaskInfoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.QueryCreateTaskInfoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCreateTaskInfoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCreateTaskInfoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCreateTaskInfoRsp;

  static equals(a: QueryCreateTaskInfoRsp | PlainMessage<QueryCreateTaskInfoRsp> | undefined, b: QueryCreateTaskInfoRsp | PlainMessage<QueryCreateTaskInfoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.DeleteCreateTaskReq
 */
export declare class DeleteCreateTaskReq extends Message<DeleteCreateTaskReq> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  constructor(data?: PartialMessage<DeleteCreateTaskReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.DeleteCreateTaskReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteCreateTaskReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteCreateTaskReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteCreateTaskReq;

  static equals(a: DeleteCreateTaskReq | PlainMessage<DeleteCreateTaskReq> | undefined, b: DeleteCreateTaskReq | PlainMessage<DeleteCreateTaskReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.DeleteCreateTaskRsp
 */
export declare class DeleteCreateTaskRsp extends Message<DeleteCreateTaskRsp> {
  constructor(data?: PartialMessage<DeleteCreateTaskRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.DeleteCreateTaskRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteCreateTaskRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteCreateTaskRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteCreateTaskRsp;

  static equals(a: DeleteCreateTaskRsp | PlainMessage<DeleteCreateTaskRsp> | undefined, b: DeleteCreateTaskRsp | PlainMessage<DeleteCreateTaskRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.GenAdminActImageDescReq
 */
export declare class GenAdminActImageDescReq extends Message<GenAdminActImageDescReq> {
  /**
   * @generated from field: step.raccoon.world.AdminActContent act_content = 1;
   */
  actContent?: AdminActContent;

  constructor(data?: PartialMessage<GenAdminActImageDescReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.GenAdminActImageDescReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenAdminActImageDescReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenAdminActImageDescReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenAdminActImageDescReq;

  static equals(a: GenAdminActImageDescReq | PlainMessage<GenAdminActImageDescReq> | undefined, b: GenAdminActImageDescReq | PlainMessage<GenAdminActImageDescReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.GenAdminActImageDescRsp
 */
export declare class GenAdminActImageDescRsp extends Message<GenAdminActImageDescRsp> {
  /**
   * @generated from field: string image_desc = 2;
   */
  imageDesc: string;

  constructor(data?: PartialMessage<GenAdminActImageDescRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.GenAdminActImageDescRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenAdminActImageDescRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenAdminActImageDescRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenAdminActImageDescRsp;

  static equals(a: GenAdminActImageDescRsp | PlainMessage<GenAdminActImageDescRsp> | undefined, b: GenAdminActImageDescRsp | PlainMessage<GenAdminActImageDescRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.GenAdminActImageReq
 */
export declare class GenAdminActImageReq extends Message<GenAdminActImageReq> {
  /**
   * @generated from field: string image_desc = 1;
   */
  imageDesc: string;

  /**
   * @generated from field: int32 need_image_cnt = 2;
   */
  needImageCnt: number;

  /**
   * @generated from field: string card_id = 3;
   */
  cardId: string;

  constructor(data?: PartialMessage<GenAdminActImageReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.GenAdminActImageReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenAdminActImageReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenAdminActImageReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenAdminActImageReq;

  static equals(a: GenAdminActImageReq | PlainMessage<GenAdminActImageReq> | undefined, b: GenAdminActImageReq | PlainMessage<GenAdminActImageReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.ImageInfo
 */
export declare class ImageInfo extends Message<ImageInfo> {
  /**
   * @generated from field: string image_id = 1;
   */
  imageId: string;

  /**
   * @generated from field: string image_url = 2;
   */
  imageUrl: string;

  constructor(data?: PartialMessage<ImageInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.ImageInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImageInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImageInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImageInfo;

  static equals(a: ImageInfo | PlainMessage<ImageInfo> | undefined, b: ImageInfo | PlainMessage<ImageInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.GenAdminActImageRsp
 */
export declare class GenAdminActImageRsp extends Message<GenAdminActImageRsp> {
  /**
   * @generated from field: repeated step.raccoon.world.ImageInfo image = 1;
   */
  image: ImageInfo[];

  constructor(data?: PartialMessage<GenAdminActImageRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.GenAdminActImageRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenAdminActImageRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenAdminActImageRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenAdminActImageRsp;

  static equals(a: GenAdminActImageRsp | PlainMessage<GenAdminActImageRsp> | undefined, b: GenAdminActImageRsp | PlainMessage<GenAdminActImageRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.UpdateAdminActReq
 */
export declare class UpdateAdminActReq extends Message<UpdateAdminActReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * @generated from field: string plot_id = 2;
   */
  plotId: string;

  /**
   * @generated from field: step.raccoon.world.AdminActContent act = 3;
   */
  act?: AdminActContent;

  constructor(data?: PartialMessage<UpdateAdminActReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.UpdateAdminActReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAdminActReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAdminActReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAdminActReq;

  static equals(a: UpdateAdminActReq | PlainMessage<UpdateAdminActReq> | undefined, b: UpdateAdminActReq | PlainMessage<UpdateAdminActReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.UpdateAdminActRsp
 */
export declare class UpdateAdminActRsp extends Message<UpdateAdminActRsp> {
  constructor(data?: PartialMessage<UpdateAdminActRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.UpdateAdminActRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAdminActRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAdminActRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAdminActRsp;

  static equals(a: UpdateAdminActRsp | PlainMessage<UpdateAdminActRsp> | undefined, b: UpdateAdminActRsp | PlainMessage<UpdateAdminActRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.DeleteAdminActReq
 */
export declare class DeleteAdminActReq extends Message<DeleteAdminActReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * @generated from field: string plot_id = 2;
   */
  plotId: string;

  /**
   * @generated from field: string act_id = 3;
   */
  actId: string;

  constructor(data?: PartialMessage<DeleteAdminActReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.DeleteAdminActReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAdminActReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAdminActReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAdminActReq;

  static equals(a: DeleteAdminActReq | PlainMessage<DeleteAdminActReq> | undefined, b: DeleteAdminActReq | PlainMessage<DeleteAdminActReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.DeleteAdminActRsp
 */
export declare class DeleteAdminActRsp extends Message<DeleteAdminActRsp> {
  constructor(data?: PartialMessage<DeleteAdminActRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.DeleteAdminActRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAdminActRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAdminActRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAdminActRsp;

  static equals(a: DeleteAdminActRsp | PlainMessage<DeleteAdminActRsp> | undefined, b: DeleteAdminActRsp | PlainMessage<DeleteAdminActRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.InsertAdminActReq
 */
export declare class InsertAdminActReq extends Message<InsertAdminActReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * @generated from field: string plot_id = 2;
   */
  plotId: string;

  /**
   * @generated from field: string pre_act_id = 3;
   */
  preActId: string;

  constructor(data?: PartialMessage<InsertAdminActReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.InsertAdminActReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InsertAdminActReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InsertAdminActReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InsertAdminActReq;

  static equals(a: InsertAdminActReq | PlainMessage<InsertAdminActReq> | undefined, b: InsertAdminActReq | PlainMessage<InsertAdminActReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.InsertAdminActRsp
 */
export declare class InsertAdminActRsp extends Message<InsertAdminActRsp> {
  /**
   * @generated from field: string act_id = 1;
   */
  actId: string;

  constructor(data?: PartialMessage<InsertAdminActRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.InsertAdminActRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InsertAdminActRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InsertAdminActRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InsertAdminActRsp;

  static equals(a: InsertAdminActRsp | PlainMessage<InsertAdminActRsp> | undefined, b: InsertAdminActRsp | PlainMessage<InsertAdminActRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.UpdateAdminWorldReq
 */
export declare class UpdateAdminWorldReq extends Message<UpdateAdminWorldReq> {
  /**
   * 卡片id
   *
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * 标题
   *
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * 主线特有-基础信息
   *
   * @generated from field: step.raccoon.world.AdminWorldOriginInfo origin_info = 3;
   */
  originInfo?: AdminWorldOriginInfo;

  /**
   * 封面图
   *
   * @generated from field: step.raccoon.world.ImageInfo cover_image = 4;
   */
  coverImage?: ImageInfo;

  constructor(data?: PartialMessage<UpdateAdminWorldReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.UpdateAdminWorldReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAdminWorldReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAdminWorldReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAdminWorldReq;

  static equals(a: UpdateAdminWorldReq | PlainMessage<UpdateAdminWorldReq> | undefined, b: UpdateAdminWorldReq | PlainMessage<UpdateAdminWorldReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.UpdateAdminWorldRsp
 */
export declare class UpdateAdminWorldRsp extends Message<UpdateAdminWorldRsp> {
  constructor(data?: PartialMessage<UpdateAdminWorldRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.UpdateAdminWorldRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAdminWorldRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAdminWorldRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAdminWorldRsp;

  static equals(a: UpdateAdminWorldRsp | PlainMessage<UpdateAdminWorldRsp> | undefined, b: UpdateAdminWorldRsp | PlainMessage<UpdateAdminWorldRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.PublishAdminWorldReq
 */
export declare class PublishAdminWorldReq extends Message<PublishAdminWorldReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  constructor(data?: PartialMessage<PublishAdminWorldReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.PublishAdminWorldReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishAdminWorldReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishAdminWorldReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishAdminWorldReq;

  static equals(a: PublishAdminWorldReq | PlainMessage<PublishAdminWorldReq> | undefined, b: PublishAdminWorldReq | PlainMessage<PublishAdminWorldReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.PublishAdminWorldRsp
 */
export declare class PublishAdminWorldRsp extends Message<PublishAdminWorldRsp> {
  constructor(data?: PartialMessage<PublishAdminWorldRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.PublishAdminWorldRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishAdminWorldRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishAdminWorldRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishAdminWorldRsp;

  static equals(a: PublishAdminWorldRsp | PlainMessage<PublishAdminWorldRsp> | undefined, b: PublishAdminWorldRsp | PlainMessage<PublishAdminWorldRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RefreshTTSReq
 */
export declare class RefreshTTSReq extends Message<RefreshTTSReq> {
  /**
   * @generated from field: int64 start_card_id = 1;
   */
  startCardId: bigint;

  /**
   * @generated from field: repeated string role_names = 2;
   */
  roleNames: string[];

  /**
   * @generated from field: bool need_cover = 3;
   */
  needCover: boolean;

  constructor(data?: PartialMessage<RefreshTTSReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RefreshTTSReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshTTSReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshTTSReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshTTSReq;

  static equals(a: RefreshTTSReq | PlainMessage<RefreshTTSReq> | undefined, b: RefreshTTSReq | PlainMessage<RefreshTTSReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RefreshTTSResp
 */
export declare class RefreshTTSResp extends Message<RefreshTTSResp> {
  constructor(data?: PartialMessage<RefreshTTSResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RefreshTTSResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshTTSResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshTTSResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshTTSResp;

  static equals(a: RefreshTTSResp | PlainMessage<RefreshTTSResp> | undefined, b: RefreshTTSResp | PlainMessage<RefreshTTSResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.UpdateAdminPlotReq
 */
export declare class UpdateAdminPlotReq extends Message<UpdateAdminPlotReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * @generated from field: string plot_id = 2;
   */
  plotId: string;

  /**
   * 章节剧情
   *
   * @generated from field: string content = 3;
   */
  content: string;

  /**
   * 章节的决策点
   *
   * @generated from field: string choice_point = 4;
   */
  choicePoint: string;

  /**
   * 生成该章节的选项
   *
   * @generated from field: string choice = 5;
   */
  choice: string;

  constructor(data?: PartialMessage<UpdateAdminPlotReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.UpdateAdminPlotReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAdminPlotReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAdminPlotReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAdminPlotReq;

  static equals(a: UpdateAdminPlotReq | PlainMessage<UpdateAdminPlotReq> | undefined, b: UpdateAdminPlotReq | PlainMessage<UpdateAdminPlotReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.UpdateAdminPlotRsp
 */
export declare class UpdateAdminPlotRsp extends Message<UpdateAdminPlotRsp> {
  constructor(data?: PartialMessage<UpdateAdminPlotRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.UpdateAdminPlotRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAdminPlotRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAdminPlotRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAdminPlotRsp;

  static equals(a: UpdateAdminPlotRsp | PlainMessage<UpdateAdminPlotRsp> | undefined, b: UpdateAdminPlotRsp | PlainMessage<UpdateAdminPlotRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RefreshCardAuditReq
 */
export declare class RefreshCardAuditReq extends Message<RefreshCardAuditReq> {
  /**
   * @generated from field: int64 start_card_id = 1;
   */
  startCardId: bigint;

  /**
   * @generated from field: bool do_refresh = 2;
   */
  doRefresh: boolean;

  /**
   * @generated from field: int32 parrel_cnt = 3;
   */
  parrelCnt: number;

  constructor(data?: PartialMessage<RefreshCardAuditReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RefreshCardAuditReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshCardAuditReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshCardAuditReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshCardAuditReq;

  static equals(a: RefreshCardAuditReq | PlainMessage<RefreshCardAuditReq> | undefined, b: RefreshCardAuditReq | PlainMessage<RefreshCardAuditReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RefreshCardAuditRsp
 */
export declare class RefreshCardAuditRsp extends Message<RefreshCardAuditRsp> {
  constructor(data?: PartialMessage<RefreshCardAuditRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RefreshCardAuditRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshCardAuditRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshCardAuditRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshCardAuditRsp;

  static equals(a: RefreshCardAuditRsp | PlainMessage<RefreshCardAuditRsp> | undefined, b: RefreshCardAuditRsp | PlainMessage<RefreshCardAuditRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RetryTaskReq
 */
export declare class RetryTaskReq extends Message<RetryTaskReq> {
  /**
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  /**
   * @generated from field: string plot_id = 2;
   */
  plotId: string;

  constructor(data?: PartialMessage<RetryTaskReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RetryTaskReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RetryTaskReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RetryTaskReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RetryTaskReq;

  static equals(a: RetryTaskReq | PlainMessage<RetryTaskReq> | undefined, b: RetryTaskReq | PlainMessage<RetryTaskReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.RetryTaskRsp
 */
export declare class RetryTaskRsp extends Message<RetryTaskRsp> {
  constructor(data?: PartialMessage<RetryTaskRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.RetryTaskRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RetryTaskRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RetryTaskRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RetryTaskRsp;

  static equals(a: RetryTaskRsp | PlainMessage<RetryTaskRsp> | undefined, b: RetryTaskRsp | PlainMessage<RetryTaskRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.MainWorldExportReq
 */
export declare class MainWorldExportReq extends Message<MainWorldExportReq> {
  constructor(data?: PartialMessage<MainWorldExportReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.MainWorldExportReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MainWorldExportReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MainWorldExportReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MainWorldExportReq;

  static equals(a: MainWorldExportReq | PlainMessage<MainWorldExportReq> | undefined, b: MainWorldExportReq | PlainMessage<MainWorldExportReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.world.MainWorldExportRsp
 */
export declare class MainWorldExportRsp extends Message<MainWorldExportRsp> {
  constructor(data?: PartialMessage<MainWorldExportRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.world.MainWorldExportRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MainWorldExportRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MainWorldExportRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MainWorldExportRsp;

  static equals(a: MainWorldExportRsp | PlainMessage<MainWorldExportRsp> | undefined, b: MainWorldExportRsp | PlainMessage<MainWorldExportRsp> | undefined): boolean;
}

