// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/makephoto/admin.proto (package step.raccoon.makephoto, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GetStyleConfigReq, GetStyleConfigRsp, ImportFeatureReq, ImportFeatureRsp, ImportFeaturesReq, ImportFeatureTypeReq, ImportFeatureTypeRsp, ImportStylePhotoReq, ImportStylePhotoRsp, OnlineFeatureReq, OnlineFeatureRsp, OnlineFeatureTypeReq, OnlineFeatureTypeRsp, OnlineStylePhotoReq, OnlineStylePhotoRsp, PreviewStyleConfigReq, PreviewStyleConfigRsp, QueryFeaturesReq, QueryFeaturesRsp, QueryFeatureTypesReq, QueryFeatureTypesRsp, QueryRichPhotoReq, QueryRichPhotoRsp, QueryStylePhotosReq, QueryStylePhotosRsp, UpdateFeatureReq, UpdateFeatureRsp, UpdateFeatureTypeReq, UpdateFeatureTypeRsp, UpdateStyleConfigReq, UpdateStyleConfigRsp, UpdateStylePhotoReq, UpdateStylePhotoRsp } from "./admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.makephoto.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.makephoto.Admin",
  readonly methods: {
    /**
     * 查询内部捏图图片全信息列表
     *
     * @generated from rpc step.raccoon.makephoto.Admin.QueryRichPhoto
     */
    readonly queryRichPhoto: {
      readonly name: "QueryRichPhoto",
      readonly I: typeof QueryRichPhotoReq,
      readonly O: typeof QueryRichPhotoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 风格素材管理
     *
     * 创建风格素材
     *
     * @generated from rpc step.raccoon.makephoto.Admin.ImportStylePhoto
     */
    readonly importStylePhoto: {
      readonly name: "ImportStylePhoto",
      readonly I: typeof ImportStylePhotoReq,
      readonly O: typeof ImportStylePhotoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新风格素材
     *
     * @generated from rpc step.raccoon.makephoto.Admin.UpdateStylePhoto
     */
    readonly updateStylePhoto: {
      readonly name: "UpdateStylePhoto",
      readonly I: typeof UpdateStylePhotoReq,
      readonly O: typeof UpdateStylePhotoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 上/下线风格素材
     *
     * @generated from rpc step.raccoon.makephoto.Admin.OnlineStylePhoto
     */
    readonly onlineStylePhoto: {
      readonly name: "OnlineStylePhoto",
      readonly I: typeof OnlineStylePhotoReq,
      readonly O: typeof OnlineStylePhotoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询风格素材列表
     *
     * @generated from rpc step.raccoon.makephoto.Admin.QueryStylePhotos
     */
    readonly queryStylePhotos: {
      readonly name: "QueryStylePhotos",
      readonly I: typeof QueryStylePhotosReq,
      readonly O: typeof QueryStylePhotosRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 单人/双人风格展示配置
     *
     * 更新风格配置
     *
     * @generated from rpc step.raccoon.makephoto.Admin.UpdateStyleConfig
     */
    readonly updateStyleConfig: {
      readonly name: "UpdateStyleConfig",
      readonly I: typeof UpdateStyleConfigReq,
      readonly O: typeof UpdateStyleConfigRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取风格配置
     *
     * @generated from rpc step.raccoon.makephoto.Admin.GetStyleConfig
     */
    readonly getStyleConfig: {
      readonly name: "GetStyleConfig",
      readonly I: typeof GetStyleConfigReq,
      readonly O: typeof GetStyleConfigRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 预览风格配置
     *
     * @generated from rpc step.raccoon.makephoto.Admin.PreviewStyleConfig
     */
    readonly previewStyleConfig: {
      readonly name: "PreviewStyleConfig",
      readonly I: typeof PreviewStyleConfigReq,
      readonly O: typeof PreviewStyleConfigRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 炖图tab管理
     *
     * 创建炖图tab
     *
     * @generated from rpc step.raccoon.makephoto.Admin.ImportFeatureType
     */
    readonly importFeatureType: {
      readonly name: "ImportFeatureType",
      readonly I: typeof ImportFeatureTypeReq,
      readonly O: typeof ImportFeatureTypeRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新炖图tab
     *
     * @generated from rpc step.raccoon.makephoto.Admin.UpdateFeatureType
     */
    readonly updateFeatureType: {
      readonly name: "UpdateFeatureType",
      readonly I: typeof UpdateFeatureTypeReq,
      readonly O: typeof UpdateFeatureTypeRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 上/下线炖图tab
     *
     * @generated from rpc step.raccoon.makephoto.Admin.OnlineFeatureType
     */
    readonly onlineFeatureType: {
      readonly name: "OnlineFeatureType",
      readonly I: typeof OnlineFeatureTypeReq,
      readonly O: typeof OnlineFeatureTypeRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询炖图tab列表
     *
     * @generated from rpc step.raccoon.makephoto.Admin.QueryFeatureTypes
     */
    readonly queryFeatureTypes: {
      readonly name: "QueryFeatureTypes",
      readonly I: typeof QueryFeatureTypesReq,
      readonly O: typeof QueryFeatureTypesRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 灵感词管理
     *
     * 创建炖图tab下面的元素
     *
     * @generated from rpc step.raccoon.makephoto.Admin.ImportFeature
     */
    readonly importFeature: {
      readonly name: "ImportFeature",
      readonly I: typeof ImportFeatureReq,
      readonly O: typeof ImportFeatureRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 通过excel批量创建炖图tab下面的元素
     *
     * @generated from rpc step.raccoon.makephoto.Admin.ImportFeatures
     */
    readonly importFeatures: {
      readonly name: "ImportFeatures",
      readonly I: typeof ImportFeaturesReq,
      readonly O: typeof ImportFeatureRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新炖图tab下面的元素
     *
     * @generated from rpc step.raccoon.makephoto.Admin.UpdateFeature
     */
    readonly updateFeature: {
      readonly name: "UpdateFeature",
      readonly I: typeof UpdateFeatureReq,
      readonly O: typeof UpdateFeatureRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 上/下线炖图tab下面的元素
     *
     * @generated from rpc step.raccoon.makephoto.Admin.OnlineFeature
     */
    readonly onlineFeature: {
      readonly name: "OnlineFeature",
      readonly I: typeof OnlineFeatureReq,
      readonly O: typeof OnlineFeatureRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询炖图tab下面的元素
     *
     * @generated from rpc step.raccoon.makephoto.Admin.QueryFeatures
     */
    readonly queryFeatures: {
      readonly name: "QueryFeatures",
      readonly I: typeof QueryFeaturesReq,
      readonly O: typeof QueryFeaturesRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

