// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/bgadmin/bgadmin.proto (package step.raccoon.bgadmin, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { HelloRequest, HelloResponse } from "./bgadmin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * Admin handles
 *
 * @generated from service step.raccoon.bgadmin.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.bgadmin.Admin",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.bgadmin.Admin.SayHello
     */
    readonly sayHello: {
      readonly name: "<PERSON><PERSON><PERSON>",
      readonly I: typeof HelloRequest,
      readonly O: typeof HelloResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

