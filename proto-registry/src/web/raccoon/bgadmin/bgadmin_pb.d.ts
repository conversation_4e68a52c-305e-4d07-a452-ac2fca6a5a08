// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bgadmin/bgadmin.proto (package step.raccoon.bgadmin, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * Request Message
 *
 * @generated from message step.raccoon.bgadmin.HelloRequest
 */
export declare class HelloRequest extends Message<HelloRequest> {
  /**
   * @generated from field: string msg = 1;
   */
  msg: string;

  constructor(data?: PartialMessage<HelloRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bgadmin.HelloRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HelloRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HelloRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HelloRequest;

  static equals(a: HelloRequest | PlainMessage<HelloRequest> | undefined, b: HelloRequest | PlainMessage<HelloRequest> | undefined): boolean;
}

/**
 * Response Message
 *
 * @generated from message step.raccoon.bgadmin.HelloResponse
 */
export declare class HelloResponse extends Message<HelloResponse> {
  /**
   * @generated from field: string msg = 1;
   */
  msg: string;

  constructor(data?: PartialMessage<HelloResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bgadmin.HelloResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HelloResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HelloResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HelloResponse;

  static equals(a: HelloResponse | PlainMessage<HelloResponse> | undefined, b: HelloResponse | PlainMessage<HelloResponse> | undefined): boolean;
}

