// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/brandsubscribe/brandsubscribe.proto (package step.raccoon.brandsubscribe, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * Request Message
 *
 * @generated from message step.raccoon.brandsubscribe.SubscribeBrandReq
 */
export declare class SubscribeBrandReq extends Message<SubscribeBrandReq> {
  /**
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  constructor(data?: PartialMessage<SubscribeBrandReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.SubscribeBrandReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SubscribeBrandReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SubscribeBrandReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SubscribeBrandReq;

  static equals(a: SubscribeBrandReq | PlainMessage<SubscribeBrandReq> | undefined, b: SubscribeBrandReq | PlainMessage<SubscribeBrandReq> | undefined): boolean;
}

/**
 * Response Message
 *
 * @generated from message step.raccoon.brandsubscribe.SubscribeBrandRsp
 */
export declare class SubscribeBrandRsp extends Message<SubscribeBrandRsp> {
  constructor(data?: PartialMessage<SubscribeBrandRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.SubscribeBrandRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SubscribeBrandRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SubscribeBrandRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SubscribeBrandRsp;

  static equals(a: SubscribeBrandRsp | PlainMessage<SubscribeBrandRsp> | undefined, b: SubscribeBrandRsp | PlainMessage<SubscribeBrandRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.GetWishingRankingListReq
 */
export declare class GetWishingRankingListReq extends Message<GetWishingRankingListReq> {
  constructor(data?: PartialMessage<GetWishingRankingListReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.GetWishingRankingListReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetWishingRankingListReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetWishingRankingListReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetWishingRankingListReq;

  static equals(a: GetWishingRankingListReq | PlainMessage<GetWishingRankingListReq> | undefined, b: GetWishingRankingListReq | PlainMessage<GetWishingRankingListReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.GetWishingRankingListRsp
 */
export declare class GetWishingRankingListRsp extends Message<GetWishingRankingListRsp> {
  /**
   * @generated from field: repeated step.raccoon.brandsubscribe.WishingBrand wishing_brands = 1;
   */
  wishingBrands: WishingBrand[];

  constructor(data?: PartialMessage<GetWishingRankingListRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.GetWishingRankingListRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetWishingRankingListRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetWishingRankingListRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetWishingRankingListRsp;

  static equals(a: GetWishingRankingListRsp | PlainMessage<GetWishingRankingListRsp> | undefined, b: GetWishingRankingListRsp | PlainMessage<GetWishingRankingListRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.WishingBrand
 */
export declare class WishingBrand extends Message<WishingBrand> {
  /**
   * brand唯一id
   *
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * 显示标题名
   *
   * @generated from field: string display_name = 2;
   */
  displayName: string;

  /**
   * 许愿数量
   *
   * @generated from field: uint32 wishes = 3;
   */
  wishes: number;

  /**
   * 是否已许愿
   *
   * @generated from field: bool maked = 4;
   */
  maked: boolean;

  constructor(data?: PartialMessage<WishingBrand>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.WishingBrand";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WishingBrand;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WishingBrand;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WishingBrand;

  static equals(a: WishingBrand | PlainMessage<WishingBrand> | undefined, b: WishingBrand | PlainMessage<WishingBrand> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.MakeBrandWishReq
 */
export declare class MakeBrandWishReq extends Message<MakeBrandWishReq> {
  /**
   * brand唯一id
   *
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * 许愿/取消许愿
   *
   * @generated from field: bool make = 2;
   */
  make: boolean;

  constructor(data?: PartialMessage<MakeBrandWishReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.MakeBrandWishReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MakeBrandWishReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MakeBrandWishReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MakeBrandWishReq;

  static equals(a: MakeBrandWishReq | PlainMessage<MakeBrandWishReq> | undefined, b: MakeBrandWishReq | PlainMessage<MakeBrandWishReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.MakeBrandWishRsp
 */
export declare class MakeBrandWishRsp extends Message<MakeBrandWishRsp> {
  constructor(data?: PartialMessage<MakeBrandWishRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.MakeBrandWishRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MakeBrandWishRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MakeBrandWishRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MakeBrandWishRsp;

  static equals(a: MakeBrandWishRsp | PlainMessage<MakeBrandWishRsp> | undefined, b: MakeBrandWishRsp | PlainMessage<MakeBrandWishRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.UnSubscribeBrandReq
 */
export declare class UnSubscribeBrandReq extends Message<UnSubscribeBrandReq> {
  /**
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  constructor(data?: PartialMessage<UnSubscribeBrandReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.UnSubscribeBrandReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnSubscribeBrandReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnSubscribeBrandReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnSubscribeBrandReq;

  static equals(a: UnSubscribeBrandReq | PlainMessage<UnSubscribeBrandReq> | undefined, b: UnSubscribeBrandReq | PlainMessage<UnSubscribeBrandReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.UnSubscribeBrandRsp
 */
export declare class UnSubscribeBrandRsp extends Message<UnSubscribeBrandRsp> {
  constructor(data?: PartialMessage<UnSubscribeBrandRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.UnSubscribeBrandRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnSubscribeBrandRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnSubscribeBrandRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnSubscribeBrandRsp;

  static equals(a: UnSubscribeBrandRsp | PlainMessage<UnSubscribeBrandRsp> | undefined, b: UnSubscribeBrandRsp | PlainMessage<UnSubscribeBrandRsp> | undefined): boolean;
}

