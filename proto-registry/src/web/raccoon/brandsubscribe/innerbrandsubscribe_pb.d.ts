// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/brandsubscribe/innerbrandsubscribe.proto (package step.raccoon.brandsubscribe, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * Request Message
 *
 * @generated from message step.raccoon.brandsubscribe.GetSubscribesReq
 */
export declare class GetSubscribesReq extends Message<GetSubscribesReq> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  constructor(data?: PartialMessage<GetSubscribesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.GetSubscribesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSubscribesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSubscribesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSubscribesReq;

  static equals(a: GetSubscribesReq | PlainMessage<GetSubscribesReq> | undefined, b: GetSubscribesReq | PlainMessage<GetSubscribesReq> | undefined): boolean;
}

/**
 * Response Message
 *
 * @generated from message step.raccoon.brandsubscribe.GetSubscribesRsp
 */
export declare class GetSubscribesRsp extends Message<GetSubscribesRsp> {
  /**
   * @generated from field: repeated int32 brands = 1;
   */
  brands: number[];

  constructor(data?: PartialMessage<GetSubscribesRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.GetSubscribesRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSubscribesRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSubscribesRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSubscribesRsp;

  static equals(a: GetSubscribesRsp | PlainMessage<GetSubscribesRsp> | undefined, b: GetSubscribesRsp | PlainMessage<GetSubscribesRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.NotifyLaunchedBrandReq
 */
export declare class NotifyLaunchedBrandReq extends Message<NotifyLaunchedBrandReq> {
  /**
   * 上架brand
   *
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * 站内信title
   *
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * 站内信content
   *
   * @generated from field: string content = 3;
   */
  content: string;

  /**
   * 图片url
   *
   * @generated from field: string cover = 4;
   */
  cover: string;

  constructor(data?: PartialMessage<NotifyLaunchedBrandReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.NotifyLaunchedBrandReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotifyLaunchedBrandReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotifyLaunchedBrandReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotifyLaunchedBrandReq;

  static equals(a: NotifyLaunchedBrandReq | PlainMessage<NotifyLaunchedBrandReq> | undefined, b: NotifyLaunchedBrandReq | PlainMessage<NotifyLaunchedBrandReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.NotifyLaunchedBrandRsp
 */
export declare class NotifyLaunchedBrandRsp extends Message<NotifyLaunchedBrandRsp> {
  constructor(data?: PartialMessage<NotifyLaunchedBrandRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.NotifyLaunchedBrandRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotifyLaunchedBrandRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotifyLaunchedBrandRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotifyLaunchedBrandRsp;

  static equals(a: NotifyLaunchedBrandRsp | PlainMessage<NotifyLaunchedBrandRsp> | undefined, b: NotifyLaunchedBrandRsp | PlainMessage<NotifyLaunchedBrandRsp> | undefined): boolean;
}

