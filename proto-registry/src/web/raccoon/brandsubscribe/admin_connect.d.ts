// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/brandsubscribe/admin.proto (package step.raccoon.brandsubscribe, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { RemoveWishingBrandReq, RemoveWishingBrandRsp, SetWishingBrandCountReq, SetWishingBrandCountRsp } from "./admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.brandsubscribe.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.brandsubscribe.Admin",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.brandsubscribe.Admin.SetWishingBrandCount
     */
    readonly setWishingBrandCount: {
      readonly name: "SetWishingBrandCount",
      readonly I: typeof SetWishingBrandCountReq,
      readonly O: typeof SetWishingBrandCountRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.brandsubscribe.Admin.RemoveWishingBrand
     */
    readonly removeWishingBrand: {
      readonly name: "RemoveWishingBrand",
      readonly I: typeof RemoveWishingBrandReq,
      readonly O: typeof RemoveWishingBrandRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

