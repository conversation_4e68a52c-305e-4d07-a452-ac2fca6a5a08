// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/brandsubscribe/brandsubscribe.proto (package step.raccoon.brandsubscribe, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GetWishingRankingListReq, GetWishingRankingListRsp, MakeBrandWishReq, MakeBrandWishRsp, SubscribeBrandReq, SubscribeBrandRsp, UnSubscribeBrandReq, UnSubscribeBrandRsp } from "./brandsubscribe_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * Subscribe handles
 *
 * @generated from service step.raccoon.brandsubscribe.Subscribe
 */
export declare const Subscribe: {
  readonly typeName: "step.raccoon.brandsubscribe.Subscribe",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.brandsubscribe.Subscribe.SubscribeBrand
     */
    readonly subscribeBrand: {
      readonly name: "SubscribeBrand",
      readonly I: typeof SubscribeBrandReq,
      readonly O: typeof SubscribeBrandRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.brandsubscribe.Subscribe.GetWishingRankingList
     */
    readonly getWishingRankingList: {
      readonly name: "GetWishingRankingList",
      readonly I: typeof GetWishingRankingListReq,
      readonly O: typeof GetWishingRankingListRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.brandsubscribe.Subscribe.MakeBrandWish
     */
    readonly makeBrandWish: {
      readonly name: "MakeBrandWish",
      readonly I: typeof MakeBrandWishReq,
      readonly O: typeof MakeBrandWishRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.brandsubscribe.Subscribe.UnSubscribeBrand
     */
    readonly unSubscribeBrand: {
      readonly name: "UnSubscribeBrand",
      readonly I: typeof UnSubscribeBrandReq,
      readonly O: typeof UnSubscribeBrandRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

