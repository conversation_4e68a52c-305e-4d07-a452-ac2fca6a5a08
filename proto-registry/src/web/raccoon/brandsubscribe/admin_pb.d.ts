// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/brandsubscribe/admin.proto (package step.raccoon.brandsubscribe, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message step.raccoon.brandsubscribe.SetWishingBrandCountReq
 */
export declare class SetWishingBrandCountReq extends Message<SetWishingBrandCountReq> {
  /**
   * @generated from field: repeated step.raccoon.brandsubscribe.WishingBrandCount wishing_counts = 1;
   */
  wishingCounts: WishingBrandCount[];

  constructor(data?: PartialMessage<SetWishingBrandCountReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.SetWishingBrandCountReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetWishingBrandCountReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetWishingBrandCountReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetWishingBrandCountReq;

  static equals(a: SetWishingBrandCountReq | PlainMessage<SetWishingBrandCountReq> | undefined, b: SetWishingBrandCountReq | PlainMessage<SetWishingBrandCountReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.WishingBrandCount
 */
export declare class WishingBrandCount extends Message<WishingBrandCount> {
  /**
   * 添加到许愿列表的brand
   *
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * 设定许愿数
   *
   * @generated from field: int32 init_count = 2;
   */
  initCount: number;

  constructor(data?: PartialMessage<WishingBrandCount>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.WishingBrandCount";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WishingBrandCount;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WishingBrandCount;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WishingBrandCount;

  static equals(a: WishingBrandCount | PlainMessage<WishingBrandCount> | undefined, b: WishingBrandCount | PlainMessage<WishingBrandCount> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.SetWishingBrandCountRsp
 */
export declare class SetWishingBrandCountRsp extends Message<SetWishingBrandCountRsp> {
  constructor(data?: PartialMessage<SetWishingBrandCountRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.SetWishingBrandCountRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetWishingBrandCountRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetWishingBrandCountRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetWishingBrandCountRsp;

  static equals(a: SetWishingBrandCountRsp | PlainMessage<SetWishingBrandCountRsp> | undefined, b: SetWishingBrandCountRsp | PlainMessage<SetWishingBrandCountRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.RemoveWishingBrandReq
 */
export declare class RemoveWishingBrandReq extends Message<RemoveWishingBrandReq> {
  /**
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  constructor(data?: PartialMessage<RemoveWishingBrandReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.RemoveWishingBrandReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveWishingBrandReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveWishingBrandReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveWishingBrandReq;

  static equals(a: RemoveWishingBrandReq | PlainMessage<RemoveWishingBrandReq> | undefined, b: RemoveWishingBrandReq | PlainMessage<RemoveWishingBrandReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.brandsubscribe.RemoveWishingBrandRsp
 */
export declare class RemoveWishingBrandRsp extends Message<RemoveWishingBrandRsp> {
  constructor(data?: PartialMessage<RemoveWishingBrandRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.brandsubscribe.RemoveWishingBrandRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveWishingBrandRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveWishingBrandRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveWishingBrandRsp;

  static equals(a: RemoveWishingBrandRsp | PlainMessage<RemoveWishingBrandRsp> | undefined, b: RemoveWishingBrandRsp | PlainMessage<RemoveWishingBrandRsp> | undefined): boolean;
}

