// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/brandsubscribe/innerbrandsubscribe.proto (package step.raccoon.brandsubscribe, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GetSubscribesReq, GetSubscribesRsp, NotifyLaunchedBrandReq, NotifyLaunchedBrandRsp } from "./innerbrandsubscribe_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * Subscribe handles
 *
 * @generated from service step.raccoon.brandsubscribe.Internal
 */
export declare const Internal: {
  readonly typeName: "step.raccoon.brandsubscribe.Internal",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.brandsubscribe.Internal.GetSubscribes
     */
    readonly getSubscribes: {
      readonly name: "GetSubscribes",
      readonly I: typeof GetSubscribesReq,
      readonly O: typeof GetSubscribesRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.brandsubscribe.Internal.NotifyLaunchedBrand
     */
    readonly notifyLaunchedBrand: {
      readonly name: "NotifyLaunchedBrand",
      readonly I: typeof NotifyLaunchedBrandReq,
      readonly O: typeof NotifyLaunchedBrandRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

