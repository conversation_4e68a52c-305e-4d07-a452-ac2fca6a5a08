// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/cardpreposter/cardpreposter.proto (package step.raccoon.cardpreposter, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { RefreshCardReateBgmReq, RefreshCardReateBgmRsp } from "./cardpreposter_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * preposter handles
 *
 * @generated from service step.raccoon.cardpreposter.Preposter
 */
export declare const Preposter: {
  readonly typeName: "step.raccoon.cardpreposter.Preposter",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.cardpreposter.Preposter.RefreshCardReateBgm
     */
    readonly refreshCardReateBgm: {
      readonly name: "RefreshCardReateBgm",
      readonly I: typeof RefreshCardReateBgmReq,
      readonly O: typeof RefreshCardReateBgmRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

