// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/cardpreposter/cardpreposter.proto (package step.raccoon.cardpreposter, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message step.raccoon.cardpreposter.RefreshCardReateBgmReq
 */
export declare class RefreshCardReateBgmReq extends Message<RefreshCardReateBgmReq> {
  /**
   * @generated from field: int64 start_card_sn = 1;
   */
  startCardSn: bigint;

  /**
   * @generated from field: int64 end_card_sn = 2;
   */
  endCardSn: bigint;

  constructor(data?: PartialMessage<RefreshCardReateBgmReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.cardpreposter.RefreshCardReateBgmReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshCardReateBgmReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshCardReateBgmReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshCardReateBgmReq;

  static equals(a: RefreshCardReateBgmReq | PlainMessage<RefreshCardReateBgmReq> | undefined, b: RefreshCardReateBgmReq | PlainMessage<RefreshCardReateBgmReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.cardpreposter.RefreshCardReateBgmRsp
 */
export declare class RefreshCardReateBgmRsp extends Message<RefreshCardReateBgmRsp> {
  constructor(data?: PartialMessage<RefreshCardReateBgmRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.cardpreposter.RefreshCardReateBgmRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshCardReateBgmRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshCardReateBgmRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshCardReateBgmRsp;

  static equals(a: RefreshCardReateBgmRsp | PlainMessage<RefreshCardReateBgmRsp> | undefined, b: RefreshCardReateBgmRsp | PlainMessage<RefreshCardReateBgmRsp> | undefined): boolean;
}

