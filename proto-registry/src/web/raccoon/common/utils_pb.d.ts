// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/utils.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum step.raccoon.common.OrderTypes
 */
export declare enum OrderTypes {
  /**
   * 升序
   *
   * @generated from enum value: ORDER_TYPE_ASC = 0;
   */
  ORDER_TYPE_ASC = 0,

  /**
   * 降序
   *
   * @generated from enum value: ORDER_TYPE_DESC = 1;
   */
  ORDER_TYPE_DESC = 1,
}

/**
 * 游标分页
 *
 * @generated from message step.raccoon.common.Pagination
 */
export declare class Pagination extends Message<Pagination> {
  /**
   * 客户端查询用的参考游标; 不同后端应该自己控制具体类型
   *
   * @generated from field: string cursor = 1;
   */
  cursor: string;

  /**
   * 数据条数，最多限制32条
   *
   * @generated from field: uint32 size = 2;
   */
  size: number;

  /**
   * 服务端响应的下一次客户端翻页的游标，若服务端已经没有更多数据，应该传""; 客户端应该根据此字段是否为空，来决定是否结束翻页
   *
   * @generated from field: string nextCursor = 3;
   */
  nextCursor: string;

  /**
   * 总的数量
   *
   * @generated from field: int64 total = 4;
   */
  total: bigint;

  constructor(data?: PartialMessage<Pagination>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Pagination";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Pagination;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Pagination;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Pagination;

  static equals(a: Pagination | PlainMessage<Pagination> | undefined, b: Pagination | PlainMessage<Pagination> | undefined): boolean;
}

/**
 * 传统分页
 *
 * @generated from message step.raccoon.common.PagePagination
 */
export declare class PagePagination extends Message<PagePagination> {
  /**
   * 每页数量
   *
   * @generated from field: int32 size = 1;
   */
  size: number;

  /**
   * 页数
   *
   * @generated from field: int32 page = 2;
   */
  page: number;

  constructor(data?: PartialMessage<PagePagination>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.PagePagination";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PagePagination;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PagePagination;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PagePagination;

  static equals(a: PagePagination | PlainMessage<PagePagination> | undefined, b: PagePagination | PlainMessage<PagePagination> | undefined): boolean;
}

/**
 * 时间范围，应该遵循左开右闭原则
 *
 * @generated from message step.raccoon.common.Timerange
 */
export declare class Timerange extends Message<Timerange> {
  /**
   * 开始时间
   *
   * @generated from field: int64 start_from = 1;
   */
  startFrom: bigint;

  /**
   * 截止时间
   *
   * @generated from field: int64 end_at = 2;
   */
  endAt: bigint;

  constructor(data?: PartialMessage<Timerange>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Timerange";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Timerange;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Timerange;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Timerange;

  static equals(a: Timerange | PlainMessage<Timerange> | undefined, b: Timerange | PlainMessage<Timerange> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.QueryOrderItem
 */
export declare class QueryOrderItem extends Message<QueryOrderItem> {
  /**
   * 字段名 
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * 排序规则: asc/desc
   *
   * @generated from field: string type = 2;
   */
  type: string;

  constructor(data?: PartialMessage<QueryOrderItem>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.QueryOrderItem";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryOrderItem;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryOrderItem;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryOrderItem;

  static equals(a: QueryOrderItem | PlainMessage<QueryOrderItem> | undefined, b: QueryOrderItem | PlainMessage<QueryOrderItem> | undefined): boolean;
}

/**
 * 客户端页面数据埋点，用于服务端记录客户端的一些埋点信息
 *
 * @generated from message step.raccoon.common.PageTracking
 */
export declare class PageTracking extends Message<PageTracking> {
  /**
   * 页面id，也可以是一次完整的交互id
   *
   * @generated from field: string page = 1;
   */
  page: string;

  constructor(data?: PartialMessage<PageTracking>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.PageTracking";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PageTracking;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PageTracking;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PageTracking;

  static equals(a: PageTracking | PlainMessage<PageTracking> | undefined, b: PageTracking | PlainMessage<PageTracking> | undefined): boolean;
}

