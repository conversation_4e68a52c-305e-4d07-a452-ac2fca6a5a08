// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/imagen.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Photo } from "./assets_pb.js";
import type { UserProfile } from "./profile_pb.js";
import type { RoleInfo } from "./role_pb.js";

/**
 * @generated from enum step.raccoon.common.ImageAspectRatio
 */
export declare enum ImageAspectRatio {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: ASPECT_RATIO_1x1 = 1;
   */
  ASPECT_RATIO_1x1 = 1,

  /**
   * @generated from enum value: ASPECT_RATIO_4x3 = 2;
   */
  ASPECT_RATIO_4x3 = 2,

  /**
   * @generated from enum value: ASPECT_RATION_3x4 = 3;
   */
  ASPECT_RATION_3x4 = 3,

  /**
   * @generated from enum value: ASPECT_RATIO_16x9 = 4;
   */
  ASPECT_RATIO_16x9 = 4,

  /**
   * @generated from enum value: ASPECT_RATION_9x16 = 5;
   */
  ASPECT_RATION_9x16 = 5,
}

/**
 * @generated from enum step.raccoon.common.Style
 */
export declare enum Style {
  /**
   * 通用IP角色生图用的图片风格（文生图）
   *
   * 标准IP角色原画风格
   *
   * @generated from enum value: ORIGINAL = 0;
   */
  ORIGINAL = 0,

  /**
   * 标准IP角色q版风格
   *
   * @generated from enum value: CHIBI = 1;
   */
  CHIBI = 1,

  /**
   * 标准IP角色光影风格
   *
   * @generated from enum value: LIGHT = 2;
   */
  LIGHT = 2,

  /**
   * 标准IP角色赛博风格
   *
   * @generated from enum value: CYBER = 3;
   */
  CYBER = 3,

  /**
   * 标准IP角色2.5d风格
   *
   * @generated from enum value: PSEUDOD = 4;
   */
  PSEUDOD = 4,

  /**
   * 标准IP角色SREF图片参照风格
   *
   * @generated from enum value: SREF = 5;
   */
  SREF = 5,

  /**
   * 飞天小女警
   *
   * @generated from enum value: POWERPUFF = 6;
   */
  POWERPUFF = 6,

  /**
   * 手绘国风
   *
   * @generated from enum value: HANDCHINESE = 7;
   */
  HANDCHINESE = 7,

  /**
   * 宫崎骏画风
   *
   * @generated from enum value: MIYAZAKI_HAYAO = 8;
   */
  MIYAZAKI_HAYAO = 8,

  /**
   * 卡通网络风
   *
   * @generated from enum value: CARTOON_INTERNET = 9;
   */
  CARTOON_INTERNET = 9,

  /**
   * labubu 风
   *
   * @generated from enum value: LABUBU = 10;
   */
  LABUBU = 10,

  /**
   * 原生风格，包括非SREF的捏图所有风格
   *
   * @generated from enum value: NATIVE = 11;
   */
  NATIVE = 11,

  /**
   * controlnet 生图风格（表情包/梗图）
   *
   * 默认表情包风格
   *
   * @generated from enum value: CTRLNET_DEFAULT = 200;
   */
  CTRLNET_DEFAULT = 200,

  /**
   * canny表情包风格
   *
   * @generated from enum value: CTRLNET_CANNY = 201;
   */
  CTRLNET_CANNY = 201,

  /**
   * dwpose表情包风格
   *
   * @generated from enum value: CTRLNET_DWPOSE = 202;
   */
  CTRLNET_DWPOSE = 202,

  /**
   * mask表情包风格
   *
   * @generated from enum value: CTRLNET_MASK = 203;
   */
  CTRLNET_MASK = 203,
}

/**
 * 生图proto/prompt 类型管理
 *
 * @generated from enum step.raccoon.common.ImagegenProtoType
 */
export declare enum ImagegenProtoType {
  /**
   * 标准捏图proto/prompt类型：捏图prompt是标签化的sd提示词字符串组合，形如<addition:prompt1>,<cloth:prompt2>...
   *
   * @generated from enum value: PROTO_TYPE_TAGED = 0;
   */
  PROTO_TYPE_TAGED = 0,

  /**
   * 标准proto/prompt类型，prompt就是sd直接提示词；目前用于表情包
   *
   * @generated from enum value: PROTO_TYPE_STANDARD = 1;
   */
  PROTO_TYPE_STANDARD = 1,

  /**
   * 标准proto/prompt类型，但是需要给端上捏图tag化
   *
   * @generated from enum value: PROTO_TYPE_STANDARD_TO_TAG = 2;
   */
  PROTO_TYPE_STANDARD_TO_TAG = 2,
}

/**
 * @generated from message step.raccoon.common.Role
 */
export declare class Role extends Message<Role> {
  /**
   * ip type
   *
   * @generated from field: int32 brand_type = 1;
   */
  brandType: number;

  /**
   * system role id or user's role photo url
   *
   * @generated from field: string role = 2;
   */
  role: string;

  /**
   * 角色额外信息/json dict
   *
   * @generated from field: string extra = 3;
   */
  extra: string;

  /**
   * 角色名字，注意：作废字段
   *
   * @generated from field: string name = 4;
   */
  name: string;

  /**
   * 特征信息，注意：作废字段
   *
   * @generated from field: string prompt = 5;
   */
  prompt: string;

  /**
   * 角色简介，注意：作废字段
   *
   * @generated from field: string desc = 6;
   */
  desc: string;

  /**
   * 角色形象图片，注意：作废字段
   *
   * @generated from field: string image_url = 7;
   */
  imageUrl: string;

  constructor(data?: PartialMessage<Role>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Role";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Role;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Role;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Role;

  static equals(a: Role | PlainMessage<Role> | undefined, b: Role | PlainMessage<Role> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.ImagegenProto
 */
export declare class ImagegenProto extends Message<ImagegenProto> {
  /**
   * @generated from field: string proto_id = 1;
   */
  protoId: string;

  /**
   * 角色列表
   *
   * @generated from field: repeated step.raccoon.common.Role roles = 2;
   */
  roles: Role[];

  /**
   * 生图风格
   *
   * @generated from field: string style = 3;
   */
  style: string;

  /**
   * format like 1024x1024
   *
   * @generated from field: string size = 4;
   */
  size: string;

  /**
   * 不同玩法需要控制自己的prompt格式
   *
   * @generated from field: string prompt = 5;
   */
  prompt: string;

  /**
   * 此原型生图trace url
   *
   * @generated from field: string trace_url = 6;
   */
  traceUrl: string;

  /**
   * sref用的图片信息，目前仅对捏图的proto有效
   *
   * @generated from field: step.raccoon.common.Photo sref_photo = 7;
   */
  srefPhoto?: Photo;

  /**
   * 拍同款源作品id
   *
   * @generated from field: string reference_card_id = 8;
   */
  referenceCardId: string;

  /**
   * 拍同款源作者信息
   *
   * @generated from field: step.raccoon.common.UserProfile reference_card_user = 9;
   */
  referenceCardUser?: UserProfile;

  /**
   * 额外信息，可用于存储业务玩法的特殊信息/json dict
   *
   * @generated from field: string extra = 10;
   */
  extra: string;

  /**
   * proto/prompt类型
   *
   * @generated from field: step.raccoon.common.ImagegenProtoType proto_type = 11;
   */
  protoType: ImagegenProtoType;

  /**
   * 所属用户id
   *
   * @generated from field: string uid = 12;
   */
  uid: string;

  /**
   * 角色详细信息，包含ugc/pgc，适配ugc，pgc角色
   *
   * @generated from field: repeated step.raccoon.common.RoleInfo rolesV2 = 13;
   */
  rolesV2: RoleInfo[];

  constructor(data?: PartialMessage<ImagegenProto>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ImagegenProto";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImagegenProto;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImagegenProto;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImagegenProto;

  static equals(a: ImagegenProto | PlainMessage<ImagegenProto> | undefined, b: ImagegenProto | PlainMessage<ImagegenProto> | undefined): boolean;
}

