// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/profile.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { CensoredState, RoleState } from "./state_pb.js";
import type { UserSocialStat } from "./stat_pb.js";

/**
 * @generated from enum step.raccoon.common.UserType
 */
export declare enum UserType {
  /**
   * @generated from enum value: DefaultUserType = 0;
   */
  DefaultUserType = 0,

  /**
   * @generated from enum value: Official = 1;
   */
  Official = 1,

  /**
   * 角色类用户
   *
   * @generated from enum value: RoleUserType = 3;
   */
  RoleUserType = 3,
}

/**
 * 用户终端的平台
 *
 * @generated from enum step.raccoon.common.UserClientPlatform
 */
export declare enum UserClientPlatform {
  /**
   * 默认版本
   *
   * @generated from enum value: DefaultPlatform = 0;
   */
  DefaultPlatform = 0,

  /**
   * Web H5
   *
   * @generated from enum value: WebBrowser = 1;
   */
  WebBrowser = 1,

  /**
   * 微信小程序
   *
   * @generated from enum value: WxMiniProgram = 2;
   */
  WxMiniProgram = 2,

  /**
   * 安卓
   *
   * @generated from enum value: AndroidPhone = 3;
   */
  AndroidPhone = 3,

  /**
   * 苹果iPhone
   *
   * @generated from enum value: ApplePhone = 4;
   */
  ApplePhone = 4,

  /**
   * QQ小程序
   *
   * @generated from enum value: QqMiniProgram = 5;
   */
  QqMiniProgram = 5,
}

/**
 * @generated from enum step.raccoon.common.RoleType
 */
export declare enum RoleType {
  /**
   * 官方
   *
   * @generated from enum value: ROLE_TYPE_OFFICIAL = 0;
   */
  OFFICIAL = 0,

  /**
   * ugc自建角色
   *
   * @generated from enum value: ROLE_TYPE_UGC = 1;
   */
  UGC = 1,
}

/**
 * 用户简要信息
 *
 * @generated from message step.raccoon.common.UserProfile
 */
export declare class UserProfile extends Message<UserProfile> {
  /**
   * 用户id
   *
   * @generated from field: string uid = 1;
   */
  uid: string;

  /**
   * 用户昵称
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * 用户头像
   *
   * @generated from field: string avatar = 3;
   */
  avatar: string;

  /**
   * 用户类型
   *
   * @generated from field: step.raccoon.common.UserType type = 4;
   */
  type: UserType;

  /**
   * 附加信息
   *
   * @generated from field: map<string, string> extra = 5;
   */
  extra: { [key: string]: string };

  /**
   * 需要的角色信息
   *
   * @generated from field: step.raccoon.common.ProfileRoleInfo role_info = 6;
   */
  roleInfo?: ProfileRoleInfo;

  /**
   * 性别, 0=未设置，1=男，2=女，3=其他
   *
   * @generated from field: uint32 gender = 7;
   */
  gender: number;

  /**
   * 挂件信息
   *
   * @generated from field: step.raccoon.common.PendantInfo pendant = 8;
   */
  pendant?: PendantInfo;

  /**
   * 称号信息
   *
   * @generated from field: step.raccoon.common.SimpleAchievementInfo simple_achievement_info = 9;
   */
  simpleAchievementInfo?: SimpleAchievementInfo;

  constructor(data?: PartialMessage<UserProfile>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.UserProfile";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserProfile;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserProfile;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserProfile;

  static equals(a: UserProfile | PlainMessage<UserProfile> | undefined, b: UserProfile | PlainMessage<UserProfile> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.PendantInfo
 */
export declare class PendantInfo extends Message<PendantInfo> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  /**
   * @generated from field: string pendant_url = 2;
   */
  pendantUrl: string;

  /**
   * 挂件名称
   *
   * @generated from field: string pendant_name = 3;
   */
  pendantName: string;

  /**
   * 解锁挂件所需条件
   *
   * @generated from field: string pendant_condition = 4;
   */
  pendantCondition: string;

  /**
   * 挂件的有效期文案
   *
   * @generated from field: string validity_period_desc = 5;
   */
  validityPeriodDesc: string;

  /**
   * 是否已解锁
   *
   * @generated from field: bool is_avaiable = 6;
   */
  isAvaiable: boolean;

  /**
   * 是否已过期
   *
   * @generated from field: bool is_expire = 7;
   */
  isExpire: boolean;

  constructor(data?: PartialMessage<PendantInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.PendantInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PendantInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PendantInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PendantInfo;

  static equals(a: PendantInfo | PlainMessage<PendantInfo> | undefined, b: PendantInfo | PlainMessage<PendantInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.SimpleAchievementInfo
 */
export declare class SimpleAchievementInfo extends Message<SimpleAchievementInfo> {
  /**
   * @generated from field: string achievement_id = 1;
   */
  achievementId: string;

  /**
   * 称号名称
   *
   * @generated from field: string achievement_title = 2;
   */
  achievementTitle: string;

  /**
   * @generated from field: string achievement_type_id = 3;
   */
  achievementTypeId: string;

  /**
   * 是否等级称号
   *
   * @generated from field: bool is_level = 4;
   */
  isLevel: boolean;

  /**
   * @generated from field: string level = 5;
   */
  level: string;

  /**
   * @generated from field: string achievement_icon_url = 6;
   */
  achievementIconUrl: string;

  /**
   * @generated from field: string achievement_url = 7;
   */
  achievementUrl: string;

  /**
   * 解锁条件
   *
   * @generated from field: string condition = 8;
   */
  condition: string;

  /**
   * 发作品
   *
   * @generated from field: string button_content = 9;
   */
  buttonContent: string;

  /**
   * 称号的有效期文案
   *
   * @generated from field: string validity_period_desc = 10;
   */
  validityPeriodDesc: string;

  /**
   * 是否已解锁
   *
   * @generated from field: bool is_avaiable = 11;
   */
  isAvaiable: boolean;

  /**
   * 是否已过期
   *
   * @generated from field: bool is_expire = 12;
   */
  isExpire: boolean;

  /**
   * @generated from field: map<string, string> extra = 13;
   */
  extra: { [key: string]: string };

  /**
   * 跳转url
   *
   * @generated from field: string link_url = 14;
   */
  linkUrl: string;

  constructor(data?: PartialMessage<SimpleAchievementInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.SimpleAchievementInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SimpleAchievementInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SimpleAchievementInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SimpleAchievementInfo;

  static equals(a: SimpleAchievementInfo | PlainMessage<SimpleAchievementInfo> | undefined, b: SimpleAchievementInfo | PlainMessage<SimpleAchievementInfo> | undefined): boolean;
}

/**
 * 用户信息中角色的相关信息
 *
 * @generated from message step.raccoon.common.ProfileRoleInfo
 */
export declare class ProfileRoleInfo extends Message<ProfileRoleInfo> {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;

  /**
   * @generated from field: int64 create_uid = 2;
   */
  createUid: bigint;

  /**
   * 亲密度
   *
   * @generated from field: int32 intimacy = 3;
   */
  intimacy: number;

  /**
   * 角色类型
   *
   * @generated from field: step.raccoon.common.RoleType role_type = 4;
   */
  roleType: RoleType;

  /**
   * 是否星标
   *
   * @generated from field: bool is_star = 5;
   */
  isStar: boolean;

  /**
   * 是否保存了
   *
   * @generated from field: bool is_save = 6;
   */
  isSave: boolean;

  /**
   * 名称
   *
   * @generated from field: string name = 7;
   */
  name: string;

  /**
   * 头像
   *
   * @generated from field: string avatar = 8;
   */
  avatar: string;

  /**
   * 安全状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored = 9;
   */
  censored: CensoredState;

  /**
   * 角色状态
   *
   * @generated from field: step.raccoon.common.RoleState role_state = 10;
   */
  roleState: RoleState;

  /**
   * 角色状态
   *
   * @generated from field: string state = 11;
   */
  state: string;

  constructor(data?: PartialMessage<ProfileRoleInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ProfileRoleInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProfileRoleInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProfileRoleInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProfileRoleInfo;

  static equals(a: ProfileRoleInfo | PlainMessage<ProfileRoleInfo> | undefined, b: ProfileRoleInfo | PlainMessage<ProfileRoleInfo> | undefined): boolean;
}

/**
 * 带社交统计信息的用户信息
 *
 * @generated from message step.raccoon.common.RichUserProfile
 */
export declare class RichUserProfile extends Message<RichUserProfile> {
  /**
   * @generated from field: step.raccoon.common.UserProfile profile = 1;
   */
  profile?: UserProfile;

  /**
   * 用户社交统计数据
   *
   * @generated from field: step.raccoon.common.UserSocialStat stat = 2;
   */
  stat?: UserSocialStat;

  constructor(data?: PartialMessage<RichUserProfile>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RichUserProfile";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RichUserProfile;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RichUserProfile;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RichUserProfile;

  static equals(a: RichUserProfile | PlainMessage<RichUserProfile> | undefined, b: RichUserProfile | PlainMessage<RichUserProfile> | undefined): boolean;
}

