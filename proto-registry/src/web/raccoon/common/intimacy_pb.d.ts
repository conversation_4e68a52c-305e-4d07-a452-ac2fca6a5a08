// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/intimacy.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum step.raccoon.common.IntimacyInteractType
 */
export declare enum IntimacyInteractType {
  /**
   * @generated from enum value: INTIMACY_INTERACT_TYPE_UNKNOWN = 0;
   */
  INTIMACY_INTERACT_TYPE_UNKNOWN = 0,

  /**
   * 回复过该角色并评论成功
   *
   * @generated from enum value: INTIMACY_INTERACT_TYPE_REPLY_ROLE_AND_COMMENT = 1;
   */
  INTIMACY_INTERACT_TYPE_REPLY_ROLE_AND_COMMENT = 1,

  /**
   * @过该角色并评论成功
   *
   * @generated from enum value: INTIMACY_INTERACT_TYPE_AT_ROLE_AND_COMMENT = 2;
   */
  INTIMACY_INTERACT_TYPE_AT_ROLE_AND_COMMENT = 2,

  /**
   * 创建UGC角色成功
   *
   * @generated from enum value: INTIMACY_INTERACT_TYPE_CREATE_UGC_ROLE = 3;
   */
  INTIMACY_INTERACT_TYPE_CREATE_UGC_ROLE = 3,

  /**
   * 制作该角色图片成功
   *
   * @generated from enum value: INTIMACY_INTERACT_TYPE_MAKE_ROLE_IMAGE = 4;
   */
  INTIMACY_INTERACT_TYPE_MAKE_ROLE_IMAGE = 4,

  /**
   * 添加角色
   *
   * @generated from enum value: INTIMACY_INTERACT_TYPE_ADD_ROLE = 5;
   */
  INTIMACY_INTERACT_TYPE_ADD_ROLE = 5,

  /**
   * 送礼物
   *
   * @generated from enum value: INTIMACY_INTERACT_TYPE_Send_Gift = 6;
   */
  INTIMACY_INTERACT_TYPE_Send_Gift = 6,

  /**
   * 指定时间内没互动
   *
   * @generated from enum value: INTIMACY_INTERACT_DELAY_NO_INTERACT = 100;
   */
  INTIMACY_INTERACT_DELAY_NO_INTERACT = 100,
}

/**
 * @generated from enum step.raccoon.common.IntimacyChangeScore
 */
export declare enum IntimacyChangeScore {
  /**
   * @generated from enum value: ZERO = 0;
   */
  ZERO = 0,

  /**
   * @generated from enum value: FIVE = 5;
   */
  FIVE = 5,

  /**
   * @generated from enum value: TEN = 10;
   */
  TEN = 10,

  /**
   * @generated from enum value: TWENTY = 20;
   */
  TWENTY = 20,

  /**
   * @generated from enum value: NEGATIVE_TEN = -10;
   */
  NEGATIVE_TEN = -10,
}

/**
 * @generated from message step.raccoon.common.IntimacyMsg
 */
export declare class IntimacyMsg extends Message<IntimacyMsg> {
  /**
   * @generated from field: step.raccoon.common.IntimacyInteractType intimacy_interact_type = 1;
   */
  intimacyInteractType: IntimacyInteractType;

  /**
   * @generated from field: step.raccoon.common.IntimacyChangeScore intimacy_change_score = 2;
   */
  intimacyChangeScore: IntimacyChangeScore;

  constructor(data?: PartialMessage<IntimacyMsg>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.IntimacyMsg";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IntimacyMsg;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IntimacyMsg;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IntimacyMsg;

  static equals(a: IntimacyMsg | PlainMessage<IntimacyMsg> | undefined, b: IntimacyMsg | PlainMessage<IntimacyMsg> | undefined): boolean;
}

