// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/pk.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { UserProfile } from "./profile_pb.js";
import type { CommonMediaItem } from "./assets_pb.js";

/**
 * pk给卡片的信息
 *
 * @generated from message step.raccoon.common.PkExtInfo
 */
export declare class PkExtInfo extends Message<PkExtInfo> {
  /**
   * pk主题
   *
   * @generated from field: string pktheme_name = 1;
   */
  pkthemeName: string;

  /**
   * 真实参与人次
   *
   * @generated from field: int32 total = 2;
   */
  total: number;

  /**
   * 参与者
   *
   * @generated from field: repeated step.raccoon.common.UserProfile participants = 3;
   */
  participants: UserProfile[];

  /**
   * 封面图信息，两张
   *
   * @generated from field: repeated step.raccoon.common.CoverObj media_items = 4;
   */
  mediaItems: CoverObj[];

  /**
   * 是否参与过
   *
   * @generated from field: bool is_played = 5;
   */
  isPlayed: boolean;

  /**
   * 参与人次 
   *
   * @generated from field: int64 parti_num = 6;
   */
  partiNum: bigint;

  /**
   * 围观
   *
   * @generated from field: int64 seen_num = 7;
   */
  seenNum: bigint;

  constructor(data?: PartialMessage<PkExtInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.PkExtInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PkExtInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PkExtInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PkExtInfo;

  static equals(a: PkExtInfo | PlainMessage<PkExtInfo> | undefined, b: PkExtInfo | PlainMessage<PkExtInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CoverObj
 */
export declare class CoverObj extends Message<CoverObj> {
  /**
   * pk obj name
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * url信息
   *
   * @generated from field: step.raccoon.common.CommonMediaItem item = 2;
   */
  item?: CommonMediaItem;

  constructor(data?: PartialMessage<CoverObj>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CoverObj";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CoverObj;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CoverObj;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CoverObj;

  static equals(a: CoverObj | PlainMessage<CoverObj> | undefined, b: CoverObj | PlainMessage<CoverObj> | undefined): boolean;
}

