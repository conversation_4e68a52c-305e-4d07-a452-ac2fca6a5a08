// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/livephoto.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { UserProfile } from "./profile_pb.js";
import type { RoleInfo } from "./role_pb.js";
import type { GameType } from "./types_pb.js";
import type { BgmTagInfo } from "./bgm_pb.js";

/**
 * @generated from enum step.raccoon.common.LivePhotoWorkFlowType
 */
export declare enum LivePhotoWorkFlowType {
  /**
   * @generated from enum value: LIVEPHOTO_WORK_TYPE_UNKNOWN = 0;
   */
  LIVEPHOTO_WORK_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: LIVEPHOTO_WORK_TYPE_FIX = 1;
   */
  LIVEPHOTO_WORK_TYPE_FIX = 1,

  /**
   * @generated from enum value: LIVEPHOTO_WORK_TYPE_WITH_IMAGE_UNDERSTAND = 2;
   */
  LIVEPHOTO_WORK_TYPE_WITH_IMAGE_UNDERSTAND = 2,
}

/**
 * @generated from enum step.raccoon.common.LivePhotoTemplateStatus
 */
export declare enum LivePhotoTemplateStatus {
  /**
   * @generated from enum value: LIVEPHOTO_TEMPLATE_STATUS_UNKNOWN = 0;
   */
  LIVEPHOTO_TEMPLATE_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: LIVEPHOTO_TEMPLATE_STATUS_OFFLINE = 1;
   */
  LIVEPHOTO_TEMPLATE_STATUS_OFFLINE = 1,

  /**
   * @generated from enum value: LIVEPHOTO_TEMPLATE_STATUS_ONLINE = 2;
   */
  LIVEPHOTO_TEMPLATE_STATUS_ONLINE = 2,
}

/**
 * @generated from enum step.raccoon.common.LivePhotoBgmStatus
 */
export declare enum LivePhotoBgmStatus {
  /**
   * @generated from enum value: LIVEPHOTO_BGM_STATUS_UNKNOWN = 0;
   */
  LIVEPHOTO_BGM_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: LIVEPHOTO_BGM_STATUS_OFFLINE = 1;
   */
  LIVEPHOTO_BGM_STATUS_OFFLINE = 1,

  /**
   * @generated from enum value: LIVEPHOTO_BGM_STATUS_ONLINE = 2;
   */
  LIVEPHOTO_BGM_STATUS_ONLINE = 2,
}

/**
 * @generated from enum step.raccoon.common.LivePhotoStatus
 */
export declare enum LivePhotoStatus {
  /**
   * @generated from enum value: LIVEPHOTO_STATUS_UNKNOWN = 0;
   */
  LIVEPHOTO_STATUS_UNKNOWN = 0,

  /**
   * 排队中
   *
   * @generated from enum value: LIVEPHOTO_STATUS_WAITING = 1;
   */
  LIVEPHOTO_STATUS_WAITING = 1,

  /**
   * 处理中
   *
   * @generated from enum value: LIVEPHOTO_STATUS_PROCESSING = 2;
   */
  LIVEPHOTO_STATUS_PROCESSING = 2,

  /**
   * prompt生成完成
   *
   * @generated from enum value: LIVEPHOTO_STAUS_PROMPT_GENERATE = 3;
   */
  LIVEPHOTO_STAUS_PROMPT_GENERATE = 3,

  /**
   * 视频生成完成
   *
   * @generated from enum value: LIVEPHOTO_STATUS_VIDEO_GENERATE = 4;
   */
  LIVEPHOTO_STATUS_VIDEO_GENERATE = 4,

  /**
   * 视频后处理完成
   *
   * @generated from enum value: LIVEPHOTO_STATUS_VIDEO_PROCESS = 5;
   */
  LIVEPHOTO_STATUS_VIDEO_PROCESS = 5,

  /**
   * 图片生成完成
   *
   * @generated from enum value: LIVEPHOTO_STATUS_IMAGE_GENERATE = 6;
   */
  LIVEPHOTO_STATUS_IMAGE_GENERATE = 6,

  /**
   * 处理失败
   *
   * @generated from enum value: LIVEPHOTO_STATUS_FAIL = 997;
   */
  LIVEPHOTO_STATUS_FAIL = 997,

  /**
   * 正常生成
   *
   * @generated from enum value: LIVEPHOTO_STATUS_SUC = 998;
   */
  LIVEPHOTO_STATUS_SUC = 998,

  /**
   * 已删除
   *
   * @generated from enum value: LIVEPHOTO_STATUS_DELETE = 999;
   */
  LIVEPHOTO_STATUS_DELETE = 999,
}

/**
 * @generated from enum step.raccoon.common.LivePhotoPublishStatus
 */
export declare enum LivePhotoPublishStatus {
  /**
   * @generated from enum value: LIVEPHOTO_PUBLISH_STATUS_UNKNOWN = 0;
   */
  LIVEPHOTO_PUBLISH_STATUS_UNKNOWN = 0,

  /**
   * 排队中
   *
   * @generated from enum value: LIVEPHOTO_PUBLISH_STATUS_WAITING = 1;
   */
  LIVEPHOTO_PUBLISH_STATUS_WAITING = 1,

  /**
   * 处理中
   *
   * @generated from enum value: LIVEPHOTO_PUBLISH_STATUS_PROCESS = 2;
   */
  LIVEPHOTO_PUBLISH_STATUS_PROCESS = 2,

  /**
   * 处理失败
   *
   * @generated from enum value: LIVEPHOTO_PUBLISH_STATUS_FAIL = 997;
   */
  LIVEPHOTO_PUBLISH_STATUS_FAIL = 997,

  /**
   * 正常生成
   *
   * @generated from enum value: LIVEPHOTO_PUBLISH_STATUS_SUC = 998;
   */
  LIVEPHOTO_PUBLISH_STATUS_SUC = 998,
}

/**
 * @generated from enum step.raccoon.common.LivePhotoPublishFailReason
 */
export declare enum LivePhotoPublishFailReason {
  /**
   * @generated from enum value: LIVEPHOTO_PUBLISH_FAIL_REASON_UNKNOWN = 0;
   */
  LIVEPHOTO_PUBLISH_FAIL_REASON_UNKNOWN = 0,

  /**
   * 审核失败
   *
   * @generated from enum value: LIVEPHOTO_PUBLISH_FAIL_REASON_AUDIT_NOT_PASS = 1;
   */
  LIVEPHOTO_PUBLISH_FAIL_REASON_AUDIT_NOT_PASS = 1,

  /**
   * 后处理失败
   *
   * @generated from enum value: LIVEPHOTO_PUBLISH_FAIL_REASON_PROCESS_FAIL = 2;
   */
  LIVEPHOTO_PUBLISH_FAIL_REASON_PROCESS_FAIL = 2,

  /**
   * 万能
   *
   * @generated from enum value: LIVEPHOTO_PUBLISH_FAIL_REASON_ONLY_FAIL = 99;
   */
  LIVEPHOTO_PUBLISH_FAIL_REASON_ONLY_FAIL = 99,
}

/**
 * @generated from enum step.raccoon.common.LivePhotoCreateFailReason
 */
export declare enum LivePhotoCreateFailReason {
  /**
   * @generated from enum value: LIVEPHOTO_CREATE_FAIL_REASON_UNKNOWN = 0;
   */
  LIVEPHOTO_CREATE_FAIL_REASON_UNKNOWN = 0,

  /**
   * 审核
   *
   * @generated from enum value: LIVEPHOTO_CREATE_FAIL_REASON_AUDIT_NOT_PASS = 1;
   */
  LIVEPHOTO_CREATE_FAIL_REASON_AUDIT_NOT_PASS = 1,

  /**
   * 生成失败
   *
   * @generated from enum value: LIVEPHOTO_CREATE_FAIL_REASON_GENERATE_FAIL = 2;
   */
  LIVEPHOTO_CREATE_FAIL_REASON_GENERATE_FAIL = 2,

  /**
   * 生成超时
   *
   * @generated from enum value: LIVEPHOTO_CREATE_FAIL_REASON_GENERATE_TIMEOUT = 3;
   */
  LIVEPHOTO_CREATE_FAIL_REASON_GENERATE_TIMEOUT = 3,

  /**
   * 万能
   *
   * @generated from enum value: LIVEPHOTO_CREATE_FAIL_REASON_ONLY_FAIL = 99;
   */
  LIVEPHOTO_CREATE_FAIL_REASON_ONLY_FAIL = 99,
}

/**
 * @generated from enum step.raccoon.common.LivePhotoVideoModel
 */
export declare enum LivePhotoVideoModel {
  /**
   * @generated from enum value: LIVEPHOTO_VIDEO_MODEL_UNKNOWM = 0;
   */
  LIVEPHOTO_VIDEO_MODEL_UNKNOWM = 0,

  /**
   * step底模
   *
   * @generated from enum value: LIVEPHOTO_VIDEO_MODEL_STEP = 1;
   */
  LIVEPHOTO_VIDEO_MODEL_STEP = 1,

  /**
   * 可灵
   *
   * @generated from enum value: LIVEPHOTO_VIDEO_MODEL_KELING = 2;
   */
  LIVEPHOTO_VIDEO_MODEL_KELING = 2,

  /**
   * 万象
   *
   * @generated from enum value: LIVEPHOTO_VIDEO_MODEL_WAN = 3;
   */
  LIVEPHOTO_VIDEO_MODEL_WAN = 3,
}

/**
 * 动态壁纸模板
 *
 * @generated from message step.raccoon.common.LivePhotoTemplate
 */
export declare class LivePhotoTemplate extends Message<LivePhotoTemplate> {
  /**
   * 模板id
   *
   * @generated from field: string template_id = 1;
   */
  templateId: string;

  /**
   * 模板名
   *
   * @generated from field: string template_name = 2;
   */
  templateName: string;

  /**
   * 封面图链接
   *
   * @generated from field: string cover_img_url = 3;
   */
  coverImgUrl: string;

  /**
   * 封面视频链接
   *
   * @generated from field: string cover_video_url = 4;
   */
  coverVideoUrl: string;

  /**
   * 封面预览视频链接
   *
   * @generated from field: string preview_cover_video_url = 5;
   */
  previewCoverVideoUrl: string;

  constructor(data?: PartialMessage<LivePhotoTemplate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.LivePhotoTemplate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LivePhotoTemplate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LivePhotoTemplate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LivePhotoTemplate;

  static equals(a: LivePhotoTemplate | PlainMessage<LivePhotoTemplate> | undefined, b: LivePhotoTemplate | PlainMessage<LivePhotoTemplate> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.BgmInfo
 */
export declare class BgmInfo extends Message<BgmInfo> {
  /**
   * @generated from field: string bgm_id = 1;
   */
  bgmId: string;

  /**
   * 歌曲名称
   *
   * @generated from field: string bgm_name = 2;
   */
  bgmName: string;

  /**
   * 歌曲链接
   *
   * @generated from field: string bgm_url = 3;
   */
  bgmUrl: string;

  /**
   * 上传用户
   *
   * @generated from field: step.raccoon.common.UserProfile upload_user = 4;
   */
  uploadUser?: UserProfile;

  /**
   * 音乐长度 单位s
   *
   * @generated from field: int64 bgm_length = 5;
   */
  bgmLength: bigint;

  /**
   * 封面图
   *
   * @generated from field: string cover_img_url = 6;
   */
  coverImgUrl: string;

  /**
   * 音频id
   *
   * @generated from field: string bgm_audio_id = 7;
   */
  bgmAudioId: string;

  /**
   * 封面图id
   *
   * @generated from field: string cover_img_id = 8;
   */
  coverImgId: string;

  /**
   * 音乐标签
   *
   * @generated from field: repeated string music_tag = 9;
   */
  musicTag: string[];

  constructor(data?: PartialMessage<BgmInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.BgmInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BgmInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BgmInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BgmInfo;

  static equals(a: BgmInfo | PlainMessage<BgmInfo> | undefined, b: BgmInfo | PlainMessage<BgmInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.LivePhotoExtInfo
 */
export declare class LivePhotoExtInfo extends Message<LivePhotoExtInfo> {
  /**
   * 原视频地址
   *
   * @generated from field: string video_url = 1;
   */
  videoUrl: string;

  /**
   * @generated from field: uint32 video_width = 2;
   */
  videoWidth: number;

  /**
   * @generated from field: uint32 video_height = 3;
   */
  videoHeight: number;

  /**
   * @generated from field: string livephoto_id = 4;
   */
  livephotoId: string;

  /**
   * @generated from field: string template_id = 5;
   */
  templateId: string;

  /**
   * @generated from field: string template_name = 6;
   */
  templateName: string;

  /**
   * 下载地址
   *
   * @generated from field: string download_video_url = 7;
   */
  downloadVideoUrl: string;

  /**
   * 发布的正文数据
   *
   * @generated from field: string publish_content = 8;
   */
  publishContent: string;

  /**
   * 角色详细信息，包含ugc/pgc，适配ugc，pgc角色
   *
   * @generated from field: repeated step.raccoon.common.RoleInfo roles = 9;
   */
  roles: RoleInfo[];

  /**
   * 视频封面
   *
   * @generated from field: string video_cover_img_url = 10;
   */
  videoCoverImgUrl: string;

  /**
   * 预览视频地址
   *
   * @generated from field: string preview_video_url = 11;
   */
  previewVideoUrl: string;

  /**
   * 推荐音乐标签
   *
   * @generated from field: repeated string music_tags = 12;
   */
  musicTags: string[];

  /**
   * 基础图片的photoid
   *
   * @generated from field: string base_photo_id = 13;
   */
  basePhotoId: string;

  /**
   * 基础图片的protoid
   *
   * @generated from field: string base_proto_id = 14;
   */
  baseProtoId: string;

  /**
   * 基础图片的prompt
   *
   * @generated from field: string base_photo_prompt = 15;
   */
  basePhotoPrompt: string;

  /**
   *  基础图片的玩法
   *
   * @generated from field: step.raccoon.common.GameType base_photo_game_type = 16;
   */
  basePhotoGameType: GameType;

  /**
   * 音乐标签信息
   *
   * @generated from field: step.raccoon.common.BgmTagInfo bgm_tag_info = 51;
   */
  bgmTagInfo?: BgmTagInfo;

  constructor(data?: PartialMessage<LivePhotoExtInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.LivePhotoExtInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LivePhotoExtInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LivePhotoExtInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LivePhotoExtInfo;

  static equals(a: LivePhotoExtInfo | PlainMessage<LivePhotoExtInfo> | undefined, b: LivePhotoExtInfo | PlainMessage<LivePhotoExtInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.LivePhotoPublishProgress
 */
export declare class LivePhotoPublishProgress extends Message<LivePhotoPublishProgress> {
  /**
   * @generated from field: string live_photo_id = 1;
   */
  livePhotoId: string;

  /**
   * @generated from field: string card_id = 2;
   */
  cardId: string;

  /**
   * @generated from field: int32 progress = 3;
   */
  progress: number;

  /**
   * @generated from field: step.raccoon.common.LivePhotoPublishStatus status = 4;
   */
  status: LivePhotoPublishStatus;

  constructor(data?: PartialMessage<LivePhotoPublishProgress>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.LivePhotoPublishProgress";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LivePhotoPublishProgress;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LivePhotoPublishProgress;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LivePhotoPublishProgress;

  static equals(a: LivePhotoPublishProgress | PlainMessage<LivePhotoPublishProgress> | undefined, b: LivePhotoPublishProgress | PlainMessage<LivePhotoPublishProgress> | undefined): boolean;
}

