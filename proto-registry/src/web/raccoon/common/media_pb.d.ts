// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/media.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { GameType } from "./types_pb.js";

/**
 * @generated from enum step.raccoon.common.MediaType
 */
export declare enum MediaType {
  /**
   * @generated from enum value: MEDIA_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: MEDIA_TYPE_TEXT = 1;
   */
  TEXT = 1,

  /**
   * @generated from enum value: MEDIA_TYPE_IMAGE = 2;
   */
  IMAGE = 2,

  /**
   * @generated from enum value: MEDIA_TYPE_VIDEO = 3;
   */
  VIDEO = 3,

  /**
   * @generated from enum value: MEDIA_TYPE_AUDIO = 4;
   */
  AUDIO = 4,

  /**
   * @generated from enum value: MEDIA_TYPE_VIDEO_FRAME = 5;
   */
  VIDEO_FRAME = 5,
}

/**
 * 内部图片数据来源类型
 *
 * @generated from enum step.raccoon.common.ImageDataType
 */
export declare enum ImageDataType {
  /**
   * 图片url，UrlImage
   *
   * @generated from enum value: ImageFromUrl = 0;
   */
  ImageFromUrl = 0,

  /**
   * 图片二进制数据, BytesImage
   *
   * @generated from enum value: ImageFromBytes = 1;
   */
  ImageFromBytes = 1,

  /**
   * 图片来源的media service, MediaImage
   *
   * @generated from enum value: ImageFromMedia = 2;
   */
  ImageFromMedia = 2,
}

/**
 * ********************** 通用媒体类型 ************************* //
 * 内部视频数据来源类型
 *
 * @generated from enum step.raccoon.common.MediaDataType
 */
export declare enum MediaDataType {
  /**
   * 视频url，UrlVideo
   *
   * @generated from enum value: MediaFromUrl = 0;
   */
  MediaFromUrl = 0,

  /**
   * 视频二进制数据, BytesVideo
   *
   * @generated from enum value: MediaFromBytes = 1;
   */
  MediaFromBytes = 1,

  /**
   * 视频来源的media service, MediaVideo
   *
   * @generated from enum value: MediaFromMedia = 2;
   */
  MediaFromMedia = 2,
}

/**
 * @generated from message step.raccoon.common.Media
 */
export declare class Media extends Message<Media> {
  /**
   * @generated from field: step.raccoon.common.MediaType type = 1;
   */
  type: MediaType;

  /**
   * @generated from field: string key = 2;
   */
  key: string;

  /**
   * 不一定存在，如为Text元素时
   *
   * @generated from field: string url = 3;
   */
  url: string;

  /**
   * @generated from oneof step.raccoon.common.Media.meta
   */
  meta: {
    /**
     * @generated from field: step.raccoon.common.Text text = 10;
     */
    value: Text;
    case: "text";
  } | {
    /**
     * @generated from field: step.raccoon.common.Image image = 11;
     */
    value: Image;
    case: "image";
  } | {
    /**
     * @generated from field: step.raccoon.common.Video video = 12;
     */
    value: Video;
    case: "video";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<Media>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Media";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Media;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Media;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Media;

  static equals(a: Media | PlainMessage<Media> | undefined, b: Media | PlainMessage<Media> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.Text
 */
export declare class Text extends Message<Text> {
  /**
   * @generated from field: string content = 1;
   */
  content: string;

  constructor(data?: PartialMessage<Text>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Text";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Text;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Text;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Text;

  static equals(a: Text | PlainMessage<Text> | undefined, b: Text | PlainMessage<Text> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.Image
 */
export declare class Image extends Message<Image> {
  /**
   * @generated from field: int32 width = 1;
   */
  width: number;

  /**
   * @generated from field: int32 height = 2;
   */
  height: number;

  /**
   * @generated from field: string photo_id = 3;
   */
  photoId: string;

  /**
   * @generated from field: step.raccoon.common.GameType game_type = 4;
   */
  gameType: GameType;

  constructor(data?: PartialMessage<Image>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Image";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Image;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Image;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Image;

  static equals(a: Image | PlainMessage<Image> | undefined, b: Image | PlainMessage<Image> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.Video
 */
export declare class Video extends Message<Video> {
  /**
   * @generated from field: int32 width = 1;
   */
  width: number;

  /**
   * @generated from field: int32 height = 2;
   */
  height: number;

  /**
   * 单位毫秒
   *
   * @generated from field: int32 duration = 3;
   */
  duration: number;

  constructor(data?: PartialMessage<Video>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Video";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Video;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Video;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Video;

  static equals(a: Video | PlainMessage<Video> | undefined, b: Video | PlainMessage<Video> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.Audio
 */
export declare class Audio extends Message<Audio> {
  /**
   * 单位毫秒
   *
   * @generated from field: int32 duration = 1;
   */
  duration: number;

  constructor(data?: PartialMessage<Audio>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Audio";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Audio;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Audio;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Audio;

  static equals(a: Audio | PlainMessage<Audio> | undefined, b: Audio | PlainMessage<Audio> | undefined): boolean;
}

/**
 * 从url下载image
 *
 * @generated from message step.raccoon.common.UrlImage
 */
export declare class UrlImage extends Message<UrlImage> {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  constructor(data?: PartialMessage<UrlImage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.UrlImage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UrlImage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UrlImage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UrlImage;

  static equals(a: UrlImage | PlainMessage<UrlImage> | undefined, b: UrlImage | PlainMessage<UrlImage> | undefined): boolean;
}

/**
 * 二进制image源数据
 *
 * @generated from message step.raccoon.common.BytesImage
 */
export declare class BytesImage extends Message<BytesImage> {
  /**
   * @generated from field: bytes data = 1;
   */
  data: Uint8Array;

  constructor(data?: PartialMessage<BytesImage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.BytesImage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BytesImage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BytesImage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BytesImage;

  static equals(a: BytesImage | PlainMessage<BytesImage> | undefined, b: BytesImage | PlainMessage<BytesImage> | undefined): boolean;
}

/**
 * 传media service的image_id
 *
 * @generated from message step.raccoon.common.MediaImage
 */
export declare class MediaImage extends Message<MediaImage> {
  /**
   * @generated from field: string image_id = 1;
   */
  imageId: string;

  constructor(data?: PartialMessage<MediaImage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.MediaImage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MediaImage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MediaImage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MediaImage;

  static equals(a: MediaImage | PlainMessage<MediaImage> | undefined, b: MediaImage | PlainMessage<MediaImage> | undefined): boolean;
}

/**
 * 图片元数据，尺寸信息、图片类型信息等，可选
 *
 * @generated from message step.raccoon.common.ImageMeta
 */
export declare class ImageMeta extends Message<ImageMeta> {
  /**
   * @generated from field: uint32 width = 1;
   */
  width: number;

  /**
   * @generated from field: uint32 height = 2;
   */
  height: number;

  constructor(data?: PartialMessage<ImageMeta>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ImageMeta";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImageMeta;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImageMeta;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImageMeta;

  static equals(a: ImageMeta | PlainMessage<ImageMeta> | undefined, b: ImageMeta | PlainMessage<ImageMeta> | undefined): boolean;
}

/**
 * 内部图片后处理传参类型
 *
 * @generated from message step.raccoon.common.InternalImage
 */
export declare class InternalImage extends Message<InternalImage> {
  /**
   * @generated from field: step.raccoon.common.ImageDataType data_type = 1;
   */
  dataType: ImageDataType;

  /**
   * @generated from field: step.raccoon.common.ImageMeta meta = 2;
   */
  meta?: ImageMeta;

  /**
   * @generated from oneof step.raccoon.common.InternalImage.image
   */
  image: {
    /**
     * @generated from field: step.raccoon.common.UrlImage url_image = 3;
     */
    value: UrlImage;
    case: "urlImage";
  } | {
    /**
     * @generated from field: step.raccoon.common.BytesImage bytes_image = 4;
     */
    value: BytesImage;
    case: "bytesImage";
  } | {
    /**
     * @generated from field: step.raccoon.common.MediaImage media_image = 6;
     */
    value: MediaImage;
    case: "mediaImage";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<InternalImage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.InternalImage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InternalImage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InternalImage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InternalImage;

  static equals(a: InternalImage | PlainMessage<InternalImage> | undefined, b: InternalImage | PlainMessage<InternalImage> | undefined): boolean;
}

/**
 * 后处理返回的二进制图片数据
 *
 * @generated from message step.raccoon.common.ProcBytesImage
 */
export declare class ProcBytesImage extends Message<ProcBytesImage> {
  /**
   * @generated from field: bytes data = 1;
   */
  data: Uint8Array;

  constructor(data?: PartialMessage<ProcBytesImage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ProcBytesImage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProcBytesImage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProcBytesImage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProcBytesImage;

  static equals(a: ProcBytesImage | PlainMessage<ProcBytesImage> | undefined, b: ProcBytesImage | PlainMessage<ProcBytesImage> | undefined): boolean;
}

/**
 * 后处理返回的存到media service的图片数据
 *
 * @generated from message step.raccoon.common.ProcMediaImage
 */
export declare class ProcMediaImage extends Message<ProcMediaImage> {
  /**
   * @generated from field: string image_id = 1;
   */
  imageId: string;

  /**
   * @generated from field: string url = 2;
   */
  url: string;

  /**
   * @generated from field: string tos_url = 3;
   */
  tosUrl: string;

  /**
   * @generated from field: string inner_tos_url = 4;
   */
  innerTosUrl: string;

  constructor(data?: PartialMessage<ProcMediaImage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ProcMediaImage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProcMediaImage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProcMediaImage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProcMediaImage;

  static equals(a: ProcMediaImage | PlainMessage<ProcMediaImage> | undefined, b: ProcMediaImage | PlainMessage<ProcMediaImage> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.InternalProcImage
 */
export declare class InternalProcImage extends Message<InternalProcImage> {
  /**
   * @generated from field: step.raccoon.common.ImageDataType data_type = 1;
   */
  dataType: ImageDataType;

  /**
   * @generated from field: step.raccoon.common.ImageMeta meta = 2;
   */
  meta?: ImageMeta;

  /**
   * @generated from oneof step.raccoon.common.InternalProcImage.image
   */
  image: {
    /**
     * @generated from field: step.raccoon.common.ProcBytesImage bytes_image = 3;
     */
    value: ProcBytesImage;
    case: "bytesImage";
  } | {
    /**
     * @generated from field: step.raccoon.common.ProcMediaImage media_image = 4;
     */
    value: ProcMediaImage;
    case: "mediaImage";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<InternalProcImage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.InternalProcImage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InternalProcImage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InternalProcImage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InternalProcImage;

  static equals(a: InternalProcImage | PlainMessage<InternalProcImage> | undefined, b: InternalProcImage | PlainMessage<InternalProcImage> | undefined): boolean;
}

/**
 * 从url下载媒体
 *
 * @generated from message step.raccoon.common.MediaUrl
 */
export declare class MediaUrl extends Message<MediaUrl> {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  constructor(data?: PartialMessage<MediaUrl>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.MediaUrl";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MediaUrl;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MediaUrl;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MediaUrl;

  static equals(a: MediaUrl | PlainMessage<MediaUrl> | undefined, b: MediaUrl | PlainMessage<MediaUrl> | undefined): boolean;
}

/**
 * 二进制媒体源数据
 *
 * @generated from message step.raccoon.common.MediaBytes
 */
export declare class MediaBytes extends Message<MediaBytes> {
  /**
   * @generated from field: bytes data = 1;
   */
  data: Uint8Array;

  constructor(data?: PartialMessage<MediaBytes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.MediaBytes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MediaBytes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MediaBytes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MediaBytes;

  static equals(a: MediaBytes | PlainMessage<MediaBytes> | undefined, b: MediaBytes | PlainMessage<MediaBytes> | undefined): boolean;
}

/**
 * 传media service的video_id
 *
 * @generated from message step.raccoon.common.MediaId
 */
export declare class MediaId extends Message<MediaId> {
  /**
   * @generated from field: string media_id = 1;
   */
  mediaId: string;

  constructor(data?: PartialMessage<MediaId>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.MediaId";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MediaId;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MediaId;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MediaId;

  static equals(a: MediaId | PlainMessage<MediaId> | undefined, b: MediaId | PlainMessage<MediaId> | undefined): boolean;
}

/**
 * 参数设置，比如返回的内容等
 *
 * @generated from message step.raccoon.common.MediaOptions
 */
export declare class MediaOptions extends Message<MediaOptions> {
  /**
   * 当url或者media id的请求响应时期望返回数据时
   *
   * @generated from field: bool is_data_included = 1;
   */
  isDataIncluded: boolean;

  /**
   * 循环模式, 1=视频循环（默认），2=音频循环
   *
   * @generated from field: uint32 loop_mode = 2;
   */
  loopMode: number;

  constructor(data?: PartialMessage<MediaOptions>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.MediaOptions";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MediaOptions;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MediaOptions;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MediaOptions;

  static equals(a: MediaOptions | PlainMessage<MediaOptions> | undefined, b: MediaOptions | PlainMessage<MediaOptions> | undefined): boolean;
}

/**
 * 内部视频后处理传参类型
 *
 * @generated from message step.raccoon.common.InternalMedia
 */
export declare class InternalMedia extends Message<InternalMedia> {
  /**
   * @generated from field: step.raccoon.common.MediaDataType data_type = 1;
   */
  dataType: MediaDataType;

  /**
   * @generated from oneof step.raccoon.common.InternalMedia.media
   */
  media: {
    /**
     * @generated from field: step.raccoon.common.MediaUrl url = 2;
     */
    value: MediaUrl;
    case: "url";
  } | {
    /**
     * @generated from field: step.raccoon.common.MediaBytes bytes = 3;
     */
    value: MediaBytes;
    case: "bytes";
  } | {
    /**
     * @generated from field: step.raccoon.common.MediaId media_id = 4;
     */
    value: MediaId;
    case: "mediaId";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<InternalMedia>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.InternalMedia";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InternalMedia;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InternalMedia;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InternalMedia;

  static equals(a: InternalMedia | PlainMessage<InternalMedia> | undefined, b: InternalMedia | PlainMessage<InternalMedia> | undefined): boolean;
}

/**
 * 后处理返回的存到media service的媒体数据
 *
 * @generated from message step.raccoon.common.ProcMedia
 */
export declare class ProcMedia extends Message<ProcMedia> {
  /**
   * media service的id
   *
   * @generated from field: string media_id = 1;
   */
  mediaId: string;

  /**
   * 媒体数据，要显示式指定才会返回数据
   *
   * @generated from field: string data = 2;
   */
  data: string;

  /**
   * @generated from field: string url = 3;
   */
  url: string;

  /**
   * @generated from field: string tos_url = 4;
   */
  tosUrl: string;

  /**
   * @generated from field: string inner_tos_url = 5;
   */
  innerTosUrl: string;

  constructor(data?: PartialMessage<ProcMedia>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ProcMedia";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProcMedia;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProcMedia;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProcMedia;

  static equals(a: ProcMedia | PlainMessage<ProcMedia> | undefined, b: ProcMedia | PlainMessage<ProcMedia> | undefined): boolean;
}

