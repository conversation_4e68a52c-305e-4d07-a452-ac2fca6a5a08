// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/videogen.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Media } from "./media_pb.js";
import type { UserProfile } from "./profile_pb.js";

/**
 * 特效合成模版
 *
 * @generated from message step.raccoon.common.SpeTpl
 */
export declare class SpeTpl extends Message<SpeTpl> {
  /**
   * 特效id
   *
   * @generated from field: string spe_id = 1;
   */
  speId: string;

  /**
   * 特效名称
   *
   * @generated from field: string spe_name = 2;
   */
  speName: string;

  /**
   * 特效素材视频展示文件
   *
   * @generated from field: step.raccoon.common.Media cover_video = 3;
   */
  coverVideo?: Media;

  /**
   * 特效素材封面
   *
   * @generated from field: step.raccoon.common.Media cover_image = 4;
   */
  coverImage?: Media;

  /**
   * 特效数据文件url
   *
   * @generated from field: string data_url = 11;
   */
  dataUrl: string;

  /**
   * @generated from field: map<string, string> extra = 101;
   */
  extra: { [key: string]: string };

  constructor(data?: PartialMessage<SpeTpl>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.SpeTpl";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SpeTpl;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SpeTpl;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SpeTpl;

  static equals(a: SpeTpl | PlainMessage<SpeTpl> | undefined, b: SpeTpl | PlainMessage<SpeTpl> | undefined): boolean;
}

/**
 * 背景音乐合成模版
 *
 * @generated from message step.raccoon.common.BgmTpl
 */
export declare class BgmTpl extends Message<BgmTpl> {
  /**
   * 背景音乐id
   *
   * @generated from field: string bgm_id = 1;
   */
  bgmId: string;

  /**
   * 歌曲名称
   *
   * @generated from field: string bgm_name = 2;
   */
  bgmName: string;

  /**
   * 音乐素材文件
   *
   * @generated from field: step.raccoon.common.Media bgm_audio = 3;
   */
  bgmAudio?: Media;

  /**
   * 音乐素材封面
   *
   * @generated from field: step.raccoon.common.Media bgm_cover = 4;
   */
  bgmCover?: Media;

  /**
   * 上传用户
   *
   * @generated from field: step.raccoon.common.UserProfile upload_user = 5;
   */
  uploadUser?: UserProfile;

  constructor(data?: PartialMessage<BgmTpl>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.BgmTpl";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BgmTpl;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BgmTpl;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BgmTpl;

  static equals(a: BgmTpl | PlainMessage<BgmTpl> | undefined, b: BgmTpl | PlainMessage<BgmTpl> | undefined): boolean;
}

/**
 * 视频混合驱动生成模版（宅舞用，包括素材视频/音乐）
 *
 * @generated from message step.raccoon.common.HybTpl
 */
export declare class HybTpl extends Message<HybTpl> {
  /**
   * @generated from field: string hyb_id = 1;
   */
  hybId: string;

  /**
   * 驱动素材名称
   *
   * @generated from field: string hyb_name = 2;
   */
  hybName: string;

  /**
   * 驱动素材音乐名称
   *
   * @generated from field: string hyb_audio_name = 3;
   */
  hybAudioName: string;

  /**
   * 驱动素材视频封面
   *
   * @generated from field: step.raccoon.common.Media hyb_video = 4;
   */
  hybVideo?: Media;

  /**
   * 驱动素材图片封面
   *
   * @generated from field: step.raccoon.common.Media hyb_cover = 5;
   */
  hybCover?: Media;

  constructor(data?: PartialMessage<HybTpl>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.HybTpl";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HybTpl;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HybTpl;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HybTpl;

  static equals(a: HybTpl | PlainMessage<HybTpl> | undefined, b: HybTpl | PlainMessage<HybTpl> | undefined): boolean;
}

/**
 * 视频纯音乐驱动生成模版
 *
 * @generated from message step.raccoon.common.MusTpl
 */
export declare class MusTpl extends Message<MusTpl> {
  /**
   * 背景音乐id
   *
   * @generated from field: string mus_id = 1;
   */
  musId: string;

  /**
   * 歌曲名称
   *
   * @generated from field: string bgm_name = 2;
   */
  bgmName: string;

  /**
   * 音乐素材文件
   *
   * @generated from field: step.raccoon.common.Media bgm_audio = 3;
   */
  bgmAudio?: Media;

  /**
   * 音乐素材封面
   *
   * @generated from field: step.raccoon.common.Media bgm_cover = 4;
   */
  bgmCover?: Media;

  /**
   * 上传用户
   *
   * @generated from field: step.raccoon.common.UserProfile upload_user = 5;
   */
  uploadUser?: UserProfile;

  constructor(data?: PartialMessage<MusTpl>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.MusTpl";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MusTpl;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MusTpl;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MusTpl;

  static equals(a: MusTpl | PlainMessage<MusTpl> | undefined, b: MusTpl | PlainMessage<MusTpl> | undefined): boolean;
}

