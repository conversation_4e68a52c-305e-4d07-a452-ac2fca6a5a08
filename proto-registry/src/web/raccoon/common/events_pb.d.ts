// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/events.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { RoleInfo } from "./role_pb.js";
import type { CardInfo, TopicInfo } from "./showcase_pb.js";
import type { CommentType, GameType } from "./types_pb.js";

/**
 * @generated from enum step.raccoon.common.RoleEventType
 */
export declare enum RoleEventType {
  /**
   * @generated from enum value: ROLE_EVENT_UNKNOWN_TYPE = 0;
   */
  ROLE_EVENT_UNKNOWN_TYPE = 0,

  /**
   * @generated from enum value: CENSOR_CHANGE = 1;
   */
  CENSOR_CHANGE = 1,
}

/**
 * @generated from enum step.raccoon.common.UserEventType
 */
export declare enum UserEventType {
  /**
   * 新用户登陆
   *
   * @generated from enum value: NewUser = 0;
   */
  NewUser = 0,

  /**
   * 注销用户
   *
   * @generated from enum value: DeleteUser = 1;
   */
  DeleteUser = 1,
}

/**
 * @generated from enum step.raccoon.common.SessionEventType
 */
export declare enum SessionEventType {
  /**
   * 手机号/一键登录，登入
   *
   * @generated from enum value: Login = 0;
   */
  Login = 0,

  /**
   * 在个人中心主动登出
   *
   * @generated from enum value: Logout = 1;
   */
  Logout = 1,
}

/**
 * @generated from enum step.raccoon.common.CardEventType
 */
export declare enum CardEventType {
  /**
   * @generated from enum value: PublishedCard = 0;
   */
  PublishedCard = 0,

  /**
   * @generated from enum value: UpdateCard = 1;
   */
  UpdateCard = 1,

  /**
   * @generated from enum value: DeleteCard = 2;
   */
  DeleteCard = 2,

  /**
   * @generated from enum value: SaveCard = 3;
   */
  SaveCard = 3,

  /**
   * @generated from enum value: PublishFail = 4;
   */
  PublishFail = 4,
}

/**
 * @generated from enum step.raccoon.common.FollowEventType
 */
export declare enum FollowEventType {
  /**
   * 关注
   *
   * @generated from enum value: Follow = 0;
   */
  Follow = 0,

  /**
   * 取消关注
   *
   * @generated from enum value: UnFollow = 1;
   */
  UnFollow = 1,
}

/**
 * @generated from enum step.raccoon.common.CommentEventType
 */
export declare enum CommentEventType {
  /**
   * 创建一级、二级评论且安全状态过了pass
   *
   * @generated from enum value: CreateComment = 0;
   */
  CreateComment = 0,

  /**
   * 删除评论
   *
   * @generated from enum value: DeleteComment = 1;
   */
  DeleteComment = 1,

  /**
   * 评论过了机审，发布
   *
   * @generated from enum value: SaveComment = 2;
   */
  SaveComment = 2,
}

/**
 * @generated from enum step.raccoon.common.LikeEventType
 */
export declare enum LikeEventType {
  /**
   * 点赞
   *
   * @generated from enum value: Like = 0;
   */
  Like = 0,

  /**
   * 取消点赞
   *
   * @generated from enum value: UnLike = 1;
   */
  UnLike = 1,
}

/**
 * @generated from enum step.raccoon.common.InboxEventType
 */
export declare enum InboxEventType {
  /**
   * 公告
   *
   * @generated from enum value: Announce = 0;
   */
  Announce = 0,

  /**
   * 系统推荐作品
   *
   * @generated from enum value: RecommendWork = 1;
   */
  RecommendWork = 1,

  /**
   * 作品被点赞
   *
   * @generated from enum value: LikeWork = 2;
   */
  LikeWork = 2,

  /**
   * 作品被评论
   *
   * @generated from enum value: CommentWork = 3;
   */
  CommentWork = 3,

  /**
   * 粉丝关注
   *
   * @generated from enum value: UserFollow = 4;
   */
  UserFollow = 4,

  /**
   * 评论被回复
   *
   * @generated from enum value: ReplyComment = 5;
   */
  ReplyComment = 5,

  /**
   * 点赞评论
   *
   * @generated from enum value: LikeComment = 6;
   */
  LikeComment = 6,

  /**
   * 拍同款
   *
   * @generated from enum value: MakeCopy = 7;
   */
  MakeCopy = 7,

  /**
   * brand上架
   *
   * @generated from enum value: BrandLaunched = 8;
   */
  BrandLaunched = 8,

  /**
   * 积分派发
   *
   * @generated from enum value: DispatchPoints = 9;
   */
  DispatchPoints = 9,

  /**
   * 全局公告v2
   *
   * @generated from enum value: PublicCircular = 10;
   */
  PublicCircular = 10,

  /**
   * 圈人公告
   *
   * @generated from enum value: RangeCircular = 11;
   */
  RangeCircular = 11,

  /**
   * 奖励发放
   *
   * @generated from enum value: DistributePrize = 12;
   */
  DistributePrize = 12,

  /**
   * 角色审核
   *
   * @generated from enum value: RoleCensor = 13;
   */
  RoleCensor = 13,

  /**
   * 异步卡片生成失败
   *
   * @generated from enum value: AsyncCardGenerateFail = 14;
   */
  AsyncCardGenerateFail = 14,

  /**
   * 动态壁纸发布失败
   *
   * @generated from enum value: LivePhotoPublishFail = 15;
   */
  LivePhotoPublishFail = 15,

  /**
   * 异步卡片生成成功
   *
   * @generated from enum value: AsyncCardGenerateSuc = 16;
   */
  AsyncCardGenerateSuc = 16,

  /**
   * 作品发布后的人审站内信（用于block）
   *
   * @generated from enum value: WorkCensoreBlocked = 17;
   */
  WorkCensoreBlocked = 17,

  /**
   * 副本奖励领取通知
   *
   * @generated from enum value: InstanceClaimReward = 18;
   */
  InstanceClaimReward = 18,

  /**
   * 角色互动通知
   *
   * @generated from enum value: RoleInteraction = 19;
   */
  RoleInteraction = 19,

  /**
   * 通用Asyncard发布失败
   *
   * @generated from enum value: AsyncCardPublishFail = 20;
   */
  AsyncCardPublishFail = 20,

  /**
   *  魔改视频发布失败
   *
   * @generated from enum value: ReimaginePublishFail = 21;
   */
  ReimaginePublishFail = 21,

  /**
   * 通用的通知类型，可带push
   *
   * @generated from enum value: CommonAnnounce = 22;
   */
  CommonAnnounce = 22,

  /**
   * 点赞谷子
   *
   * @generated from enum value: LikeGoods = 23;
   */
  LikeGoods = 23,

  /**
   * 点赞痛墙
   *
   * @generated from enum value: LikeFandomWall = 24;
   */
  LikeFandomWall = 24,

  /**
   * 查看主页
   *
   * @generated from enum value: VisitHomepage = 25;
   */
  VisitHomepage = 25,

  /**
   * bbs更新通知
   *
   * @generated from enum value: PostUpdateNotice = 26;
   */
  PostUpdateNotice = 26,

  /**
   * 积分退还通知
   *
   * @generated from enum value: RevertPoints = 27;
   */
  RevertPoints = 27,

  /**
   * 订单支付完成通知
   *
   * @generated from enum value: OrderPaySucNotice = 28;
   */
  OrderPaySucNotice = 28,

  /**
   * 订单失败通知
   *
   * @generated from enum value: OrderFailNotice = 29;
   */
  OrderFailNotice = 29,

  /**
   * 活动中心 任务目标达成
   *
   * @generated from enum value: BonusNormalTaskComplete = 30;
   */
  BonusNormalTaskComplete = 30,

  /**
   * 活动中心 签到订阅提醒
   *
   * @generated from enum value: BonusCheckInRemind = 31;
   */
  BonusCheckInRemind = 31,

  /**
   * 称号
   *
   * @generated from enum value: Achievement = 32;
   */
  Achievement = 32,
}

/**
 * @generated from enum step.raccoon.common.PushTriggerType
 */
export declare enum PushTriggerType {
  /**
   * @generated from enum value: PUSH_TRIGGER_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: PUSH_TRIGGER_TYPE_MANUAL = 1;
   */
  MANUAL = 1,

  /**
   * @generated from enum value: PUSH_TRIGGER_TYPE_AUTO = 2;
   */
  AUTO = 2,
}

/**
 * @generated from enum step.raccoon.common.PushType
 */
export declare enum PushType {
  /**
   * @generated from enum value: PUSH_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: PUSH_TYPE_UNICAST = 1;
   */
  UNICAST = 1,

  /**
   * @generated from enum value: PUSH_TYPE_BROADCAST = 2;
   */
  BROADCAST = 2,
}

/**
 * @generated from enum step.raccoon.common.BotCommentEventType
 */
export declare enum BotCommentEventType {
  /**
   * 发布评论
   *
   * @generated from enum value: PublishComment = 0;
   */
  PublishComment = 0,

  /**
   * 点赞作品
   *
   * @generated from enum value: LikeWord = 1;
   */
  LikeWord = 1,
}

/**
 * @generated from enum step.raccoon.common.InboxButtonType
 */
export declare enum InboxButtonType {
  /**
   * 无能力
   *
   * @generated from enum value: ButtonTypeNone = 0;
   */
  ButtonTypeNone = 0,

  /**
   * 跳转能力
   *
   * @generated from enum value: ButtonTypeJump = 1;
   */
  ButtonTypeJump = 1,

  /**
   * 复制能力
   *
   * @generated from enum value: ButtonTypeCopy = 2;
   */
  ButtonTypeCopy = 2,
}

/**
 * @generated from enum step.raccoon.common.UserRoleEventType
 */
export declare enum UserRoleEventType {
  /**
   * 保存角色事件
   *
   * @generated from enum value: UserRoleEventTypeBind = 0;
   */
  UserRoleEventTypeBind = 0,

  /**
   * 解绑角色事件
   *
   * @generated from enum value: UserRoleEventTypeUnBind = 1;
   */
  UserRoleEventTypeUnBind = 1,
}

/**
 * 角色的互动消息子类型
 *
 * @generated from enum step.raccoon.common.RoleInteractionInboxType
 */
export declare enum RoleInteractionInboxType {
  /**
   * 角色查看作品
   *
   * @generated from enum value: InteractionTypeRoleWatchWork = 0;
   */
  InteractionTypeRoleWatchWork = 0,

  /**
   * 角色访问主页
   *
   * @generated from enum value: InteractionTypeWatchProfile = 1;
   */
  InteractionTypeWatchProfile = 1,

  /**
   * 点赞作品
   *
   * @generated from enum value: InteractionTypeLikeWork = 2;
   */
  InteractionTypeLikeWork = 2,

  /**
   * 访问他人
   *
   * @generated from enum value: InteractionTypeWatchOther = 3;
   */
  InteractionTypeWatchOther = 3,

  /**
   * 其密度变化
   *
   * @generated from enum value: InteractionTypeIntimacy = 4;
   */
  InteractionTypeIntimacy = 4,

  /**
   * 礼物
   *
   * @generated from enum value: InteractionTypeGift = 5;
   */
  InteractionTypeGift = 5,

  /**
   * 状态
   *
   * @generated from enum value: InteractionTypeState = 6;
   */
  InteractionTypeState = 6,
}

/**
 * 运营事件标识
 *
 * @generated from enum step.raccoon.common.OperationEvenType
 */
export declare enum OperationEvenType {
  /**
   * @generated from enum value: UNKOWN = 0;
   */
  UNKOWN = 0,

  /**
   * 星标
   *
   * @generated from enum value: STAR = 1;
   */
  STAR = 1,

  /**
   * 付费
   *
   * @generated from enum value: PAY = 2;
   */
  PAY = 2,
}

/**
 * @generated from message step.raccoon.common.RoleEvent
 */
export declare class RoleEvent extends Message<RoleEvent> {
  /**
   * @generated from field: step.raccoon.common.RoleEventType event_type = 1;
   */
  eventType: RoleEventType;

  /**
   * @generated from field: step.raccoon.common.RoleInfo role = 2;
   */
  role?: RoleInfo;

  /**
   * @generated from field: map<string, string> ext_info = 3;
   */
  extInfo: { [key: string]: string };

  constructor(data?: PartialMessage<RoleEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleEvent;

  static equals(a: RoleEvent | PlainMessage<RoleEvent> | undefined, b: RoleEvent | PlainMessage<RoleEvent> | undefined): boolean;
}

/**
 * 作品安全人审站站内信安全事件
 *
 * @generated from message step.raccoon.common.WorkBlockedEvent
 */
export declare class WorkBlockedEvent extends Message<WorkBlockedEvent> {
  /**
   * @generated from field: step.raccoon.common.CardInfo card = 1;
   */
  card?: CardInfo;

  /**
   * 安全审核站内信提示信息锚点, scenes: image/title/content/prompt
   *
   * @generated from field: map<string, string> ext_info = 2;
   */
  extInfo: { [key: string]: string };

  constructor(data?: PartialMessage<WorkBlockedEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.WorkBlockedEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkBlockedEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkBlockedEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkBlockedEvent;

  static equals(a: WorkBlockedEvent | PlainMessage<WorkBlockedEvent> | undefined, b: WorkBlockedEvent | PlainMessage<WorkBlockedEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.UserEvent
 */
export declare class UserEvent extends Message<UserEvent> {
  /**
   * @generated from field: step.raccoon.common.UserEventType event_type = 1;
   */
  eventType: UserEventType;

  /**
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  /**
   * @generated from field: int64 event_time = 3;
   */
  eventTime: bigint;

  constructor(data?: PartialMessage<UserEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.UserEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserEvent;

  static equals(a: UserEvent | PlainMessage<UserEvent> | undefined, b: UserEvent | PlainMessage<UserEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.SessionEvent
 */
export declare class SessionEvent extends Message<SessionEvent> {
  /**
   * @generated from field: step.raccoon.common.SessionEventType event_type = 1;
   */
  eventType: SessionEventType;

  /**
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  /**
   * @generated from field: string session_id = 3;
   */
  sessionId: string;

  /**
   * @generated from field: int64 event_time = 4;
   */
  eventTime: bigint;

  /**
   * 自定义保留字段
   *
   * @generated from field: map<string, string> reserved = 5;
   */
  reserved: { [key: string]: string };

  /**
   * @generated from field: string app_version = 6;
   */
  appVersion: string;

  /**
   * @generated from field: string product = 7;
   */
  product: string;

  /**
   * 0=所有版本, 1=Web H5, 2=小程序, 3=Android, 4=iPhone
   *
   * @generated from field: uint32 platform = 8;
   */
  platform: number;

  /**
   * @generated from field: string device = 9;
   */
  device: string;

  constructor(data?: PartialMessage<SessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.SessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SessionEvent;

  static equals(a: SessionEvent | PlainMessage<SessionEvent> | undefined, b: SessionEvent | PlainMessage<SessionEvent> | undefined): boolean;
}

/**
 * 客户端建连鉴权事件
 *
 * @generated from message step.raccoon.common.AuthEvent
 */
export declare class AuthEvent extends Message<AuthEvent> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * @generated from field: bool logined = 2;
   */
  logined: boolean;

  /**
   * @generated from field: int64 event_time = 3;
   */
  eventTime: bigint;

  /**
   * @generated from field: string app_version = 4;
   */
  appVersion: string;

  constructor(data?: PartialMessage<AuthEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.AuthEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuthEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuthEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuthEvent;

  static equals(a: AuthEvent | PlainMessage<AuthEvent> | undefined, b: AuthEvent | PlainMessage<AuthEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.HeartBeatEvent
 */
export declare class HeartBeatEvent extends Message<HeartBeatEvent> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * @generated from field: int64 event_time = 2;
   */
  eventTime: bigint;

  constructor(data?: PartialMessage<HeartBeatEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.HeartBeatEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HeartBeatEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HeartBeatEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HeartBeatEvent;

  static equals(a: HeartBeatEvent | PlainMessage<HeartBeatEvent> | undefined, b: HeartBeatEvent | PlainMessage<HeartBeatEvent> | undefined): boolean;
}

/**
 * 作品发布/删除/更新/发布失败事件
 *
 * @generated from message step.raccoon.common.CardEvent
 */
export declare class CardEvent extends Message<CardEvent> {
  /**
   * @generated from field: step.raccoon.common.CardEventType event_type = 1;
   */
  eventType: CardEventType;

  /**
   * @generated from field: int64 card_id = 2;
   */
  cardId: bigint;

  /**
   * @generated from field: step.raccoon.common.CardInfo card = 3;
   */
  card?: CardInfo;

  /**
   * @generated from field: int64 event_time = 4;
   */
  eventTime: bigint;

  /**
   * @generated from field: repeated step.raccoon.common.TopicInfo topic = 5;
   */
  topic: TopicInfo[];

  /**
   * 设备did信息
   *
   * @generated from field: string did = 10;
   */
  did: string;

  /**
   * app版本号
   *
   * @generated from field: string app_version = 11;
   */
  appVersion: string;

  /**
   * 消息处理平台策略，0=所有版本, 1=Web H5, 2=小程序, 3=Android, 4=iPhone
   *
   * @generated from field: uint32 platform = 12;
   */
  platform: number;

  /**
   * @generated from field: step.raccoon.common.CardEventExtInfo ext_info = 99;
   */
  extInfo?: CardEventExtInfo;

  constructor(data?: PartialMessage<CardEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardEvent;

  static equals(a: CardEvent | PlainMessage<CardEvent> | undefined, b: CardEvent | PlainMessage<CardEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.PublishFailExtInfo
 */
export declare class PublishFailExtInfo extends Message<PublishFailExtInfo> {
  /**
   * @generated from field: string fail_reason = 1;
   */
  failReason: string;

  constructor(data?: PartialMessage<PublishFailExtInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.PublishFailExtInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishFailExtInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishFailExtInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishFailExtInfo;

  static equals(a: PublishFailExtInfo | PlainMessage<PublishFailExtInfo> | undefined, b: PublishFailExtInfo | PlainMessage<PublishFailExtInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CardEventExtInfo
 */
export declare class CardEventExtInfo extends Message<CardEventExtInfo> {
  /**
   * @generated from oneof step.raccoon.common.CardEventExtInfo.ext_info
   */
  extInfo: {
    /**
     * @generated from field: step.raccoon.common.PublishFailExtInfo publish_fail = 1;
     */
    value: PublishFailExtInfo;
    case: "publishFail";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<CardEventExtInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardEventExtInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardEventExtInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardEventExtInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardEventExtInfo;

  static equals(a: CardEventExtInfo | PlainMessage<CardEventExtInfo> | undefined, b: CardEventExtInfo | PlainMessage<CardEventExtInfo> | undefined): boolean;
}

/**
 * 关注取关事件通知
 *
 * @generated from message step.raccoon.common.FollowEvent
 */
export declare class FollowEvent extends Message<FollowEvent> {
  /**
   * @generated from field: step.raccoon.common.FollowEventType event_type = 1;
   */
  eventType: FollowEventType;

  /**
   * 用户id
   *
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  /**
   * 关注取关用户id
   *
   * @generated from field: int64 target_uid = 3;
   */
  targetUid: bigint;

  /**
   * 关注取关操作表操作id
   *
   * @generated from field: int64 log_id = 4;
   */
  logId: bigint;

  /**
   * @generated from field: int64 event_time = 5;
   */
  eventTime: bigint;

  constructor(data?: PartialMessage<FollowEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.FollowEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FollowEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FollowEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FollowEvent;

  static equals(a: FollowEvent | PlainMessage<FollowEvent> | undefined, b: FollowEvent | PlainMessage<FollowEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.Comment
 */
export declare class Comment extends Message<Comment> {
  /**
   * 评论id
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * 评论的uid
   *
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  /**
   * 评论内容
   *
   * @generated from field: string content = 3;
   */
  content: string;

  /**
   * 表情包id
   *
   * @generated from field: string emoji_id = 4;
   */
  emojiId: string;

  /**
   * 评论类型
   *
   * @generated from field: step.raccoon.common.CommentType comment_type = 5;
   */
  commentType: CommentType;

  /**
   * at的用户
   *
   * @generated from field: repeated int64 at_uids = 6;
   */
  atUids: bigint[];

  /**
   * at的用户名
   *
   * @generated from field: repeated string at_user_name = 7;
   */
  atUserName: string[];

  /**
   * at的角色
   *
   * @generated from field: repeated string at_roles = 8;
   */
  atRoles: string[];

  constructor(data?: PartialMessage<Comment>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Comment";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Comment;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Comment;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Comment;

  static equals(a: Comment | PlainMessage<Comment> | undefined, b: Comment | PlainMessage<Comment> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.Card
 */
export declare class Card extends Message<Card> {
  /**
   * 评论关联的作品id
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * 评论关联的作品的uid
   *
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  /**
   * 封面图片URL
   *
   * @generated from field: string image = 3;
   */
  image: string;

  /**
   * 对应玩法的子作品id
   *
   * @generated from field: string game_id = 4;
   */
  gameId: string;

  /**
   * 玩法类型
   *
   * @generated from field: step.raccoon.common.GameType game_type = 5;
   */
  gameType: GameType;

  /**
   * IP类型
   *
   * @generated from field: int32 brand = 6;
   */
  brand: number;

  constructor(data?: PartialMessage<Card>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Card";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Card;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Card;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Card;

  static equals(a: Card | PlainMessage<Card> | undefined, b: Card | PlainMessage<Card> | undefined): boolean;
}

/**
 * topic: comment
 *
 * @generated from message step.raccoon.common.CommentEvent
 */
export declare class CommentEvent extends Message<CommentEvent> {
  /**
   * 评论事件类型
   *
   * @generated from field: step.raccoon.common.CommentEventType event_type = 1;
   */
  eventType: CommentEventType;

  /**
   * 评论
   *
   * @generated from field: step.raccoon.common.Comment comment = 2;
   */
  comment?: Comment;

  /**
   * 二级评论时的一级评论
   *
   * @generated from field: step.raccoon.common.Comment parent_comment = 3;
   */
  parentComment?: Comment;

  /**
   * 被回复的二级评论
   *
   * @generated from field: step.raccoon.common.Comment replied_comment = 4;
   */
  repliedComment?: Comment;

  /**
   * 评论关联的作品id
   *
   * @generated from field: step.raccoon.common.Card card = 5;
   */
  card?: Card;

  /**
   * 事件时间
   *
   * @generated from field: int64 event_time = 6;
   */
  eventTime: bigint;

  constructor(data?: PartialMessage<CommentEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CommentEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CommentEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CommentEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CommentEvent;

  static equals(a: CommentEvent | PlainMessage<CommentEvent> | undefined, b: CommentEvent | PlainMessage<CommentEvent> | undefined): boolean;
}

/**
 * topic:like-comment
 *
 * @generated from message step.raccoon.common.LikeCommentEvent
 */
export declare class LikeCommentEvent extends Message<LikeCommentEvent> {
  /**
   * 点赞评论类型
   *
   * @generated from field: step.raccoon.common.LikeEventType event_type = 1;
   */
  eventType: LikeEventType;

  /**
   * 评论
   *
   * @generated from field: step.raccoon.common.Comment comment = 2;
   */
  comment?: Comment;

  /**
   * 关联作品
   *
   * @generated from field: step.raccoon.common.Card card = 3;
   */
  card?: Card;

  /**
   * 点赞用户id
   *
   * @generated from field: int64 uid = 4;
   */
  uid: bigint;

  /**
   * 点赞时间
   *
   * @generated from field: int64 event_time = 5;
   */
  eventTime: bigint;

  /**
   * 点赞数量
   *
   * @generated from field: int64 like_cnt = 6;
   */
  likeCnt: bigint;

  constructor(data?: PartialMessage<LikeCommentEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.LikeCommentEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeCommentEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeCommentEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeCommentEvent;

  static equals(a: LikeCommentEvent | PlainMessage<LikeCommentEvent> | undefined, b: LikeCommentEvent | PlainMessage<LikeCommentEvent> | undefined): boolean;
}

/**
 * topic: like-card
 *
 * @generated from message step.raccoon.common.LikeCardEvent
 */
export declare class LikeCardEvent extends Message<LikeCardEvent> {
  /**
   * 点赞作品类型
   *
   * @generated from field: step.raccoon.common.LikeEventType event_type = 1;
   */
  eventType: LikeEventType;

  /**
   * 作品
   *
   * @generated from field: step.raccoon.common.Card card = 2;
   */
  card?: Card;

  /**
   * 点赞用户id
   *
   * @generated from field: int64 uid = 3;
   */
  uid: bigint;

  /**
   * 点赞时间
   *
   * @generated from field: int64 event_time = 4;
   */
  eventTime: bigint;

  /**
   * 点赞数量
   *
   * @generated from field: int64 like_cnt = 5;
   */
  likeCnt: bigint;

  constructor(data?: PartialMessage<LikeCardEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.LikeCardEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeCardEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeCardEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeCardEvent;

  static equals(a: LikeCardEvent | PlainMessage<LikeCardEvent> | undefined, b: LikeCardEvent | PlainMessage<LikeCardEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.InboxEvent
 */
export declare class InboxEvent extends Message<InboxEvent> {
  /**
   * 消息事件类型
   *
   * @generated from field: step.raccoon.common.InboxEventType event_type = 1;
   */
  eventType: InboxEventType;

  /**
   * 站内信的用户uid
   *
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  /**
   * 站内信消息的发送者uid，如果是系统消息则为0
   *
   * @generated from field: int64 sender_uid = 3;
   */
  senderUid: bigint;

  /**
   * 站内信的消息id
   *
   * @generated from field: int64 msg_id = 4;
   */
  msgId: bigint;

  /**
   * 源事件的创建时间，单位毫秒
   *
   * @generated from field: int64 msg_time = 5;
   */
  msgTime: bigint;

  /**
   * 消息时间，单位毫秒
   *
   * @generated from field: int64 event_time = 6;
   */
  eventTime: bigint;

  /**
   * 是否不需要发送push,默认为需要
   *
   * @generated from field: bool no_need_push = 7;
   */
  noNeedPush: boolean;

  constructor(data?: PartialMessage<InboxEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.InboxEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InboxEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InboxEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InboxEvent;

  static equals(a: InboxEvent | PlainMessage<InboxEvent> | undefined, b: InboxEvent | PlainMessage<InboxEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.RecallPushEvent
 */
export declare class RecallPushEvent extends Message<RecallPushEvent> {
  /**
   * 推送类型: 群推/单点推送
   *
   * @generated from field: step.raccoon.common.PushType push_type = 1;
   */
  pushType: PushType;

  /**
   * 推送触发方式：手动/自动推送
   *
   * @generated from field: step.raccoon.common.PushTriggerType push_trigger_type = 2;
   */
  pushTriggerType: PushTriggerType;

  /**
   * 消息处理平台策略，0=所有版本, 1=Web H5, 2=小程序, 3=Android, 4=iPhone
   *
   * @generated from field: uint32 platform = 3;
   */
  platform: number;

  /**
   * 推送用户uid，如果是群推则为0
   *
   * @generated from field: int64 uid = 4;
   */
  uid: bigint;

  /**
   * recall的消息id
   *
   * @generated from field: int64 msg_id = 5;
   */
  msgId: bigint;

  /**
   * 消息的发送时间，单位毫秒
   *
   * @generated from field: int64 msg_send_time = 6;
   */
  msgSendTime: bigint;

  /**
   * 事件时间，单位毫秒
   *
   * @generated from field: int64 event_time = 7;
   */
  eventTime: bigint;

  constructor(data?: PartialMessage<RecallPushEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RecallPushEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RecallPushEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RecallPushEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RecallPushEvent;

  static equals(a: RecallPushEvent | PlainMessage<RecallPushEvent> | undefined, b: RecallPushEvent | PlainMessage<RecallPushEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.OffsiteEvent
 */
export declare class OffsiteEvent extends Message<OffsiteEvent> {
  /**
   * 是否为运营消息
   *
   * @generated from field: bool is_announce = 1;
   */
  isAnnounce: boolean;

  /**
   * 消息处理平台策略，0=所有版本, 1=Web H5, 2=小程序, 3=Android, 4=iPhone
   *
   * @generated from field: uint32 platform = 2;
   */
  platform: number;

  /**
   * 站内信的用户uid，当InboxEventType为Announce类型时，uid为0
   *
   * @generated from field: int64 uid = 3;
   */
  uid: bigint;

  /**
   * 站外信触发归属时间节点，ios统一都为0，andriod分别有11、14、17、20、22共计5个时间节点
   *
   * @generated from field: int32 belong_time = 4;
   */
  belongTime: number;

  constructor(data?: PartialMessage<OffsiteEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.OffsiteEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OffsiteEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OffsiteEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OffsiteEvent;

  static equals(a: OffsiteEvent | PlainMessage<OffsiteEvent> | undefined, b: OffsiteEvent | PlainMessage<OffsiteEvent> | undefined): boolean;
}

/**
 * 拍同款事件
 *
 * @generated from message step.raccoon.common.CardReferenceEvent
 */
export declare class CardReferenceEvent extends Message<CardReferenceEvent> {
  /**
   * 创作者作品
   *
   * @generated from field: step.raccoon.common.Card card = 1;
   */
  card?: Card;

  /**
   * 被拍同款的作品
   *
   * @generated from field: step.raccoon.common.Card reference_card = 2;
   */
  referenceCard?: Card;

  /**
   * 拍同款时间，毫秒
   *
   * @generated from field: int64 event_time = 3;
   */
  eventTime: bigint;

  /**
   * 拍同款站内信内容
   *
   * @generated from field: string content = 4;
   */
  content: string;

  constructor(data?: PartialMessage<CardReferenceEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardReferenceEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardReferenceEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardReferenceEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardReferenceEvent;

  static equals(a: CardReferenceEvent | PlainMessage<CardReferenceEvent> | undefined, b: CardReferenceEvent | PlainMessage<CardReferenceEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CensorEvent
 */
export declare class CensorEvent extends Message<CensorEvent> {
  /**
   * @generated from field: string package_id = 1;
   */
  packageId: string;

  /**
   * 只有机审返回结果为block时存在,L1/L2
   *
   * @generated from field: string risk_level = 2;
   */
  riskLevel: string;

  /**
   * 业务类型
   *
   * @generated from field: string biz_type = 3;
   */
  bizType: string;

  /**
   * 用于标识本次调用风控的最终审核结果
   *
   * @generated from field: string decision = 4;
   */
  decision: string;

  /**
   * 标识业务方调用环境
   *
   * @generated from field: string env = 5;
   */
  env: string;

  /**
   * 业务方透传字段
   *
   * @generated from field: string biz_penetrate_data = 6;
   */
  bizPenetrateData: string;

  /**
   * @generated from field: repeated step.raccoon.common.CensorResource resources = 7;
   */
  resources: CensorResource[];

  constructor(data?: PartialMessage<CensorEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CensorEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CensorEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CensorEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CensorEvent;

  static equals(a: CensorEvent | PlainMessage<CensorEvent> | undefined, b: CensorEvent | PlainMessage<CensorEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CensorResource
 */
export declare class CensorResource extends Message<CensorResource> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * IMG_URL/TEXT/TXT/IMG_BASE64/IMG
   *
   * @generated from field: string context = 3;
   */
  context: string;

  /**
   * @generated from field: string type = 4;
   */
  type: string;

  /**
   * @generated from field: string scene = 5;
   */
  scene: string;

  /**
   * 审核时间
   *
   * @generated from field: string happen_time = 6;
   */
  happenTime: string;

  /**
   * 标识资源审核明细结果
   *
   * @generated from field: string decision = 7;
   */
  decision: string;

  /**
   * 人审结果
   *
   * @generated from field: string manual_decision = 8;
   */
  manualDecision: string;

  /**
   * 人审标签
   *
   * @generated from field: repeated string manual_reason = 9;
   */
  manualReason: string[];

  /**
   * @generated from field: repeated string risk_text = 10;
   */
  riskText: string[];

  constructor(data?: PartialMessage<CensorResource>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CensorResource";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CensorResource;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CensorResource;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CensorResource;

  static equals(a: CensorResource | PlainMessage<CensorResource> | undefined, b: CensorResource | PlainMessage<CensorResource> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.BotCommentPublishEvent
 */
export declare class BotCommentPublishEvent extends Message<BotCommentPublishEvent> {
  /**
   * @generated from field: int64 publish_record_id = 1;
   */
  publishRecordId: bigint;

  /**
   * 类型
   *
   * @generated from field: step.raccoon.common.BotCommentEventType event_type = 2;
   */
  eventType: BotCommentEventType;

  /**
   * 点赞作品事件的字段
   *
   * @generated from field: step.raccoon.common.BotLikeWorkEvent like_work_event = 3;
   */
  likeWorkEvent?: BotLikeWorkEvent;

  constructor(data?: PartialMessage<BotCommentPublishEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.BotCommentPublishEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BotCommentPublishEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BotCommentPublishEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BotCommentPublishEvent;

  static equals(a: BotCommentPublishEvent | PlainMessage<BotCommentPublishEvent> | undefined, b: BotCommentPublishEvent | PlainMessage<BotCommentPublishEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.BotLikeWorkEvent
 */
export declare class BotLikeWorkEvent extends Message<BotLikeWorkEvent> {
  /**
   * bot_uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 被点赞的卡片id
   *
   * @generated from field: int64 card_id = 2;
   */
  cardId: bigint;

  constructor(data?: PartialMessage<BotLikeWorkEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.BotLikeWorkEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BotLikeWorkEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BotLikeWorkEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BotLikeWorkEvent;

  static equals(a: BotLikeWorkEvent | PlainMessage<BotLikeWorkEvent> | undefined, b: BotLikeWorkEvent | PlainMessage<BotLikeWorkEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.ShareEvent
 */
export declare class ShareEvent extends Message<ShareEvent> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * @generated from field: int64 event_time = 2;
   */
  eventTime: bigint;

  constructor(data?: PartialMessage<ShareEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ShareEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ShareEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ShareEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ShareEvent;

  static equals(a: ShareEvent | PlainMessage<ShareEvent> | undefined, b: ShareEvent | PlainMessage<ShareEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.RecallInboxJobEvent
 */
export declare class RecallInboxJobEvent extends Message<RecallInboxJobEvent> {
  /**
   * @generated from field: repeated int64 user_ids = 1;
   */
  userIds: bigint[];

  /**
   * @generated from field: int64 job_id = 2;
   */
  jobId: bigint;

  /**
   * 标题
   *
   * @generated from field: string inbox_title = 3;
   */
  inboxTitle: string;

  /**
   * 正文
   *
   * @generated from field: string inbox_content = 4;
   */
  inboxContent: string;

  /**
   * 图片
   *
   * @generated from field: string image_url = 5;
   */
  imageUrl: string;

  /**
   * 按钮能力
   *
   * @generated from field: step.raccoon.common.InboxButtonType button_type = 6;
   */
  buttonType: InboxButtonType;

  /**
   * 按钮文案
   *
   * @generated from field: string button_text = 7;
   */
  buttonText: string;

  /**
   * 按钮点击后的反应
   *
   * @generated from field: string button_content = 8;
   */
  buttonContent: string;

  /**
   * 是否置顶
   *
   * @generated from field: bool is_pin = 9;
   */
  isPin: boolean;

  /**
   * 置顶结束时间
   *
   * @generated from field: int64 pin_end_time = 10;
   */
  pinEndTime: bigint;

  /**
   * 用户消息表是否需要去重,默认去重
   *
   * @generated from field: bool NeedDeduplication = 11;
   */
  NeedDeduplication: boolean;

  /**
   * 站内信发送者uid
   *
   * @generated from field: int64 sender_uid = 12;
   */
  senderUid: bigint;

  /**
   * 消息事件类型
   *
   * @generated from field: step.raccoon.common.InboxEventType event_type = 13;
   */
  eventType: InboxEventType;

  constructor(data?: PartialMessage<RecallInboxJobEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RecallInboxJobEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RecallInboxJobEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RecallInboxJobEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RecallInboxJobEvent;

  static equals(a: RecallInboxJobEvent | PlainMessage<RecallInboxJobEvent> | undefined, b: RecallInboxJobEvent | PlainMessage<RecallInboxJobEvent> | undefined): boolean;
}

/**
 * 游戏副本排行榜初始化事件
 *
 * @generated from message step.raccoon.common.InstanceReinitEvent
 */
export declare class InstanceReinitEvent extends Message<InstanceReinitEvent> {
  /**
   * 排行榜周期
   *
   * @generated from field: string period = 1;
   */
  period: string;

  /**
   * 副本id
   *
   * @generated from field: int64 instance_id = 2;
   */
  instanceId: bigint;

  /**
   * 马甲号uid
   *
   * @generated from field: int64 uid = 3;
   */
  uid: bigint;

  /**
   * 预置分数
   *
   * @generated from field: int32 score = 4;
   */
  score: number;

  /**
   * 完成次数
   *
   * @generated from field: int32 times = 5;
   */
  times: number;

  /**
   * 距离新一周开始的秒数
   *
   * @generated from field: int32 since_time = 6;
   */
  sinceTime: number;

  constructor(data?: PartialMessage<InstanceReinitEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.InstanceReinitEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InstanceReinitEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InstanceReinitEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InstanceReinitEvent;

  static equals(a: InstanceReinitEvent | PlainMessage<InstanceReinitEvent> | undefined, b: InstanceReinitEvent | PlainMessage<InstanceReinitEvent> | undefined): boolean;
}

/**
 * 用户角色关系的事件
 *
 * @generated from message step.raccoon.common.UserRoleEvent
 */
export declare class UserRoleEvent extends Message<UserRoleEvent> {
  /**
   * @generated from field: step.raccoon.common.UserRoleEventType user_role_type = 1;
   */
  userRoleType: UserRoleEventType;

  /**
   * 角色id
   *
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * 用户id
   *
   * @generated from field: int64 user_id = 3;
   */
  userId: bigint;

  constructor(data?: PartialMessage<UserRoleEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.UserRoleEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserRoleEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserRoleEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserRoleEvent;

  static equals(a: UserRoleEvent | PlainMessage<UserRoleEvent> | undefined, b: UserRoleEvent | PlainMessage<UserRoleEvent> | undefined): boolean;
}

/**
 * 7天内用户未互动事件
 *
 * @generated from message step.raccoon.common.IntimacyNoInteractEvent
 */
export declare class IntimacyNoInteractEvent extends Message<IntimacyNoInteractEvent> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: int64 event_time = 3;
   */
  eventTime: bigint;

  /**
   * @generated from field: int32 change_type = 4;
   */
  changeType: number;

  constructor(data?: PartialMessage<IntimacyNoInteractEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.IntimacyNoInteractEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IntimacyNoInteractEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IntimacyNoInteractEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IntimacyNoInteractEvent;

  static equals(a: IntimacyNoInteractEvent | PlainMessage<IntimacyNoInteractEvent> | undefined, b: IntimacyNoInteractEvent | PlainMessage<IntimacyNoInteractEvent> | undefined): boolean;
}

/**
 * 刷数据任务事件
 *
 * @generated from message step.raccoon.common.RescanJobEvent
 */
export declare class RescanJobEvent extends Message<RescanJobEvent> {
  /**
   * 0-评论 1-角色
   *
   * @generated from field: int32 type = 1;
   */
  type: number;

  /**
   * 角色id
   *
   * @generated from field: repeated int64 commment_id = 2;
   */
  commmentId: bigint[];

  /**
   * 角色id
   *
   * @generated from field: repeated string role_id = 3;
   */
  roleId: string[];

  constructor(data?: PartialMessage<RescanJobEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RescanJobEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RescanJobEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RescanJobEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RescanJobEvent;

  static equals(a: RescanJobEvent | PlainMessage<RescanJobEvent> | undefined, b: RescanJobEvent | PlainMessage<RescanJobEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.RoleInteractionInboxEvent
 */
export declare class RoleInteractionInboxEvent extends Message<RoleInteractionInboxEvent> {
  /**
   * 互动消息子类型
   *
   * @generated from field: step.raccoon.common.RoleInteractionInboxType event_type = 1;
   */
  eventType: RoleInteractionInboxType;

  /**
   * 发送者的uid
   *
   * @generated from field: int64 sender_uid = 2;
   */
  senderUid: bigint;

  /**
   * 发送者的角色id
   *
   * @generated from field: string sender_role_id = 3;
   */
  senderRoleId: string;

  /**
   * 接受者uid
   *
   * @generated from field: int64 recive_uid = 4;
   */
  reciveUid: bigint;

  /**
   * RoleInteractionMsgBody 角色的信息 marshal
   *
   * @generated from field: string msg_body = 5;
   */
  msgBody: string;

  constructor(data?: PartialMessage<RoleInteractionInboxEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleInteractionInboxEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleInteractionInboxEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleInteractionInboxEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleInteractionInboxEvent;

  static equals(a: RoleInteractionInboxEvent | PlainMessage<RoleInteractionInboxEvent> | undefined, b: RoleInteractionInboxEvent | PlainMessage<RoleInteractionInboxEvent> | undefined): boolean;
}

/**
 * 角色评论通知消息
 *
 * @generated from message step.raccoon.common.RoleCommentUnicastEvent
 */
export declare class RoleCommentUnicastEvent extends Message<RoleCommentUnicastEvent> {
  /**
   * 卡片id
   *
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * 一级评论id
   *
   * @generated from field: string parent_comment_id = 2;
   */
  parentCommentId: string;

  /**
   * 本条评论id
   *
   * @generated from field: string comment_id = 3;
   */
  commentId: string;

  constructor(data?: PartialMessage<RoleCommentUnicastEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleCommentUnicastEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleCommentUnicastEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleCommentUnicastEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleCommentUnicastEvent;

  static equals(a: RoleCommentUnicastEvent | PlainMessage<RoleCommentUnicastEvent> | undefined, b: RoleCommentUnicastEvent | PlainMessage<RoleCommentUnicastEvent> | undefined): boolean;
}

/**
 * 访问个人主页通知消息
 *
 * @generated from message step.raccoon.common.HomepageVisitNotifyEvent
 */
export declare class HomepageVisitNotifyEvent extends Message<HomepageVisitNotifyEvent> {
  /**
   * 被访问的uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 访问者uid
   *
   * @generated from field: int64 visit_uid = 2;
   */
  visitUid: bigint;

  /**
   * 访问时间戳，单位毫秒
   *
   * @generated from field: int64 visit_time = 3;
   */
  visitTime: bigint;

  constructor(data?: PartialMessage<HomepageVisitNotifyEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.HomepageVisitNotifyEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HomepageVisitNotifyEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HomepageVisitNotifyEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HomepageVisitNotifyEvent;

  static equals(a: HomepageVisitNotifyEvent | PlainMessage<HomepageVisitNotifyEvent> | undefined, b: HomepageVisitNotifyEvent | PlainMessage<HomepageVisitNotifyEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CardOperationEvent
 */
export declare class CardOperationEvent extends Message<CardOperationEvent> {
  /**
   * 运营事件主体作品id
   *
   * @generated from field: repeated int64 card_ids = 1;
   */
  cardIds: bigint[];

  /**
   * 运营事件类型
   *
   * @generated from field: step.raccoon.common.OperationEvenType event = 2;
   */
  event: OperationEvenType;

  constructor(data?: PartialMessage<CardOperationEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardOperationEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardOperationEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardOperationEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardOperationEvent;

  static equals(a: CardOperationEvent | PlainMessage<CardOperationEvent> | undefined, b: CardOperationEvent | PlainMessage<CardOperationEvent> | undefined): boolean;
}

