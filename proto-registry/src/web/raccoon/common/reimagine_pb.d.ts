// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/reimagine.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { UserProfile } from "./profile_pb.js";
import type { MediaType } from "./media_pb.js";
import type { BgmInfo } from "./livephoto_pb.js";

/**
 * @generated from enum step.raccoon.common.PlotType
 */
export declare enum PlotType {
  /**
   * @generated from enum value: Image2Video = 0;
   */
  Image2Video = 0,

  /**
   * @generated from enum value: RawVideo = 1;
   */
  RawVideo = 1,
}

/**
 * @generated from message step.raccoon.common.ReimaginePlotTemplate
 */
export declare class ReimaginePlotTemplate extends Message<ReimaginePlotTemplate> {
  /**
   * 图片链接
   *
   * @generated from field: string image_url = 1;
   */
  imageUrl: string;

  /**
   * 图片id
   *
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  /**
   * tts角色
   *
   * @generated from field: string tts_role = 3;
   */
  ttsRole: string;

  /**
   * tts版本
   *
   * @generated from field: string tts_version = 4;
   */
  ttsVersion: string;

  constructor(data?: PartialMessage<ReimaginePlotTemplate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ReimaginePlotTemplate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReimaginePlotTemplate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReimaginePlotTemplate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReimaginePlotTemplate;

  static equals(a: ReimaginePlotTemplate | PlainMessage<ReimaginePlotTemplate> | undefined, b: ReimaginePlotTemplate | PlainMessage<ReimaginePlotTemplate> | undefined): boolean;
}

/**
 * 视频魔改模板
 *
 * @generated from message step.raccoon.common.ReimagineTemplate
 */
export declare class ReimagineTemplate extends Message<ReimagineTemplate> {
  /**
   * 模板id
   *
   * @generated from field: string template_id = 1;
   */
  templateId: string;

  /**
   * 模板名
   *
   * @generated from field: string template_name = 2;
   */
  templateName: string;

  /**
   * 封面图链接
   *
   * @generated from field: string cover_img_url = 3;
   */
  coverImgUrl: string;

  /**
   * 视频链接
   *
   * @generated from field: string video_url = 4;
   */
  videoUrl: string;

  /**
   * 上传用户
   *
   * @generated from field: step.raccoon.common.UserProfile upload_user = 5;
   */
  uploadUser?: UserProfile;

  /**
   * 章节
   *
   * @generated from field: repeated step.raccoon.common.ReimaginePlotTemplate plots = 6;
   */
  plots: ReimaginePlotTemplate[];

  /**
   * 片头视频长度
   *
   * @generated from field: int32 duration = 7;
   */
  duration: number;

  /**
   * 模版类型，0表示视频爆改，1表示台词爆改
   *
   * @generated from field: int32 template_type = 8;
   */
  templateType: number;

  /**
   * 台词字段，供台词爆改用
   *
   * @generated from field: step.raccoon.common.ReimagineLines lines = 9;
   */
  lines?: ReimagineLines;

  /**
   * 台词爆改在模版页面播放用的视频
   *
   * @generated from field: string template_video = 10;
   */
  templateVideo: string;

  /**
   * 爆改视频对应的videoId
   *
   * @generated from field: string video_id = 11;
   */
  videoId: string;

  /**
   * 台词爆改在模版页面播放用的视频id
   *
   * @generated from field: string template_video_id = 12;
   */
  templateVideoId: string;

  constructor(data?: PartialMessage<ReimagineTemplate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ReimagineTemplate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReimagineTemplate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReimagineTemplate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReimagineTemplate;

  static equals(a: ReimagineTemplate | PlainMessage<ReimagineTemplate> | undefined, b: ReimagineTemplate | PlainMessage<ReimagineTemplate> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.ReimagineLines
 */
export declare class ReimagineLines extends Message<ReimagineLines> {
  /**
   * id
   *
   * @generated from field: string line_id = 1;
   */
  lineId: string;

  /**
   * 台词
   *
   * @generated from field: repeated step.raccoon.common.TtsLine lines = 2;
   */
  lines: TtsLine[];

  /**
   * tts角色
   *
   * @generated from field: repeated step.raccoon.common.TtsRole roles = 3;
   */
  roles: TtsRole[];

  /**
   * 无人声视频
   *
   * @generated from field: string raw_video_url = 4;
   */
  rawVideoUrl: string;

  constructor(data?: PartialMessage<ReimagineLines>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ReimagineLines";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReimagineLines;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReimagineLines;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReimagineLines;

  static equals(a: ReimagineLines | PlainMessage<ReimagineLines> | undefined, b: ReimagineLines | PlainMessage<ReimagineLines> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.TtsRole
 */
export declare class TtsRole extends Message<TtsRole> {
  /**
   * 调用模型所传role
   *
   * @generated from field: string ttsRole = 1;
   */
  ttsRole: string;

  /**
   * 展示名称
   *
   * @generated from field: string ttsName = 2;
   */
  ttsName: string;

  /**
   * tts文本
   *
   * @generated from field: string ttsText = 3;
   */
  ttsText: string;

  /**
   * tts试听
   *
   * @generated from field: string ttsUrl = 4;
   */
  ttsUrl: string;

  /**
   * 头像
   *
   * @generated from field: string imageUrl = 5;
   */
  imageUrl: string;

  constructor(data?: PartialMessage<TtsRole>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.TtsRole";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TtsRole;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TtsRole;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TtsRole;

  static equals(a: TtsRole | PlainMessage<TtsRole> | undefined, b: TtsRole | PlainMessage<TtsRole> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.TtsLine
 */
export declare class TtsLine extends Message<TtsLine> {
  /**
   * 开始时间，单位ms
   *
   * @generated from field: int32 start_time = 1;
   */
  startTime: number;

  /**
   * 结束时间，单位ms
   *
   * @generated from field: int32 end_time = 2;
   */
  endTime: number;

  /**
   * 台词
   *
   * @generated from field: string text = 3;
   */
  text: string;

  /**
   * 角色
   *
   * @generated from field: string tts_role = 4;
   */
  ttsRole: string;

  /**
   * @generated from field: string audio_id = 5;
   */
  audioId: string;

  /**
   * url
   *
   * @generated from field: string audio_url = 6;
   */
  audioUrl: string;

  /**
   * 是否设为为prompt_wav
   *
   * @generated from field: bool is_prompt_wav = 7;
   */
  isPromptWav: boolean;

  /**
   * 音频长度
   *
   * @generated from field: int32 audio_duration = 8;
   */
  audioDuration: number;

  constructor(data?: PartialMessage<TtsLine>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.TtsLine";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TtsLine;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TtsLine;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TtsLine;

  static equals(a: TtsLine | PlainMessage<TtsLine> | undefined, b: TtsLine | PlainMessage<TtsLine> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.ReimaginePlot
 */
export declare class ReimaginePlot extends Message<ReimaginePlot> {
  /**
   * id
   *
   * @generated from field: string plot_id = 1;
   */
  plotId: string;

  /**
   * 图片链接
   *
   * @generated from field: string image_url = 2;
   */
  imageUrl: string;

  /**
   * 图片id
   *
   * @generated from field: string image_id = 3;
   */
  imageId: string;

  /**
   * tts角色
   *
   * @generated from field: string tts_role = 4;
   */
  ttsRole: string;

  /**
   * tts版本
   *
   * @generated from field: string tts_version = 5;
   */
  ttsVersion: string;

  /**
   * 生视频输入
   *
   * @generated from field: string prompt = 6;
   */
  prompt: string;

  /**
   * tts文案
   *
   * @generated from field: string tts_text = 7;
   */
  ttsText: string;

  /**
   * tts链接
   *
   * @generated from field: repeated string tts_url = 8;
   */
  ttsUrl: string[];

  /**
   * err_code
   *
   * @generated from field: int32 error_code = 9;
   */
  errorCode: number;

  /**
   * err_msg 展示用
   *
   * @generated from field: string error_msg = 10;
   */
  errorMsg: string;

  /**
   * 错误mediaType
   *
   * @generated from field: step.raccoon.common.MediaType error_media_type = 11;
   */
  errorMediaType: MediaType;

  /**
   * 章节类型，目前有两种，i2v or rawVideo
   *
   * @generated from field: step.raccoon.common.PlotType type = 12;
   */
  type: PlotType;

  /**
   * 视频id，如果有视频输入，则忽略tts等数据，也不会重新生成视频
   *
   * @generated from field: string video_id = 13;
   */
  videoId: string;

  /**
   * 视频url
   *
   * @generated from field: string video_url = 14;
   */
  videoUrl: string;

  /**
   * 持续时间，单位ms
   *
   * @generated from field: int32 duration = 15;
   */
  duration: number;

  /**
   * 台词文件
   *
   * @generated from field: step.raccoon.common.ReimagineLines lines = 16;
   */
  lines?: ReimagineLines;

  constructor(data?: PartialMessage<ReimaginePlot>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ReimaginePlot";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReimaginePlot;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReimaginePlot;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReimaginePlot;

  static equals(a: ReimaginePlot | PlainMessage<ReimaginePlot> | undefined, b: ReimaginePlot | PlainMessage<ReimaginePlot> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.VideoClip
 */
export declare class VideoClip extends Message<VideoClip> {
  /**
   * 封面
   *
   * @generated from field: string image_url = 1;
   */
  imageUrl: string;

  /**
   * 开始时间
   *
   * @generated from field: int32 from_time = 2;
   */
  fromTime: number;

  /**
   * 持续时间，单位ms
   *
   * @generated from field: int32 duration = 3;
   */
  duration: number;

  constructor(data?: PartialMessage<VideoClip>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.VideoClip";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VideoClip;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VideoClip;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VideoClip;

  static equals(a: VideoClip | PlainMessage<VideoClip> | undefined, b: VideoClip | PlainMessage<VideoClip> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.ReimagineExtInfo
 */
export declare class ReimagineExtInfo extends Message<ReimagineExtInfo> {
  /**
   * 原视频地址
   *
   * @generated from field: string video_url = 1;
   */
  videoUrl: string;

  /**
   * @generated from field: uint32 video_width = 2;
   */
  videoWidth: number;

  /**
   * @generated from field: uint32 video_height = 3;
   */
  videoHeight: number;

  /**
   * @generated from field: string reimagine_id = 4;
   */
  reimagineId: string;

  /**
   * 视频封面
   *
   * @generated from field: string video_cover_img_url = 5;
   */
  videoCoverImgUrl: string;

  /**
   * 视频片段
   *
   * @generated from field: repeated step.raccoon.common.VideoClip video_clip = 6;
   */
  videoClip: VideoClip[];

  /**
   * 视频片头
   *
   * @generated from field: step.raccoon.common.VideoClip head_video_clip = 7;
   */
  headVideoClip?: VideoClip;

  /**
   * 是否需要片头视频 
   *
   * @generated from field: bool need_head_video = 8;
   */
  needHeadVideo: boolean;

  /**
   * 模版id
   *
   * @generated from field: string template_id = 9;
   */
  templateId: string;

  /**
   * 视频长度
   *
   * @generated from field: int32 video_duration = 10;
   */
  videoDuration: number;

  /**
   * 创建时间
   *
   * @generated from field: string create_time = 11;
   */
  createTime: string;

  /**
   * bgm
   *
   * @generated from field: step.raccoon.common.BgmInfo bgm_info = 12;
   */
  bgmInfo?: BgmInfo;

  /**
   * 模版类型 0为视频魔改，1为台词爆改
   *
   * @generated from field: int32 template_type = 13;
   */
  templateType: number;

  /**
   * 下列字段在详情页使用
   *
   * 下载链接，带水印
   *
   * @generated from field: string download_video_url = 30;
   */
  downloadVideoUrl: string;

  /**
   * 拍同款id
   *
   * @generated from field: string reference_card_id = 31;
   */
  referenceCardId: string;

  /**
   * 拍同款用户
   *
   * @generated from field: step.raccoon.common.UserProfile reference_user = 32;
   */
  referenceUser?: UserProfile;

  /**
   * 作品正文
   *
   * @generated from field: string publish_content = 33;
   */
  publishContent: string;

  constructor(data?: PartialMessage<ReimagineExtInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ReimagineExtInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReimagineExtInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReimagineExtInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReimagineExtInfo;

  static equals(a: ReimagineExtInfo | PlainMessage<ReimagineExtInfo> | undefined, b: ReimagineExtInfo | PlainMessage<ReimagineExtInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.ReimagineProtoInfo
 */
export declare class ReimagineProtoInfo extends Message<ReimagineProtoInfo> {
  /**
   * 模版
   *
   * @generated from field: step.raccoon.common.ReimagineTemplate template = 1;
   */
  template?: ReimagineTemplate;

  /**
   * 章节
   *
   * @generated from field: repeated step.raccoon.common.ReimaginePlot plots = 2;
   */
  plots: ReimaginePlot[];

  /**
   * 是否需要片头视频 
   *
   * @generated from field: bool need_head_video = 3;
   */
  needHeadVideo: boolean;

  constructor(data?: PartialMessage<ReimagineProtoInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ReimagineProtoInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReimagineProtoInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReimagineProtoInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReimagineProtoInfo;

  static equals(a: ReimagineProtoInfo | PlainMessage<ReimagineProtoInfo> | undefined, b: ReimagineProtoInfo | PlainMessage<ReimagineProtoInfo> | undefined): boolean;
}

