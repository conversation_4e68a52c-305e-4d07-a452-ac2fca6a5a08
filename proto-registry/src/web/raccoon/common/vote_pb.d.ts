// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/vote.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { UserProfile } from "./profile_pb.js";

/**
 * @generated from enum step.raccoon.common.VoteType
 */
export declare enum VoteType {
  /**
   * @generated from enum value: VOTE_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * pk类型
   *
   * @generated from enum value: VOTE_TYPE_PK = 1;
   */
  PK = 1,

  /**
   * 普通投票
   *
   * @generated from enum value: VOTE_TYPE_NORMAL = 2;
   */
  NORMAL = 2,
}

/**
 * @generated from message step.raccoon.common.VoteOption
 */
export declare class VoteOption extends Message<VoteOption> {
  /**
   * @generated from field: string option_id = 1;
   */
  optionId: string;

  /**
   * 选项内容
   *
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * 被投数量
   *
   * @generated from field: int64 vote_cnt = 3;
   */
  voteCnt: bigint;

  /**
   * for pk
   *
   * @generated from field: bool red = 4;
   */
  red: boolean;

  constructor(data?: PartialMessage<VoteOption>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.VoteOption";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VoteOption;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VoteOption;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VoteOption;

  static equals(a: VoteOption | PlainMessage<VoteOption> | undefined, b: VoteOption | PlainMessage<VoteOption> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.VoteRecord
 */
export declare class VoteRecord extends Message<VoteRecord> {
  /**
   * @generated from field: string record_id = 1;
   */
  recordId: string;

  /**
   * @generated from field: step.raccoon.common.VoteInfo vote_info = 2;
   */
  voteInfo?: VoteInfo;

  /**
   * @generated from field: step.raccoon.common.VoteOption option_info = 3;
   */
  optionInfo?: VoteOption;

  /**
   * @generated from field: step.raccoon.common.UserProfile uinfo = 4;
   */
  uinfo?: UserProfile;

  /**
   * 毫秒级时间戳
   *
   * @generated from field: int64 vote_time = 5;
   */
  voteTime: bigint;

  constructor(data?: PartialMessage<VoteRecord>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.VoteRecord";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VoteRecord;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VoteRecord;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VoteRecord;

  static equals(a: VoteRecord | PlainMessage<VoteRecord> | undefined, b: VoteRecord | PlainMessage<VoteRecord> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.VoteInfo
 */
export declare class VoteInfo extends Message<VoteInfo> {
  /**
   * @generated from field: string vote_id = 1;
   */
  voteId: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: step.raccoon.common.VoteType vote_type = 4;
   */
  voteType: VoteType;

  /**
   * @generated from field: repeated step.raccoon.common.VoteOption option_infos = 5;
   */
  optionInfos: VoteOption[];

  constructor(data?: PartialMessage<VoteInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.VoteInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VoteInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VoteInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VoteInfo;

  static equals(a: VoteInfo | PlainMessage<VoteInfo> | undefined, b: VoteInfo | PlainMessage<VoteInfo> | undefined): boolean;
}

