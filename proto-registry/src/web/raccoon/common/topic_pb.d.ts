// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/topic.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { ActivityConf, RichCardInfo } from "./showcase_pb.js";
import type { GameType } from "./types_pb.js";

/**
 * 话题页展示topic
 *
 * @generated from message step.raccoon.common.TopicDetail
 */
export declare class TopicDetail extends Message<TopicDetail> {
  /**
   * @generated from field: string topic_id = 1;
   */
  topicId: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string content = 3;
   */
  content: string;

  /**
   * @generated from field: string background_color = 4;
   */
  backgroundColor: string;

  /**
   * @generated from field: string avatar = 5;
   */
  avatar: string;

  /**
   * @generated from field: string heat = 6;
   */
  heat: string;

  /**
   * @generated from field: step.raccoon.common.ActivityConf activity_conf = 7;
   */
  activityConf?: ActivityConf;

  /**
   * @generated from field: step.raccoon.common.GameType game_type = 8;
   */
  gameType: GameType;

  constructor(data?: PartialMessage<TopicDetail>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.TopicDetail";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicDetail;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicDetail;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicDetail;

  static equals(a: TopicDetail | PlainMessage<TopicDetail> | undefined, b: TopicDetail | PlainMessage<TopicDetail> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.TopicWithCard
 */
export declare class TopicWithCard extends Message<TopicWithCard> {
  /**
   * @generated from field: step.raccoon.common.TopicDetail topic = 1;
   */
  topic?: TopicDetail;

  /**
   * @generated from field: repeated step.raccoon.common.RichCardInfo cards = 2;
   */
  cards: RichCardInfo[];

  constructor(data?: PartialMessage<TopicWithCard>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.TopicWithCard";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicWithCard;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicWithCard;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicWithCard;

  static equals(a: TopicWithCard | PlainMessage<TopicWithCard> | undefined, b: TopicWithCard | PlainMessage<TopicWithCard> | undefined): boolean;
}

