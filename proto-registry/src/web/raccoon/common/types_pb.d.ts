// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/types.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

/**
 * @generated from enum step.raccoon.common.GameType
 */
export declare enum GameType {
  /**
   * @generated from enum value: GAME_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 舌战
   *
   * @generated from enum value: GAME_TYPE_TALK = 1;
   */
  TALK = 1,

  /**
   * 策略
   *
   * @generated from enum value: GAME_TYPE_STRATEGY = 2;
   */
  STRATEGY = 2,

  /**
   * 捏图
   *
   * @generated from enum value: GAME_TYPE_DRAWING = 3;
   */
  DRAWING = 3,

  /**
   * 战斗
   *
   * @generated from enum value: GAME_TYPE_FIGHT = 4;
   */
  FIGHT = 4,

  /**
   * 平行世界
   *
   * @generated from enum value: GAME_TYPE_WORLD = 5;
   */
  WORLD = 5,

  /**
   * 表情包
   *
   * @generated from enum value: GAME_TYPE_EMOJI = 6;
   */
  EMOJI = 6,

  /**
   * 官方出版物
   *
   * @generated from enum value: GAME_TYPE_PUBLICATION = 7;
   */
  PUBLICATION = 7,

  /**
   * 灵魂提取器
   *
   * @generated from enum value: GAME_TYPE_REVIVE = 8;
   */
  REVIVE = 8,

  /**
   * 梗图
   *
   * @generated from enum value: GAME_TYPE_MEME = 10;
   */
  MEME = 10,

  /**
   * 微动
   *
   * @generated from enum value: GAME_TYPE_LIVE_PHOTO = 11;
   */
  LIVE_PHOTO = 11,

  /**
   * ugc角色
   *
   * @generated from enum value: GAME_TYPE_UGCROLE = 12;
   */
  UGCROLE = 12,

  /**
   * 梦核
   *
   * @generated from enum value: GAME_TYPE_DREAMCORE = 13;
   */
  DREAMCORE = 13,

  /**
   * 影视副本
   *
   * @generated from enum value: GAME_TYPE_INSTANCE = 14;
   */
  INSTANCE = 14,

  /**
   * 魔改
   *
   * @generated from enum value: GAME_TYPE_REIMAGINE = 15;
   */
  REIMAGINE = 15,

  /**
   * 宅舞
   *
   * @generated from enum value: GAME_TYPE_OTAKUDANCE = 16;
   */
  OTAKUDANCE = 16,

  /**
   * 钥匙扣
   *
   * @generated from enum value: GAME_TYPE_JELLYCAT = 17;
   */
  JELLYCAT = 17,

  /**
   * bbs
   *
   * @generated from enum value: GAME_TYPE_BBS = 18;
   */
  BBS = 18,

  /**
   * 谷子
   *
   * @generated from enum value: GAME_TYPE_GOODS = 19;
   */
  GOODS = 19,

  /**
   * pk
   *
   * @generated from enum value: GAME_TYPE_PK = 20;
   */
  PK = 20,

  /**
   * 春节一起跳
   *
   * @generated from enum value: GAME_TYPE_DANCE_TOGETHER = 21;
   */
  DANCE_TOGETHER = 21,

  /**
   * 图片换料
   *
   * @generated from enum value: GAME_TYPE_PHOTO_FINETUNING = 22;
   */
  PHOTO_FINETUNING = 22,

  /**
   * 飞天小女警
   *
   * @generated from enum value: GAME_TYPE_POWERPUFF_GIRLS = 23;
   */
  POWERPUFF_GIRLS = 23,

  /**
   * 三只松鼠
   *
   * @generated from enum value: GAME_TYPE_THREE_SQ = 24;
   */
  THREE_SQ = 24,

  /**
   * labubu
   *
   * @generated from enum value: GAME_TYPE_LABUBU = 25;
   */
  LABUBU = 25,

  /**
   * 和23，24，25一样，以下都是快速上线玩法
   *
   * 次元破壁
   *
   * @generated from enum value: GAME_TYPE_BREAK_WALL = 100;
   */
  BREAK_WALL = 100,
}

/**
 * 一类交互相同的game，未来替代game type用于业务路由
 *
 * @generated from enum step.raccoon.common.GameGroup
 */
export declare enum GameGroup {
  /**
   * @generated from enum value: GAME_GROUP_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 小于1w用作兼容性保留
   * 图生图
   *
   * @generated from enum value: GAME_GROUP_I2I = 10001;
   */
  I2I = 10001,

  /**
   * 文生图
   *
   * @generated from enum value: GAME_GROUP_T2I = 10002;
   */
  T2I = 10002,
}

/**
 * @generated from enum step.raccoon.common.CardType
 */
export declare enum CardType {
  /**
   * @generated from enum value: CARD_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 游戏类
   *
   * @generated from enum value: CARD_TYPE_GAME = 1;
   */
  GAME = 1,

  /**
   * 视频类
   *
   * @generated from enum value: CARD_TYPE_VIDEO = 2;
   */
  VIDEO = 2,

  /**
   * 图片类
   *
   * @generated from enum value: CARD_TYPE_IMAGE = 3;
   */
  IMAGE = 3,

  /**
   * 首页运营位类
   *
   * @generated from enum value: CARD_TYPE_OPERATION = 4;
   */
  OPERATION = 4,

  /**
   * IP落地页
   *
   * @generated from enum value: CARD_TYPE_BRAND = 5;
   */
  BRAND = 5,

  /**
   * 平行世界话题页
   *
   * @generated from enum value: CARD_TYPE_WORLD_TOPIC = 6;
   */
  WORLD_TOPIC = 6,

  /**
   * bbs卡片
   *
   * @generated from enum value: CARD_TYPE_BBS = 7;
   */
  BBS = 7,

  /**
   * 玩法模板
   *
   * @generated from enum value: CARD_TYPE_GAME_TEMPLATE = 8;
   */
  GAME_TEMPLATE = 8,
}

/**
 * 和app中每个消费积分的操作一一对应
 *
 * @generated from enum step.raccoon.common.InvokeType
 */
export declare enum InvokeType {
  /**
   * @generated from enum value: INVOKE_UNKNOWN = 0;
   */
  INVOKE_UNKNOWN = 0,

  /**
   * 生图
   *
   * @generated from enum value: INVOKE_DRAWING_GEN = 20601;
   */
  INVOKE_DRAWING_GEN = 20601,

  /**
   * 重炖
   *
   * @generated from enum value: INVOKE_DRAWING_REDO = 20602;
   */
  INVOKE_DRAWING_REDO = 20602,

  /**
   * 生成形象
   *
   * @generated from enum value: INVOKE_DRAWING_UGC_IMAGE_GEN = 20610;
   */
  INVOKE_DRAWING_UGC_IMAGE_GEN = 20610,

  /**
   * 重新生成形象
   *
   * @generated from enum value: INVOKE_DRAWING_UGC_IMAGE_REDO = 20611;
   */
  INVOKE_DRAWING_UGC_IMAGE_REDO = 20611,

  /**
   * 创建角色
   *
   * @generated from enum value: INVOKE_DRAWING_UGC_ROLE_GEN = 20612;
   */
  INVOKE_DRAWING_UGC_ROLE_GEN = 20612,

  /**
   * 编辑角色
   *
   * @generated from enum value: INVOKE_DRAWING_UGC_ROLE_EDIT = 20613;
   */
  INVOKE_DRAWING_UGC_ROLE_EDIT = 20613,

  /**
   * 表情包生成4图
   *
   * @generated from enum value: INVOKE_EMOJI_QUADRUPLE_GEN = 20801 [deprecated = true];
   * @deprecated
   */
  INVOKE_EMOJI_QUADRUPLE_GEN = 20801,

  /**
   * 表情包生成1图
   *
   * @generated from enum value: INVOKE_EMOJI_SINGEL_GEN = 20802;
   */
  INVOKE_EMOJI_SINGEL_GEN = 20802,

  /**
   * 表情包重炖4图
   *
   * @generated from enum value: INVOKE_EMOJI_QUADRUPLE_REDO = 20803 [deprecated = true];
   * @deprecated
   */
  INVOKE_EMOJI_QUADRUPLE_REDO = 20803,

  /**
   * 表情包重炖1图
   *
   * @generated from enum value: INVOKE_EMOJI_SINGEL_REDO = 20804;
   */
  INVOKE_EMOJI_SINGEL_REDO = 20804,

  /**
   * 表情包模板炖图
   *
   * @generated from enum value: INVOKE_EMOJI_TMPL_GEN = 20810;
   */
  INVOKE_EMOJI_TMPL_GEN = 20810,

  /**
   * 表情包模板重炖
   *
   * @generated from enum value: INVOKE_EMOJI_TMPL_REDO = 20811;
   */
  INVOKE_EMOJI_TMPL_REDO = 20811,

  /**
   * 微动创作
   *
   * @generated from enum value: INVOKE_LIVEPHOTO_GEN = 21501;
   */
  INVOKE_LIVEPHOTO_GEN = 21501,

  /**
   * 微动失败重做
   *
   * @generated from enum value: INVOKE_LIVEPHOTO_REDO = 21502;
   */
  INVOKE_LIVEPHOTO_REDO = 21502,

  /**
   * 微动-视频拍同款
   *
   * @generated from enum value: INVOKE_LIVEPHOTO_VIDEO_MAKECOPY = 21503;
   */
  INVOKE_LIVEPHOTO_VIDEO_MAKECOPY = 21503,

  /**
   * 副本游玩
   *
   * @generated from enum value: INVOKE_INSTANCE_PLAY = 21601;
   */
  INVOKE_INSTANCE_PLAY = 21601,

  /**
   * 宅舞创作
   *
   * @generated from enum value: INVOKE_OTAKUDANCE_GEN = 21701;
   */
  INVOKE_OTAKUDANCE_GEN = 21701,

  /**
   * 宅舞创作失败重做
   *
   * @generated from enum value: INVOKE_OTAKUDANCE_REDO = 21702;
   */
  INVOKE_OTAKUDANCE_REDO = 21702,

  /**
   * 魔改创作
   *
   * @generated from enum value: INVOKE_REIMAGINE_GEN = 21801;
   */
  INVOKE_REIMAGINE_GEN = 21801,

  /**
   * 魔改重做
   *
   * @generated from enum value: INVOKE_REIMAGINE_REDO = 21802;
   */
  INVOKE_REIMAGINE_REDO = 21802,

  /**
   * 吧唧创作
   *
   * @generated from enum value: INVOKE_GOODS_BADGE_GEN = 22001;
   */
  INVOKE_GOODS_BADGE_GEN = 22001,

  /**
   * 吧唧重做
   *
   * @generated from enum value: INVOKE_GOODS_BADGE_REDO = 22002;
   */
  INVOKE_GOODS_BADGE_REDO = 22002,

  /**
   * 新春吧唧创作
   *
   * @generated from enum value: INVOKE_GOODS_FESTIVAL_BADGE_GEN = 22003;
   */
  INVOKE_GOODS_FESTIVAL_BADGE_GEN = 22003,

  /**
   * 新春吧唧重做
   *
   * @generated from enum value: INVOKE_GOODS_FESTIVAL_BADGE_REDO = 22004;
   */
  INVOKE_GOODS_FESTIVAL_BADGE_REDO = 22004,

  /**
   * 方吧唧创作
   *
   * @generated from enum value: INVOKE_GOODS_SQUARE_BADGE_GEN = 22005;
   */
  INVOKE_GOODS_SQUARE_BADGE_GEN = 22005,

  /**
   * 方吧唧重做
   *
   * @generated from enum value: INVOKE_GOODS_SQUARE_BADGE_REDO = 22006;
   */
  INVOKE_GOODS_SQUARE_BADGE_REDO = 22006,

  /**
   * 镭射票创作
   *
   * @generated from enum value: INVOKE_GOODS_LASER_TICKET_GEN = 22011;
   */
  INVOKE_GOODS_LASER_TICKET_GEN = 22011,

  /**
   * 镭射票重做
   *
   * @generated from enum value: INVOKE_GOODS_LASER_TICKET_REDO = 22012;
   */
  INVOKE_GOODS_LASER_TICKET_REDO = 22012,

  /**
   * 贴纸创作
   *
   * @generated from enum value: INVOKE_GOODS_STICKER_GEN = 22021;
   */
  INVOKE_GOODS_STICKER_GEN = 22021,

  /**
   * 贴纸重做
   *
   * @generated from enum value: INVOKE_GOODS_STICKER_REDO = 22022;
   */
  INVOKE_GOODS_STICKER_REDO = 22022,

  /**
   * 换料生图
   *
   * @generated from enum value: INVOKE_FINETUNING_GEN = 23001;
   */
  INVOKE_FINETUNING_GEN = 23001,

  /**
   * 换料重新生图
   *
   * @generated from enum value: INVOKE_FINETUNING_REGEN = 23002;
   */
  INVOKE_FINETUNING_REGEN = 23002,
}

/**
 * @generated from enum step.raccoon.common.CommentType
 */
export declare enum CommentType {
  /**
   * @generated from enum value: USER_NORMAL_COMMENT = 0;
   */
  USER_NORMAL_COMMENT = 0,

  /**
   * @generated from enum value: COPY_NOTICE_COMMENT = 1;
   */
  COPY_NOTICE_COMMENT = 1,

  /**
   * @generated from enum value: AUTO_BOT_COMMENT = 2;
   */
  AUTO_BOT_COMMENT = 2,

  /**
   * @generated from enum value: BOT_COMMENT = 3;
   */
  BOT_COMMENT = 3,

  /**
   * 无意义评论
   *
   * @generated from enum value: MEANING_LESS = 4;
   */
  MEANING_LESS = 4,

  /**
   * 角色互动消息
   *
   * @generated from enum value: ROLE_INTERACTION = 5;
   */
  ROLE_INTERACTION = 5,

  /**
   * 换料抄作业评论
   *
   * @generated from enum value: FINETUNING_COMMENT = 6;
   */
  FINETUNING_COMMENT = 6,
}

/**
 * 首次触发事件
 *
 * @generated from enum step.raccoon.common.FirstTriggerType
 */
export declare enum FirstTriggerType {
  /**
   * 未知
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 关注官号
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_OFFICE_FOLLOW = 1;
   */
  OFFICE_FOLLOW = 1,

  /**
   * 推荐关注
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_RECOMMEND_FOLLOW = 2;
   */
  RECOMMEND_FOLLOW = 2,

  /**
   * 进入炖图页
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_DRAWING_ENTER = 3;
   */
  DRAWING_ENTER = 3,

  /**
   * 炖图:feed流下滑
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_DRAWING_FEED_SLIDE = 4;
   */
  DRAWING_FEED_SLIDE = 4,

  /**
   * 进入炖图详情页
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_DRAWING_DETAIL_ENTER = 5;
   */
  DRAWING_DETAIL_ENTER = 5,

  /**
   * 进入灵魂提取页
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_REVIVE_ENTER = 6;
   */
  REVIVE_ENTER = 6,

  /**
   * 新用户当日首次打开app隐藏feed流运营位
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_HIDE_MARKETING_FEED = 7;
   */
  HIDE_MARKETING_FEED = 7,

  /**
   * 搜索:feed流下滑
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_SEARCH_FEED_SLIDE = 8;
   */
  SEARCH_FEED_SLIDE = 8,

  /**
   * 搜索:滑动ip展示完毕
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_SEARCH_LAST_BRAND = 9;
   */
  SEARCH_LAST_BRAND = 9,

  /**
   * 提示用户进入自己狸小窝
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_ENTER_OWN_FANDOM_WALL = 10;
   */
  ENTER_OWN_FANDOM_WALL = 10,

  /**
   * 提示用户进入他人狸小窝
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_ENTER_OTHER_FANDOM_WALL = 11;
   */
  ENTER_OTHER_FANDOM_WALL = 11,

  /**
   * 新用户首次发布作品发布奖励
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_NEW_USER_PUBLISH_CARD_GET_REWARD = 12;
   */
  NEW_USER_PUBLISH_CARD_GET_REWARD = 12,

  /**
   * 互动后引导用户关注
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_INTERACTION_FOLLOW = 13;
   */
  INTERACTION_FOLLOW = 13,

  /**
   * 访问客态个人主页出现关注弹窗
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_HOMEPAGE_FOLLOW = 14;
   */
  HOMEPAGE_FOLLOW = 14,

  /**
   * 提示痛墙谷子拖动
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_DRAG_GOODS = 15;
   */
  DRAG_GOODS = 15,

  /**
   * 提示痛墙谷子模板布局
   *
   * @generated from enum value: FIRST_TRIGGER_TYPE_GOODS_LAYOUT = 16;
   */
  GOODS_LAYOUT = 16,
}

/**
 * @generated from enum step.raccoon.common.RewardType
 */
export declare enum RewardType {
  /**
   * @generated from enum value: REWARD_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: REWARD_TYPE_POINTS = 1;
   */
  POINTS = 1,

  /**
   * @generated from enum value: REWARD_TYPE_JD_CARD = 2;
   */
  JD_CARD = 2,

  /**
   * 新人挂件
   *
   * @generated from enum value: REWARD_TYPE_NEW_USER_PENDENT = 3;
   */
  NEW_USER_PENDENT = 3,

  /**
   * 活动中心抽奖券
   *
   * @generated from enum value: REWARD_TYPE_BONUS_DRAW_TICKET = 4;
   */
  BONUS_DRAW_TICKET = 4,

  /**
   * 成就称号
   *
   * @generated from enum value: REWARD_TYPE_ACHIEVEMENT = 5;
   */
  ACHIEVEMENT = 5,

  /**
   * 活动中心实物奖励
   *
   * @generated from enum value: REWARD_TYPE_BONUS_PHYSICAL_REWARD = 6;
   */
  BONUS_PHYSICAL_REWARD = 6,
}

/**
 * @generated from enum step.raccoon.common.PopupType
 */
export declare enum PopupType {
  /**
   * @generated from enum value: POPUP_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: POPUP_TYPE_REWARD = 1;
   */
  REWARD = 1,

  /**
   * @generated from enum value: POPUP_TYPE_ACTIVITY = 2;
   */
  ACTIVITY = 2,

  /**
   * 引导应用商店评价弹窗
   *
   * @generated from enum value: POPUP_TYPE_APP_STORE_FEEDBACK = 3;
   */
  APP_STORE_FEEDBACK = 3,
}

/**
 * @generated from enum step.raccoon.common.FeedbackType
 */
export declare enum FeedbackType {
  /**
   * @generated from enum value: FEEDBACK_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 不喜欢该作者
   *
   * @generated from enum value: FEEDBACK_TYPE_DISLIKE_AUTHOR = 1;
   */
  DISLIKE_AUTHOR = 1,

  /**
   * 不喜欢该作品
   *
   * @generated from enum value: FEEDBACK_TYPE_DISLIKE_CARD = 2;
   */
  DISLIKE_CARD = 2,

  /**
   * 不喜欢该角色
   *
   * @generated from enum value: FEEDBACK_TYPE_DISLIKE_ROLE = 3;
   */
  DISLIKE_ROLE = 3,

  /**
   * 不喜欢该玩法
   *
   * @generated from enum value: FEEDBACK_TYPE_DISLIKE_GAME_TYPE = 4;
   */
  DISLIKE_GAME_TYPE = 4,

  /**
   * 内容不适
   *
   * @generated from enum value: FEEDBACK_TYPE_UNCOMFORTABLE_CONTENT = 11;
   */
  UNCOMFORTABLE_CONTENT = 11,

  /**
   * 内容质量差
   *
   * @generated from enum value: FEEDBACK_TYPE_LOW_QUALITY_CONTENT = 12;
   */
  LOW_QUALITY_CONTENT = 12,

  /**
   * 内容相似
   *
   * @generated from enum value: FEEDBACK_TYPE_SIMILAR_CONTENT = 13;
   */
  SIMILAR_CONTENT = 13,

  /**
   * 内容色情低俗
   *
   * @generated from enum value: FEEDBACK_TYPE_VULGAR_CONTENT = 14;
   */
  VULGAR_CONTENT = 14,
}

/**
 * @generated from enum step.raccoon.common.PaymentChannelType
 */
export declare enum PaymentChannelType {
  /**
   * 未知
   *
   * @generated from enum value: PAYMENT_CHANNEL_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 微信支付
   *
   * @generated from enum value: PAYMENT_CHANNEL_TYPE_WECHAT = 1;
   */
  WECHAT = 1,

  /**
   * 支付宝
   *
   * @generated from enum value: PAYMENT_CHANNEL_TYPE_ALIPAY = 2;
   */
  ALIPAY = 2,

  /**
   * 苹果支付
   *
   * @generated from enum value: PAYMENT_CHANNEL_TYPE_APPLE = 3;
   */
  APPLE = 3,
}

