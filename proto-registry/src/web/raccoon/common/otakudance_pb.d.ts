// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/otakudance.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Media } from "./media_pb.js";
import type { RoleInfo } from "./role_pb.js";
import type { HybTpl } from "./videogen_pb.js";
import type { GeneState } from "./state_pb.js";

/**
 * @generated from message step.raccoon.common.OtakudanceExtInfo
 */
export declare class OtakudanceExtInfo extends Message<OtakudanceExtInfo> {
  /**
   * 业务id
   *
   * @generated from field: string dance_id = 1;
   */
  danceId: string;

  /**
   * 视频图片封面
   *
   * @generated from field: step.raccoon.common.Media cover_image = 2;
   */
  coverImage?: Media;

  /**
   * 视频信息
   *
   * @generated from field: step.raccoon.common.Media video = 3;
   */
  video?: Media;

  /**
   * 用于下载的视频
   *
   * @generated from field: step.raccoon.common.Media video4dl = 4;
   */
  video4dl?: Media;

  /**
   * 宅舞发布后的正文部分（历史问题，暂时放在这）
   *
   * @generated from field: string publish_content = 5;
   */
  publishContent: string;

  /**
   * 视频proto信息
   *
   * 角色列表
   *
   * @generated from field: repeated step.raccoon.common.RoleInfo roles = 6;
   */
  roles: RoleInfo[];

  /**
   * prompt，暂未定义
   *
   * @generated from field: string prompt = 7;
   */
  prompt: string;

  /**
   * 视频生成视频驱动模版（目前用于宅舞）
   *
   * @generated from field: step.raccoon.common.HybTpl hyb_tpl = 8;
   */
  hybTpl?: HybTpl;

  /**
   * 生视频额外信息：json dict格式
   *
   * @generated from field: string extra = 9;
   */
  extra: string;

  constructor(data?: PartialMessage<OtakudanceExtInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.OtakudanceExtInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OtakudanceExtInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OtakudanceExtInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OtakudanceExtInfo;

  static equals(a: OtakudanceExtInfo | PlainMessage<OtakudanceExtInfo> | undefined, b: OtakudanceExtInfo | PlainMessage<OtakudanceExtInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.OtakudancePublishProgress
 */
export declare class OtakudancePublishProgress extends Message<OtakudancePublishProgress> {
  /**
   * publish id
   *
   * @generated from field: string publish_id = 1;
   */
  publishId: string;

  /**
   * 卡片id
   *
   * @generated from field: string card_id = 2;
   */
  cardId: string;

  /**
   * 进度数值，百分位
   *
   * @generated from field: int32 progress = 3;
   */
  progress: number;

  /**
   * 视频合成状态
   *
   * @generated from field: step.raccoon.common.GeneState status = 4;
   */
  status: GeneState;

  /**
   * 宅舞发布失败通知
   *
   * @generated from field: string error = 5;
   */
  error: string;

  constructor(data?: PartialMessage<OtakudancePublishProgress>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.OtakudancePublishProgress";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OtakudancePublishProgress;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OtakudancePublishProgress;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OtakudancePublishProgress;

  static equals(a: OtakudancePublishProgress | PlainMessage<OtakudancePublishProgress> | undefined, b: OtakudancePublishProgress | PlainMessage<OtakudancePublishProgress> | undefined): boolean;
}

