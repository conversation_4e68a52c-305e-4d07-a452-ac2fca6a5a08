// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/jellycat.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message step.raccoon.common.JellycatExtInfo
 */
export declare class JellycatExtInfo extends Message<JellycatExtInfo> {
  /**
   * @generated from field: string jellycat_photo_id = 1;
   */
  jellycatPhotoId: string;

  /**
   * 原图片地址
   *
   * @generated from field: string raw_img_url = 2;
   */
  rawImgUrl: string;

  /**
   * @generated from field: uint32 raw_img_width = 3;
   */
  rawImgWidth: number;

  /**
   * @generated from field: uint32 raw_img_height = 4;
   */
  rawImgHeight: number;

  /**
   * 合成图片地址
   *
   * @generated from field: string compound_img_url = 5;
   */
  compoundImgUrl: string;

  /**
   * @generated from field: uint32 compound_img_width = 6;
   */
  compoundImgWidth: number;

  /**
   * @generated from field: uint32 compound_img_height = 7;
   */
  compoundImgHeight: number;

  /**
   * @generated from field: string template_id = 8;
   */
  templateId: string;

  /**
   * @generated from field: string template_name = 9;
   */
  templateName: string;

  /**
   * 发布的正文数据
   *
   * @generated from field: string publish_content = 10;
   */
  publishContent: string;

  /**
   * 生成成功后的图片id
   *
   * @generated from field: string cover_img_id = 11;
   */
  coverImgId: string;

  /**
   * 上一次生成成功的cardid
   *
   * @generated from field: string async_card_id = 12;
   */
  asyncCardId: string;

  constructor(data?: PartialMessage<JellycatExtInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.JellycatExtInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): JellycatExtInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): JellycatExtInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): JellycatExtInfo;

  static equals(a: JellycatExtInfo | PlainMessage<JellycatExtInfo> | undefined, b: JellycatExtInfo | PlainMessage<JellycatExtInfo> | undefined): boolean;
}

