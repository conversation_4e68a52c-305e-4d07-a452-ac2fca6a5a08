// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/common/nullservice.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

/**
 * @generated from service step.raccoon.common.NullService
 */
export declare const NullService: {
  readonly typeName: "step.raccoon.common.NullService",
  readonly methods: {
  }
};

