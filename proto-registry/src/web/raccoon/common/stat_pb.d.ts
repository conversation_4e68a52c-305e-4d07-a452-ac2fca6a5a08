// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/stat.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { GameType } from "./types_pb.js";

/**
 * @generated from message step.raccoon.common.GameTypeCopyCount
 */
export declare class GameTypeCopyCount extends Message<GameTypeCopyCount> {
  /**
   * @generated from field: step.raccoon.common.GameType game_type = 1;
   */
  gameType: GameType;

  /**
   * 被拍同款数
   *
   * @generated from field: int64 beingCopieds = 2;
   */
  beingCopieds: bigint;

  constructor(data?: PartialMessage<GameTypeCopyCount>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.GameTypeCopyCount";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GameTypeCopyCount;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GameTypeCopyCount;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GameTypeCopyCount;

  static equals(a: GameTypeCopyCount | PlainMessage<GameTypeCopyCount> | undefined, b: GameTypeCopyCount | PlainMessage<GameTypeCopyCount> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.UserSocialStat
 */
export declare class UserSocialStat extends Message<UserSocialStat> {
  /**
   * 关注数
   *
   * @generated from field: int64 followings = 1;
   */
  followings: bigint;

  /**
   * 被关注数/粉丝数
   *
   * @generated from field: int64 fans = 2;
   */
  fans: bigint;

  /**
   * 获赞数
   *
   * @generated from field: int64 beingLikeds = 3;
   */
  beingLikeds: bigint;

  /**
   * 被拍同款数
   *
   * @generated from field: int64 beingCopieds = 4;
   */
  beingCopieds: bigint;

  /**
   * 查看者是否关注过此用户
   *
   * @generated from field: bool followed = 5;
   */
  followed: boolean;

  /**
   * 查看者是否被此用户关注
   *
   * @generated from field: bool beingFollowed = 6;
   */
  beingFollowed: boolean;

  /**
   * @generated from field: repeated step.raccoon.common.GameTypeCopyCount gameTypeBeingCopieds = 7;
   */
  gameTypeBeingCopieds: GameTypeCopyCount[];

  /**
   * 作品数量
   *
   * @generated from field: int64 work_cnt = 8;
   */
  workCnt: bigint;

  constructor(data?: PartialMessage<UserSocialStat>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.UserSocialStat";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserSocialStat;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserSocialStat;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserSocialStat;

  static equals(a: UserSocialStat | PlainMessage<UserSocialStat> | undefined, b: UserSocialStat | PlainMessage<UserSocialStat> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CardSocialStat
 */
export declare class CardSocialStat extends Message<CardSocialStat> {
  /**
   * 获赞数
   *
   * @generated from field: uint64 beingLikeds = 1;
   */
  beingLikeds: bigint;

  /**
   * 被拍同款数
   *
   * @generated from field: uint64 beingCopieds = 2;
   */
  beingCopieds: bigint;

  /**
   * 或不被赞数
   *
   * @generated from field: uint64 beingUnlikeds = 3;
   */
  beingUnlikeds: bigint;

  /**
   * 评论数
   *
   * @generated from field: uint64 comments = 4;
   */
  comments: bigint;

  /**
   * 是否点赞过
   *
   * @generated from field: bool liked = 5;
   */
  liked: boolean;

  /**
   * 是否点讨厌过
   *
   * @generated from field: bool unliked = 6;
   */
  unliked: boolean;

  constructor(data?: PartialMessage<CardSocialStat>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardSocialStat";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardSocialStat;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardSocialStat;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardSocialStat;

  static equals(a: CardSocialStat | PlainMessage<CardSocialStat> | undefined, b: CardSocialStat | PlainMessage<CardSocialStat> | undefined): boolean;
}

