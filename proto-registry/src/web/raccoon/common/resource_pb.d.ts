// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/resource.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

/**
 * @generated from enum step.raccoon.common.ResourceID
 */
export declare enum ResourceID {
  /**
   * 位置配置resource id
   *
   * @generated from enum value: Res_Unkown = 0;
   */
  Res_Unkown = 0,

  /**
   * 默认搜索词配置
   *
   * @generated from enum value: Res_SerchKeyword = 1001;
   */
  Res_SerchKeyword = 1001,

  /**
   * 搜索热榜配置
   *
   * @generated from enum value: Res_SearchHostlist = 1002;
   */
  Res_SearchHostlist = 1002,

  /**
   * @generated from enum value: Res_Features = 1003;
   */
  Res_Features = 1003,

  /**
   * 玩法入口配置
   *
   * @generated from enum value: Res_GameEntry = 1004;
   */
  Res_GameEntry = 1004,

  /**
   * 换肤配置
   *
   * @generated from enum value: Res_ChangeSkin = 1005;
   */
  Res_ChangeSkin = 1005,

  /**
   * 掉落动效配置
   *
   * @generated from enum value: Res_DropSPE = 1006;
   */
  Res_DropSPE = 1006,

  /**
   * 活动奖励配置
   *
   * @generated from enum value: ReourceID_ActivityAward = 1007;
   */
  ReourceID_ActivityAward = 1007,

  /**
   * 灵魂提取器话题配置
   *
   * @generated from enum value: Res_ReviveTopic = 1008;
   */
  Res_ReviveTopic = 1008,

  /**
   * 梗图模版配置
   *
   * @generated from enum value: Res_GentuTemplate = 1009;
   */
  Res_GentuTemplate = 1009,

  /**
   * 动态壁纸配置
   *
   * @generated from enum value: Res_LivePhoto = 1010;
   */
  Res_LivePhoto = 1010,

  /**
   * 活动配置
   *
   * @generated from enum value: Res_Activity = 1011;
   */
  Res_Activity = 1011,

  /**
   * 文案配置
   *
   * @generated from enum value: Res_Copywriter = 1012;
   */
  Res_Copywriter = 1012,

  /**
   * 话题配置
   *
   * @generated from enum value: Res_Topic = 1013;
   */
  Res_Topic = 1013,

  /**
   * 搜索干预
   *
   * @generated from enum value: Res_SearchIntervent = 1014;
   */
  Res_SearchIntervent = 1014,

  /**
   * 玩法功能开关
   *
   * @generated from enum value: Res_GameSwitch = 1015;
   */
  Res_GameSwitch = 1015,

  /**
   * 水印配置
   *
   * @generated from enum value: Res_Watermark = 1016;
   */
  Res_Watermark = 1016,

  /**
   * 小程序提审素材开关
   *
   * @generated from enum value: Res_LightAppInvoke = 1017;
   */
  Res_LightAppInvoke = 1017,

  /**
   * 微动视频素材配置
   *
   * @generated from enum value: Res_LivePhotoTemplate = 1018;
   */
  Res_LivePhotoTemplate = 1018,

  /**
   * 微动音乐素材配置
   *
   * @generated from enum value: Res_LivePhotoBGM = 1019;
   */
  Res_LivePhotoBGM = 1019,

  /**
   * 新手引导配置
   *
   * @generated from enum value: Res_BeginerGuied = 1020;
   */
  Res_BeginerGuied = 1020,

  /**
   * 宅舞视频素材配置
   *
   * @generated from enum value: Res_OtakuDanceTemplate = 1021;
   */
  Res_OtakuDanceTemplate = 1021,

  /**
   * 宅舞音乐配置
   *
   * @generated from enum value: Res_OtakuDanceBGM = 1022;
   */
  Res_OtakuDanceBGM = 1022,
}

