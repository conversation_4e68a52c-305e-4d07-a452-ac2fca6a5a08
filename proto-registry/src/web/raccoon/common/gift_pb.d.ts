// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/gift.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum step.raccoon.common.GiftBelongType
 */
export declare enum GiftBelongType {
  /**
   * 用户自定义礼物
   *
   * @generated from enum value: GiftBelongTypeUser = 0;
   */
  GiftBelongTypeUser = 0,

  /**
   * 官方定义礼物
   *
   * @generated from enum value: GiftBelongTypeOfficial = 1;
   */
  GiftBelongTypeOfficial = 1,
}

/**
 * @generated from message step.raccoon.common.Gift
 */
export declare class Gift extends Message<Gift> {
  /**
   * 礼物id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 名称
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * 描述
   *
   * @generated from field: string descript = 3;
   */
  descript: string;

  /**
   * 消耗的积分
   *
   * @generated from field: int32 consume_points = 4;
   */
  consumePoints: number;

  /**
   * 增加的热度
   *
   * @generated from field: int32 increase_heat = 5;
   */
  increaseHeat: number;

  /**
   * 礼物所属类型
   *
   * @generated from field: step.raccoon.common.GiftBelongType gift_belong_type = 6;
   */
  giftBelongType: GiftBelongType;

  /**
   * 礼物图片
   *
   * @generated from field: string image = 7;
   */
  image: string;

  constructor(data?: PartialMessage<Gift>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Gift";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Gift;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Gift;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Gift;

  static equals(a: Gift | PlainMessage<Gift> | undefined, b: Gift | PlainMessage<Gift> | undefined): boolean;
}

