// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/role.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { RoleType, UserProfile } from "./profile_pb.js";
import type { CensoredState, RoleState } from "./state_pb.js";

/**
 * @generated from enum step.raccoon.common.RoleSetType
 */
export declare enum RoleSetType {
  /**
   * 默认的 例如我的角色，热门角色
   *
   * @generated from enum value: ROLE_TYPE_DEFAULT = 0;
   */
  ROLE_TYPE_DEFAULT = 0,

  /**
   * 官方ip
   *
   * @generated from enum value: ROLE_SET_BRAND = 1;
   */
  ROLE_SET_BRAND = 1,

  /**
   * 用户自建set
   *
   * @generated from enum value: ROLE_SET_UGC = 2;
   */
  ROLE_SET_UGC = 2,
}

/**
 * @generated from enum step.raccoon.common.RoleGender
 */
export declare enum RoleGender {
  /**
   * 男
   *
   * @generated from enum value: ROLE_GENDER_MALE = 0;
   */
  MALE = 0,

  /**
   * 女
   *
   * @generated from enum value: ROLE_GENDER_FEMALE = 1;
   */
  FEMALE = 1,

  /**
   * 其他
   *
   * @generated from enum value: ROLE_GENDER_OTHER = 2;
   */
  OTHER = 2,
}

/**
 * @generated from enum step.raccoon.common.RoleImageType
 */
export declare enum RoleImageType {
  /**
   * 动漫角色类型
   *
   * @generated from enum value: ROLE_IMAGE_TYPE_CARTOON = 0;
   */
  CARTOON = 0,

  /**
   * 真人角色类型
   *
   * @generated from enum value: ROLE_IMAGE_TYPE_REAL = 1;
   */
  REAL = 1,
}

/**
 * @generated from message step.raccoon.common.RoleBID
 */
export declare class RoleBID extends Message<RoleBID> {
  /**
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * @generated from field: string id = 2;
   */
  id: string;

  constructor(data?: PartialMessage<RoleBID>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleBID";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleBID;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleBID;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleBID;

  static equals(a: RoleBID | PlainMessage<RoleBID> | undefined, b: RoleBID | PlainMessage<RoleBID> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.RoleInfo
 */
export declare class RoleInfo extends Message<RoleInfo> {
  /**
   * @generated from field: step.raccoon.common.RoleType role_type = 1;
   */
  roleType: RoleType;

  /**
   * @generated from field: string id = 2;
   */
  id: string;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * 头像
   *
   * @generated from field: string material = 4;
   */
  material: string;

  /**
   * 拍同款数量
   *
   * @generated from field: int64 copy_cnt = 5;
   */
  copyCnt: bigint;

  /**
   * 创建人
   *
   * @generated from field: step.raccoon.common.UserProfile creator = 6;
   */
  creator?: UserProfile;

  /**
   * 所属的角色集
   *
   * @generated from field: step.raccoon.common.RoleSet role_set = 7;
   */
  roleSet?: RoleSet;

  /**
   * 特征描述
   *
   * @generated from field: string prompt = 8;
   */
  prompt: string;

  /**
   * 角色简介
   *
   * @generated from field: string description = 9;
   */
  description: string;

  /**
   * 性别
   *
   * @generated from field: step.raccoon.common.RoleGender sexy = 10;
   */
  sexy: RoleGender;

  /**
   * 安全状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored = 11;
   */
  censored: CensoredState;

  /**
   * 角色形象类型
   *
   * @generated from field: step.raccoon.common.RoleImageType role_image_type = 12;
   */
  roleImageType: RoleImageType;

  /**
   * 角色状态
   *
   * @generated from field: step.raccoon.common.RoleState role_state = 13;
   */
  roleState: RoleState;

  /**
   * 是否已经保存
   *
   * @generated from field: bool is_save = 14;
   */
  isSave: boolean;

  /**
   * 是否上新
   *
   * @generated from field: bool is_new = 15;
   */
  isNew: boolean;

  /**
   * 图片的image_id
   *
   * @generated from field: string image_id = 16;
   */
  imageId: string;

  /**
   * 角色额外信息, json
   *
   * @generated from field: string extra = 17;
   */
  extra: string;

  /**
   * 亲密度
   *
   * @generated from field: int32 intimacy = 18;
   */
  intimacy: number;

  /**
   * 是否星标
   *
   * @generated from field: bool is_star = 19;
   */
  isStar: boolean;

  /**
   * 角色的sd prompt
   *
   * @generated from field: string prompt4model = 20;
   */
  prompt4model: string;

  /**
   * 外显的名称
   *
   * @generated from field: string display_name = 21;
   */
  displayName: string;

  /**
   * 角色生成的模型版本
   *
   * @generated from field: uint32 model_version = 22;
   */
  modelVersion: number;

  /**
   * 热度
   *
   * @generated from field: int32 heat = 23;
   */
  heat: number;

  /**
   * 相识的时间
   *
   * @generated from field: int32 acquaintance_time = 24;
   */
  acquaintanceTime: number;

  constructor(data?: PartialMessage<RoleInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleInfo;

  static equals(a: RoleInfo | PlainMessage<RoleInfo> | undefined, b: RoleInfo | PlainMessage<RoleInfo> | undefined): boolean;
}

/**
 * 角色描述信息
 *
 * @generated from message step.raccoon.common.RoleDetailInfo
 */
export declare class RoleDetailInfo extends Message<RoleDetailInfo> {
  /**
   * 年龄
   *
   * @generated from field: int32 age = 1;
   */
  age: number;

  /**
   * 身份
   *
   * @generated from field: string identity = 2;
   */
  identity: string;

  /**
   * 性格
   *
   * @generated from field: string character = 3;
   */
  character: string;

  /**
   * 兴趣
   *
   * @generated from field: string interest = 4;
   */
  interest: string;

  /**
   * 习惯
   *
   * @generated from field: string habit = 5;
   */
  habit: string;

  constructor(data?: PartialMessage<RoleDetailInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleDetailInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleDetailInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleDetailInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleDetailInfo;

  static equals(a: RoleDetailInfo | PlainMessage<RoleDetailInfo> | undefined, b: RoleDetailInfo | PlainMessage<RoleDetailInfo> | undefined): boolean;
}

/**
 * 角色集列表结构
 *
 * @generated from message step.raccoon.common.RoleSet
 */
export declare class RoleSet extends Message<RoleSet> {
  /**
   * @generated from field: step.raccoon.common.RoleSetType set_type = 1;
   */
  setType: RoleSetType;

  /**
   * 创建时所属的ip的name
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * 用户保存的ip id
   *
   * @generated from field: int32 id = 3;
   */
  id: number;

  /**
   * 是否上新
   *
   * @generated from field: bool is_new = 4;
   */
  isNew: boolean;

  /**
   * 创建时所属的ipid
   *
   * @generated from field: int32 origin_brand_id = 5;
   */
  originBrandId: number;

  /**
   * 顶部banner数据
   *
   * @generated from field: step.raccoon.common.RoleSetBanner banner = 6;
   */
  banner?: RoleSetBanner;

  /**
   * 外显的名称
   *
   * @generated from field: string display_name = 7;
   */
  displayName: string;

  /**
   * 创建的用户id
   *
   * @generated from field: string create_uid = 8;
   */
  createUid: string;

  /**
   * 是否所属总的角色集，关联到总的角色集
   *
   * @generated from field: bool belong_total = 9;
   */
  belongTotal: boolean;

  /**
   * 是否可以添加角色
   *
   * @generated from field: bool can_add_role = 10;
   */
  canAddRole: boolean;

  constructor(data?: PartialMessage<RoleSet>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleSet";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleSet;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleSet;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleSet;

  static equals(a: RoleSet | PlainMessage<RoleSet> | undefined, b: RoleSet | PlainMessage<RoleSet> | undefined): boolean;
}

/**
 * 角色集基础信息
 *
 * @generated from message step.raccoon.common.RoleSetBaseInfo
 */
export declare class RoleSetBaseInfo extends Message<RoleSetBaseInfo> {
  /**
   * @generated from field: step.raccoon.common.RoleSetType set_type = 1;
   */
  setType: RoleSetType;

  /**
   * 创建时所属的ip的name
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * ip id
   *
   * @generated from field: int32 id = 3;
   */
  id: number;

  /**
   * 外显的名称
   *
   * @generated from field: string display_name = 4;
   */
  displayName: string;

  /**
   * 保存人数
   *
   * @generated from field: int64 save_cnt = 5;
   */
  saveCnt: bigint;

  /**
   * 是否已经保存
   *
   * @generated from field: bool is_save = 6;
   */
  isSave: boolean;

  /**
   * 创建人
   *
   * @generated from field: int64 create_uid = 7;
   */
  createUid: bigint;

  constructor(data?: PartialMessage<RoleSetBaseInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleSetBaseInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleSetBaseInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleSetBaseInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleSetBaseInfo;

  static equals(a: RoleSetBaseInfo | PlainMessage<RoleSetBaseInfo> | undefined, b: RoleSetBaseInfo | PlainMessage<RoleSetBaseInfo> | undefined): boolean;
}

/**
 * 角色集搜索卡片样式
 *
 * @generated from message step.raccoon.common.RoleSetRichInfo
 */
export declare class RoleSetRichInfo extends Message<RoleSetRichInfo> {
  /**
   * 基础信息
   *
   * @generated from field: step.raccoon.common.RoleSetBaseInfo base_info = 1;
   */
  baseInfo?: RoleSetBaseInfo;

  /**
   * 创建人
   *
   * @generated from field: step.raccoon.common.UserProfile creator = 2;
   */
  creator?: UserProfile;

  constructor(data?: PartialMessage<RoleSetRichInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleSetRichInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleSetRichInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleSetRichInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleSetRichInfo;

  static equals(a: RoleSetRichInfo | PlainMessage<RoleSetRichInfo> | undefined, b: RoleSetRichInfo | PlainMessage<RoleSetRichInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.RoleSetBanner
 */
export declare class RoleSetBanner extends Message<RoleSetBanner> {
  /**
   * 用户名称
   *
   * @generated from field: string user_name = 1;
   */
  userName: string;

  /**
   * 用户头像
   *
   * @generated from field: string user_avater = 2;
   */
  userAvater: string;

  /**
   * 创建时间
   *
   * @generated from field: int64 create_time = 3;
   */
  createTime: bigint;

  /**
   * 保存人数
   *
   * @generated from field: int64 save_cnt = 4;
   */
  saveCnt: bigint;

  /**
   * 创建的用户id
   *
   * @generated from field: string user_id = 5;
   */
  userId: string;

  /**
   * 查看者是否关注此用户
   *
   * @generated from field: bool followed = 6;
   */
  followed: boolean;

  constructor(data?: PartialMessage<RoleSetBanner>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RoleSetBanner";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoleSetBanner;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoleSetBanner;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoleSetBanner;

  static equals(a: RoleSetBanner | PlainMessage<RoleSetBanner> | undefined, b: RoleSetBanner | PlainMessage<RoleSetBanner> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.SearchRoleSetCard
 */
export declare class SearchRoleSetCard extends Message<SearchRoleSetCard> {
  /**
   * 角色集信息
   *
   * @generated from field: step.raccoon.common.RoleSetRichInfo role_set = 1;
   */
  roleSet?: RoleSetRichInfo;

  /**
   * 角色信息
   *
   * @generated from field: repeated step.raccoon.common.RoleInfo role_infos = 2;
   */
  roleInfos: RoleInfo[];

  constructor(data?: PartialMessage<SearchRoleSetCard>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.SearchRoleSetCard";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SearchRoleSetCard;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SearchRoleSetCard;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SearchRoleSetCard;

  static equals(a: SearchRoleSetCard | PlainMessage<SearchRoleSetCard> | undefined, b: SearchRoleSetCard | PlainMessage<SearchRoleSetCard> | undefined): boolean;
}

