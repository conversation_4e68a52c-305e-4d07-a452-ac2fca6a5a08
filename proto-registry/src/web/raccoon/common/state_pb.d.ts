// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/state.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * 安全审核状态，请严格保证级别从低到高，方便业务层面数据可见规则调整
 *
 * @generated from enum step.raccoon.common.CensoredState
 */
export declare enum CensoredState {
  /**
   * 安全审核服务异常，任何人不可见
   *
   * @generated from enum value: CENSORED_EXCEPTIONED = 0;
   */
  CENSORED_EXCEPTIONED = 0,

  /**
   * 安全审核不通过，任何人不可见, 对应安全的BLOCK
   *
   * @generated from enum value: CENSORED_BLOCKED = 1;
   */
  CENSORED_BLOCKED = 1,

  /**
   * 安全审核中，暂时任何人不可见
   *
   * @generated from enum value: CENSORED_PROCESSING = 2;
   */
  CENSORED_PROCESSING = 2,

  /**
   * 审核判定为：仅自己可见，不可分享, 对应安全的SELF_VIEW
   *
   * @generated from enum value: CENSORED_REVIEWING = 3;
   */
  CENSORED_REVIEWING = 3,

  /**
   * 安全判定为：仅自己可见，可分享, 对应安全的SELF_VIEW_WITH_SHARE
   *
   * @generated from enum value: CENSORED_SELF_VIEW_WITH_SHARE = 5;
   */
  CENSORED_SELF_VIEW_WITH_SHARE = 5,

  /**
   * 安全判定为仅对粉丝客态主页可见，可分享，对应安全的ONLY_FANS
   *
   * @generated from enum value: CENSORED_ONLY_FANS = 6;
   */
  CENSORED_ONLY_FANS = 6,

  /**
   * 安全判定为客态主页可见，不可分享, 对应安全的HOMEPAGE_VIEW
   *
   * @generated from enum value: CENSORED_HOMEPAGE_VIEW = 7;
   */
  CENSORED_HOMEPAGE_VIEW = 7,

  /**
   * 安全判定为客态主页可见，可分享, 对应安全的HOMEPAGE_VIEW_WITH_SHARE
   *
   * @generated from enum value: CENSORED_HOMEPAGE_VIEW_WITH_SHARE = 8;
   */
  CENSORED_HOMEPAGE_VIEW_WITH_SHARE = 8,

  /**
   * 审核通过，对外完全可见 -> 对应安全的PASS
   *
   * @generated from enum value: CENSORED_PASSED = 12;
   */
  CENSORED_PASSED = 12,
}

/**
 * @generated from enum step.raccoon.common.GeneState
 */
export declare enum GeneState {
  /**
   * 生成中
   *
   * @generated from enum value: GENE_PROCESSING = 0;
   */
  GENE_PROCESSING = 0,

  /**
   * 生成完成
   *
   * @generated from enum value: GENE_DONE = 1;
   */
  GENE_DONE = 1,

  /**
   * 生成失败/异常
   *
   * @generated from enum value: GENE_EXCEPTIONED = 2;
   */
  GENE_EXCEPTIONED = 2,
}

/**
 * @generated from enum step.raccoon.common.AccessibleState
 */
export declare enum AccessibleState {
  /**
   * 完全不可见
   *
   * @generated from enum value: ACCESSIBLE_FORIDDEN = 0;
   */
  ACCESSIBLE_FORIDDEN = 0,

  /**
   * 只对作者可见
   *
   * @generated from enum value: ACCESSIBLE_OWNER = 1;
   */
  ACCESSIBLE_OWNER = 1,

  /**
   * 对所有人可见
   *
   * @generated from enum value: ACCESSIBLE_ALL = 2;
   */
  ACCESSIBLE_ALL = 2,
}

/**
 * ip状态
 *
 * @generated from enum step.raccoon.common.BrandState
 */
export declare enum BrandState {
  /**
   * 下线
   *
   * @generated from enum value: BRAND_STATE_OFFLINE = 0;
   */
  OFFLINE = 0,

  /**
   * 上线
   *
   * @generated from enum value: BRAND_STATE_ONLINE = 1;
   */
  ONLINE = 1,

  /**
   * 待上架
   *
   * @generated from enum value: BRAND_STATE_PENDING = 2;
   */
  PENDING = 2,
}

/**
 * 角色状态
 *
 * @generated from enum step.raccoon.common.RoleState
 */
export declare enum RoleState {
  /**
   * 下线
   *
   * @generated from enum value: ROLE_STATE_OFFLINE = 0;
   */
  OFFLINE = 0,

  /**
   * 上线
   *
   * @generated from enum value: ROLE_STATE_ONLINE = 1;
   */
  ONLINE = 1,

  /**
   * 官方下线
   *
   * @generated from enum value: ROLE_STATE_ADMIN_OFFLINE = 2;
   */
  ADMIN_OFFLINE = 2,
}

/**
 * @generated from message step.raccoon.common.ViewerCtrl
 */
export declare class ViewerCtrl extends Message<ViewerCtrl> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * @generated from field: int64 viewer_uid = 2;
   */
  viewerUid: bigint;

  /**
   * 是否可见
   *
   * @generated from field: bool accessible = 3;
   */
  accessible: boolean;

  /**
   * 是否可分享
   *
   * @generated from field: bool sharable = 4;
   */
  sharable: boolean;

  /**
   * 是否需要进一步审核
   *
   * @generated from field: bool censoring = 5;
   */
  censoring: boolean;

  constructor(data?: PartialMessage<ViewerCtrl>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ViewerCtrl";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ViewerCtrl;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ViewerCtrl;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ViewerCtrl;

  static equals(a: ViewerCtrl | PlainMessage<ViewerCtrl> | undefined, b: ViewerCtrl | PlainMessage<ViewerCtrl> | undefined): boolean;
}

