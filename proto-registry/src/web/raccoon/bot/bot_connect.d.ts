// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/bot/bot.proto (package step.raccoon.bot, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { Empty, MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.bot.Bot
 */
export declare const Bot: {
  readonly typeName: "step.raccoon.bot.Bot",
  readonly methods: {
    /**
     * echo
     *
     * @generated from rpc step.raccoon.bot.Bot.Echo
     */
    readonly echo: {
      readonly name: "<PERSON>",
      readonly I: typeof Empty,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
  }
};

