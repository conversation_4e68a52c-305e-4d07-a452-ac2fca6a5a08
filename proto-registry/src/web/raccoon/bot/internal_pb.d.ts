// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bot/internal.proto (package step.raccoon.bot, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { TopicInfo } from "../common/showcase_pb.js";
import type { AtUser } from "../comment/comment_pb.js";

/**
 * @generated from message step.raccoon.bot.Action
 */
export declare class Action extends Message<Action> {
  /**
   * 对应上个任务延迟执行的时间，毫秒
   *
   * @generated from field: int64 delay = 1;
   */
  delay: bigint;

  /**
   * @generated from oneof step.raccoon.bot.Action.action
   */
  action: {
    /**
     * @generated from field: step.raccoon.bot.CommentAction comment = 2;
     */
    value: CommentAction;
    case: "comment";
  } | {
    /**
     * @generated from field: step.raccoon.bot.LikeWorkAction like_work = 3;
     */
    value: LikeWorkAction;
    case: "likeWork";
  } | {
    /**
     * @generated from field: step.raccoon.bot.FollowAction follow = 4;
     */
    value: FollowAction;
    case: "follow";
  } | {
    /**
     * @generated from field: step.raccoon.bot.ProfileAction profile = 5;
     */
    value: ProfileAction;
    case: "profile";
  } | {
    /**
     * @generated from field: step.raccoon.bot.MakephotoAction makephoto = 6;
     */
    value: MakephotoAction;
    case: "makephoto";
  } | {
    /**
     * @generated from field: step.raccoon.bot.LikeCommentAction like_comment = 7;
     */
    value: LikeCommentAction;
    case: "likeComment";
  } | {
    /**
     * @generated from field: step.raccoon.bot.PublishPhotoAction publish_photo_action = 8;
     */
    value: PublishPhotoAction;
    case: "publishPhotoAction";
  } | {
    /**
     * @generated from field: step.raccoon.bot.PublishLivePhotoAction publish_live_photo_action = 9;
     */
    value: PublishLivePhotoAction;
    case: "publishLivePhotoAction";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<Action>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.Action";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Action;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Action;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Action;

  static equals(a: Action | PlainMessage<Action> | undefined, b: Action | PlainMessage<Action> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.PublishLivePhotoAction
 */
export declare class PublishLivePhotoAction extends Message<PublishLivePhotoAction> {
  /**
   * @generated from field: string live_photo_id = 1;
   */
  livePhotoId: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string content = 3;
   */
  content: string;

  /**
   * @generated from field: string bgm_id = 4;
   */
  bgmId: string;

  /**
   * 话题
   *
   * @generated from field: repeated step.raccoon.common.TopicInfo topics = 5;
   */
  topics: TopicInfo[];

  /**
   * @generated from field: string uuid = 6;
   */
  uuid: string;

  constructor(data?: PartialMessage<PublishLivePhotoAction>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.PublishLivePhotoAction";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishLivePhotoAction;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishLivePhotoAction;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishLivePhotoAction;

  static equals(a: PublishLivePhotoAction | PlainMessage<PublishLivePhotoAction> | undefined, b: PublishLivePhotoAction | PlainMessage<PublishLivePhotoAction> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.PublishPhotoAction
 */
export declare class PublishPhotoAction extends Message<PublishPhotoAction> {
  /**
   * @generated from field: repeated string photo_ids = 1;
   */
  photoIds: string[];

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string story = 3;
   */
  story: string;

  /**
   * @generated from field: string uuid = 4;
   */
  uuid: string;

  constructor(data?: PartialMessage<PublishPhotoAction>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.PublishPhotoAction";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishPhotoAction;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishPhotoAction;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishPhotoAction;

  static equals(a: PublishPhotoAction | PlainMessage<PublishPhotoAction> | undefined, b: PublishPhotoAction | PlainMessage<PublishPhotoAction> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.CommentAction
 */
export declare class CommentAction extends Message<CommentAction> {
  /**
   * 作品id
   *
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * 评论内容
   *
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * 表情包id
   *
   * @generated from field: int64 emoji_id = 3;
   */
  emojiId: bigint;

  /**
   * 评论分类，仅记录
   *
   * @generated from field: string category = 4;
   */
  category: string;

  /**
   * 被@的用户列表
   *
   * @generated from field: repeated step.raccoon.comment.AtUser at_users = 5;
   */
  atUsers: AtUser[];

  /**
   * 评论来源追踪
   *
   * @generated from field: string trace = 10;
   */
  trace: string;

  constructor(data?: PartialMessage<CommentAction>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.CommentAction";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CommentAction;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CommentAction;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CommentAction;

  static equals(a: CommentAction | PlainMessage<CommentAction> | undefined, b: CommentAction | PlainMessage<CommentAction> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.LikeWorkAction
 */
export declare class LikeWorkAction extends Message<LikeWorkAction> {
  /**
   * 作品id
   *
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  constructor(data?: PartialMessage<LikeWorkAction>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.LikeWorkAction";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeWorkAction;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeWorkAction;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeWorkAction;

  static equals(a: LikeWorkAction | PlainMessage<LikeWorkAction> | undefined, b: LikeWorkAction | PlainMessage<LikeWorkAction> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.FollowAction
 */
export declare class FollowAction extends Message<FollowAction> {
  /**
   * 关注的目标用户uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  constructor(data?: PartialMessage<FollowAction>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.FollowAction";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FollowAction;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FollowAction;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FollowAction;

  static equals(a: FollowAction | PlainMessage<FollowAction> | undefined, b: FollowAction | PlainMessage<FollowAction> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.ProfileAction
 */
export declare class ProfileAction extends Message<ProfileAction> {
  /**
   * @generated from field: string nick_name = 1;
   */
  nickName: string;

  /**
   * @generated from field: string avatar_image_id = 2;
   */
  avatarImageId: string;

  constructor(data?: PartialMessage<ProfileAction>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.ProfileAction";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProfileAction;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProfileAction;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProfileAction;

  static equals(a: ProfileAction | PlainMessage<ProfileAction> | undefined, b: ProfileAction | PlainMessage<ProfileAction> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.MakephotoAction
 */
export declare class MakephotoAction extends Message<MakephotoAction> {
  /**
   * @generated from field: uint32 brand_id = 1;
   */
  brandId: number;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: string style = 3;
   */
  style: string;

  /**
   * @generated from field: string prompt = 4;
   */
  prompt: string;

  constructor(data?: PartialMessage<MakephotoAction>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.MakephotoAction";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MakephotoAction;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MakephotoAction;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MakephotoAction;

  static equals(a: MakephotoAction | PlainMessage<MakephotoAction> | undefined, b: MakephotoAction | PlainMessage<MakephotoAction> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.LikeCommentAction
 */
export declare class LikeCommentAction extends Message<LikeCommentAction> {
  /**
   * 评论id
   *
   * @generated from field: int64 comment_id = 1;
   */
  commentId: bigint;

  constructor(data?: PartialMessage<LikeCommentAction>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.LikeCommentAction";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeCommentAction;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeCommentAction;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeCommentAction;

  static equals(a: LikeCommentAction | PlainMessage<LikeCommentAction> | undefined, b: LikeCommentAction | PlainMessage<LikeCommentAction> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.BotAccount
 */
export declare class BotAccount extends Message<BotAccount> {
  /**
   * 虚拟手机号
   *
   * @generated from field: string phone = 1;
   */
  phone: string;

  /**
   * 虚拟验证码
   *
   * @generated from field: string code = 2;
   */
  code: string;

  constructor(data?: PartialMessage<BotAccount>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.BotAccount";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BotAccount;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BotAccount;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BotAccount;

  static equals(a: BotAccount | PlainMessage<BotAccount> | undefined, b: BotAccount | PlainMessage<BotAccount> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.BotId
 */
export declare class BotId extends Message<BotId> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  constructor(data?: PartialMessage<BotId>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.BotId";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BotId;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BotId;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BotId;

  static equals(a: BotId | PlainMessage<BotId> | undefined, b: BotId | PlainMessage<BotId> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.StartBotTaskReq
 */
export declare class StartBotTaskReq extends Message<StartBotTaskReq> {
  /**
   * bot的身份信息
   *
   * @generated from oneof step.raccoon.bot.StartBotTaskReq.bot
   */
  bot: {
    /**
     * @generated from field: step.raccoon.bot.BotAccount bot_account = 1;
     */
    value: BotAccount;
    case: "botAccount";
  } | {
    /**
     * @generated from field: step.raccoon.bot.BotId bot_id = 2;
     */
    value: BotId;
    case: "botId";
  } | { case: undefined; value?: undefined };

  /**
   * bot的行为序列
   *
   * @generated from field: repeated step.raccoon.bot.Action actions = 3;
   */
  actions: Action[];

  constructor(data?: PartialMessage<StartBotTaskReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.StartBotTaskReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StartBotTaskReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StartBotTaskReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StartBotTaskReq;

  static equals(a: StartBotTaskReq | PlainMessage<StartBotTaskReq> | undefined, b: StartBotTaskReq | PlainMessage<StartBotTaskReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.StartBotTaskRsp
 */
export declare class StartBotTaskRsp extends Message<StartBotTaskRsp> {
  /**
   * task的uuid
   *
   * @generated from field: string task_id = 1;
   */
  taskId: string;

  constructor(data?: PartialMessage<StartBotTaskRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.StartBotTaskRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StartBotTaskRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StartBotTaskRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StartBotTaskRsp;

  static equals(a: StartBotTaskRsp | PlainMessage<StartBotTaskRsp> | undefined, b: StartBotTaskRsp | PlainMessage<StartBotTaskRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.QueryBotCardStatReq
 */
export declare class QueryBotCardStatReq extends Message<QueryBotCardStatReq> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  constructor(data?: PartialMessage<QueryBotCardStatReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.QueryBotCardStatReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryBotCardStatReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryBotCardStatReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryBotCardStatReq;

  static equals(a: QueryBotCardStatReq | PlainMessage<QueryBotCardStatReq> | undefined, b: QueryBotCardStatReq | PlainMessage<QueryBotCardStatReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.QueryBotCardStatRsp
 */
export declare class QueryBotCardStatRsp extends Message<QueryBotCardStatRsp> {
  /**
   * @generated from field: int64 total_comments = 1;
   */
  totalComments: bigint;

  /**
   * @generated from field: int64 total_likes = 2;
   */
  totalLikes: bigint;

  /**
   * @generated from field: map<string, int64> comment_type_count = 10;
   */
  commentTypeCount: { [key: string]: bigint };

  constructor(data?: PartialMessage<QueryBotCardStatRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.QueryBotCardStatRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryBotCardStatRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryBotCardStatRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryBotCardStatRsp;

  static equals(a: QueryBotCardStatRsp | PlainMessage<QueryBotCardStatRsp> | undefined, b: QueryBotCardStatRsp | PlainMessage<QueryBotCardStatRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.BatchQueryBotCardStatReq
 */
export declare class BatchQueryBotCardStatReq extends Message<BatchQueryBotCardStatReq> {
  /**
   * @generated from field: repeated int64 card_ids = 1;
   */
  cardIds: bigint[];

  constructor(data?: PartialMessage<BatchQueryBotCardStatReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.BatchQueryBotCardStatReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchQueryBotCardStatReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchQueryBotCardStatReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchQueryBotCardStatReq;

  static equals(a: BatchQueryBotCardStatReq | PlainMessage<BatchQueryBotCardStatReq> | undefined, b: BatchQueryBotCardStatReq | PlainMessage<BatchQueryBotCardStatReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.BatchQueryBotCardStatRsp
 */
export declare class BatchQueryBotCardStatRsp extends Message<BatchQueryBotCardStatRsp> {
  /**
   * @generated from field: map<int64, step.raccoon.bot.BotCardStat> bot_card_stat_map = 1;
   */
  botCardStatMap: { [key: string]: BotCardStat };

  constructor(data?: PartialMessage<BatchQueryBotCardStatRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.BatchQueryBotCardStatRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchQueryBotCardStatRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchQueryBotCardStatRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchQueryBotCardStatRsp;

  static equals(a: BatchQueryBotCardStatRsp | PlainMessage<BatchQueryBotCardStatRsp> | undefined, b: BatchQueryBotCardStatRsp | PlainMessage<BatchQueryBotCardStatRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.BotCardStat
 */
export declare class BotCardStat extends Message<BotCardStat> {
  /**
   * @generated from field: int64 total_comments = 1;
   */
  totalComments: bigint;

  /**
   * @generated from field: int64 total_likes = 2;
   */
  totalLikes: bigint;

  /**
   * @generated from field: map<string, int64> comment_type_count = 10;
   */
  commentTypeCount: { [key: string]: bigint };

  constructor(data?: PartialMessage<BotCardStat>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.BotCardStat";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BotCardStat;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BotCardStat;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BotCardStat;

  static equals(a: BotCardStat | PlainMessage<BotCardStat> | undefined, b: BotCardStat | PlainMessage<BotCardStat> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.QueryBotFollowStatReq
 */
export declare class QueryBotFollowStatReq extends Message<QueryBotFollowStatReq> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  constructor(data?: PartialMessage<QueryBotFollowStatReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.QueryBotFollowStatReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryBotFollowStatReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryBotFollowStatReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryBotFollowStatReq;

  static equals(a: QueryBotFollowStatReq | PlainMessage<QueryBotFollowStatReq> | undefined, b: QueryBotFollowStatReq | PlainMessage<QueryBotFollowStatReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bot.QueryBotFollowStatRsp
 */
export declare class QueryBotFollowStatRsp extends Message<QueryBotFollowStatRsp> {
  /**
   * @generated from field: int64 total_follows = 1;
   */
  totalFollows: bigint;

  constructor(data?: PartialMessage<QueryBotFollowStatRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bot.QueryBotFollowStatRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryBotFollowStatRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryBotFollowStatRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryBotFollowStatRsp;

  static equals(a: QueryBotFollowStatRsp | PlainMessage<QueryBotFollowStatRsp> | undefined, b: QueryBotFollowStatRsp | PlainMessage<QueryBotFollowStatRsp> | undefined): boolean;
}

