// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/bot/internal.proto (package step.raccoon.bot, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { BatchQueryBotCardStatReq, BatchQueryBotCardStatRsp, QueryBotCardStatReq, QueryBotCardStatRsp, QueryBotFollowStatReq, QueryBotFollowStatRsp, StartBotTaskReq, StartBotTaskRsp } from "./internal_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.bot.Internal
 */
export declare const Internal: {
  readonly typeName: "step.raccoon.bot.Internal",
  readonly methods: {
    /**
     * 启动机器人任务
     *
     * @generated from rpc step.raccoon.bot.Internal.StartBotTask
     */
    readonly startBotTask: {
      readonly name: "StartBotTask",
      readonly I: typeof StartBotTaskReq,
      readonly O: typeof StartBotTaskRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询卡片的机器人数据
     *
     * @generated from rpc step.raccoon.bot.Internal.QueryBotCardStat
     */
    readonly queryBotCardStat: {
      readonly name: "QueryBotCardStat",
      readonly I: typeof QueryBotCardStatReq,
      readonly O: typeof QueryBotCardStatRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批量查询卡片的机器人数据
     *
     * @generated from rpc step.raccoon.bot.Internal.BatchQueryBotCardStat
     */
    readonly batchQueryBotCardStat: {
      readonly name: "BatchQueryBotCardStat",
      readonly I: typeof BatchQueryBotCardStatReq,
      readonly O: typeof BatchQueryBotCardStatRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询用户的机器人关注数据
     *
     * @generated from rpc step.raccoon.bot.Internal.QueryBotFollowStat
     */
    readonly queryBotFollowStat: {
      readonly name: "QueryBotFollowStat",
      readonly I: typeof QueryBotFollowStatReq,
      readonly O: typeof QueryBotFollowStatRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

