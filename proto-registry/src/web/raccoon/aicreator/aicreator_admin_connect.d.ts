// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/aicreator/aicreator_admin.proto (package step.raccoon.aicreator, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CreatePlanRequest, LabelResultRequest, ListPlanRequest, ListPlanResponse, ListTaskRequest, ListTaskResponse, PromptListRequest, PromptListResponse, PublishPlanRequest, PublishPlanResponse, UpdatePromptRequest, UploadCsvRequest, UploadCsvResponse } from "./aicreator_admin_pb.js";
import { Empty, MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.aicreator.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.aicreator.Admin",
  readonly methods: {
    /**
     * 创建批跑计划
     *
     * @generated from rpc step.raccoon.aicreator.Admin.CreatePlan
     */
    readonly createPlan: {
      readonly name: "<PERSON><PERSON><PERSON><PERSON>",
      readonly I: typeof CreatePlanRequest,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批跑列表
     *
     * @generated from rpc step.raccoon.aicreator.Admin.ListPlan
     */
    readonly listPlan: {
      readonly name: "ListPlan",
      readonly I: typeof ListPlanRequest,
      readonly O: typeof ListPlanResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 将计划修改为待发布状态，不可撤销
     *
     * @generated from rpc step.raccoon.aicreator.Admin.PublishPlan
     */
    readonly publishPlan: {
      readonly name: "PublishPlan",
      readonly I: typeof PublishPlanRequest,
      readonly O: typeof PublishPlanResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批跑结果详情
     *
     * @generated from rpc step.raccoon.aicreator.Admin.ListTask
     */
    readonly listTask: {
      readonly name: "ListTask",
      readonly I: typeof ListTaskRequest,
      readonly O: typeof ListTaskResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 打标
     *
     * @generated from rpc step.raccoon.aicreator.Admin.LabelResult
     */
    readonly labelResult: {
      readonly name: "LabelResult",
      readonly I: typeof LabelResultRequest,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取prompt
     *
     * @generated from rpc step.raccoon.aicreator.Admin.PromptList
     */
    readonly promptList: {
      readonly name: "PromptList",
      readonly I: typeof PromptListRequest,
      readonly O: typeof PromptListResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 新增or修改prompt 
     *
     * @generated from rpc step.raccoon.aicreator.Admin.UpdatePrompt
     */
    readonly updatePrompt: {
      readonly name: "UpdatePrompt",
      readonly I: typeof UpdatePromptRequest,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 上传Csv
     *
     * @generated from rpc step.raccoon.aicreator.Admin.UploadCsv
     */
    readonly uploadCsv: {
      readonly name: "UploadCsv",
      readonly I: typeof UploadCsvRequest,
      readonly O: typeof UploadCsvResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

