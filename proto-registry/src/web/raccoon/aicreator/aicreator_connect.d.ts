// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/aicreator/aicreator.proto (package step.raccoon.aicreator, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { HelloRequest, HelloResponse } from "./aicreator_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * MyService handles
 *
 * @generated from service step.raccoon.aicreator.MyService
 */
export declare const MyService: {
  readonly typeName: "step.raccoon.aicreator.MyService",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.aicreator.MyService.SayHello
     */
    readonly sayHello: {
      readonly name: "<PERSON><PERSON><PERSON>",
      readonly I: typeof HelloRequest,
      readonly O: typeof HelloResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

