// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/aicreator/aicreator_admin.proto (package step.raccoon.aicreator, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Role, Style } from "../common/imagen_pb.js";
import type { PagePagination } from "../common/utils_pb.js";

/**
 * @generated from enum step.raccoon.aicreator.PplType
 */
export declare enum PplType {
  /**
   * @generated from enum value: Unknown_ppl_type = 0;
   */
  Unknown_ppl_type = 0,

  /**
   * 风格
   *
   * @generated from enum value: Style = 1;
   */
  Style = 1,

  /**
   * sref
   *
   * @generated from enum value: Sref = 2;
   */
  Sref = 2,
}

/**
 * @generated from enum step.raccoon.aicreator.PlanStatus
 */
export declare enum PlanStatus {
  /**
   * 草稿
   *
   * @generated from enum value: Draft = 0;
   */
  Draft = 0,

  /**
   * 执行中
   *
   * @generated from enum value: Executing = 1;
   */
  Executing = 1,

  /**
   * 执行完成
   *
   * @generated from enum value: Finish = 2;
   */
  Finish = 2,

  /**
   * 发布中
   *
   * @generated from enum value: Publishing = 3;
   */
  Publishing = 3,

  /**
   * 已发布
   *
   * @generated from enum value: Publish = 4;
   */
  Publish = 4,

  /**
   * 执行失败
   *
   * @generated from enum value: Failed = -2;
   */
  Failed = -2,
}

/**
 * @generated from enum step.raccoon.aicreator.LabelResult
 */
export declare enum LabelResult {
  /**
   * @generated from enum value: Unknown_label_result = 0;
   */
  Unknown_label_result = 0,

  /**
   * @generated from enum value: Pass = 1;
   */
  Pass = 1,

  /**
   * @generated from enum value: Reject = 2;
   */
  Reject = 2,

  /**
   * @generated from enum value: Published = 3;
   */
  Published = 3,
}

/**
 * @generated from message step.raccoon.aicreator.PhotoPlan
 */
export declare class PhotoPlan extends Message<PhotoPlan> {
  /**
   * 灵感词
   *
   * @generated from field: string inspiration = 1;
   */
  inspiration: string;

  /**
   * 灵感词url
   *
   * @generated from field: string inspiration_url = 2;
   */
  inspirationUrl: string;

  /**
   * 质量词
   *
   * @generated from field: string quantity = 3;
   */
  quantity: string;

  /**
   * 角色
   *
   * @generated from field: repeated step.raccoon.common.Role roles = 4;
   */
  roles: Role[];

  /**
   * @generated from field: repeated string role_ids = 5;
   */
  roleIds: string[];

  /**
   * todo sref
   *
   * 风格
   *
   * @generated from field: step.raccoon.common.Style style = 6;
   */
  style: Style;

  /**
   * @generated from field: repeated string sref_photo_ids = 7;
   */
  srefPhotoIds: string[];

  /**
   * cref角色id
   *
   * @generated from field: repeated string cref_role_ids = 8;
   */
  crefRoleIds: string[];

  constructor(data?: PartialMessage<PhotoPlan>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.PhotoPlan";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PhotoPlan;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PhotoPlan;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PhotoPlan;

  static equals(a: PhotoPlan | PlainMessage<PhotoPlan> | undefined, b: PhotoPlan | PlainMessage<PhotoPlan> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.Prompt
 */
export declare class Prompt extends Message<Prompt> {
  /**
   * prompt_id
   *
   * @generated from field: string prompt_id = 1;
   */
  promptId: string;

  /**
   * prompt_name
   *
   * @generated from field: string prompt_name = 2;
   */
  promptName: string;

  /**
   * sp
   *
   * @generated from field: string system_prompt = 3;
   */
  systemPrompt: string;

  /**
   * user prompt
   *
   * @generated from field: string user_prompt = 4;
   */
  userPrompt: string;

  /**
   * @generated from field: int32 version = 5;
   */
  version: number;

  /**
   * status
   *
   * @generated from field: int32 status = 6;
   */
  status: number;

  /**
   * 更新人
   *
   * @generated from field: string updator = 7;
   */
  updator: string;

  /**
   * 更新时间
   *
   * @generated from field: string updated_at = 8;
   */
  updatedAt: string;

  constructor(data?: PartialMessage<Prompt>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.Prompt";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Prompt;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Prompt;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Prompt;

  static equals(a: Prompt | PlainMessage<Prompt> | undefined, b: Prompt | PlainMessage<Prompt> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.CreatePlanRequest
 */
export declare class CreatePlanRequest extends Message<CreatePlanRequest> {
  /**
   * ppl类型
   *
   * @generated from field: step.raccoon.aicreator.PplType ppl_type = 1;
   */
  pplType: PplType;

  /**
   * 次数
   *
   * @generated from field: int32 count = 2;
   */
  count: number;

  /**
   * 模型名称
   *
   * @generated from field: string model_name = 3;
   */
  modelName: string;

  /**
   * temparature
   *
   * @generated from field: float temparature = 4;
   */
  temparature: number;

  /**
   * prompt
   *
   * @generated from field: string prompt_id = 5;
   */
  promptId: string;

  /**
   * photo计划
   *
   * @generated from field: step.raccoon.aicreator.PhotoPlan plan = 100;
   */
  plan?: PhotoPlan;

  constructor(data?: PartialMessage<CreatePlanRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.CreatePlanRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreatePlanRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreatePlanRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreatePlanRequest;

  static equals(a: CreatePlanRequest | PlainMessage<CreatePlanRequest> | undefined, b: CreatePlanRequest | PlainMessage<CreatePlanRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.PlanListParam
 */
export declare class PlanListParam extends Message<PlanListParam> {
  constructor(data?: PartialMessage<PlanListParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.PlanListParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PlanListParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PlanListParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PlanListParam;

  static equals(a: PlanListParam | PlainMessage<PlanListParam> | undefined, b: PlanListParam | PlainMessage<PlanListParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.ListPlanRequest
 */
export declare class ListPlanRequest extends Message<ListPlanRequest> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.PagePagination pagination = 1;
   */
  pagination?: PagePagination;

  /**
   * @generated from field: step.raccoon.aicreator.PlanListParam search = 2;
   */
  search?: PlanListParam;

  constructor(data?: PartialMessage<ListPlanRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.ListPlanRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListPlanRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListPlanRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListPlanRequest;

  static equals(a: ListPlanRequest | PlainMessage<ListPlanRequest> | undefined, b: ListPlanRequest | PlainMessage<ListPlanRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.PlanItem
 */
export declare class PlanItem extends Message<PlanItem> {
  /**
   * id
   *
   * @generated from field: string plan_id = 1;
   */
  planId: string;

  /**
   * ppl类型
   *
   * @generated from field: step.raccoon.aicreator.PplType ppl_type = 2;
   */
  pplType: PplType;

  /**
   * 总数量
   *
   * @generated from field: int32 total_count = 3;
   */
  totalCount: number;

  /**
   * 打标过的数量
   *
   * @generated from field: int32 tag_count = 4;
   */
  tagCount: number;

  /**
   * 高质量数量
   *
   * @generated from field: int32 quality_count = 5;
   */
  qualityCount: number;

  /**
   * 创建时间
   *
   * @generated from field: string create_time = 6;
   */
  createTime: string;

  /**
   * 创建人 
   *
   * @generated from field: string creator = 7;
   */
  creator: string;

  /**
   * 状态
   *
   * @generated from field: step.raccoon.aicreator.PlanStatus status = 8;
   */
  status: PlanStatus;

  /**
   * @generated from field: step.raccoon.aicreator.PhotoPlan plan = 100;
   */
  plan?: PhotoPlan;

  constructor(data?: PartialMessage<PlanItem>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.PlanItem";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PlanItem;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PlanItem;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PlanItem;

  static equals(a: PlanItem | PlainMessage<PlanItem> | undefined, b: PlanItem | PlainMessage<PlanItem> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.ListPlanResponse
 */
export declare class ListPlanResponse extends Message<ListPlanResponse> {
  /**
   * @generated from field: int64 total = 1;
   */
  total: bigint;

  /**
   * @generated from field: repeated step.raccoon.aicreator.PlanItem planItems = 2;
   */
  planItems: PlanItem[];

  constructor(data?: PartialMessage<ListPlanResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.ListPlanResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListPlanResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListPlanResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListPlanResponse;

  static equals(a: ListPlanResponse | PlainMessage<ListPlanResponse> | undefined, b: ListPlanResponse | PlainMessage<ListPlanResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.ListTaskRequest
 */
export declare class ListTaskRequest extends Message<ListTaskRequest> {
  /**
   * @generated from field: string plan_id = 1;
   */
  planId: string;

  constructor(data?: PartialMessage<ListTaskRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.ListTaskRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTaskRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTaskRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTaskRequest;

  static equals(a: ListTaskRequest | PlainMessage<ListTaskRequest> | undefined, b: ListTaskRequest | PlainMessage<ListTaskRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.Photo
 */
export declare class Photo extends Message<Photo> {
  /**
   * id
   *
   * @generated from field: string result_id = 1;
   */
  resultId: string;

  /**
   * url
   *
   * @generated from field: string url = 2;
   */
  url: string;

  /**
   * @generated from field: string photo_id = 3;
   */
  photoId: string;

  /**
   * @generated from field: string proto_id = 4;
   */
  protoId: string;

  /**
   * 打标结果
   *
   * @generated from field: step.raccoon.aicreator.LabelResult label_result = 5;
   */
  labelResult: LabelResult;

  constructor(data?: PartialMessage<Photo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.Photo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Photo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Photo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Photo;

  static equals(a: Photo | PlainMessage<Photo> | undefined, b: Photo | PlainMessage<Photo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.TaskResult
 */
export declare class TaskResult extends Message<TaskResult> {
  /**
   * prompt
   *
   * @generated from field: string prompt = 1;
   */
  prompt: string;

  /**
   * @generated from field: step.raccoon.aicreator.PhotoResult result = 100;
   */
  result?: PhotoResult;

  constructor(data?: PartialMessage<TaskResult>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.TaskResult";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskResult;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskResult;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskResult;

  static equals(a: TaskResult | PlainMessage<TaskResult> | undefined, b: TaskResult | PlainMessage<TaskResult> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.PhotoResult
 */
export declare class PhotoResult extends Message<PhotoResult> {
  /**
   * 灵感词
   *
   * @generated from field: string inspiration = 1;
   */
  inspiration: string;

  /**
   * 灵感词类型
   *
   * @generated from field: string inspiration_type = 2;
   */
  inspirationType: string;

  /**
   * 角色
   *
   * @generated from field: step.raccoon.common.Role role = 3;
   */
  role?: Role;

  /**
   * @generated from field: string sref = 4;
   */
  sref: string;

  /**
   * @generated from field: repeated step.raccoon.aicreator.Photo photos = 5;
   */
  photos: Photo[];

  constructor(data?: PartialMessage<PhotoResult>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.PhotoResult";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PhotoResult;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PhotoResult;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PhotoResult;

  static equals(a: PhotoResult | PlainMessage<PhotoResult> | undefined, b: PhotoResult | PlainMessage<PhotoResult> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.ListTaskResponse
 */
export declare class ListTaskResponse extends Message<ListTaskResponse> {
  /**
   * @generated from field: int64 total = 1;
   */
  total: bigint;

  /**
   * @generated from field: repeated step.raccoon.aicreator.TaskResult result = 2;
   */
  result: TaskResult[];

  constructor(data?: PartialMessage<ListTaskResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.ListTaskResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTaskResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTaskResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTaskResponse;

  static equals(a: ListTaskResponse | PlainMessage<ListTaskResponse> | undefined, b: ListTaskResponse | PlainMessage<ListTaskResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.LabelResultRequest
 */
export declare class LabelResultRequest extends Message<LabelResultRequest> {
  /**
   * photo_id
   *
   * @generated from field: repeated string result_id = 1;
   */
  resultId: string[];

  /**
   * 打标结果
   *
   * @generated from field: step.raccoon.aicreator.LabelResult label_result = 2;
   */
  labelResult: LabelResult;

  constructor(data?: PartialMessage<LabelResultRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.LabelResultRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LabelResultRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LabelResultRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LabelResultRequest;

  static equals(a: LabelResultRequest | PlainMessage<LabelResultRequest> | undefined, b: LabelResultRequest | PlainMessage<LabelResultRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.PromptListRequest
 */
export declare class PromptListRequest extends Message<PromptListRequest> {
  /**
   * 暂时为空
   *
   * @generated from field: step.raccoon.aicreator.PromptListParam search = 1;
   */
  search?: PromptListParam;

  constructor(data?: PartialMessage<PromptListRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.PromptListRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PromptListRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PromptListRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PromptListRequest;

  static equals(a: PromptListRequest | PlainMessage<PromptListRequest> | undefined, b: PromptListRequest | PlainMessage<PromptListRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.PromptListParam
 */
export declare class PromptListParam extends Message<PromptListParam> {
  /**
   * @generated from field: string prompt_name = 1;
   */
  promptName: string;

  constructor(data?: PartialMessage<PromptListParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.PromptListParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PromptListParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PromptListParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PromptListParam;

  static equals(a: PromptListParam | PlainMessage<PromptListParam> | undefined, b: PromptListParam | PlainMessage<PromptListParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.PromptListResponse
 */
export declare class PromptListResponse extends Message<PromptListResponse> {
  /**
   * @generated from field: repeated step.raccoon.aicreator.Prompt prompts = 1;
   */
  prompts: Prompt[];

  constructor(data?: PartialMessage<PromptListResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.PromptListResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PromptListResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PromptListResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PromptListResponse;

  static equals(a: PromptListResponse | PlainMessage<PromptListResponse> | undefined, b: PromptListResponse | PlainMessage<PromptListResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.UpdatePromptRequest
 */
export declare class UpdatePromptRequest extends Message<UpdatePromptRequest> {
  /**
   * @generated from field: step.raccoon.aicreator.Prompt prompt = 1;
   */
  prompt?: Prompt;

  constructor(data?: PartialMessage<UpdatePromptRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.UpdatePromptRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdatePromptRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdatePromptRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdatePromptRequest;

  static equals(a: UpdatePromptRequest | PlainMessage<UpdatePromptRequest> | undefined, b: UpdatePromptRequest | PlainMessage<UpdatePromptRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.UploadCsvRequest
 */
export declare class UploadCsvRequest extends Message<UploadCsvRequest> {
  /**
   * @generated from field: bytes csv_file = 1;
   */
  csvFile: Uint8Array;

  constructor(data?: PartialMessage<UploadCsvRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.UploadCsvRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UploadCsvRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UploadCsvRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UploadCsvRequest;

  static equals(a: UploadCsvRequest | PlainMessage<UploadCsvRequest> | undefined, b: UploadCsvRequest | PlainMessage<UploadCsvRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.UploadCsvResponse
 */
export declare class UploadCsvResponse extends Message<UploadCsvResponse> {
  /**
   * @generated from field: string csv_path = 1;
   */
  csvPath: string;

  constructor(data?: PartialMessage<UploadCsvResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.UploadCsvResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UploadCsvResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UploadCsvResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UploadCsvResponse;

  static equals(a: UploadCsvResponse | PlainMessage<UploadCsvResponse> | undefined, b: UploadCsvResponse | PlainMessage<UploadCsvResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.PublishPlanRequest
 */
export declare class PublishPlanRequest extends Message<PublishPlanRequest> {
  /**
   * @generated from field: string plan_id = 1;
   */
  planId: string;

  constructor(data?: PartialMessage<PublishPlanRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.PublishPlanRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishPlanRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishPlanRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishPlanRequest;

  static equals(a: PublishPlanRequest | PlainMessage<PublishPlanRequest> | undefined, b: PublishPlanRequest | PlainMessage<PublishPlanRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.aicreator.PublishPlanResponse
 */
export declare class PublishPlanResponse extends Message<PublishPlanResponse> {
  constructor(data?: PartialMessage<PublishPlanResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.aicreator.PublishPlanResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishPlanResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishPlanResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishPlanResponse;

  static equals(a: PublishPlanResponse | PlainMessage<PublishPlanResponse> | undefined, b: PublishPlanResponse | PlainMessage<PublishPlanResponse> | undefined): boolean;
}

