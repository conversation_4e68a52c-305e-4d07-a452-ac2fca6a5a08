// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/appinfo/admin.proto (package step.raccoon.appinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AddVersionNameReq, AddVersionNameRsp, BatchConfigUpdateStrategyReq, BatchConfigUpdateStrategyRsp, BatchCreateNewVersionAppReq, BatchCreateNewVersionAppRsp, ConfigUpdateStrategyReq, ConfigUpdateStrategyRsp, CreateNewVersionAppReq, CreateNewVersionAppRsp, CreateSwitchReq, CreateSwitchRsp, DeleteAppReq, DeleteAppRsp, GetAllChannelsReq, GetAllChannelsRsp, GetAllSwitchDetailReq, GetAllSwitchDetailsRsp, GetAllVersionNamesReq, GetAllVersionNamesRsp, GetSwitchStrategiesReq, GetSwitchStrategiesRsp, OverwriteAppReq, OverwriteAppRsp, PageListAppReq, PageListAppRsp, UpdateSwitchStrategyReq, UpdateSwitchStrategyRsp } from "./admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.appinfo.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.appinfo.Admin",
  readonly methods: {
    /**
     * 创建新版本app
     *
     * @generated from rpc step.raccoon.appinfo.Admin.CreateNewVersionApp
     */
    readonly createNewVersionApp: {
      readonly name: "CreateNewVersionApp",
      readonly I: typeof CreateNewVersionAppReq,
      readonly O: typeof CreateNewVersionAppRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批量创建新版本app
     *
     * @generated from rpc step.raccoon.appinfo.Admin.BatchCreateNewVersionApp
     */
    readonly batchCreateNewVersionApp: {
      readonly name: "BatchCreateNewVersionApp",
      readonly I: typeof BatchCreateNewVersionAppReq,
      readonly O: typeof BatchCreateNewVersionAppRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 删除app
     *
     * @generated from rpc step.raccoon.appinfo.Admin.DeleteApp
     */
    readonly deleteApp: {
      readonly name: "DeleteApp",
      readonly I: typeof DeleteAppReq,
      readonly O: typeof DeleteAppRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 分页查询
     *
     * @generated from rpc step.raccoon.appinfo.Admin.PageListApp
     */
    readonly pageListApp: {
      readonly name: "PageListApp",
      readonly I: typeof PageListAppReq,
      readonly O: typeof PageListAppRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 配置apk更新策略
     *
     * @generated from rpc step.raccoon.appinfo.Admin.ConfigUpdateStrategy
     */
    readonly configUpdateStrategy: {
      readonly name: "ConfigUpdateStrategy",
      readonly I: typeof ConfigUpdateStrategyReq,
      readonly O: typeof ConfigUpdateStrategyRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批量更新apk配置策略
     *
     * @generated from rpc step.raccoon.appinfo.Admin.BatchConfigUpdateStrategy
     */
    readonly batchConfigUpdateStrategy: {
      readonly name: "BatchConfigUpdateStrategy",
      readonly I: typeof BatchConfigUpdateStrategyReq,
      readonly O: typeof BatchConfigUpdateStrategyRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 覆盖线上apk包
     *
     * @generated from rpc step.raccoon.appinfo.Admin.OverwriteApp
     */
    readonly overwriteApp: {
      readonly name: "OverwriteApp",
      readonly I: typeof OverwriteAppReq,
      readonly O: typeof OverwriteAppRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 新增版本号（临时方案）
     *
     * @generated from rpc step.raccoon.appinfo.Admin.AddVersionName
     */
    readonly addVersionName: {
      readonly name: "AddVersionName",
      readonly I: typeof AddVersionNameReq,
      readonly O: typeof AddVersionNameRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取全部版本号（临时方案）
     *
     * @generated from rpc step.raccoon.appinfo.Admin.GetAllVersionNames
     */
    readonly getAllVersionNames: {
      readonly name: "GetAllVersionNames",
      readonly I: typeof GetAllVersionNamesReq,
      readonly O: typeof GetAllVersionNamesRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 创建开关
     *
     * @generated from rpc step.raccoon.appinfo.Admin.CreateSwitch
     */
    readonly createSwitch: {
      readonly name: "CreateSwitch",
      readonly I: typeof CreateSwitchReq,
      readonly O: typeof CreateSwitchRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取全部开关数据
     *
     * @generated from rpc step.raccoon.appinfo.Admin.GetAllSwitchDetails
     */
    readonly getAllSwitchDetails: {
      readonly name: "GetAllSwitchDetails",
      readonly I: typeof GetAllSwitchDetailReq,
      readonly O: typeof GetAllSwitchDetailsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取指定场景（某个版本号&某个渠道包）下的开关策略
     *
     * @generated from rpc step.raccoon.appinfo.Admin.GetSwitchStrategies
     */
    readonly getSwitchStrategies: {
      readonly name: "GetSwitchStrategies",
      readonly I: typeof GetSwitchStrategiesReq,
      readonly O: typeof GetSwitchStrategiesRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新开关策略
     *
     * @generated from rpc step.raccoon.appinfo.Admin.UpdateSwitchStrategy
     */
    readonly updateSwitchStrategy: {
      readonly name: "UpdateSwitchStrategy",
      readonly I: typeof UpdateSwitchStrategyReq,
      readonly O: typeof UpdateSwitchStrategyRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取所有渠道信息
     *
     * @generated from rpc step.raccoon.appinfo.Admin.GetAllChannels
     */
    readonly getAllChannels: {
      readonly name: "GetAllChannels",
      readonly I: typeof GetAllChannelsReq,
      readonly O: typeof GetAllChannelsRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

