// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/appinfo/internal.proto (package step.raccoon.appinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { FetchAllSwitchStrategiesReq, FetchAllSwitchStrategiesRsp } from "./internal_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.appinfo.Internal
 */
export declare const Internal: {
  readonly typeName: "step.raccoon.appinfo.Internal",
  readonly methods: {
    /**
     * 获取全部开关策略（仅供网关调用，相同渠道包仅返回最近3条版本配置记录）
     *
     * @generated from rpc step.raccoon.appinfo.Internal.FetchAllSwitchStrategies
     */
    readonly fetchAllSwitchStrategies: {
      readonly name: "FetchAllSwitchStrategies",
      readonly I: typeof FetchAllSwitchStrategiesReq,
      readonly O: typeof FetchAllSwitchStrategiesRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

