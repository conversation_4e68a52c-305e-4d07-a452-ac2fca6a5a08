// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/appinfo/common.proto (package step.raccoon.appinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

/**
 * @generated from enum step.raccoon.appinfo.UpdateStatus
 */
export declare enum UpdateStatus {
  /**
   * @generated from enum value: UPDATE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: UPDATE_STATUS_FORCE = 1;
   */
  FORCE = 1,

  /**
   * @generated from enum value: UPDATE_STATUS_OPTION = 2;
   */
  OPTION = 2,
}

/**
 * @generated from enum step.raccoon.appinfo.UaTypes
 */
export declare enum UaTypes {
  /**
   * @generated from enum value: UA_UNKNOWN = 0;
   */
  UA_UNKNOWN = 0,

  /**
   * @generated from enum value: UA_ANDROID = 1;
   */
  UA_ANDROID = 1,

  /**
   * @generated from enum value: UA_IOS = 2;
   */
  UA_IOS = 2,
}

/**
 * @generated from enum step.raccoon.appinfo.AppSourceStatus
 */
export declare enum AppSourceStatus {
  /**
   * @generated from enum value: STATUS_UNSPECIFIED = 0;
   */
  STATUS_UNSPECIFIED = 0,

  /**
   * @generated from enum value: STATUS_RELEASE = 1;
   */
  STATUS_RELEASE = 1,
}

