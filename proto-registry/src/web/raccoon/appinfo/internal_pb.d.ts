// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/appinfo/internal.proto (package step.raccoon.appinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message step.raccoon.appinfo.FetchAllSwitchStrategiesReq
 */
export declare class FetchAllSwitchStrategiesReq extends Message<FetchAllSwitchStrategiesReq> {
  constructor(data?: PartialMessage<FetchAllSwitchStrategiesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.FetchAllSwitchStrategiesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FetchAllSwitchStrategiesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FetchAllSwitchStrategiesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FetchAllSwitchStrategiesReq;

  static equals(a: FetchAllSwitchStrategiesReq | PlainMessage<FetchAllSwitchStrategiesReq> | undefined, b: FetchAllSwitchStrategiesReq | PlainMessage<FetchAllSwitchStrategiesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.FetchAllSwitchStrategiesRsp
 */
export declare class FetchAllSwitchStrategiesRsp extends Message<FetchAllSwitchStrategiesRsp> {
  /**
   * @generated from field: repeated step.raccoon.appinfo.ConditionSwithStrategies conditions = 1;
   */
  conditions: ConditionSwithStrategies[];

  constructor(data?: PartialMessage<FetchAllSwitchStrategiesRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.FetchAllSwitchStrategiesRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FetchAllSwitchStrategiesRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FetchAllSwitchStrategiesRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FetchAllSwitchStrategiesRsp;

  static equals(a: FetchAllSwitchStrategiesRsp | PlainMessage<FetchAllSwitchStrategiesRsp> | undefined, b: FetchAllSwitchStrategiesRsp | PlainMessage<FetchAllSwitchStrategiesRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.ConditionSwithStrategies
 */
export declare class ConditionSwithStrategies extends Message<ConditionSwithStrategies> {
  /**
   * @generated from field: string version_name = 1;
   */
  versionName: string;

  /**
   * @generated from field: string channel_id = 2;
   */
  channelId: string;

  /**
   * @generated from field: map<string, string> switch_map = 3;
   */
  switchMap: { [key: string]: string };

  constructor(data?: PartialMessage<ConditionSwithStrategies>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.ConditionSwithStrategies";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ConditionSwithStrategies;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ConditionSwithStrategies;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ConditionSwithStrategies;

  static equals(a: ConditionSwithStrategies | PlainMessage<ConditionSwithStrategies> | undefined, b: ConditionSwithStrategies | PlainMessage<ConditionSwithStrategies> | undefined): boolean;
}

