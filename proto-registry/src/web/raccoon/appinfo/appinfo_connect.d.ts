// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/appinfo/appinfo.proto (package step.raccoon.appinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CheckUpdateReq, CheckUpdateRes, FetchSwitchesReq, FetchSwitchesRsp } from "./appinfo_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.appinfo.AppInfo
 */
export declare const AppInfo: {
  readonly typeName: "step.raccoon.appinfo.AppInfo",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.appinfo.AppInfo.CheckUpdate
     */
    readonly checkUpdate: {
      readonly name: "CheckUpdate",
      readonly I: typeof CheckUpdateReq,
      readonly O: typeof CheckUpdateRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取开关详情
     *
     * @generated from rpc step.raccoon.appinfo.AppInfo.FetchSwitches
     */
    readonly fetchSwitches: {
      readonly name: "FetchSwitches",
      readonly I: typeof FetchSwitchesReq,
      readonly O: typeof FetchSwitchesRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

