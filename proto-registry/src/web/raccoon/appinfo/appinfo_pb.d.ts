// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/appinfo/appinfo.proto (package step.raccoon.appinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { AppSourceStatus, UpdateStatus } from "./common_pb.js";

/**
 * @generated from message step.raccoon.appinfo.AppSource
 */
export declare class AppSource extends Message<AppSource> {
  /**
   * @generated from field: string source_id = 1;
   */
  sourceId: string;

  /**
   * @generated from field: string ua = 2;
   */
  ua: string;

  /**
   * @generated from field: string key = 3;
   */
  key: string;

  /**
   * @generated from field: string version = 4;
   */
  version: string;

  /**
   * @generated from field: step.raccoon.appinfo.AppSourceStatus status = 5;
   */
  status: AppSourceStatus;

  /**
   * 创建时间，毫秒
   *
   * @generated from field: int64 created_time = 6;
   */
  createdTime: bigint;

  /**
   * 更新时间，毫秒
   *
   * @generated from field: int64 updated_time = 7;
   */
  updatedTime: bigint;

  constructor(data?: PartialMessage<AppSource>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.AppSource";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AppSource;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AppSource;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AppSource;

  static equals(a: AppSource | PlainMessage<AppSource> | undefined, b: AppSource | PlainMessage<AppSource> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.CheckUpdateReq
 */
export declare class CheckUpdateReq extends Message<CheckUpdateReq> {
  constructor(data?: PartialMessage<CheckUpdateReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.CheckUpdateReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckUpdateReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckUpdateReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckUpdateReq;

  static equals(a: CheckUpdateReq | PlainMessage<CheckUpdateReq> | undefined, b: CheckUpdateReq | PlainMessage<CheckUpdateReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.CheckUpdateRes
 */
export declare class CheckUpdateRes extends Message<CheckUpdateRes> {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  /**
   * @generated from field: step.raccoon.appinfo.UpdateStatus force_update = 2;
   */
  forceUpdate: UpdateStatus;

  /**
   * @generated from field: string release_notes = 3;
   */
  releaseNotes: string;

  /**
   * @generated from field: step.raccoon.appinfo.AppSource latest_source = 4;
   */
  latestSource?: AppSource;

  /**
   * @generated from field: map<string, string> release_note_map = 5;
   */
  releaseNoteMap: { [key: string]: string };

  constructor(data?: PartialMessage<CheckUpdateRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.CheckUpdateRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckUpdateRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckUpdateRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckUpdateRes;

  static equals(a: CheckUpdateRes | PlainMessage<CheckUpdateRes> | undefined, b: CheckUpdateRes | PlainMessage<CheckUpdateRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.FetchSwitchesReq
 */
export declare class FetchSwitchesReq extends Message<FetchSwitchesReq> {
  constructor(data?: PartialMessage<FetchSwitchesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.FetchSwitchesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FetchSwitchesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FetchSwitchesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FetchSwitchesReq;

  static equals(a: FetchSwitchesReq | PlainMessage<FetchSwitchesReq> | undefined, b: FetchSwitchesReq | PlainMessage<FetchSwitchesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.FetchSwitchesRsp
 */
export declare class FetchSwitchesRsp extends Message<FetchSwitchesRsp> {
  /**
   * @generated from field: map<string, string> switch_map = 1;
   */
  switchMap: { [key: string]: string };

  constructor(data?: PartialMessage<FetchSwitchesRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.FetchSwitchesRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FetchSwitchesRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FetchSwitchesRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FetchSwitchesRsp;

  static equals(a: FetchSwitchesRsp | PlainMessage<FetchSwitchesRsp> | undefined, b: FetchSwitchesRsp | PlainMessage<FetchSwitchesRsp> | undefined): boolean;
}

