// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/appinfo/admin.proto (package step.raccoon.appinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { AppSourceStatus, UpdateStatus } from "./common_pb.js";
import type { PagePagination } from "../common/utils_pb.js";

/**
 * @generated from enum step.raccoon.appinfo.SwitchStatus
 */
export declare enum SwitchStatus {
  /**
   * @generated from enum value: SWITCH_STATUS_CLOSE = 0;
   */
  CLOSE = 0,

  /**
   * @generated from enum value: SWITCH_STATUS_OPEN = 1;
   */
  OPEN = 1,
}

/**
 * @generated from message step.raccoon.appinfo.AdminAppSource
 */
export declare class AdminAppSource extends Message<AdminAppSource> {
  /**
   * @generated from field: string source_id = 1;
   */
  sourceId: string;

  /**
   * @generated from field: string channel_id = 2;
   */
  channelId: string;

  /**
   * @generated from field: string url = 3;
   */
  url: string;

  /**
   * @generated from field: string version_name = 4;
   */
  versionName: string;

  /**
   * 更新策略
   *
   * @generated from field: step.raccoon.appinfo.UpdateStatus update_status = 5;
   */
  updateStatus: UpdateStatus;

  /**
   * 发布状态
   *
   * @generated from field: step.raccoon.appinfo.AppSourceStatus app_source_status = 6;
   */
  appSourceStatus: AppSourceStatus;

  /**
   * 更新人
   *
   * @generated from field: string update_user = 7;
   */
  updateUser: string;

  /**
   * 更新时间，毫秒
   *
   * @generated from field: int64 updated_time = 8;
   */
  updatedTime: bigint;

  /**
   * 弹窗配置
   *
   * @generated from field: repeated step.raccoon.appinfo.ReleaseNoteConfig release_note_config = 9;
   */
  releaseNoteConfig: ReleaseNoteConfig[];

  constructor(data?: PartialMessage<AdminAppSource>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.AdminAppSource";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminAppSource;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminAppSource;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminAppSource;

  static equals(a: AdminAppSource | PlainMessage<AdminAppSource> | undefined, b: AdminAppSource | PlainMessage<AdminAppSource> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.CreateNewVersionAppReq
 */
export declare class CreateNewVersionAppReq extends Message<CreateNewVersionAppReq> {
  /**
   * 渠道
   *
   * @generated from field: string channel_id = 1;
   */
  channelId: string;

  /**
   * 版本号
   *
   * @generated from field: string version_name = 2;
   */
  versionName: string;

  /**
   * apk url
   *
   * @generated from field: string url = 3;
   */
  url: string;

  constructor(data?: PartialMessage<CreateNewVersionAppReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.CreateNewVersionAppReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateNewVersionAppReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateNewVersionAppReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateNewVersionAppReq;

  static equals(a: CreateNewVersionAppReq | PlainMessage<CreateNewVersionAppReq> | undefined, b: CreateNewVersionAppReq | PlainMessage<CreateNewVersionAppReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.CreateNewVersionAppRsp
 */
export declare class CreateNewVersionAppRsp extends Message<CreateNewVersionAppRsp> {
  constructor(data?: PartialMessage<CreateNewVersionAppRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.CreateNewVersionAppRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateNewVersionAppRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateNewVersionAppRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateNewVersionAppRsp;

  static equals(a: CreateNewVersionAppRsp | PlainMessage<CreateNewVersionAppRsp> | undefined, b: CreateNewVersionAppRsp | PlainMessage<CreateNewVersionAppRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.BatchCreateNewVersionAppReq
 */
export declare class BatchCreateNewVersionAppReq extends Message<BatchCreateNewVersionAppReq> {
  /**
   * @generated from field: repeated step.raccoon.appinfo.CreateNewVersionAppReq req_list = 1;
   */
  reqList: CreateNewVersionAppReq[];

  constructor(data?: PartialMessage<BatchCreateNewVersionAppReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.BatchCreateNewVersionAppReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchCreateNewVersionAppReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchCreateNewVersionAppReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchCreateNewVersionAppReq;

  static equals(a: BatchCreateNewVersionAppReq | PlainMessage<BatchCreateNewVersionAppReq> | undefined, b: BatchCreateNewVersionAppReq | PlainMessage<BatchCreateNewVersionAppReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.BatchCreateNewVersionAppRsp
 */
export declare class BatchCreateNewVersionAppRsp extends Message<BatchCreateNewVersionAppRsp> {
  constructor(data?: PartialMessage<BatchCreateNewVersionAppRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.BatchCreateNewVersionAppRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchCreateNewVersionAppRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchCreateNewVersionAppRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchCreateNewVersionAppRsp;

  static equals(a: BatchCreateNewVersionAppRsp | PlainMessage<BatchCreateNewVersionAppRsp> | undefined, b: BatchCreateNewVersionAppRsp | PlainMessage<BatchCreateNewVersionAppRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.DeleteAppReq
 */
export declare class DeleteAppReq extends Message<DeleteAppReq> {
  /**
   * @generated from field: uint64 source_id = 1;
   */
  sourceId: bigint;

  constructor(data?: PartialMessage<DeleteAppReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.DeleteAppReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAppReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAppReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAppReq;

  static equals(a: DeleteAppReq | PlainMessage<DeleteAppReq> | undefined, b: DeleteAppReq | PlainMessage<DeleteAppReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.DeleteAppRsp
 */
export declare class DeleteAppRsp extends Message<DeleteAppRsp> {
  constructor(data?: PartialMessage<DeleteAppRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.DeleteAppRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAppRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAppRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAppRsp;

  static equals(a: DeleteAppRsp | PlainMessage<DeleteAppRsp> | undefined, b: DeleteAppRsp | PlainMessage<DeleteAppRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.PageListAppReq
 */
export declare class PageListAppReq extends Message<PageListAppReq> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.PagePagination pagination = 1;
   */
  pagination?: PagePagination;

  /**
   * @generated from field: step.raccoon.appinfo.ListAppQueryParam search = 2;
   */
  search?: ListAppQueryParam;

  constructor(data?: PartialMessage<PageListAppReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.PageListAppReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PageListAppReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PageListAppReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PageListAppReq;

  static equals(a: PageListAppReq | PlainMessage<PageListAppReq> | undefined, b: PageListAppReq | PlainMessage<PageListAppReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.ListAppQueryParam
 */
export declare class ListAppQueryParam extends Message<ListAppQueryParam> {
  /**
   * 渠道
   *
   * @generated from field: optional string channel_id = 1;
   */
  channelId?: string;

  /**
   * 版本号
   *
   * @generated from field: optional string version_name = 2;
   */
  versionName?: string;

  constructor(data?: PartialMessage<ListAppQueryParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.ListAppQueryParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAppQueryParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAppQueryParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAppQueryParam;

  static equals(a: ListAppQueryParam | PlainMessage<ListAppQueryParam> | undefined, b: ListAppQueryParam | PlainMessage<ListAppQueryParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.PageListAppRsp
 */
export declare class PageListAppRsp extends Message<PageListAppRsp> {
  /**
   * @generated from field: int64 total = 1;
   */
  total: bigint;

  /**
   * @generated from field: repeated step.raccoon.appinfo.AdminAppSource app_list = 2;
   */
  appList: AdminAppSource[];

  constructor(data?: PartialMessage<PageListAppRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.PageListAppRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PageListAppRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PageListAppRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PageListAppRsp;

  static equals(a: PageListAppRsp | PlainMessage<PageListAppRsp> | undefined, b: PageListAppRsp | PlainMessage<PageListAppRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.ReleaseNoteConfig
 */
export declare class ReleaseNoteConfig extends Message<ReleaseNoteConfig> {
  /**
   * @generated from field: repeated string available_pages = 1;
   */
  availablePages: string[];

  /**
   * @generated from field: string release_note = 2;
   */
  releaseNote: string;

  constructor(data?: PartialMessage<ReleaseNoteConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.ReleaseNoteConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReleaseNoteConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReleaseNoteConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReleaseNoteConfig;

  static equals(a: ReleaseNoteConfig | PlainMessage<ReleaseNoteConfig> | undefined, b: ReleaseNoteConfig | PlainMessage<ReleaseNoteConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.ConfigUpdateStrategyReq
 */
export declare class ConfigUpdateStrategyReq extends Message<ConfigUpdateStrategyReq> {
  /**
   * @generated from field: uint64 source_id = 1;
   */
  sourceId: bigint;

  /**
   * 更新策略
   *
   * @generated from field: step.raccoon.appinfo.UpdateStatus update_status = 2;
   */
  updateStatus: UpdateStatus;

  /**
   * 弹窗配置
   *
   * @generated from field: repeated step.raccoon.appinfo.ReleaseNoteConfig release_note_config = 3;
   */
  releaseNoteConfig: ReleaseNoteConfig[];

  constructor(data?: PartialMessage<ConfigUpdateStrategyReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.ConfigUpdateStrategyReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ConfigUpdateStrategyReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ConfigUpdateStrategyReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ConfigUpdateStrategyReq;

  static equals(a: ConfigUpdateStrategyReq | PlainMessage<ConfigUpdateStrategyReq> | undefined, b: ConfigUpdateStrategyReq | PlainMessage<ConfigUpdateStrategyReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.ConfigUpdateStrategyRsp
 */
export declare class ConfigUpdateStrategyRsp extends Message<ConfigUpdateStrategyRsp> {
  constructor(data?: PartialMessage<ConfigUpdateStrategyRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.ConfigUpdateStrategyRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ConfigUpdateStrategyRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ConfigUpdateStrategyRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ConfigUpdateStrategyRsp;

  static equals(a: ConfigUpdateStrategyRsp | PlainMessage<ConfigUpdateStrategyRsp> | undefined, b: ConfigUpdateStrategyRsp | PlainMessage<ConfigUpdateStrategyRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.BatchConfigUpdateStrategyReq
 */
export declare class BatchConfigUpdateStrategyReq extends Message<BatchConfigUpdateStrategyReq> {
  /**
   * @generated from field: repeated step.raccoon.appinfo.ConfigUpdateStrategyReq req_list = 1;
   */
  reqList: ConfigUpdateStrategyReq[];

  constructor(data?: PartialMessage<BatchConfigUpdateStrategyReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.BatchConfigUpdateStrategyReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchConfigUpdateStrategyReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchConfigUpdateStrategyReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchConfigUpdateStrategyReq;

  static equals(a: BatchConfigUpdateStrategyReq | PlainMessage<BatchConfigUpdateStrategyReq> | undefined, b: BatchConfigUpdateStrategyReq | PlainMessage<BatchConfigUpdateStrategyReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.BatchConfigUpdateStrategyRsp
 */
export declare class BatchConfigUpdateStrategyRsp extends Message<BatchConfigUpdateStrategyRsp> {
  constructor(data?: PartialMessage<BatchConfigUpdateStrategyRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.BatchConfigUpdateStrategyRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchConfigUpdateStrategyRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchConfigUpdateStrategyRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchConfigUpdateStrategyRsp;

  static equals(a: BatchConfigUpdateStrategyRsp | PlainMessage<BatchConfigUpdateStrategyRsp> | undefined, b: BatchConfigUpdateStrategyRsp | PlainMessage<BatchConfigUpdateStrategyRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.OverwriteAppReq
 */
export declare class OverwriteAppReq extends Message<OverwriteAppReq> {
  /**
   * @generated from field: uint64 source_id = 1;
   */
  sourceId: bigint;

  constructor(data?: PartialMessage<OverwriteAppReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.OverwriteAppReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OverwriteAppReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OverwriteAppReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OverwriteAppReq;

  static equals(a: OverwriteAppReq | PlainMessage<OverwriteAppReq> | undefined, b: OverwriteAppReq | PlainMessage<OverwriteAppReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.OverwriteAppRsp
 */
export declare class OverwriteAppRsp extends Message<OverwriteAppRsp> {
  constructor(data?: PartialMessage<OverwriteAppRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.OverwriteAppRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OverwriteAppRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OverwriteAppRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OverwriteAppRsp;

  static equals(a: OverwriteAppRsp | PlainMessage<OverwriteAppRsp> | undefined, b: OverwriteAppRsp | PlainMessage<OverwriteAppRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.AddVersionNameReq
 */
export declare class AddVersionNameReq extends Message<AddVersionNameReq> {
  /**
   * 版本号
   *
   * @generated from field: string version_name = 1;
   */
  versionName: string;

  constructor(data?: PartialMessage<AddVersionNameReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.AddVersionNameReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddVersionNameReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddVersionNameReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddVersionNameReq;

  static equals(a: AddVersionNameReq | PlainMessage<AddVersionNameReq> | undefined, b: AddVersionNameReq | PlainMessage<AddVersionNameReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.AddVersionNameRsp
 */
export declare class AddVersionNameRsp extends Message<AddVersionNameRsp> {
  constructor(data?: PartialMessage<AddVersionNameRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.AddVersionNameRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddVersionNameRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddVersionNameRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddVersionNameRsp;

  static equals(a: AddVersionNameRsp | PlainMessage<AddVersionNameRsp> | undefined, b: AddVersionNameRsp | PlainMessage<AddVersionNameRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.GetAllVersionNamesReq
 */
export declare class GetAllVersionNamesReq extends Message<GetAllVersionNamesReq> {
  constructor(data?: PartialMessage<GetAllVersionNamesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.GetAllVersionNamesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllVersionNamesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllVersionNamesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllVersionNamesReq;

  static equals(a: GetAllVersionNamesReq | PlainMessage<GetAllVersionNamesReq> | undefined, b: GetAllVersionNamesReq | PlainMessage<GetAllVersionNamesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.GetAllVersionNamesRsp
 */
export declare class GetAllVersionNamesRsp extends Message<GetAllVersionNamesRsp> {
  /**
   * @generated from field: repeated string version_names = 1;
   */
  versionNames: string[];

  constructor(data?: PartialMessage<GetAllVersionNamesRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.GetAllVersionNamesRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllVersionNamesRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllVersionNamesRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllVersionNamesRsp;

  static equals(a: GetAllVersionNamesRsp | PlainMessage<GetAllVersionNamesRsp> | undefined, b: GetAllVersionNamesRsp | PlainMessage<GetAllVersionNamesRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.CreateSwitchReq
 */
export declare class CreateSwitchReq extends Message<CreateSwitchReq> {
  /**
   * 开关唯一标识
   *
   * @generated from field: string switch_id = 1;
   */
  switchId: string;

  /**
   * 开关注释
   *
   * @generated from field: string remark = 2;
   */
  remark: string;

  constructor(data?: PartialMessage<CreateSwitchReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.CreateSwitchReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateSwitchReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateSwitchReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateSwitchReq;

  static equals(a: CreateSwitchReq | PlainMessage<CreateSwitchReq> | undefined, b: CreateSwitchReq | PlainMessage<CreateSwitchReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.CreateSwitchRsp
 */
export declare class CreateSwitchRsp extends Message<CreateSwitchRsp> {
  constructor(data?: PartialMessage<CreateSwitchRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.CreateSwitchRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateSwitchRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateSwitchRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateSwitchRsp;

  static equals(a: CreateSwitchRsp | PlainMessage<CreateSwitchRsp> | undefined, b: CreateSwitchRsp | PlainMessage<CreateSwitchRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.GetAllSwitchDetailReq
 */
export declare class GetAllSwitchDetailReq extends Message<GetAllSwitchDetailReq> {
  constructor(data?: PartialMessage<GetAllSwitchDetailReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.GetAllSwitchDetailReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllSwitchDetailReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllSwitchDetailReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllSwitchDetailReq;

  static equals(a: GetAllSwitchDetailReq | PlainMessage<GetAllSwitchDetailReq> | undefined, b: GetAllSwitchDetailReq | PlainMessage<GetAllSwitchDetailReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.GetAllSwitchDetailsRsp
 */
export declare class GetAllSwitchDetailsRsp extends Message<GetAllSwitchDetailsRsp> {
  /**
   * @generated from field: repeated step.raccoon.appinfo.SwitchDetail switches = 1;
   */
  switches: SwitchDetail[];

  constructor(data?: PartialMessage<GetAllSwitchDetailsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.GetAllSwitchDetailsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllSwitchDetailsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllSwitchDetailsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllSwitchDetailsRsp;

  static equals(a: GetAllSwitchDetailsRsp | PlainMessage<GetAllSwitchDetailsRsp> | undefined, b: GetAllSwitchDetailsRsp | PlainMessage<GetAllSwitchDetailsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.SwitchDetail
 */
export declare class SwitchDetail extends Message<SwitchDetail> {
  /**
   * 开关唯一标识
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 开关注释
   *
   * @generated from field: string remark = 2;
   */
  remark: string;

  /**
   * 创建人
   *
   * @generated from field: string created_user = 10;
   */
  createdUser: string;

  /**
   * 创建时间，秒
   *
   * @generated from field: uint32 created_time = 9;
   */
  createdTime: number;

  constructor(data?: PartialMessage<SwitchDetail>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.SwitchDetail";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SwitchDetail;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SwitchDetail;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SwitchDetail;

  static equals(a: SwitchDetail | PlainMessage<SwitchDetail> | undefined, b: SwitchDetail | PlainMessage<SwitchDetail> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.GetSwitchStrategiesReq
 */
export declare class GetSwitchStrategiesReq extends Message<GetSwitchStrategiesReq> {
  /**
   * 版本号
   *
   * @generated from field: string version_name = 1;
   */
  versionName: string;

  /**
   * 渠道
   *
   * @generated from field: string channel_id = 2;
   */
  channelId: string;

  constructor(data?: PartialMessage<GetSwitchStrategiesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.GetSwitchStrategiesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSwitchStrategiesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSwitchStrategiesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSwitchStrategiesReq;

  static equals(a: GetSwitchStrategiesReq | PlainMessage<GetSwitchStrategiesReq> | undefined, b: GetSwitchStrategiesReq | PlainMessage<GetSwitchStrategiesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.GetSwitchStrategiesRsp
 */
export declare class GetSwitchStrategiesRsp extends Message<GetSwitchStrategiesRsp> {
  /**
   * @generated from field: repeated step.raccoon.appinfo.SwitchStrategy strategies = 1;
   */
  strategies: SwitchStrategy[];

  constructor(data?: PartialMessage<GetSwitchStrategiesRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.GetSwitchStrategiesRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSwitchStrategiesRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSwitchStrategiesRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSwitchStrategiesRsp;

  static equals(a: GetSwitchStrategiesRsp | PlainMessage<GetSwitchStrategiesRsp> | undefined, b: GetSwitchStrategiesRsp | PlainMessage<GetSwitchStrategiesRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.SwitchStrategy
 */
export declare class SwitchStrategy extends Message<SwitchStrategy> {
  /**
   * 开关策略id
   *
   * @generated from field: optional uint32 id = 1;
   */
  id?: number;

  /**
   * 开关id
   *
   * @generated from field: string switch_id = 2;
   */
  switchId: string;

  /**
   * 开关状态
   *
   * @generated from field: step.raccoon.appinfo.SwitchStatus status = 3;
   */
  status: SwitchStatus;

  /**
   * 版本号
   *
   * @generated from field: string version_name = 4;
   */
  versionName: string;

  /**
   * 渠道
   *
   * @generated from field: string channel_id = 5;
   */
  channelId: string;

  constructor(data?: PartialMessage<SwitchStrategy>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.SwitchStrategy";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SwitchStrategy;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SwitchStrategy;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SwitchStrategy;

  static equals(a: SwitchStrategy | PlainMessage<SwitchStrategy> | undefined, b: SwitchStrategy | PlainMessage<SwitchStrategy> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.UpdateSwitchStrategyReq
 */
export declare class UpdateSwitchStrategyReq extends Message<UpdateSwitchStrategyReq> {
  /**
   * 开关策略id
   *
   * @generated from field: optional uint32 id = 1;
   */
  id?: number;

  /**
   * 版本号
   *
   * @generated from field: string version_name = 2;
   */
  versionName: string;

  /**
   * 渠道
   *
   * @generated from field: string channel_id = 3;
   */
  channelId: string;

  /**
   * 开关id
   *
   * @generated from field: string switch_id = 4;
   */
  switchId: string;

  /**
   * 更新开关状态值
   *
   * @generated from field: step.raccoon.appinfo.SwitchStatus status = 5;
   */
  status: SwitchStatus;

  constructor(data?: PartialMessage<UpdateSwitchStrategyReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.UpdateSwitchStrategyReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSwitchStrategyReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSwitchStrategyReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSwitchStrategyReq;

  static equals(a: UpdateSwitchStrategyReq | PlainMessage<UpdateSwitchStrategyReq> | undefined, b: UpdateSwitchStrategyReq | PlainMessage<UpdateSwitchStrategyReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.UpdateSwitchStrategyRsp
 */
export declare class UpdateSwitchStrategyRsp extends Message<UpdateSwitchStrategyRsp> {
  constructor(data?: PartialMessage<UpdateSwitchStrategyRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.UpdateSwitchStrategyRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSwitchStrategyRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSwitchStrategyRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSwitchStrategyRsp;

  static equals(a: UpdateSwitchStrategyRsp | PlainMessage<UpdateSwitchStrategyRsp> | undefined, b: UpdateSwitchStrategyRsp | PlainMessage<UpdateSwitchStrategyRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.GetAllChannelsReq
 */
export declare class GetAllChannelsReq extends Message<GetAllChannelsReq> {
  /**
   * @generated from field: optional bool with_default = 1;
   */
  withDefault?: boolean;

  constructor(data?: PartialMessage<GetAllChannelsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.GetAllChannelsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllChannelsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllChannelsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllChannelsReq;

  static equals(a: GetAllChannelsReq | PlainMessage<GetAllChannelsReq> | undefined, b: GetAllChannelsReq | PlainMessage<GetAllChannelsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.GetAllChannelsRsp
 */
export declare class GetAllChannelsRsp extends Message<GetAllChannelsRsp> {
  /**
   * @generated from field: repeated step.raccoon.appinfo.ChannelInfo channels = 1;
   */
  channels: ChannelInfo[];

  constructor(data?: PartialMessage<GetAllChannelsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.GetAllChannelsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllChannelsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllChannelsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllChannelsRsp;

  static equals(a: GetAllChannelsRsp | PlainMessage<GetAllChannelsRsp> | undefined, b: GetAllChannelsRsp | PlainMessage<GetAllChannelsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.appinfo.ChannelInfo
 */
export declare class ChannelInfo extends Message<ChannelInfo> {
  /**
   * 英文标识
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 中文描述
   *
   * @generated from field: string desc = 2;
   */
  desc: string;

  constructor(data?: PartialMessage<ChannelInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.appinfo.ChannelInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChannelInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChannelInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChannelInfo;

  static equals(a: ChannelInfo | PlainMessage<ChannelInfo> | undefined, b: ChannelInfo | PlainMessage<ChannelInfo> | undefined): boolean;
}

