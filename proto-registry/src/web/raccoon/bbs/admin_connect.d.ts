// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/bbs/admin.proto (package step.raccoon.bbs, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AddCardsToTopReq, AddCardsToTopRsp, ConvertCardsToBBSReq, ConvertCardsToBBSRsp, DoPostImportReq, DoPostImportRsp, GetCardsTopStatusReq, GetCardsTopStatusRsp, ReBuildTransferCacheReq, ReBuildTransferCacheRsp, RefreshBBSFeedCoverReq, RefreshBBSFeedCoverRsp, UpdateBBSCoverReq, UpdateBBSCoverRsp } from "./admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.bbs.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.bbs.Admin",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.bbs.Admin.AddCardsToTop
     */
    readonly addCardsToTop: {
      readonly name: "AddCardsToTop",
      readonly I: typeof AddCardsToTopReq,
      readonly O: typeof AddCardsToTopRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.bbs.Admin.GetCardsTopStatus
     */
    readonly getCardsTopStatus: {
      readonly name: "GetCardsTopStatus",
      readonly I: typeof GetCardsTopStatusReq,
      readonly O: typeof GetCardsTopStatusRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.bbs.Admin.ConvertCardsToBBS
     */
    readonly convertCardsToBBS: {
      readonly name: "ConvertCardsToBBS",
      readonly I: typeof ConvertCardsToBBSReq,
      readonly O: typeof ConvertCardsToBBSRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.bbs.Admin.DoPostImport
     */
    readonly doPostImport: {
      readonly name: "DoPostImport",
      readonly I: typeof DoPostImportReq,
      readonly O: typeof DoPostImportRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.bbs.Admin.UpdateBBSCover
     */
    readonly updateBBSCover: {
      readonly name: "UpdateBBSCover",
      readonly I: typeof UpdateBBSCoverReq,
      readonly O: typeof UpdateBBSCoverRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.bbs.Admin.ReBuildTransferCache
     */
    readonly reBuildTransferCache: {
      readonly name: "ReBuildTransferCache",
      readonly I: typeof ReBuildTransferCacheReq,
      readonly O: typeof ReBuildTransferCacheRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.bbs.Admin.RefreshBBSFeedCover
     */
    readonly refreshBBSFeedCover: {
      readonly name: "RefreshBBSFeedCover",
      readonly I: typeof RefreshBBSFeedCoverReq,
      readonly O: typeof RefreshBBSFeedCoverRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

