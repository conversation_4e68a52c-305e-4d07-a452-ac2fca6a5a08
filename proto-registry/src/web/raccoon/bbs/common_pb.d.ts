// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bbs/common.proto (package step.raccoon.bbs, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { UserProfile } from "../common/profile_pb.js";
import type { GameType } from "../common/types_pb.js";
import type { CommonMediaItem } from "../common/assets_pb.js";
import type { BrandInfo, TopicInfo } from "../common/showcase_pb.js";
import type { VoteInfo, VoteRecord } from "../common/vote_pb.js";
import type { BBSPostType } from "../common/bbs_pb.js";
import type { Media } from "../common/media_pb.js";

/**
 * @generated from enum step.raccoon.bbs.SubscribeType
 */
export declare enum SubscribeType {
  /**
   * @generated from enum value: SUBSCRIBE_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: SUBSCRIBE_TYPE_NOT_SUBSCRIBE = 1;
   */
  NOT_SUBSCRIBE = 1,

  /**
   * @generated from enum value: SUBSCRIBE_TYPE_TRANSFER = 2;
   */
  TRANSFER = 2,

  /**
   * @generated from enum value: SUBSCRIBE_TYPE_COMMENT = 3;
   */
  COMMENT = 3,
}

/**
 * @generated from message step.raccoon.bbs.OriginPostInfo
 */
export declare class OriginPostInfo extends Message<OriginPostInfo> {
  /**
   * @generated from field: step.raccoon.common.UserProfile author_uinfo = 1;
   */
  authorUinfo?: UserProfile;

  /**
   * @generated from field: string card_id = 2;
   */
  cardId: string;

  /**
   * 标题
   *
   * @generated from field: string title = 3;
   */
  title: string;

  /**
   * @generated from field: string image_url = 4;
   */
  imageUrl: string;

  /**
   * @generated from field: step.raccoon.common.GameType game_type = 5;
   */
  gameType: GameType;

  /**
   * 毫秒时间戳
   *
   * @generated from field: int64 create_time = 6;
   */
  createTime: bigint;

  constructor(data?: PartialMessage<OriginPostInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.OriginPostInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OriginPostInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OriginPostInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OriginPostInfo;

  static equals(a: OriginPostInfo | PlainMessage<OriginPostInfo> | undefined, b: OriginPostInfo | PlainMessage<OriginPostInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.PostInfo
 */
export declare class PostInfo extends Message<PostInfo> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * @generated from field: repeated step.raccoon.common.CommonMediaItem media_items = 3;
   */
  mediaItems: CommonMediaItem[];

  /**
   * 话题
   *
   * @generated from field: repeated step.raccoon.common.TopicInfo topics = 4;
   */
  topics: TopicInfo[];

  /**
   * 投票
   *
   * @generated from field: step.raccoon.common.VoteInfo vote = 5;
   */
  vote?: VoteInfo;

  /**
   * 浏览者的投票记录
   *
   * @generated from field: step.raccoon.common.VoteRecord view_user_vote_record = 6;
   */
  viewUserVoteRecord?: VoteRecord;

  /**
   * 作者信息
   *
   * @generated from field: step.raccoon.common.UserProfile author_uinfo = 7;
   */
  authorUinfo?: UserProfile;

  /**
   * 帖子类型
   *
   * @generated from field: step.raccoon.common.BBSPostType post_type = 8;
   */
  postType: BBSPostType;

  /**
   * 原贴信息
   *
   * @generated from field: step.raccoon.bbs.OriginPostInfo origin_post = 9;
   */
  originPost?: OriginPostInfo;

  /**
   * 毫秒时间戳
   *
   * @generated from field: int64 create_time = 10;
   */
  createTime: bigint;

  /**
   * ip信息
   *
   * @generated from field: repeated step.raccoon.common.BrandInfo brands = 11;
   */
  brands: BrandInfo[];

  /**
   * 转发次数
   *
   * @generated from field: int64 transfer_cnt = 12;
   */
  transferCnt: bigint;

  /**
   * 富文本内容
   *
   * @generated from field: string rich_text_content = 13;
   */
  richTextContent: string;

  /**
   * @generated from field: step.raccoon.common.Media coverImage = 14;
   */
  coverImage?: Media;

  constructor(data?: PartialMessage<PostInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.PostInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PostInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PostInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PostInfo;

  static equals(a: PostInfo | PlainMessage<PostInfo> | undefined, b: PostInfo | PlainMessage<PostInfo> | undefined): boolean;
}

