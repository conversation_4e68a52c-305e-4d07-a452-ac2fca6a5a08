// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/bbs/bbs.proto (package step.raccoon.bbs, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GetPostReq, GetPostRsp, PublishNewPostReq, PublishNewPostRsp, TransferPostReq, TransferPostRsp } from "./bbs_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.bbs.Bbs
 */
export declare const Bbs: {
  readonly typeName: "step.raccoon.bbs.Bbs",
  readonly methods: {
    /**
     * 发布新贴
     *
     * @generated from rpc step.raccoon.bbs.Bbs.PublishNewPost
     */
    readonly publishNewPost: {
      readonly name: "PublishNewPost",
      readonly I: typeof PublishNewPostReq,
      readonly O: typeof PublishNewPostRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 转帖
     *
     * @generated from rpc step.raccoon.bbs.Bbs.TransferPost
     */
    readonly transferPost: {
      readonly name: "TransferPost",
      readonly I: typeof TransferPostReq,
      readonly O: typeof TransferPostRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取帖子详情
     *
     * @generated from rpc step.raccoon.bbs.Bbs.GetPost
     */
    readonly getPost: {
      readonly name: "GetPost",
      readonly I: typeof GetPostReq,
      readonly O: typeof GetPostRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

