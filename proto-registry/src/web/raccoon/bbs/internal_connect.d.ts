// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/bbs/internal.proto (package step.raccoon.bbs, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GenBBSCoverReq, GenBBSCoverRsp, GetCardBBSInfoReq, GetCardBBSInfoRsp, GetRecTopCardIdsReq, GetRecTopCardIdsRsp } from "./internal_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.bbs.Internal
 */
export declare const Internal: {
  readonly typeName: "step.raccoon.bbs.Internal",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.bbs.Internal.GenBBSCover
     */
    readonly genBBSCover: {
      readonly name: "GenBBSCover",
      readonly I: typeof GenBBSCoverReq,
      readonly O: typeof GenBBSCoverRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.bbs.Internal.GetCardBBSInfo
     */
    readonly getCardBBSInfo: {
      readonly name: "GetCardBBSInfo",
      readonly I: typeof GetCardBBSInfoReq,
      readonly O: typeof GetCardBBSInfoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.bbs.Internal.GetRecTopCardIds
     */
    readonly getRecTopCardIds: {
      readonly name: "GetRecTopCardIds",
      readonly I: typeof GetRecTopCardIdsReq,
      readonly O: typeof GetRecTopCardIdsRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

