// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bbs/admin.proto (package step.raccoon.bbs, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum step.raccoon.bbs.BBSCardTopType
 */
export declare enum BBSCardTopType {
  /**
   * @generated from enum value: BBS_CARD_TOP_TYPE_UNKNOWN = 0;
   */
  BBS_CARD_TOP_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: BBS_CARD_TOP_TYPE_LONG_TIME = 1;
   */
  BBS_CARD_TOP_TYPE_LONG_TIME = 1,

  /**
   * @generated from enum value: BBS_CARD_TOP_TYPE_SINGLE_TIME = 2;
   */
  BBS_CARD_TOP_TYPE_SINGLE_TIME = 2,
}

/**
 * @generated from message step.raccoon.bbs.AddCardsToTopReq
 */
export declare class AddCardsToTopReq extends Message<AddCardsToTopReq> {
  /**
   * @generated from field: repeated string card_ids = 1;
   */
  cardIds: string[];

  /**
   * @generated from field: step.raccoon.bbs.BBSCardTopType top_type = 2;
   */
  topType: BBSCardTopType;

  constructor(data?: PartialMessage<AddCardsToTopReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.AddCardsToTopReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddCardsToTopReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddCardsToTopReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddCardsToTopReq;

  static equals(a: AddCardsToTopReq | PlainMessage<AddCardsToTopReq> | undefined, b: AddCardsToTopReq | PlainMessage<AddCardsToTopReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.AddCardsToTopRsp
 */
export declare class AddCardsToTopRsp extends Message<AddCardsToTopRsp> {
  constructor(data?: PartialMessage<AddCardsToTopRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.AddCardsToTopRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddCardsToTopRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddCardsToTopRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddCardsToTopRsp;

  static equals(a: AddCardsToTopRsp | PlainMessage<AddCardsToTopRsp> | undefined, b: AddCardsToTopRsp | PlainMessage<AddCardsToTopRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GetCardsTopStatusReq
 */
export declare class GetCardsTopStatusReq extends Message<GetCardsTopStatusReq> {
  /**
   * @generated from field: repeated string card_ids = 1;
   */
  cardIds: string[];

  constructor(data?: PartialMessage<GetCardsTopStatusReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GetCardsTopStatusReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCardsTopStatusReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCardsTopStatusReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCardsTopStatusReq;

  static equals(a: GetCardsTopStatusReq | PlainMessage<GetCardsTopStatusReq> | undefined, b: GetCardsTopStatusReq | PlainMessage<GetCardsTopStatusReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GetCardsTopStatusRsp
 */
export declare class GetCardsTopStatusRsp extends Message<GetCardsTopStatusRsp> {
  /**
   * @generated from field: map<string, step.raccoon.bbs.BBSCardTopType> res = 1;
   */
  res: { [key: string]: BBSCardTopType };

  constructor(data?: PartialMessage<GetCardsTopStatusRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GetCardsTopStatusRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCardsTopStatusRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCardsTopStatusRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCardsTopStatusRsp;

  static equals(a: GetCardsTopStatusRsp | PlainMessage<GetCardsTopStatusRsp> | undefined, b: GetCardsTopStatusRsp | PlainMessage<GetCardsTopStatusRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.ConvertCardsToBBSReq
 */
export declare class ConvertCardsToBBSReq extends Message<ConvertCardsToBBSReq> {
  /**
   * @generated from field: repeated string card_ids = 1;
   */
  cardIds: string[];

  constructor(data?: PartialMessage<ConvertCardsToBBSReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.ConvertCardsToBBSReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ConvertCardsToBBSReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ConvertCardsToBBSReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ConvertCardsToBBSReq;

  static equals(a: ConvertCardsToBBSReq | PlainMessage<ConvertCardsToBBSReq> | undefined, b: ConvertCardsToBBSReq | PlainMessage<ConvertCardsToBBSReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.ConvertCardsToBBSRsp
 */
export declare class ConvertCardsToBBSRsp extends Message<ConvertCardsToBBSRsp> {
  constructor(data?: PartialMessage<ConvertCardsToBBSRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.ConvertCardsToBBSRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ConvertCardsToBBSRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ConvertCardsToBBSRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ConvertCardsToBBSRsp;

  static equals(a: ConvertCardsToBBSRsp | PlainMessage<ConvertCardsToBBSRsp> | undefined, b: ConvertCardsToBBSRsp | PlainMessage<ConvertCardsToBBSRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.DoPostImportReq
 */
export declare class DoPostImportReq extends Message<DoPostImportReq> {
  /**
   * @generated from field: int32 project_id = 1;
   */
  projectId: number;

  constructor(data?: PartialMessage<DoPostImportReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.DoPostImportReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DoPostImportReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DoPostImportReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DoPostImportReq;

  static equals(a: DoPostImportReq | PlainMessage<DoPostImportReq> | undefined, b: DoPostImportReq | PlainMessage<DoPostImportReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.DoPostImportRsp
 */
export declare class DoPostImportRsp extends Message<DoPostImportRsp> {
  constructor(data?: PartialMessage<DoPostImportRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.DoPostImportRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DoPostImportRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DoPostImportRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DoPostImportRsp;

  static equals(a: DoPostImportRsp | PlainMessage<DoPostImportRsp> | undefined, b: DoPostImportRsp | PlainMessage<DoPostImportRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.UpdateBBSCoverReq
 */
export declare class UpdateBBSCoverReq extends Message<UpdateBBSCoverReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * @generated from field: string cover_text = 2;
   */
  coverText: string;

  /**
   * @generated from field: string image_id = 3;
   */
  imageId: string;

  constructor(data?: PartialMessage<UpdateBBSCoverReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.UpdateBBSCoverReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateBBSCoverReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateBBSCoverReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateBBSCoverReq;

  static equals(a: UpdateBBSCoverReq | PlainMessage<UpdateBBSCoverReq> | undefined, b: UpdateBBSCoverReq | PlainMessage<UpdateBBSCoverReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.UpdateBBSCoverRsp
 */
export declare class UpdateBBSCoverRsp extends Message<UpdateBBSCoverRsp> {
  constructor(data?: PartialMessage<UpdateBBSCoverRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.UpdateBBSCoverRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateBBSCoverRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateBBSCoverRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateBBSCoverRsp;

  static equals(a: UpdateBBSCoverRsp | PlainMessage<UpdateBBSCoverRsp> | undefined, b: UpdateBBSCoverRsp | PlainMessage<UpdateBBSCoverRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.ReBuildTransferCacheReq
 */
export declare class ReBuildTransferCacheReq extends Message<ReBuildTransferCacheReq> {
  /**
   * @generated from field: repeated int64 card_id = 1;
   */
  cardId: bigint[];

  constructor(data?: PartialMessage<ReBuildTransferCacheReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.ReBuildTransferCacheReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReBuildTransferCacheReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReBuildTransferCacheReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReBuildTransferCacheReq;

  static equals(a: ReBuildTransferCacheReq | PlainMessage<ReBuildTransferCacheReq> | undefined, b: ReBuildTransferCacheReq | PlainMessage<ReBuildTransferCacheReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.ReBuildTransferCacheRsp
 */
export declare class ReBuildTransferCacheRsp extends Message<ReBuildTransferCacheRsp> {
  constructor(data?: PartialMessage<ReBuildTransferCacheRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.ReBuildTransferCacheRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReBuildTransferCacheRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReBuildTransferCacheRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReBuildTransferCacheRsp;

  static equals(a: ReBuildTransferCacheRsp | PlainMessage<ReBuildTransferCacheRsp> | undefined, b: ReBuildTransferCacheRsp | PlainMessage<ReBuildTransferCacheRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.RefreshBBSFeedCoverReq
 */
export declare class RefreshBBSFeedCoverReq extends Message<RefreshBBSFeedCoverReq> {
  /**
   * @generated from field: repeated int64 card_ids = 1;
   */
  cardIds: bigint[];

  constructor(data?: PartialMessage<RefreshBBSFeedCoverReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.RefreshBBSFeedCoverReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshBBSFeedCoverReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshBBSFeedCoverReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshBBSFeedCoverReq;

  static equals(a: RefreshBBSFeedCoverReq | PlainMessage<RefreshBBSFeedCoverReq> | undefined, b: RefreshBBSFeedCoverReq | PlainMessage<RefreshBBSFeedCoverReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.RefreshBBSFeedCoverRsp
 */
export declare class RefreshBBSFeedCoverRsp extends Message<RefreshBBSFeedCoverRsp> {
  /**
   * @generated from field: int32 cnt = 1;
   */
  cnt: number;

  constructor(data?: PartialMessage<RefreshBBSFeedCoverRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.RefreshBBSFeedCoverRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshBBSFeedCoverRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshBBSFeedCoverRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshBBSFeedCoverRsp;

  static equals(a: RefreshBBSFeedCoverRsp | PlainMessage<RefreshBBSFeedCoverRsp> | undefined, b: RefreshBBSFeedCoverRsp | PlainMessage<RefreshBBSFeedCoverRsp> | undefined): boolean;
}

