// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bbs/internal.proto (package step.raccoon.bbs, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message step.raccoon.bbs.GenBBSCoverReq
 */
export declare class GenBBSCoverReq extends Message<GenBBSCoverReq> {
  /**
   * @generated from field: string content = 1;
   */
  content: string;

  /**
   * @generated from field: int64 card_id = 2;
   */
  cardId: bigint;

  constructor(data?: PartialMessage<GenBBSCoverReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GenBBSCoverReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenBBSCoverReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenBBSCoverReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenBBSCoverReq;

  static equals(a: GenBBSCoverReq | PlainMessage<GenBBSCoverReq> | undefined, b: GenBBSCoverReq | PlainMessage<GenBBSCoverReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GenBBSCoverRsp
 */
export declare class GenBBSCoverRsp extends Message<GenBBSCoverRsp> {
  /**
   * @generated from field: string image_id = 1;
   */
  imageId: string;

  /**
   * @generated from field: string image_url = 2;
   */
  imageUrl: string;

  constructor(data?: PartialMessage<GenBBSCoverRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GenBBSCoverRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenBBSCoverRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenBBSCoverRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenBBSCoverRsp;

  static equals(a: GenBBSCoverRsp | PlainMessage<GenBBSCoverRsp> | undefined, b: GenBBSCoverRsp | PlainMessage<GenBBSCoverRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GetCardBBSInfoReq
 */
export declare class GetCardBBSInfoReq extends Message<GetCardBBSInfoReq> {
  /**
   * @generated from field: repeated int64 card_id = 1;
   */
  cardId: bigint[];

  constructor(data?: PartialMessage<GetCardBBSInfoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GetCardBBSInfoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCardBBSInfoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCardBBSInfoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCardBBSInfoReq;

  static equals(a: GetCardBBSInfoReq | PlainMessage<GetCardBBSInfoReq> | undefined, b: GetCardBBSInfoReq | PlainMessage<GetCardBBSInfoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.CoverInfo
 */
export declare class CoverInfo extends Message<CoverInfo> {
  /**
   * @generated from field: string bbs_cover_image_id = 1;
   */
  bbsCoverImageId: string;

  /**
   * @generated from field: string bbs_cover_image_url = 2;
   */
  bbsCoverImageUrl: string;

  /**
   * @generated from field: uint32 bbs_cover_image_width = 3;
   */
  bbsCoverImageWidth: number;

  /**
   * @generated from field: uint32 bbs_cover_image_height = 4;
   */
  bbsCoverImageHeight: number;

  constructor(data?: PartialMessage<CoverInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.CoverInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CoverInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CoverInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CoverInfo;

  static equals(a: CoverInfo | PlainMessage<CoverInfo> | undefined, b: CoverInfo | PlainMessage<CoverInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.CardBBsInfo
 */
export declare class CardBBsInfo extends Message<CardBBsInfo> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * @generated from field: step.raccoon.bbs.CoverInfo cover = 2;
   */
  cover?: CoverInfo;

  /**
   * @generated from field: int64 heat_score = 3;
   */
  heatScore: bigint;

  constructor(data?: PartialMessage<CardBBsInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.CardBBsInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardBBsInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardBBsInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardBBsInfo;

  static equals(a: CardBBsInfo | PlainMessage<CardBBsInfo> | undefined, b: CardBBsInfo | PlainMessage<CardBBsInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GetCardBBSInfoRsp
 */
export declare class GetCardBBSInfoRsp extends Message<GetCardBBSInfoRsp> {
  /**
   * @generated from field: map<int64, step.raccoon.bbs.CardBBsInfo> bbs_info = 1;
   */
  bbsInfo: { [key: string]: CardBBsInfo };

  constructor(data?: PartialMessage<GetCardBBSInfoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GetCardBBSInfoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCardBBSInfoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCardBBSInfoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCardBBSInfoRsp;

  static equals(a: GetCardBBSInfoRsp | PlainMessage<GetCardBBSInfoRsp> | undefined, b: GetCardBBSInfoRsp | PlainMessage<GetCardBBSInfoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GetRecTopCardIdsReq
 */
export declare class GetRecTopCardIdsReq extends Message<GetRecTopCardIdsReq> {
  constructor(data?: PartialMessage<GetRecTopCardIdsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GetRecTopCardIdsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRecTopCardIdsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRecTopCardIdsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRecTopCardIdsReq;

  static equals(a: GetRecTopCardIdsReq | PlainMessage<GetRecTopCardIdsReq> | undefined, b: GetRecTopCardIdsReq | PlainMessage<GetRecTopCardIdsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GetRecTopCardIdsRsp
 */
export declare class GetRecTopCardIdsRsp extends Message<GetRecTopCardIdsRsp> {
  /**
   * @generated from field: repeated int64 long_term_top_card_ids = 1;
   */
  longTermTopCardIds: bigint[];

  /**
   * @generated from field: repeated int64 single_time_top_card_ids = 2;
   */
  singleTimeTopCardIds: bigint[];

  constructor(data?: PartialMessage<GetRecTopCardIdsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GetRecTopCardIdsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRecTopCardIdsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRecTopCardIdsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRecTopCardIdsRsp;

  static equals(a: GetRecTopCardIdsRsp | PlainMessage<GetRecTopCardIdsRsp> | undefined, b: GetRecTopCardIdsRsp | PlainMessage<GetRecTopCardIdsRsp> | undefined): boolean;
}

