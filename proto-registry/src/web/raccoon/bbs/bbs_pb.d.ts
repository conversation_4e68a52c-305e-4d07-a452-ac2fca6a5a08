// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bbs/bbs.proto (package step.raccoon.bbs, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { CommonMediaItem } from "../common/assets_pb.js";
import type { TopicInfo } from "../common/showcase_pb.js";
import type { PostInfo } from "./common_pb.js";

/**
 * @generated from enum step.raccoon.bbs.GeneratePostCoverScene
 */
export declare enum GeneratePostCoverScene {
  /**
   * @generated from enum value: FEED_COVER = 0;
   */
  FEED_COVER = 0,

  /**
   * @generated from enum value: FLOW = 1;
   */
  FLOW = 1,
}

/**
 * @generated from message step.raccoon.bbs.PublishNewPostReq
 */
export declare class PublishNewPostReq extends Message<PublishNewPostReq> {
  /**
   * @generated from field: string content = 1;
   */
  content: string;

  /**
   * @generated from field: repeated step.raccoon.common.CommonMediaItem media_items = 2;
   */
  mediaItems: CommonMediaItem[];

  /**
   * 话题
   *
   * @generated from field: repeated step.raccoon.common.TopicInfo topics = 3;
   */
  topics: TopicInfo[];

  /**
   * 选择的ip
   *
   * @generated from field: repeated int32 brand_ids = 4;
   */
  brandIds: number[];

  /**
   * 发起的投票id
   *
   * @generated from field: string vote_id = 5;
   */
  voteId: string;

  /**
   * 富文本内容
   *
   * @generated from field: string rich_text_content = 6;
   */
  richTextContent: string;

  constructor(data?: PartialMessage<PublishNewPostReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.PublishNewPostReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishNewPostReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishNewPostReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishNewPostReq;

  static equals(a: PublishNewPostReq | PlainMessage<PublishNewPostReq> | undefined, b: PublishNewPostReq | PlainMessage<PublishNewPostReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.PublishNewPostRsp
 */
export declare class PublishNewPostRsp extends Message<PublishNewPostRsp> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  constructor(data?: PartialMessage<PublishNewPostRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.PublishNewPostRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishNewPostRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishNewPostRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishNewPostRsp;

  static equals(a: PublishNewPostRsp | PlainMessage<PublishNewPostRsp> | undefined, b: PublishNewPostRsp | PlainMessage<PublishNewPostRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.TransferPostReq
 */
export declare class TransferPostReq extends Message<TransferPostReq> {
  /**
   * @generated from field: string content = 1;
   */
  content: string;

  /**
   * 被转发内容的卡片id
   *
   * @generated from field: string origin_card_id = 2;
   */
  originCardId: string;

  /**
   * 话题
   *
   * @generated from field: repeated step.raccoon.common.TopicInfo topics = 3;
   */
  topics: TopicInfo[];

  /**
   * 选择的ip
   *
   * @generated from field: repeated int32 brand_ids = 4;
   */
  brandIds: number[];

  /**
   * @generated from field: string rich_text_content = 5;
   */
  richTextContent: string;

  constructor(data?: PartialMessage<TransferPostReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.TransferPostReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TransferPostReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TransferPostReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TransferPostReq;

  static equals(a: TransferPostReq | PlainMessage<TransferPostReq> | undefined, b: TransferPostReq | PlainMessage<TransferPostReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.TransferPostRsp
 */
export declare class TransferPostRsp extends Message<TransferPostRsp> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  constructor(data?: PartialMessage<TransferPostRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.TransferPostRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TransferPostRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TransferPostRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TransferPostRsp;

  static equals(a: TransferPostRsp | PlainMessage<TransferPostRsp> | undefined, b: TransferPostRsp | PlainMessage<TransferPostRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GetPostReq
 */
export declare class GetPostReq extends Message<GetPostReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  constructor(data?: PartialMessage<GetPostReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GetPostReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPostReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPostReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPostReq;

  static equals(a: GetPostReq | PlainMessage<GetPostReq> | undefined, b: GetPostReq | PlainMessage<GetPostReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GetPostRsp
 */
export declare class GetPostRsp extends Message<GetPostRsp> {
  /**
   * @generated from field: step.raccoon.bbs.PostInfo post_info = 1;
   */
  postInfo?: PostInfo;

  constructor(data?: PartialMessage<GetPostRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GetPostRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPostRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPostRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPostRsp;

  static equals(a: GetPostRsp | PlainMessage<GetPostRsp> | undefined, b: GetPostRsp | PlainMessage<GetPostRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GeneratePostCoverStyleConfig
 */
export declare class GeneratePostCoverStyleConfig extends Message<GeneratePostCoverStyleConfig> {
  /**
   * @generated from field: optional string font = 1;
   */
  font?: string;

  /**
   * @generated from field: optional string markType = 2;
   */
  markType?: string;

  /**
   * @generated from field: optional string markColor = 3;
   */
  markColor?: string;

  constructor(data?: PartialMessage<GeneratePostCoverStyleConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GeneratePostCoverStyleConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GeneratePostCoverStyleConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GeneratePostCoverStyleConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GeneratePostCoverStyleConfig;

  static equals(a: GeneratePostCoverStyleConfig | PlainMessage<GeneratePostCoverStyleConfig> | undefined, b: GeneratePostCoverStyleConfig | PlainMessage<GeneratePostCoverStyleConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bbs.GeneratePostCover
 */
export declare class GeneratePostCover extends Message<GeneratePostCover> {
  /**
   * @generated from field: optional int32 width = 1;
   */
  width?: number;

  /**
   * @generated from field: optional int32 height = 2;
   */
  height?: number;

  /**
   * @generated from field: string text = 3;
   */
  text: string;

  /**
   * @generated from field: string keyword = 4;
   */
  keyword: string;

  /**
   * @generated from field: optional step.raccoon.bbs.GeneratePostCoverStyleConfig style = 5;
   */
  style?: GeneratePostCoverStyleConfig;

  /**
   * @generated from field: optional step.raccoon.bbs.GeneratePostCoverScene scene = 6;
   */
  scene?: GeneratePostCoverScene;

  constructor(data?: PartialMessage<GeneratePostCover>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bbs.GeneratePostCover";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GeneratePostCover;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GeneratePostCover;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GeneratePostCover;

  static equals(a: GeneratePostCover | PlainMessage<GeneratePostCover> | undefined, b: GeneratePostCover | PlainMessage<GeneratePostCover> | undefined): boolean;
}

