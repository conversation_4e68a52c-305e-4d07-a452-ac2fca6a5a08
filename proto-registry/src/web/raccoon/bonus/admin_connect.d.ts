// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/bonus/admin.proto (package step.raccoon.bonus, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CreateBehaviorConfigReq, CreateBehaviorConfigResp, CreateCheckInConfigReq, CreateCheckInConfigResp, CreateRewardPoolConfigReq, CreateRewardPoolConfigResp, CreateStockItemConfigReq, CreateStockItemConfigResp, CreateTaskConfigReq, CreateTaskConfigResp, FindBehaviorConfigsReq, FindBehaviorConfigsResp, FindCheckInConfigsReq, FindCheckInConfigsResp, FindRewardPoolConfigsReq, FindRewardPoolConfigsResp, FindStockItemConfigsReq, FindStockItemConfigsResp, FindTaskConfigsReq, FindTaskConfigsResp, GetBehaviorConfigReq, GetBehaviorConfigResp, GetCheckInConfigReq, GetCheckInConfigResp, GetRewardPoolConfigReq, GetRewardPoolConfigResp, GetStockItemConfigReq, GetStockItemConfigResp, GetTaskConfigReq, GetTaskConfigResp, UpdateBehaviorConfigReq, UpdateBehaviorConfigResp, UpdateCheckInConfigReq, UpdateCheckInConfigResp, UpdateRewardPoolConfigReq, UpdateRewardPoolConfigResp, UpdateStockItemConfigReq, UpdateStockItemConfigResp, UpdateTaskConfigReq, UpdateTaskConfigResp } from "./admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.bonus.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.bonus.Admin",
  readonly methods: {
    /**
     * 签到配置列表页
     *
     * @generated from rpc step.raccoon.bonus.Admin.FindCheckInConfigs
     */
    readonly findCheckInConfigs: {
      readonly name: "FindCheckInConfigs",
      readonly I: typeof FindCheckInConfigsReq,
      readonly O: typeof FindCheckInConfigsResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 签到配置详情页
     *
     * @generated from rpc step.raccoon.bonus.Admin.GetCheckInConfig
     */
    readonly getCheckInConfig: {
      readonly name: "GetCheckInConfig",
      readonly I: typeof GetCheckInConfigReq,
      readonly O: typeof GetCheckInConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 创建签到配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.CreateCheckInConfig
     */
    readonly createCheckInConfig: {
      readonly name: "CreateCheckInConfig",
      readonly I: typeof CreateCheckInConfigReq,
      readonly O: typeof CreateCheckInConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新签到配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.UpdateCheckInConfig
     */
    readonly updateCheckInConfig: {
      readonly name: "UpdateCheckInConfig",
      readonly I: typeof UpdateCheckInConfigReq,
      readonly O: typeof UpdateCheckInConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 任务配置列表页
     *
     * @generated from rpc step.raccoon.bonus.Admin.FindTaskConfigs
     */
    readonly findTaskConfigs: {
      readonly name: "FindTaskConfigs",
      readonly I: typeof FindTaskConfigsReq,
      readonly O: typeof FindTaskConfigsResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 任务配置详情页
     *
     * @generated from rpc step.raccoon.bonus.Admin.GetTaskConfig
     */
    readonly getTaskConfig: {
      readonly name: "GetTaskConfig",
      readonly I: typeof GetTaskConfigReq,
      readonly O: typeof GetTaskConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 创建任务配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.CreateTaskConfig
     */
    readonly createTaskConfig: {
      readonly name: "CreateTaskConfig",
      readonly I: typeof CreateTaskConfigReq,
      readonly O: typeof CreateTaskConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新任务配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.UpdateTaskConfig
     */
    readonly updateTaskConfig: {
      readonly name: "UpdateTaskConfig",
      readonly I: typeof UpdateTaskConfigReq,
      readonly O: typeof UpdateTaskConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 抽奖奖池配置列表页
     *
     * @generated from rpc step.raccoon.bonus.Admin.FindRewardPoolConfigs
     */
    readonly findRewardPoolConfigs: {
      readonly name: "FindRewardPoolConfigs",
      readonly I: typeof FindRewardPoolConfigsReq,
      readonly O: typeof FindRewardPoolConfigsResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 抽奖奖池配置详情页
     *
     * @generated from rpc step.raccoon.bonus.Admin.GetRewardPoolConfig
     */
    readonly getRewardPoolConfig: {
      readonly name: "GetRewardPoolConfig",
      readonly I: typeof GetRewardPoolConfigReq,
      readonly O: typeof GetRewardPoolConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 创建任务配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.CreateRewardPoolConfig
     */
    readonly createRewardPoolConfig: {
      readonly name: "CreateRewardPoolConfig",
      readonly I: typeof CreateRewardPoolConfigReq,
      readonly O: typeof CreateRewardPoolConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新抽奖奖池配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.UpdateRewardPoolConfig
     */
    readonly updateRewardPoolConfig: {
      readonly name: "UpdateRewardPoolConfig",
      readonly I: typeof UpdateRewardPoolConfigReq,
      readonly O: typeof UpdateRewardPoolConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 库存物品配置列表页
     *
     * @generated from rpc step.raccoon.bonus.Admin.FindStockItemConfigs
     */
    readonly findStockItemConfigs: {
      readonly name: "FindStockItemConfigs",
      readonly I: typeof FindStockItemConfigsReq,
      readonly O: typeof FindStockItemConfigsResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 库存物品配置详情页
     *
     * @generated from rpc step.raccoon.bonus.Admin.GetStockItemConfig
     */
    readonly getStockItemConfig: {
      readonly name: "GetStockItemConfig",
      readonly I: typeof GetStockItemConfigReq,
      readonly O: typeof GetStockItemConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 创建库存物品配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.CreateStockItemConfig
     */
    readonly createStockItemConfig: {
      readonly name: "CreateStockItemConfig",
      readonly I: typeof CreateStockItemConfigReq,
      readonly O: typeof CreateStockItemConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新库存物品配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.UpdateStockItemConfig
     */
    readonly updateStockItemConfig: {
      readonly name: "UpdateStockItemConfig",
      readonly I: typeof UpdateStockItemConfigReq,
      readonly O: typeof UpdateStockItemConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 行为配置列表页
     *
     * @generated from rpc step.raccoon.bonus.Admin.FindBehaviorConfigs
     */
    readonly findBehaviorConfigs: {
      readonly name: "FindBehaviorConfigs",
      readonly I: typeof FindBehaviorConfigsReq,
      readonly O: typeof FindBehaviorConfigsResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 行为配置详情页
     *
     * @generated from rpc step.raccoon.bonus.Admin.GetBehaviorConfig
     */
    readonly getBehaviorConfig: {
      readonly name: "GetBehaviorConfig",
      readonly I: typeof GetBehaviorConfigReq,
      readonly O: typeof GetBehaviorConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 创建行为配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.CreateBehaviorConfig
     */
    readonly createBehaviorConfig: {
      readonly name: "CreateBehaviorConfig",
      readonly I: typeof CreateBehaviorConfigReq,
      readonly O: typeof CreateBehaviorConfigResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新行为配置
     *
     * @generated from rpc step.raccoon.bonus.Admin.UpdateBehaviorConfig
     */
    readonly updateBehaviorConfig: {
      readonly name: "UpdateBehaviorConfig",
      readonly I: typeof UpdateBehaviorConfigReq,
      readonly O: typeof UpdateBehaviorConfigResp,
      readonly kind: MethodKind.Unary,
    },
  }
};

