// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/bonus/internal.proto (package step.raccoon.bonus, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { FindTaskInternalsReq, FindTaskInternalsResp } from "./internal_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.bonus.Internal
 */
export declare const Internal: {
  readonly typeName: "step.raccoon.bonus.Internal",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.bonus.Internal.FindTaskInternals
     */
    readonly findTaskInternals: {
      readonly name: "FindTaskInternals",
      readonly I: typeof FindTaskInternalsReq,
      readonly O: typeof FindTaskInternalsResp,
      readonly kind: MethodKind.Unary,
    },
  }
};

