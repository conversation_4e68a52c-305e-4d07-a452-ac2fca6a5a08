// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bonus/internal.proto (package step.raccoon.bonus, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { TaskType } from "./common_pb.js";
import type { RewardType } from "../common/types_pb.js";

/**
 * @generated from message step.raccoon.bonus.FindTaskInternalsReq
 */
export declare class FindTaskInternalsReq extends Message<FindTaskInternalsReq> {
  /**
   * @generated from field: optional int64 user_id = 1;
   */
  userId?: bigint;

  /**
   * @generated from field: repeated string task_keys = 2;
   */
  taskKeys: string[];

  /**
   * @generated from field: repeated step.raccoon.bonus.TaskType task_types = 3;
   */
  taskTypes: TaskType[];

  constructor(data?: PartialMessage<FindTaskInternalsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindTaskInternalsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindTaskInternalsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindTaskInternalsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindTaskInternalsReq;

  static equals(a: FindTaskInternalsReq | PlainMessage<FindTaskInternalsReq> | undefined, b: FindTaskInternalsReq | PlainMessage<FindTaskInternalsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindTaskInternalsResp
 */
export declare class FindTaskInternalsResp extends Message<FindTaskInternalsResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.TaskInternal tasks = 1;
   */
  tasks: TaskInternal[];

  constructor(data?: PartialMessage<FindTaskInternalsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindTaskInternalsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindTaskInternalsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindTaskInternalsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindTaskInternalsResp;

  static equals(a: FindTaskInternalsResp | PlainMessage<FindTaskInternalsResp> | undefined, b: FindTaskInternalsResp | PlainMessage<FindTaskInternalsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.TaskInternal
 */
export declare class TaskInternal extends Message<TaskInternal> {
  /**
   * @generated from field: string task_key = 1;
   */
  taskKey: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: uint32 start_at = 4;
   */
  startAt: number;

  /**
   * @generated from field: uint32 end_at = 5;
   */
  endAt: number;

  /**
   * @generated from field: repeated step.raccoon.bonus.TaskInternal.Target targets = 11;
   */
  targets: TaskInternal_Target[];

  /**
   * @generated from field: repeated step.raccoon.bonus.TaskInternal.TargetProcess processes = 21;
   */
  processes: TaskInternal_TargetProcess[];

  constructor(data?: PartialMessage<TaskInternal>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.TaskInternal";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskInternal;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskInternal;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskInternal;

  static equals(a: TaskInternal | PlainMessage<TaskInternal> | undefined, b: TaskInternal | PlainMessage<TaskInternal> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.TaskInternal.Target
 */
export declare class TaskInternal_Target extends Message<TaskInternal_Target> {
  /**
   * @generated from field: step.raccoon.bonus.RewardInternal reward = 1;
   */
  reward?: RewardInternal;

  /**
   * @generated from field: repeated step.raccoon.bonus.RewardInternal multi_rewards = 2;
   */
  multiRewards: RewardInternal[];

  /**
   * @generated from field: uint32 target_count = 11;
   */
  targetCount: number;

  constructor(data?: PartialMessage<TaskInternal_Target>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.TaskInternal.Target";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskInternal_Target;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskInternal_Target;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskInternal_Target;

  static equals(a: TaskInternal_Target | PlainMessage<TaskInternal_Target> | undefined, b: TaskInternal_Target | PlainMessage<TaskInternal_Target> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.TaskInternal.TargetProcess
 */
export declare class TaskInternal_TargetProcess extends Message<TaskInternal_TargetProcess> {
  /**
   * @generated from field: uint32 done_at = 1;
   */
  doneAt: number;

  /**
   * @generated from field: uint32 count = 11;
   */
  count: number;

  constructor(data?: PartialMessage<TaskInternal_TargetProcess>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.TaskInternal.TargetProcess";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskInternal_TargetProcess;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskInternal_TargetProcess;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskInternal_TargetProcess;

  static equals(a: TaskInternal_TargetProcess | PlainMessage<TaskInternal_TargetProcess> | undefined, b: TaskInternal_TargetProcess | PlainMessage<TaskInternal_TargetProcess> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.RewardInternal
 */
export declare class RewardInternal extends Message<RewardInternal> {
  /**
   * @generated from field: string image_media_id = 1;
   */
  imageMediaId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: uint32 count = 4;
   */
  count: number;

  /**
   * @generated from field: step.raccoon.common.RewardType reward_type = 11;
   */
  rewardType: RewardType;

  /**
   * @generated from field: string right_key = 12;
   */
  rightKey: string;

  constructor(data?: PartialMessage<RewardInternal>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.RewardInternal";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardInternal;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardInternal;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardInternal;

  static equals(a: RewardInternal | PlainMessage<RewardInternal> | undefined, b: RewardInternal | PlainMessage<RewardInternal> | undefined): boolean;
}

