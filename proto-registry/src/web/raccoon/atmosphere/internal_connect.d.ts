// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/atmosphere/internal.proto (package step.raccoon.atmosphere, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AutoGenAiEmojiRequest, AutoGenAiEmojiResponse, AutoGenCardReq, AutoGenCardRsp, AutoGenCommentRequest, AutoGenCommentResponse, PickRobotCommentRequest, PickRobotCommentResponse, RobotMakephotoRequest, RobotMakephotoResponse, TriggerRobotRequest, TriggerRobotResponse } from "./internal_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.atmosphere.Internal
 */
export declare const Internal: {
  readonly typeName: "step.raccoon.atmosphere.Internal",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.atmosphere.Internal.TriggerRobot
     */
    readonly triggerRobot: {
      readonly name: "TriggerRobot",
      readonly I: typeof TriggerRobotRequest,
      readonly O: typeof TriggerRobotResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.atmosphere.Internal.RobotMakephoto
     */
    readonly robotMakephoto: {
      readonly name: "RobotMakephoto",
      readonly I: typeof RobotMakephotoRequest,
      readonly O: typeof RobotMakephotoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.atmosphere.Internal.PickRobotComment
     */
    readonly pickRobotComment: {
      readonly name: "PickRobotComment",
      readonly I: typeof PickRobotCommentRequest,
      readonly O: typeof PickRobotCommentResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.atmosphere.Internal.AutoGenAiEmoji
     */
    readonly autoGenAiEmoji: {
      readonly name: "AutoGenAiEmoji",
      readonly I: typeof AutoGenAiEmojiRequest,
      readonly O: typeof AutoGenAiEmojiResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.atmosphere.Internal.AutoGenComment
     */
    readonly autoGenComment: {
      readonly name: "AutoGenComment",
      readonly I: typeof AutoGenCommentRequest,
      readonly O: typeof AutoGenCommentResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.atmosphere.Internal.AutoGenCard
     */
    readonly autoGenCard: {
      readonly name: "AutoGenCard",
      readonly I: typeof AutoGenCardReq,
      readonly O: typeof AutoGenCardRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

