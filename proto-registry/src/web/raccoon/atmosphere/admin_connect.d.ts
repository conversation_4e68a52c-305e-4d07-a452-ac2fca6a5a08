// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/atmosphere/admin.proto (package step.raccoon.atmosphere, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CreateBotCommentConfigRequest, CreateBotCommentConfigResponse, GetBotCommentConfigListRequest, GetBotCommentConfigListResponse, UpdateBotCommentConfigRequest, UpdateBotCommentConfigResponse, UploadCommentConfigCsvRequest, UploadCommentConfigCsvResponse } from "./admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.atmosphere.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.atmosphere.Admin",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.atmosphere.Admin.GetBotCommentConfigList
     */
    readonly getBotCommentConfigList: {
      readonly name: "GetBotCommentConfigList",
      readonly I: typeof GetBotCommentConfigListRequest,
      readonly O: typeof GetBotCommentConfigListResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.atmosphere.Admin.CreateBotCommentConfig
     */
    readonly createBotCommentConfig: {
      readonly name: "CreateBotCommentConfig",
      readonly I: typeof CreateBotCommentConfigRequest,
      readonly O: typeof CreateBotCommentConfigResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.atmosphere.Admin.UpdateBotCommentConfig
     */
    readonly updateBotCommentConfig: {
      readonly name: "UpdateBotCommentConfig",
      readonly I: typeof UpdateBotCommentConfigRequest,
      readonly O: typeof UpdateBotCommentConfigResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.atmosphere.Admin.UploadCommentConfigCsv
     */
    readonly uploadCommentConfigCsv: {
      readonly name: "UploadCommentConfigCsv",
      readonly I: typeof UploadCommentConfigCsvRequest,
      readonly O: typeof UploadCommentConfigCsvResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

