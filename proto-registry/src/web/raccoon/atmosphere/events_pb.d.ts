// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/atmosphere/events.proto (package step.raccoon.atmosphere, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum step.raccoon.atmosphere.RobotOperation
 */
export declare enum RobotOperation {
  /**
   * @generated from enum value: RobotOperationNone = 0;
   */
  RobotOperationNone = 0,

  /**
   * @generated from enum value: RobotOperationLike = 1;
   */
  RobotOperationLike = 1,

  /**
   * @generated from enum value: RobotOperationComment = 2;
   */
  RobotOperationComment = 2,

  /**
   * @generated from enum value: RobotOperationFollow = 3;
   */
  RobotOperationFollow = 3,

  /**
   * @generated from enum value: RobotOperationLikeComment = 4;
   */
  RobotOperationLikeComment = 4,
}

/**
 * @generated from message step.raccoon.atmosphere.RobotActionEvent
 */
export declare class RobotActionEvent extends Message<RobotActionEvent> {
  /**
   * @generated from field: int64 target_time = 1;
   */
  targetTime: bigint;

  /**
   * @generated from field: int64 card_id = 2;
   */
  cardId: bigint;

  /**
   * @generated from field: int64 targetUid = 3;
   */
  targetUid: bigint;

  /**
   * @generated from field: int64 robotUid = 4;
   */
  robotUid: bigint;

  /**
   * @generated from field: repeated step.raccoon.atmosphere.RobotOperation operations = 5;
   */
  operations: RobotOperation[];

  /**
   * 目标评论id
   *
   * @generated from field: int64 comment_id = 6;
   */
  commentId: bigint;

  constructor(data?: PartialMessage<RobotActionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.RobotActionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RobotActionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RobotActionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RobotActionEvent;

  static equals(a: RobotActionEvent | PlainMessage<RobotActionEvent> | undefined, b: RobotActionEvent | PlainMessage<RobotActionEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.RobotRound2CheckEvent
 */
export declare class RobotRound2CheckEvent extends Message<RobotRound2CheckEvent> {
  /**
   * @generated from field: int64 target_time = 1;
   */
  targetTime: bigint;

  /**
   * @generated from field: int64 card_id = 2;
   */
  cardId: bigint;

  /**
   * @generated from field: int64 cardCountSnapshot = 3;
   */
  cardCountSnapshot: bigint;

  constructor(data?: PartialMessage<RobotRound2CheckEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.RobotRound2CheckEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RobotRound2CheckEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RobotRound2CheckEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RobotRound2CheckEvent;

  static equals(a: RobotRound2CheckEvent | PlainMessage<RobotRound2CheckEvent> | undefined, b: RobotRound2CheckEvent | PlainMessage<RobotRound2CheckEvent> | undefined): boolean;
}

