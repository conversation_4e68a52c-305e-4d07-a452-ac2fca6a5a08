// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/atmosphere/internal.proto (package step.raccoon.atmosphere, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { GameType } from "../common/types_pb.js";

/**
 * @generated from message step.raccoon.atmosphere.TriggerRobotRequest
 */
export declare class TriggerRobotRequest extends Message<TriggerRobotRequest> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  constructor(data?: PartialMessage<TriggerRobotRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.TriggerRobotRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TriggerRobotRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TriggerRobotRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TriggerRobotRequest;

  static equals(a: TriggerRobotRequest | PlainMessage<TriggerRobotRequest> | undefined, b: TriggerRobotRequest | PlainMessage<TriggerRobotRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.TriggerRobotResponse
 */
export declare class TriggerRobotResponse extends Message<TriggerRobotResponse> {
  constructor(data?: PartialMessage<TriggerRobotResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.TriggerRobotResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TriggerRobotResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TriggerRobotResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TriggerRobotResponse;

  static equals(a: TriggerRobotResponse | PlainMessage<TriggerRobotResponse> | undefined, b: TriggerRobotResponse | PlainMessage<TriggerRobotResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.RobotMakephotoRequest
 */
export declare class RobotMakephotoRequest extends Message<RobotMakephotoRequest> {
  constructor(data?: PartialMessage<RobotMakephotoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.RobotMakephotoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RobotMakephotoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RobotMakephotoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RobotMakephotoRequest;

  static equals(a: RobotMakephotoRequest | PlainMessage<RobotMakephotoRequest> | undefined, b: RobotMakephotoRequest | PlainMessage<RobotMakephotoRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.RobotMakephotoResponse
 */
export declare class RobotMakephotoResponse extends Message<RobotMakephotoResponse> {
  /**
   * @generated from field: int64 task_count = 1;
   */
  taskCount: bigint;

  constructor(data?: PartialMessage<RobotMakephotoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.RobotMakephotoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RobotMakephotoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RobotMakephotoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RobotMakephotoResponse;

  static equals(a: RobotMakephotoResponse | PlainMessage<RobotMakephotoResponse> | undefined, b: RobotMakephotoResponse | PlainMessage<RobotMakephotoResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.PickRobotCommentRequest
 */
export declare class PickRobotCommentRequest extends Message<PickRobotCommentRequest> {
  /**
   * @generated from field: int64 pick_times = 1;
   */
  pickTimes: bigint;

  constructor(data?: PartialMessage<PickRobotCommentRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.PickRobotCommentRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PickRobotCommentRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PickRobotCommentRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PickRobotCommentRequest;

  static equals(a: PickRobotCommentRequest | PlainMessage<PickRobotCommentRequest> | undefined, b: PickRobotCommentRequest | PlainMessage<PickRobotCommentRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.PickRobotCommentResponse
 */
export declare class PickRobotCommentResponse extends Message<PickRobotCommentResponse> {
  /**
   * @generated from field: repeated step.raccoon.atmosphere.PickInfo infos = 1;
   */
  infos: PickInfo[];

  constructor(data?: PartialMessage<PickRobotCommentResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.PickRobotCommentResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PickRobotCommentResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PickRobotCommentResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PickRobotCommentResponse;

  static equals(a: PickRobotCommentResponse | PlainMessage<PickRobotCommentResponse> | undefined, b: PickRobotCommentResponse | PlainMessage<PickRobotCommentResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.PickInfo
 */
export declare class PickInfo extends Message<PickInfo> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: int64 count = 2;
   */
  count: bigint;

  /**
   * @generated from field: int64 total = 3;
   */
  total: bigint;

  /**
   * @generated from field: double prob = 4;
   */
  prob: number;

  constructor(data?: PartialMessage<PickInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.PickInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PickInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PickInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PickInfo;

  static equals(a: PickInfo | PlainMessage<PickInfo> | undefined, b: PickInfo | PlainMessage<PickInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.AutoGenAiEmojiRequest
 */
export declare class AutoGenAiEmojiRequest extends Message<AutoGenAiEmojiRequest> {
  constructor(data?: PartialMessage<AutoGenAiEmojiRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.AutoGenAiEmojiRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoGenAiEmojiRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoGenAiEmojiRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoGenAiEmojiRequest;

  static equals(a: AutoGenAiEmojiRequest | PlainMessage<AutoGenAiEmojiRequest> | undefined, b: AutoGenAiEmojiRequest | PlainMessage<AutoGenAiEmojiRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.AutoGenAiEmojiResponse
 */
export declare class AutoGenAiEmojiResponse extends Message<AutoGenAiEmojiResponse> {
  /**
   * @generated from field: string old_csv_url = 1;
   */
  oldCsvUrl: string;

  /**
   * @generated from field: string csv_url = 2;
   */
  csvUrl: string;

  constructor(data?: PartialMessage<AutoGenAiEmojiResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.AutoGenAiEmojiResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoGenAiEmojiResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoGenAiEmojiResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoGenAiEmojiResponse;

  static equals(a: AutoGenAiEmojiResponse | PlainMessage<AutoGenAiEmojiResponse> | undefined, b: AutoGenAiEmojiResponse | PlainMessage<AutoGenAiEmojiResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.AutoGenCommentPhotoInfo
 */
export declare class AutoGenCommentPhotoInfo extends Message<AutoGenCommentPhotoInfo> {
  /**
   * @generated from field: repeated string brand = 1;
   */
  brand: string[];

  /**
   * @generated from field: repeated string roles = 2;
   */
  roles: string[];

  /**
   * @generated from field: string prompt = 3;
   */
  prompt: string;

  /**
   * @generated from field: string url = 4;
   */
  url: string;

  constructor(data?: PartialMessage<AutoGenCommentPhotoInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.AutoGenCommentPhotoInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoGenCommentPhotoInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoGenCommentPhotoInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoGenCommentPhotoInfo;

  static equals(a: AutoGenCommentPhotoInfo | PlainMessage<AutoGenCommentPhotoInfo> | undefined, b: AutoGenCommentPhotoInfo | PlainMessage<AutoGenCommentPhotoInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.AutoGenCommentCardInfo
 */
export declare class AutoGenCommentCardInfo extends Message<AutoGenCommentCardInfo> {
  /**
   * @generated from field: repeated step.raccoon.atmosphere.AutoGenCommentPhotoInfo photo_replaces = 5;
   */
  photoReplaces: AutoGenCommentPhotoInfo[];

  /**
   * @generated from field: string title_replace = 6;
   */
  titleReplace: string;

  /**
   * @generated from field: string story_replace = 7;
   */
  storyReplace: string;

  /**
   * @generated from field: string author_replace = 8;
   */
  authorReplace: string;

  constructor(data?: PartialMessage<AutoGenCommentCardInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.AutoGenCommentCardInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoGenCommentCardInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoGenCommentCardInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoGenCommentCardInfo;

  static equals(a: AutoGenCommentCardInfo | PlainMessage<AutoGenCommentCardInfo> | undefined, b: AutoGenCommentCardInfo | PlainMessage<AutoGenCommentCardInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.AutoGenCommentRequest
 */
export declare class AutoGenCommentRequest extends Message<AutoGenCommentRequest> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * @generated from field: step.raccoon.common.GameType game_type = 2;
   */
  gameType: GameType;

  /**
   * @generated from field: step.raccoon.atmosphere.AutoGenCommentCardInfo card_info = 5;
   */
  cardInfo?: AutoGenCommentCardInfo;

  /**
   * @generated from field: step.raccoon.atmosphere.AutoGenCommentRequest.CommentRange comment_range = 10;
   */
  commentRange?: AutoGenCommentRequest_CommentRange;

  constructor(data?: PartialMessage<AutoGenCommentRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.AutoGenCommentRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoGenCommentRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoGenCommentRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoGenCommentRequest;

  static equals(a: AutoGenCommentRequest | PlainMessage<AutoGenCommentRequest> | undefined, b: AutoGenCommentRequest | PlainMessage<AutoGenCommentRequest> | undefined): boolean;
}

/**
 * 从0开始 左闭右开 表示生成card下的评论的索引范围
 *
 * @generated from message step.raccoon.atmosphere.AutoGenCommentRequest.CommentRange
 */
export declare class AutoGenCommentRequest_CommentRange extends Message<AutoGenCommentRequest_CommentRange> {
  /**
   * @generated from field: int64 start = 1;
   */
  start: bigint;

  /**
   * @generated from field: int64 end = 2;
   */
  end: bigint;

  constructor(data?: PartialMessage<AutoGenCommentRequest_CommentRange>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.AutoGenCommentRequest.CommentRange";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoGenCommentRequest_CommentRange;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoGenCommentRequest_CommentRange;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoGenCommentRequest_CommentRange;

  static equals(a: AutoGenCommentRequest_CommentRange | PlainMessage<AutoGenCommentRequest_CommentRange> | undefined, b: AutoGenCommentRequest_CommentRange | PlainMessage<AutoGenCommentRequest_CommentRange> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.AutoGenCommentResponse
 */
export declare class AutoGenCommentResponse extends Message<AutoGenCommentResponse> {
  /**
   * 评论索引位置和类型
   *
   * @generated from field: map<int64, string> comment_type_map = 1;
   */
  commentTypeMap: { [key: string]: string };

  /**
   * 评论索引位置和生成的内容
   *
   * @generated from field: map<int64, string> comment_map = 2;
   */
  commentMap: { [key: string]: string };

  constructor(data?: PartialMessage<AutoGenCommentResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.AutoGenCommentResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoGenCommentResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoGenCommentResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoGenCommentResponse;

  static equals(a: AutoGenCommentResponse | PlainMessage<AutoGenCommentResponse> | undefined, b: AutoGenCommentResponse | PlainMessage<AutoGenCommentResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.AutoGenCardReq
 */
export declare class AutoGenCardReq extends Message<AutoGenCardReq> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  constructor(data?: PartialMessage<AutoGenCardReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.AutoGenCardReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoGenCardReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoGenCardReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoGenCardReq;

  static equals(a: AutoGenCardReq | PlainMessage<AutoGenCardReq> | undefined, b: AutoGenCardReq | PlainMessage<AutoGenCardReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.AutoGenCardRsp
 */
export declare class AutoGenCardRsp extends Message<AutoGenCardRsp> {
  /**
   * @generated from field: repeated string photo_ids = 1;
   */
  photoIds: string[];

  /**
   * @generated from field: string proto_id = 2;
   */
  protoId: string;

  /**
   * @generated from field: string card_id = 3;
   */
  cardId: string;

  constructor(data?: PartialMessage<AutoGenCardRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.AutoGenCardRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoGenCardRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoGenCardRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoGenCardRsp;

  static equals(a: AutoGenCardRsp | PlainMessage<AutoGenCardRsp> | undefined, b: AutoGenCardRsp | PlainMessage<AutoGenCardRsp> | undefined): boolean;
}

