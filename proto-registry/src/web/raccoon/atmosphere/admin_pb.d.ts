// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/atmosphere/admin.proto (package step.raccoon.atmosphere, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message step.raccoon.atmosphere.GetBotCommentConfigListRequest
 */
export declare class GetBotCommentConfigListRequest extends Message<GetBotCommentConfigListRequest> {
  constructor(data?: PartialMessage<GetBotCommentConfigListRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.GetBotCommentConfigListRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBotCommentConfigListRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBotCommentConfigListRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBotCommentConfigListRequest;

  static equals(a: GetBotCommentConfigListRequest | PlainMessage<GetBotCommentConfigListRequest> | undefined, b: GetBotCommentConfigListRequest | PlainMessage<GetBotCommentConfigListRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.GetBotCommentConfigListResponse
 */
export declare class GetBotCommentConfigListResponse extends Message<GetBotCommentConfigListResponse> {
  /**
   * @generated from field: repeated step.raccoon.atmosphere.BotCommentConfigInfo info_list = 1;
   */
  infoList: BotCommentConfigInfo[];

  constructor(data?: PartialMessage<GetBotCommentConfigListResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.GetBotCommentConfigListResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBotCommentConfigListResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBotCommentConfigListResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBotCommentConfigListResponse;

  static equals(a: GetBotCommentConfigListResponse | PlainMessage<GetBotCommentConfigListResponse> | undefined, b: GetBotCommentConfigListResponse | PlainMessage<GetBotCommentConfigListResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.BotCommentConfigInfo
 */
export declare class BotCommentConfigInfo extends Message<BotCommentConfigInfo> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: uint32 version = 2;
   */
  version: number;

  /**
   * @generated from field: string alias = 3;
   */
  alias: string;

  /**
   * @generated from field: uint32 probability = 4;
   */
  probability: number;

  /**
   * @generated from field: uint32 count = 5;
   */
  count: number;

  /**
   * @generated from field: string csv_path = 6;
   */
  csvPath: string;

  /**
   * @generated from field: string csv_url = 7;
   */
  csvUrl: string;

  /**
   * @generated from field: string creator = 10;
   */
  creator: string;

  /**
   * unixtime
   *
   * @generated from field: uint32 created_at = 11;
   */
  createdAt: number;

  /**
   * @generated from field: string updater = 12;
   */
  updater: string;

  /**
   * unixtime
   *
   * @generated from field: uint32 updated_at = 13;
   */
  updatedAt: number;

  constructor(data?: PartialMessage<BotCommentConfigInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.BotCommentConfigInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BotCommentConfigInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BotCommentConfigInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BotCommentConfigInfo;

  static equals(a: BotCommentConfigInfo | PlainMessage<BotCommentConfigInfo> | undefined, b: BotCommentConfigInfo | PlainMessage<BotCommentConfigInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.CreateBotCommentConfigRequest
 */
export declare class CreateBotCommentConfigRequest extends Message<CreateBotCommentConfigRequest> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: string alias = 2;
   */
  alias: string;

  /**
   * @generated from field: uint32 probability = 3;
   */
  probability: number;

  /**
   * @generated from field: string csv_path = 4;
   */
  csvPath: string;

  constructor(data?: PartialMessage<CreateBotCommentConfigRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.CreateBotCommentConfigRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateBotCommentConfigRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateBotCommentConfigRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateBotCommentConfigRequest;

  static equals(a: CreateBotCommentConfigRequest | PlainMessage<CreateBotCommentConfigRequest> | undefined, b: CreateBotCommentConfigRequest | PlainMessage<CreateBotCommentConfigRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.CreateBotCommentConfigResponse
 */
export declare class CreateBotCommentConfigResponse extends Message<CreateBotCommentConfigResponse> {
  /**
   * @generated from field: step.raccoon.atmosphere.BotCommentConfigInfo info = 1;
   */
  info?: BotCommentConfigInfo;

  constructor(data?: PartialMessage<CreateBotCommentConfigResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.CreateBotCommentConfigResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateBotCommentConfigResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateBotCommentConfigResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateBotCommentConfigResponse;

  static equals(a: CreateBotCommentConfigResponse | PlainMessage<CreateBotCommentConfigResponse> | undefined, b: CreateBotCommentConfigResponse | PlainMessage<CreateBotCommentConfigResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.UpdateBotCommentConfigRequest
 */
export declare class UpdateBotCommentConfigRequest extends Message<UpdateBotCommentConfigRequest> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: string alias = 2;
   */
  alias: string;

  /**
   * @generated from field: uint32 probability = 3;
   */
  probability: number;

  /**
   * @generated from field: string csv_path = 4;
   */
  csvPath: string;

  constructor(data?: PartialMessage<UpdateBotCommentConfigRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.UpdateBotCommentConfigRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateBotCommentConfigRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateBotCommentConfigRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateBotCommentConfigRequest;

  static equals(a: UpdateBotCommentConfigRequest | PlainMessage<UpdateBotCommentConfigRequest> | undefined, b: UpdateBotCommentConfigRequest | PlainMessage<UpdateBotCommentConfigRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.UpdateBotCommentConfigResponse
 */
export declare class UpdateBotCommentConfigResponse extends Message<UpdateBotCommentConfigResponse> {
  /**
   * @generated from field: step.raccoon.atmosphere.BotCommentConfigInfo info = 1;
   */
  info?: BotCommentConfigInfo;

  constructor(data?: PartialMessage<UpdateBotCommentConfigResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.UpdateBotCommentConfigResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateBotCommentConfigResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateBotCommentConfigResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateBotCommentConfigResponse;

  static equals(a: UpdateBotCommentConfigResponse | PlainMessage<UpdateBotCommentConfigResponse> | undefined, b: UpdateBotCommentConfigResponse | PlainMessage<UpdateBotCommentConfigResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.UploadCommentConfigCsvRequest
 */
export declare class UploadCommentConfigCsvRequest extends Message<UploadCommentConfigCsvRequest> {
  /**
   * @generated from field: bytes csv_file = 1;
   */
  csvFile: Uint8Array;

  constructor(data?: PartialMessage<UploadCommentConfigCsvRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.UploadCommentConfigCsvRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UploadCommentConfigCsvRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UploadCommentConfigCsvRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UploadCommentConfigCsvRequest;

  static equals(a: UploadCommentConfigCsvRequest | PlainMessage<UploadCommentConfigCsvRequest> | undefined, b: UploadCommentConfigCsvRequest | PlainMessage<UploadCommentConfigCsvRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.atmosphere.UploadCommentConfigCsvResponse
 */
export declare class UploadCommentConfigCsvResponse extends Message<UploadCommentConfigCsvResponse> {
  /**
   * @generated from field: string csv_path = 1;
   */
  csvPath: string;

  /**
   * @generated from field: uint32 count = 2;
   */
  count: number;

  constructor(data?: PartialMessage<UploadCommentConfigCsvResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.atmosphere.UploadCommentConfigCsvResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UploadCommentConfigCsvResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UploadCommentConfigCsvResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UploadCommentConfigCsvResponse;

  static equals(a: UploadCommentConfigCsvResponse | PlainMessage<UploadCommentConfigCsvResponse> | undefined, b: UploadCommentConfigCsvResponse | PlainMessage<UploadCommentConfigCsvResponse> | undefined): boolean;
}

