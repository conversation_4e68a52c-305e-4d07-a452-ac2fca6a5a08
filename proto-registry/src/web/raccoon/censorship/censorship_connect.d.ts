// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/censorship/censorship.proto (package step.raccoon.censorship, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { Empty, MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.censorship.Censorship
 */
export declare const Censorship: {
  readonly typeName: "step.raccoon.censorship.Censorship",
  readonly methods: {
    /**
     * 空RPC，占位
     *
     * @generated from rpc step.raccoon.censorship.Censorship.Censor
     */
    readonly censor: {
      readonly name: "Censor",
      readonly I: typeof Empty,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
  }
};

