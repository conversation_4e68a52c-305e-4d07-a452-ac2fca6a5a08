// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/babygrows/babygrows.proto (package step.raccoon.babygrows, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CheckRequest, CheckResponse, PlayRequest, PlayResponse, StartRequest, StartResponse } from "./babygrows_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * Game handles
 *
 * @generated from service step.raccoon.babygrows.Game
 */
export declare const Game: {
  readonly typeName: "step.raccoon.babygrows.Game",
  readonly methods: {
    /**
     * 开始游戏
     *
     * @generated from rpc step.raccoon.babygrows.Game.Check
     */
    readonly check: {
      readonly name: "Check",
      readonly I: typeof CheckRequest,
      readonly O: typeof CheckResponse,
      readonly kind: MethodKind.ServerStreaming,
    },
    /**
     * 开始游戏
     *
     * @generated from rpc step.raccoon.babygrows.Game.Start
     */
    readonly start: {
      readonly name: "Start",
      readonly I: typeof StartRequest,
      readonly O: typeof StartResponse,
      readonly kind: MethodKind.ServerStreaming,
    },
    /**
     * 生成选项剧情
     *
     * @generated from rpc step.raccoon.babygrows.Game.Next
     */
    readonly next: {
      readonly name: "Next",
      readonly I: typeof PlayRequest,
      readonly O: typeof PlayResponse,
      readonly kind: MethodKind.ServerStreaming,
    },
    /**
     * 选择剧情发展
     *
     * @generated from rpc step.raccoon.babygrows.Game.Play
     */
    readonly play: {
      readonly name: "Play",
      readonly I: typeof PlayRequest,
      readonly O: typeof PlayResponse,
      readonly kind: MethodKind.ServerStreaming,
    },
  }
};

