// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/babygrows/babygrows.proto (package step.raccoon.babygrows, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * 游戏状态
 *
 * @generated from enum step.raccoon.babygrows.GameState
 */
export declare enum GameState {
  /**
   * @generated from enum value: INITIAL = 0;
   */
  INITIAL = 0,

  /**
   * @generated from enum value: RUNNING = 1;
   */
  RUNNING = 1,

  /**
   * 鸡娃成功
   *
   * @generated from enum value: SUCCESS = 2;
   */
  SUCCESS = 2,

  /**
   * 鸡娃失败
   *
   * @generated from enum value: FAILURE = 3;
   */
  FAILURE = 3,
}

/**
 * 成长属性
 *
 * @generated from message step.raccoon.babygrows.GrowsAttr
 */
export declare class GrowsAttr extends Message<GrowsAttr> {
  /**
   * 年龄
   *
   * @generated from field: int32 age = 1;
   */
  age: number;

  /**
   * 品德
   *
   * @generated from field: int32 morality = 2;
   */
  morality: number;

  /**
   * 智力
   *
   * @generated from field: int32 clever = 3;
   */
  clever: number;

  /**
   * 健康
   *
   * @generated from field: int32 healthy = 4;
   */
  healthy: number;

  /**
   * 快乐
   *
   * @generated from field: int32 hapiness = 5;
   */
  hapiness: number;

  constructor(data?: PartialMessage<GrowsAttr>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.GrowsAttr";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GrowsAttr;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GrowsAttr;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GrowsAttr;

  static equals(a: GrowsAttr | PlainMessage<GrowsAttr> | undefined, b: GrowsAttr | PlainMessage<GrowsAttr> | undefined): boolean;
}

/**
 * 成长汇总
 *
 * @generated from message step.raccoon.babygrows.ChatSummary
 */
export declare class ChatSummary extends Message<ChatSummary> {
  /**
   * 称号
   *
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * 孩子的心声
   *
   * @generated from field: string words = 2;
   */
  words: string;

  /**
   * 离谱度
   *
   * @generated from field: string exponent = 3;
   */
  exponent: string;

  constructor(data?: PartialMessage<ChatSummary>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.ChatSummary";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatSummary;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatSummary;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatSummary;

  static equals(a: ChatSummary | PlainMessage<ChatSummary> | undefined, b: ChatSummary | PlainMessage<ChatSummary> | undefined): boolean;
}

/**
 * 对话结构体
 *
 * @generated from message step.raccoon.babygrows.ChatStep
 */
export declare class ChatStep extends Message<ChatStep> {
  /**
   * 角色
   *
   * @generated from field: string role = 1;
   */
  role: string;

  /**
   * 内容
   *
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * 剧情
   *
   * @generated from field: string plot = 3;
   */
  plot: string;

  /**
   * 选项
   *
   * @generated from field: repeated string choices = 4;
   */
  choices: string[];

  /**
   * 孩子人设
   *
   * @generated from field: repeated string seeds = 5;
   */
  seeds: string[];

  /**
   * 历史记录
   *
   * @generated from field: repeated string traceid = 6;
   */
  traceid: string[];

  constructor(data?: PartialMessage<ChatStep>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.ChatStep";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatStep;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatStep;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatStep;

  static equals(a: ChatStep | PlainMessage<ChatStep> | undefined, b: ChatStep | PlainMessage<ChatStep> | undefined): boolean;
}

/**
 * 对话历史结构体
 *
 * @generated from message step.raccoon.babygrows.ChatHistory
 */
export declare class ChatHistory extends Message<ChatHistory> {
  /**
   * 属性
   *
   * @generated from field: step.raccoon.babygrows.GrowsAttr attr = 1;
   */
  attr?: GrowsAttr;

  /**
   * 性别
   *
   * @generated from field: string gender = 2;
   */
  gender: string;

  /**
   * 目标
   *
   * @generated from field: string target = 3;
   */
  target: string;

  /**
   * 现实条件
   *
   * @generated from field: string setting = 4;
   */
  setting: string;

  /**
   * 后台生成的chatid
   *
   * @generated from field: string chatid = 5;
   */
  chatid: string;

  /**
   * 后台生成的traceid
   *
   * @generated from field: string traceid = 6;
   */
  traceid: string;

  /**
   * 历史记录
   *
   * @generated from field: repeated step.raccoon.babygrows.ChatStep historys = 7;
   */
  historys: ChatStep[];

  constructor(data?: PartialMessage<ChatHistory>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.ChatHistory";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatHistory;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatHistory;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatHistory;

  static equals(a: ChatHistory | PlainMessage<ChatHistory> | undefined, b: ChatHistory | PlainMessage<ChatHistory> | undefined): boolean;
}

/**
 * 对话历史结构体
 *
 * @generated from message step.raccoon.babygrows.PersonAttr
 */
export declare class PersonAttr extends Message<PersonAttr> {
  /**
   * 年龄
   *
   * @generated from field: int32 age = 1;
   */
  age: number;

  /**
   * 职业
   *
   * @generated from field: string career = 2;
   */
  career: string;

  constructor(data?: PartialMessage<PersonAttr>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.PersonAttr";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PersonAttr;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PersonAttr;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PersonAttr;

  static equals(a: PersonAttr | PlainMessage<PersonAttr> | undefined, b: PersonAttr | PlainMessage<PersonAttr> | undefined): boolean;
}

/**
 * Request Message
 *
 * @generated from message step.raccoon.babygrows.CheckRequest
 */
export declare class CheckRequest extends Message<CheckRequest> {
  /**
   * 校验内容
   *
   * @generated from field: string content = 1;
   */
  content: string;

  constructor(data?: PartialMessage<CheckRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.CheckRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckRequest;

  static equals(a: CheckRequest | PlainMessage<CheckRequest> | undefined, b: CheckRequest | PlainMessage<CheckRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.babygrows.CheckResponse
 */
export declare class CheckResponse extends Message<CheckResponse> {
  /**
   * 返回码
   *
   * @generated from field: int32 code = 1;
   */
  code: number;

  /**
   * 错误信息
   *
   * @generated from field: string msg = 2;
   */
  msg: string;

  constructor(data?: PartialMessage<CheckResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.CheckResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckResponse;

  static equals(a: CheckResponse | PlainMessage<CheckResponse> | undefined, b: CheckResponse | PlainMessage<CheckResponse> | undefined): boolean;
}

/**
 * Request Message
 *
 * @generated from message step.raccoon.babygrows.StartRequest
 */
export declare class StartRequest extends Message<StartRequest> {
  /**
   * @generated from field: string uid = 1;
   */
  uid: string;

  /**
   * 初始化名字
   *
   * @generated from field: string name = 2;
   */
  name: string;

  constructor(data?: PartialMessage<StartRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.StartRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StartRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StartRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StartRequest;

  static equals(a: StartRequest | PlainMessage<StartRequest> | undefined, b: StartRequest | PlainMessage<StartRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.babygrows.StartResponse
 */
export declare class StartResponse extends Message<StartResponse> {
  /**
   * 属性
   *
   * @generated from field: step.raccoon.babygrows.GrowsAttr attr = 1;
   */
  attr?: GrowsAttr;

  /**
   * 性别
   *
   * @generated from field: string gender = 2;
   */
  gender: string;

  /**
   * 目标
   *
   * @generated from field: string target = 3;
   */
  target: string;

  /**
   * 现实条件
   *
   * @generated from field: string setting = 4;
   */
  setting: string;

  /**
   * 后台生成的chatid
   *
   * @generated from field: string chatid = 5;
   */
  chatid: string;

  /**
   * 家庭图片的base64内容
   *
   * @generated from field: bytes image_f = 6;
   */
  imageF: Uint8Array;

  /**
   * 小孩图片的base64内容
   *
   * @generated from field: bytes image = 7;
   */
  image: Uint8Array;

  /**
   * 音效
   *
   * @generated from field: bytes voice = 8;
   */
  voice: Uint8Array;

  /**
   * 历史记录
   *
   * @generated from field: repeated step.raccoon.babygrows.ChatStep steps = 9;
   */
  steps: ChatStep[];

  /**
   * agent的traceid
   *
   * @generated from field: string traceid = 10;
   */
  traceid: string;

  constructor(data?: PartialMessage<StartResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.StartResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StartResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StartResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StartResponse;

  static equals(a: StartResponse | PlainMessage<StartResponse> | undefined, b: StartResponse | PlainMessage<StartResponse> | undefined): boolean;
}

/**
 * PlayRequest Request Message
 *
 * @generated from message step.raccoon.babygrows.PlayRequest
 */
export declare class PlayRequest extends Message<PlayRequest> {
  /**
   * 后台生成的chatid
   *
   * @generated from field: string chatid = 1;
   */
  chatid: string;

  /**
   * 输入prompt
   *
   * @generated from field: string prompt = 2;
   */
  prompt: string;

  /**
   * 性别
   *
   * @generated from field: string gender = 3;
   */
  gender: string;

  /**
   * 目标
   *
   * @generated from field: string target = 4;
   */
  target: string;

  /**
   * 初始化设定
   *
   * @generated from field: string setting = 5;
   */
  setting: string;

  /**
   * 初始化名字
   *
   * @generated from field: string name = 6;
   */
  name: string;

  /**
   * 属性
   *
   * @generated from field: step.raccoon.babygrows.GrowsAttr attr = 7;
   */
  attr?: GrowsAttr;

  /**
   * 历史记录
   *
   * @generated from field: repeated step.raccoon.babygrows.ChatStep steps = 8;
   */
  steps: ChatStep[];

  constructor(data?: PartialMessage<PlayRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.PlayRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PlayRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PlayRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PlayRequest;

  static equals(a: PlayRequest | PlainMessage<PlayRequest> | undefined, b: PlayRequest | PlainMessage<PlayRequest> | undefined): boolean;
}

/**
 * Response Message
 *
 * @generated from message step.raccoon.babygrows.PlayResponse
 */
export declare class PlayResponse extends Message<PlayResponse> {
  /**
   * 属性
   *
   * @generated from field: step.raccoon.babygrows.GrowsAttr attr = 1;
   */
  attr?: GrowsAttr;

  /**
   * 后台生成的chatid
   *
   * @generated from field: string chatid = 2;
   */
  chatid: string;

  /**
   * 图片的base64内容 
   *
   * @generated from field: bytes image = 3;
   */
  image: Uint8Array;

  /**
   * 音效
   *
   * @generated from field: bytes voice = 4;
   */
  voice: Uint8Array;

  /**
   * 模型输出
   *
   * @generated from field: string delta = 5;
   */
  delta: string;

  /**
   * 剧情
   *
   * @generated from field: string plot = 6;
   */
  plot: string;

  /**
   * BGM URL
   *
   * @generated from field: string bgm_url = 7;
   */
  bgmUrl: string;

  /**
   * 游戏状态
   *
   * @generated from field: step.raccoon.babygrows.GameState state = 8;
   */
  state: GameState;

  /**
   * 成长标签
   *
   * @generated from field: repeated string tags = 9;
   */
  tags: string[];

  /**
   * 成长路径选择
   *
   * @generated from field: repeated string choices = 10;
   */
  choices: string[];

  /**
   * 历史记录
   *
   * @generated from field: repeated step.raccoon.babygrows.ChatStep steps = 11;
   */
  steps: ChatStep[];

  /**
   * 成功率预测
   *
   * @generated from field: int64 succ_rate = 12;
   */
  succRate: bigint;

  /**
   * 鸡娃总结
   *
   * @generated from field: step.raccoon.babygrows.ChatSummary summary = 13;
   */
  summary?: ChatSummary;

  /**
   * agent的traceid
   *
   * @generated from field: string traceid = 14;
   */
  traceid: string;

  constructor(data?: PartialMessage<PlayResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.babygrows.PlayResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PlayResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PlayResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PlayResponse;

  static equals(a: PlayResponse | PlainMessage<PlayResponse> | undefined, b: PlayResponse | PlainMessage<PlayResponse> | undefined): boolean;
}

