// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/auth/auth.proto (package step.raccoon.auth, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AuthReq, AuthRes, HeartbeatReq, HeartbeatRes, LoginReq, LoginRes, LogoutReq, LogoutRes, QuerySessionReq, QuerySessionRes, QueryUserReq, QueryUserRes } from "./auth_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.auth.Auth
 */
export declare const Auth: {
  readonly typeName: "step.raccoon.auth.Auth",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.auth.Auth.Login
     */
    readonly login: {
      readonly name: "<PERSON><PERSON>",
      readonly I: typeof LoginReq,
      readonly O: typeof LoginRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.auth.Auth.Logout
     */
    readonly logout: {
      readonly name: "<PERSON>gout",
      readonly I: typeof LogoutReq,
      readonly O: typeof LogoutRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.auth.Auth.Auth
     */
    readonly auth: {
      readonly name: "Auth",
      readonly I: typeof AuthReq,
      readonly O: typeof AuthRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.auth.Auth.Heartbeat
     */
    readonly heartbeat: {
      readonly name: "Heartbeat",
      readonly I: typeof HeartbeatReq,
      readonly O: typeof HeartbeatRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.auth.Auth.QuerySession
     */
    readonly querySession: {
      readonly name: "QuerySession",
      readonly I: typeof QuerySessionReq,
      readonly O: typeof QuerySessionRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.auth.Auth.QueryUser
     */
    readonly queryUser: {
      readonly name: "QueryUser",
      readonly I: typeof QueryUserReq,
      readonly O: typeof QueryUserRes,
      readonly kind: MethodKind.Unary,
    },
  }
};

