// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/auth/auth.proto (package step.raccoon.auth, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum step.raccoon.auth.LoginStatus
 */
export declare enum LoginStatus {
  /**
   * 登录成功
   *
   * @generated from enum value: OK = 0;
   */
  OK = 0,

  /**
   * 鉴权失败
   *
   * @generated from enum value: Fail = 1;
   */
  Fail = 1,

  /**
   * access token过期
   *
   * @generated from enum value: Expired = 2;
   */
  Expired = 2,

  /**
   * @generated from enum value: Banned = 3;
   */
  Banned = 3,

  /**
   * @generated from enum value: NotActivated = 4;
   */
  NotActivated = 4,

  /**
   * 创建会话失败
   *
   * @generated from enum value: SessionFail = 5;
   */
  SessionFail = 5,
}

/**
 * @generated from message step.raccoon.auth.Node
 */
export declare class Node extends Message<Node> {
  /**
   * @generated from field: string ip = 1;
   */
  ip: string;

  /**
   * @generated from field: uint32 port = 2;
   */
  port: number;

  /**
   * @generated from field: string network = 3;
   */
  network: string;

  /**
   * @generated from field: string protocol = 4;
   */
  protocol: string;

  constructor(data?: PartialMessage<Node>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.Node";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Node;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Node;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Node;

  static equals(a: Node | PlainMessage<Node> | undefined, b: Node | PlainMessage<Node> | undefined): boolean;
}

/**
 * 长连接网关的鉴权
 *
 * @generated from message step.raccoon.auth.LoginReq
 */
export declare class LoginReq extends Message<LoginReq> {
  /**
   * 业务id
   *
   * @generated from field: string product = 1;
   */
  product: string;

  /**
   * 用户uid
   *
   * @generated from field: string uid = 2;
   */
  uid: string;

  /**
   * 校验的token
   *
   * @generated from field: string token = 3;
   */
  token: string;

  /**
   * 校验的token类型，当前只有passport
   *
   * @generated from field: string token_type = 4;
   */
  tokenType: string;

  /**
   * 0=所有版本, 1=Web H5, 2=小程序, 3=Android, 4=iPhone
   *
   * @generated from field: uint32 platform = 5;
   */
  platform: number;

  /**
   * 设备名称
   *
   * @generated from field: string device = 6;
   */
  device: string;

  /**
   * app版本
   *
   * @generated from field: string app_version = 7;
   */
  appVersion: string;

  /**
   * 连接id
   *
   * @generated from field: string conn_id = 8;
   */
  connId: string;

  /**
   * 客户端IP
   *
   * @generated from field: string client_addr = 9;
   */
  clientAddr: string;

  /**
   * 接入节点信息，用于主动Push
   *
   * @generated from field: step.raccoon.auth.Node node = 10;
   */
  node?: Node;

  /**
   * 保留字段，对齐gateway的AuthBindReq，保存openid等
   *
   * @generated from field: map<string, string> reserved = 11;
   */
  reserved: { [key: string]: string };

  constructor(data?: PartialMessage<LoginReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.LoginReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LoginReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LoginReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LoginReq;

  static equals(a: LoginReq | PlainMessage<LoginReq> | undefined, b: LoginReq | PlainMessage<LoginReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.auth.LoginRes
 */
export declare class LoginRes extends Message<LoginRes> {
  /**
   * 0:成功
   *
   * @generated from field: step.raccoon.auth.LoginStatus status = 1;
   */
  status: LoginStatus;

  /**
   * 错误提示
   *
   * @generated from field: string error = 2;
   */
  error: string;

  /**
   * uid
   *
   * @generated from field: int64 uid = 3;
   */
  uid: bigint;

  /**
   * conn_id
   *
   * @generated from field: string conn_id = 4;
   */
  connId: string;

  /**
   * 会话id
   *
   * @generated from field: string session_id = 5;
   */
  sessionId: string;

  /**
   * 0=匿名，1=登陆
   *
   * @generated from field: bool logined = 6;
   */
  logined: boolean;

  /**
   * 用户类型，与uinfo的UserRoleType枚举一致
   *
   * @generated from field: int32 user_type = 7;
   */
  userType: number;

  /**
   * 是否新用户，用于终端埋点
   *
   * @generated from field: bool new = 8;
   */
  new: boolean;

  constructor(data?: PartialMessage<LoginRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.LoginRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LoginRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LoginRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LoginRes;

  static equals(a: LoginRes | PlainMessage<LoginRes> | undefined, b: LoginRes | PlainMessage<LoginRes> | undefined): boolean;
}

/**
 * 长连接网关登出
 *
 * @generated from message step.raccoon.auth.LogoutReq
 */
export declare class LogoutReq extends Message<LogoutReq> {
  /**
   * uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 会话id
   *
   * @generated from field: string session_id = 2;
   */
  sessionId: string;

  /**
   * 原因，网关的close code
   *
   * @generated from field: int32 code = 3;
   */
  code: number;

  /**
   * 原因描述
   *
   * @generated from field: string reason = 4;
   */
  reason: string;

  /**
   * 保留自定义字段
   *
   * @generated from field: map<string, string> reserved = 5;
   */
  reserved: { [key: string]: string };

  constructor(data?: PartialMessage<LogoutReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.LogoutReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LogoutReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LogoutReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LogoutReq;

  static equals(a: LogoutReq | PlainMessage<LogoutReq> | undefined, b: LogoutReq | PlainMessage<LogoutReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.auth.LogoutRes
 */
export declare class LogoutRes extends Message<LogoutRes> {
  constructor(data?: PartialMessage<LogoutRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.LogoutRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LogoutRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LogoutRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LogoutRes;

  static equals(a: LogoutRes | PlainMessage<LogoutRes> | undefined, b: LogoutRes | PlainMessage<LogoutRes> | undefined): boolean;
}

/**
 * HTTP网关的鉴权
 *
 * @generated from message step.raccoon.auth.AuthReq
 */
export declare class AuthReq extends Message<AuthReq> {
  /**
   * 业务id
   *
   * @generated from field: string product = 1;
   */
  product: string;

  /**
   * 校验的token
   *
   * @generated from field: string token = 2;
   */
  token: string;

  /**
   * 校验的token类型，当前只有passport
   *
   * @generated from field: string token_type = 3;
   */
  tokenType: string;

  /**
   * 客户端IP
   *
   * @generated from field: string client_addr = 4;
   */
  clientAddr: string;

  /**
   * 请救的uri
   *
   * @generated from field: string uri = 5;
   */
  uri: string;

  constructor(data?: PartialMessage<AuthReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.AuthReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuthReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuthReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuthReq;

  static equals(a: AuthReq | PlainMessage<AuthReq> | undefined, b: AuthReq | PlainMessage<AuthReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.auth.AuthRes
 */
export declare class AuthRes extends Message<AuthRes> {
  /**
   * 0:成功
   *
   * @generated from field: step.raccoon.auth.LoginStatus status = 1;
   */
  status: LoginStatus;

  /**
   * 错误提示
   *
   * @generated from field: string error = 2;
   */
  error: string;

  /**
   * uid
   *
   * @generated from field: int64 uid = 3;
   */
  uid: bigint;

  /**
   * 0=匿名，1=登陆
   *
   * @generated from field: bool logined = 4;
   */
  logined: boolean;

  /**
   * 用户类型，与uinfo的UserRoleType枚举一致
   *
   * @generated from field: int32 user_type = 5;
   */
  userType: number;

  constructor(data?: PartialMessage<AuthRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.AuthRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuthRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuthRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuthRes;

  static equals(a: AuthRes | PlainMessage<AuthRes> | undefined, b: AuthRes | PlainMessage<AuthRes> | undefined): boolean;
}

/**
 * 接入层对用户会话心跳保活 
 *
 * @generated from message step.raccoon.auth.HeartbeatReq
 */
export declare class HeartbeatReq extends Message<HeartbeatReq> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * @generated from field: string conn_id = 2;
   */
  connId: string;

  /**
   * @generated from field: string session_id = 3;
   */
  sessionId: string;

  constructor(data?: PartialMessage<HeartbeatReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.HeartbeatReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HeartbeatReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HeartbeatReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HeartbeatReq;

  static equals(a: HeartbeatReq | PlainMessage<HeartbeatReq> | undefined, b: HeartbeatReq | PlainMessage<HeartbeatReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.auth.HeartbeatRes
 */
export declare class HeartbeatRes extends Message<HeartbeatRes> {
  constructor(data?: PartialMessage<HeartbeatRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.HeartbeatRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HeartbeatRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HeartbeatRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HeartbeatRes;

  static equals(a: HeartbeatRes | PlainMessage<HeartbeatRes> | undefined, b: HeartbeatRes | PlainMessage<HeartbeatRes> | undefined): boolean;
}

/**
 * 通用会话对象
 *
 * @generated from message step.raccoon.auth.Session
 */
export declare class Session extends Message<Session> {
  /**
   * 用户uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 会话id
   *
   * @generated from field: string session_id = 2;
   */
  sessionId: string;

  /**
   * 0=匿名，1=登陆
   *
   * @generated from field: bool logined = 3;
   */
  logined: boolean;

  /**
   * 业务id
   *
   * @generated from field: string product = 4;
   */
  product: string;

  /**
   * 0=所有版本, 1=Web H5, 2=小程序, 3=Android, 4=iPhone
   *
   * @generated from field: string platform = 5;
   */
  platform: string;

  /**
   * 设备名称
   *
   * @generated from field: string device = 6;
   */
  device: string;

  /**
   * app版本
   *
   * @generated from field: string app_version = 7;
   */
  appVersion: string;

  /**
   * 客户端IP
   *
   * @generated from field: string client_addr = 8;
   */
  clientAddr: string;

  /**
   * 登陆时间
   *
   * @generated from field: string logined_time = 9;
   */
  loginedTime: string;

  /**
   * 最近心跳时间
   *
   * @generated from field: string heartbeat_time = 10;
   */
  heartbeatTime: string;

  /**
   * 接入节点信息，用于主动Push
   *
   * @generated from field: step.raccoon.auth.Node node = 11;
   */
  node?: Node;

  constructor(data?: PartialMessage<Session>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.Session";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Session;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Session;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Session;

  static equals(a: Session | PlainMessage<Session> | undefined, b: Session | PlainMessage<Session> | undefined): boolean;
}

/**
 * 请求单个会话信息
 *
 * @generated from message step.raccoon.auth.QuerySessionReq
 */
export declare class QuerySessionReq extends Message<QuerySessionReq> {
  /**
   * @generated from field: string session_id = 1;
   */
  sessionId: string;

  constructor(data?: PartialMessage<QuerySessionReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.QuerySessionReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QuerySessionReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QuerySessionReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QuerySessionReq;

  static equals(a: QuerySessionReq | PlainMessage<QuerySessionReq> | undefined, b: QuerySessionReq | PlainMessage<QuerySessionReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.auth.QuerySessionRes
 */
export declare class QuerySessionRes extends Message<QuerySessionRes> {
  /**
   * @generated from field: step.raccoon.auth.Session session = 1;
   */
  session?: Session;

  constructor(data?: PartialMessage<QuerySessionRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.QuerySessionRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QuerySessionRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QuerySessionRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QuerySessionRes;

  static equals(a: QuerySessionRes | PlainMessage<QuerySessionRes> | undefined, b: QuerySessionRes | PlainMessage<QuerySessionRes> | undefined): boolean;
}

/**
 * 按用户请求所有会话信息
 *
 * @generated from message step.raccoon.auth.QueryUserReq
 */
export declare class QueryUserReq extends Message<QueryUserReq> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  constructor(data?: PartialMessage<QueryUserReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.QueryUserReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserReq;

  static equals(a: QueryUserReq | PlainMessage<QueryUserReq> | undefined, b: QueryUserReq | PlainMessage<QueryUserReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.auth.QueryUserRes
 */
export declare class QueryUserRes extends Message<QueryUserRes> {
  /**
   * @generated from field: repeated step.raccoon.auth.Session sessions = 1;
   */
  sessions: Session[];

  constructor(data?: PartialMessage<QueryUserRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.auth.QueryUserRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserRes;

  static equals(a: QueryUserRes | PlainMessage<QueryUserRes> | undefined, b: QueryUserRes | PlainMessage<QueryUserRes> | undefined): boolean;
}

