// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/realtime/realtime.proto (package step.raccoon.realtime, syntax proto3)
/* eslint-disable */
// @ts-nocheck

/**
 * websocket: Realtime handles
 *
 * @generated from service step.raccoon.realtime.Realtime
 */
export declare const Realtime: {
  readonly typeName: "step.raccoon.realtime.Realtime",
  readonly methods: {
  }
};

