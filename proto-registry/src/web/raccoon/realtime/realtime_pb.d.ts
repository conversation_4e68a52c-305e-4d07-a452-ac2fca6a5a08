// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/realtime/realtime.proto (package step.raccoon.realtime, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum step.raccoon.realtime.AudioCodec
 */
export declare enum AudioCodec {
  /**
   * @generated from enum value: AudioCodecUnknown = 0;
   */
  AudioCodecUnknown = 0,

  /**
   * @generated from enum value: AudioCodecAAC = 1;
   */
  AudioCodecAAC = 1,
}

/**
 * @generated from message step.raccoon.realtime.Audio
 */
export declare class Audio extends Message<Audio> {
  /**
   * pts时间，从0开始，播放器控制播放时机
   *
   * @generated from field: int32 timestamp = 1;
   */
  timestamp: number;

  /**
   * @generated from field: step.raccoon.realtime.AudioCodec codec = 2;
   */
  codec: AudioCodec;

  /**
   * @generated from field: int32 duration = 3;
   */
  duration: number;

  /**
   * @generated from field: bytes data = 100;
   */
  data: Uint8Array;

  constructor(data?: PartialMessage<Audio>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.Audio";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Audio;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Audio;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Audio;

  static equals(a: Audio | PlainMessage<Audio> | undefined, b: Audio | PlainMessage<Audio> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.realtime.RequestMessage
 */
export declare class RequestMessage extends Message<RequestMessage> {
  /**
   * @generated from oneof step.raccoon.realtime.RequestMessage.Msg
   */
  Msg: {
    /**
     * @generated from field: step.raccoon.realtime.Audio audio = 1;
     */
    value: Audio;
    case: "audio";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<RequestMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.RequestMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RequestMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RequestMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RequestMessage;

  static equals(a: RequestMessage | PlainMessage<RequestMessage> | undefined, b: RequestMessage | PlainMessage<RequestMessage> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.realtime.ResponseMessage
 */
export declare class ResponseMessage extends Message<ResponseMessage> {
  /**
   * @generated from oneof step.raccoon.realtime.ResponseMessage.Msg
   */
  Msg: {
    /**
     * @generated from field: step.raccoon.realtime.Audio audio = 1;
     */
    value: Audio;
    case: "audio";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<ResponseMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.ResponseMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResponseMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResponseMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResponseMessage;

  static equals(a: ResponseMessage | PlainMessage<ResponseMessage> | undefined, b: ResponseMessage | PlainMessage<ResponseMessage> | undefined): boolean;
}

