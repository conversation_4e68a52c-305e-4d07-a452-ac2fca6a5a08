// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/realtime/common.proto (package step.raccoon.realtime, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message step.raccoon.realtime.AudioFrame
 */
export declare class AudioFrame extends Message<AudioFrame> {
  /**
   * @generated from field: int64 timestamp = 1;
   */
  timestamp: bigint;

  /**
   * @generated from field: int32 channels = 2;
   */
  channels: number;

  /**
   * @generated from field: int32 sample_rate = 3;
   */
  sampleRate: number;

  /**
   * @generated from field: int32 bits_per_sample = 4;
   */
  bitsPerSample: number;

  /**
   * @generated from field: bytes data = 5;
   */
  data: Uint8Array;

  constructor(data?: PartialMessage<AudioFrame>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.AudioFrame";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AudioFrame;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AudioFrame;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AudioFrame;

  static equals(a: AudioFrame | PlainMessage<AudioFrame> | undefined, b: AudioFrame | PlainMessage<AudioFrame> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.realtime.ClientEvent
 */
export declare class ClientEvent extends Message<ClientEvent> {
  /**
   * @generated from field: string event_id = 1;
   */
  eventId: string;

  /**
   * @generated from oneof step.raccoon.realtime.ClientEvent.event
   */
  event: {
    /**
     * @generated from field: step.raccoon.realtime.InputAudioBufferAppendEvent audio_buffer_append = 100;
     */
    value: InputAudioBufferAppendEvent;
    case: "audioBufferAppend";
  } | {
    /**
     * @generated from field: step.raccoon.realtime.InputAudioBufferCommitEvent audio_buffer_commit = 101;
     */
    value: InputAudioBufferCommitEvent;
    case: "audioBufferCommit";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<ClientEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.ClientEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ClientEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ClientEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ClientEvent;

  static equals(a: ClientEvent | PlainMessage<ClientEvent> | undefined, b: ClientEvent | PlainMessage<ClientEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.realtime.InputAudioBufferAppendEvent
 */
export declare class InputAudioBufferAppendEvent extends Message<InputAudioBufferAppendEvent> {
  /**
   * @generated from field: repeated step.raccoon.realtime.AudioFrame audios = 1;
   */
  audios: AudioFrame[];

  constructor(data?: PartialMessage<InputAudioBufferAppendEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.InputAudioBufferAppendEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InputAudioBufferAppendEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InputAudioBufferAppendEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InputAudioBufferAppendEvent;

  static equals(a: InputAudioBufferAppendEvent | PlainMessage<InputAudioBufferAppendEvent> | undefined, b: InputAudioBufferAppendEvent | PlainMessage<InputAudioBufferAppendEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.realtime.InputAudioBufferCommitEvent
 */
export declare class InputAudioBufferCommitEvent extends Message<InputAudioBufferCommitEvent> {
  constructor(data?: PartialMessage<InputAudioBufferCommitEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.InputAudioBufferCommitEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InputAudioBufferCommitEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InputAudioBufferCommitEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InputAudioBufferCommitEvent;

  static equals(a: InputAudioBufferCommitEvent | PlainMessage<InputAudioBufferCommitEvent> | undefined, b: InputAudioBufferCommitEvent | PlainMessage<InputAudioBufferCommitEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.realtime.ServerEvent
 */
export declare class ServerEvent extends Message<ServerEvent> {
  /**
   * @generated from field: string event_id = 1;
   */
  eventId: string;

  /**
   * @generated from oneof step.raccoon.realtime.ServerEvent.event
   */
  event: {
    /**
     * @generated from field: step.raccoon.realtime.ResponseOutputEvent response_output = 100;
     */
    value: ResponseOutputEvent;
    case: "responseOutput";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<ServerEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.ServerEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ServerEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ServerEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ServerEvent;

  static equals(a: ServerEvent | PlainMessage<ServerEvent> | undefined, b: ServerEvent | PlainMessage<ServerEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.realtime.ResponseOutputEvent
 */
export declare class ResponseOutputEvent extends Message<ResponseOutputEvent> {
  /**
   * @generated from field: string response_id = 1;
   */
  responseId: string;

  /**
   * @generated from field: string item_id = 2;
   */
  itemId: string;

  /**
   * @generated from field: int32 output_index = 3;
   */
  outputIndex: number;

  /**
   * @generated from field: int32 content_index = 4;
   */
  contentIndex: number;

  /**
   * @generated from oneof step.raccoon.realtime.ResponseOutputEvent.content
   */
  content: {
    /**
     * @generated from field: step.raccoon.realtime.ResponseAudioDelta audio_delta = 100;
     */
    value: ResponseAudioDelta;
    case: "audioDelta";
  } | {
    /**
     * @generated from field: step.raccoon.realtime.ResponseAudioDone audio_done = 101;
     */
    value: ResponseAudioDone;
    case: "audioDone";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<ResponseOutputEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.ResponseOutputEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResponseOutputEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResponseOutputEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResponseOutputEvent;

  static equals(a: ResponseOutputEvent | PlainMessage<ResponseOutputEvent> | undefined, b: ResponseOutputEvent | PlainMessage<ResponseOutputEvent> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.realtime.ResponseAudioDelta
 */
export declare class ResponseAudioDelta extends Message<ResponseAudioDelta> {
  /**
   * @generated from field: step.raccoon.realtime.AudioFrame delta = 1;
   */
  delta?: AudioFrame;

  constructor(data?: PartialMessage<ResponseAudioDelta>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.ResponseAudioDelta";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResponseAudioDelta;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResponseAudioDelta;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResponseAudioDelta;

  static equals(a: ResponseAudioDelta | PlainMessage<ResponseAudioDelta> | undefined, b: ResponseAudioDelta | PlainMessage<ResponseAudioDelta> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.realtime.ResponseAudioDone
 */
export declare class ResponseAudioDone extends Message<ResponseAudioDone> {
  constructor(data?: PartialMessage<ResponseAudioDone>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.realtime.ResponseAudioDone";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResponseAudioDone;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResponseAudioDone;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResponseAudioDone;

  static equals(a: ResponseAudioDone | PlainMessage<ResponseAudioDone> | undefined, b: ResponseAudioDone | PlainMessage<ResponseAudioDone> | undefined): boolean;
}

