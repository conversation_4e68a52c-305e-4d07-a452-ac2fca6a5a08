// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/comment/internal.proto (package step.raccoon.comment, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { BatchGetCensorIdByCommentIdReq, BatchGetCensorIdByCommentIdRsp, BatchQueryCommentLikeStatesReq, BatchQueryCommentLikeStatesRes, BatchQueryCommentReq, BatchQueryCommentRes, BatchQueryWorkStatReq, BatchQueryWorkStatRes, IncUserBeCopiedsReq, IncUserBeCopiedsRes, ManageCommentReq, PublishCommentInternalReq, PublishCommentInternalRes, QueryCardCopyInfoReq, QueryCardCopyInfoRsp, QueryCardTopCommentReq, QueryCardTopCommentRsp, QueryCommentByIdReq, QueryCommentByIdRes, QueryCommentLikeUidsReq, QueryCommentLikeUidsRsp, QuerySubCommentReq, QuerySubCommentRes, QueryUserCommentCntReq, QueryUserCommentCntRsp, QueryUserCopyInfoReq, QueryUserCopyInfoRsp, QueryUserLikeWorksReq, QueryUserLikeWorksRes, QueryUserTotalLikesReq, QueryUserTotalLikesRes, QueryWorkCommentedByUsersReq, QueryWorkCommentedByUsersRes, QueryWorkCommentUserInfoReq, QueryWorkCommentUserInfoRsp, QueryWorkLikedByUsersReq, QueryWorkLikedByUsersRes, QueryWorkStatReq, QueryWorkStatRes, ReScanLatestCommentReq, ReScanLatestCommentRes, SendCopyNoticeCommentReq, SendCopyNoticeCommentRsp } from "./internal_pb.js";
import { Empty, MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.comment.Internal
 */
export declare const Internal: {
  readonly typeName: "step.raccoon.comment.Internal",
  readonly methods: {
    /**
     * 隐藏评论
     *
     * @generated from rpc step.raccoon.comment.Internal.RemoveComment
     */
    readonly removeComment: {
      readonly name: "RemoveComment",
      readonly I: typeof ManageCommentReq,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 置顶评论
     *
     * @generated from rpc step.raccoon.comment.Internal.PinComment
     */
    readonly pinComment: {
      readonly name: "PinComment",
      readonly I: typeof ManageCommentReq,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询作品点赞和评论总数 
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryWorkStat
     */
    readonly queryWorkStat: {
      readonly name: "QueryWorkStat",
      readonly I: typeof QueryWorkStatReq,
      readonly O: typeof QueryWorkStatRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批量查询作品点赞和评论总数
     *
     * @generated from rpc step.raccoon.comment.Internal.BatchQueryWorkStat
     */
    readonly batchQueryWorkStat: {
      readonly name: "BatchQueryWorkStat",
      readonly I: typeof BatchQueryWorkStatReq,
      readonly O: typeof BatchQueryWorkStatRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取用户点赞作品的数据
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryUserLikeWorks
     */
    readonly queryUserLikeWorks: {
      readonly name: "QueryUserLikeWorks",
      readonly I: typeof QueryUserLikeWorksReq,
      readonly O: typeof QueryUserLikeWorksRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取该作品被多个用户点赞的状态
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryWorkLikedByUsers
     */
    readonly queryWorkLikedByUsers: {
      readonly name: "QueryWorkLikedByUsers",
      readonly I: typeof QueryWorkLikedByUsersReq,
      readonly O: typeof QueryWorkLikedByUsersRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取该作品被多个用户评论的状态
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryWorkCommentedByUsers
     */
    readonly queryWorkCommentedByUsers: {
      readonly name: "QueryWorkCommentedByUsers",
      readonly I: typeof QueryWorkCommentedByUsersReq,
      readonly O: typeof QueryWorkCommentedByUsersRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取用户总的被点赞数
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryUserTotalLikes
     */
    readonly queryUserTotalLikes: {
      readonly name: "QueryUserTotalLikes",
      readonly I: typeof QueryUserTotalLikesReq,
      readonly O: typeof QueryUserTotalLikesRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 用户作品被其他用户二创统计+1
     *
     * @generated from rpc step.raccoon.comment.Internal.IncUserBeCopieds
     */
    readonly incUserBeCopieds: {
      readonly name: "IncUserBeCopieds",
      readonly I: typeof IncUserBeCopiedsReq,
      readonly O: typeof IncUserBeCopiedsRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批量获取用户对评论的点赞状态
     *
     * @generated from rpc step.raccoon.comment.Internal.BatchQueryCommentLikeStates
     */
    readonly batchQueryCommentLikeStates: {
      readonly name: "BatchQueryCommentLikeStates",
      readonly I: typeof BatchQueryCommentLikeStatesReq,
      readonly O: typeof BatchQueryCommentLikeStatesRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询单个评论
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryCommentById
     */
    readonly queryCommentById: {
      readonly name: "QueryCommentById",
      readonly I: typeof QueryCommentByIdReq,
      readonly O: typeof QueryCommentByIdRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批量查询评论
     *
     * @generated from rpc step.raccoon.comment.Internal.BatchQueryComment
     */
    readonly batchQueryComment: {
      readonly name: "BatchQueryComment",
      readonly I: typeof BatchQueryCommentReq,
      readonly O: typeof BatchQueryCommentRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询单个用户拍同款统计信息
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryUserCopyInfo
     */
    readonly queryUserCopyInfo: {
      readonly name: "QueryUserCopyInfo",
      readonly I: typeof QueryUserCopyInfoReq,
      readonly O: typeof QueryUserCopyInfoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询单个作品拍同款统计信息
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryCardCopyInfo
     */
    readonly queryCardCopyInfo: {
      readonly name: "QueryCardCopyInfo",
      readonly I: typeof QueryCardCopyInfoReq,
      readonly O: typeof QueryCardCopyInfoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取热门评论 
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryCardTopComment
     */
    readonly queryCardTopComment: {
      readonly name: "QueryCardTopComment",
      readonly I: typeof QueryCardTopCommentReq,
      readonly O: typeof QueryCardTopCommentRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询评论点赞的用户id
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryCommentLikeUids
     */
    readonly queryCommentLikeUids: {
      readonly name: "QueryCommentLikeUids",
      readonly I: typeof QueryCommentLikeUidsReq,
      readonly O: typeof QueryCommentLikeUidsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询用户历史评论数
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryUserCommentCnt
     */
    readonly queryUserCommentCnt: {
      readonly name: "QueryUserCommentCnt",
      readonly I: typeof QueryUserCommentCntReq,
      readonly O: typeof QueryUserCommentCntRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 刷取最近天数的评论
     *
     * @generated from rpc step.raccoon.comment.Internal.ReScanLatestComment
     */
    readonly reScanLatestComment: {
      readonly name: "ReScanLatestComment",
      readonly I: typeof ReScanLatestCommentReq,
      readonly O: typeof ReScanLatestCommentRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 内部发布评论
     *
     * @generated from rpc step.raccoon.comment.Internal.PublishCommentInternal
     */
    readonly publishCommentInternal: {
      readonly name: "PublishCommentInternal",
      readonly I: typeof PublishCommentInternalReq,
      readonly O: typeof PublishCommentInternalRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询二级评论
     *
     * @generated from rpc step.raccoon.comment.Internal.QuerySubComment
     */
    readonly querySubComment: {
      readonly name: "QuerySubComment",
      readonly I: typeof QuerySubCommentReq,
      readonly O: typeof QuerySubCommentRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 发送拍同款评论
     *
     * @generated from rpc step.raccoon.comment.Internal.SendCopyNoticeComment
     */
    readonly sendCopyNoticeComment: {
      readonly name: "SendCopyNoticeComment",
      readonly I: typeof SendCopyNoticeCommentReq,
      readonly O: typeof SendCopyNoticeCommentRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询评论作品的用户信息
     *
     * @generated from rpc step.raccoon.comment.Internal.QueryWorkCommentUserInfo
     */
    readonly queryWorkCommentUserInfo: {
      readonly name: "QueryWorkCommentUserInfo",
      readonly I: typeof QueryWorkCommentUserInfoReq,
      readonly O: typeof QueryWorkCommentUserInfoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 根据censor_id获取评论id
     *
     * @generated from rpc step.raccoon.comment.Internal.BatchGetCensorIdByCommentId
     */
    readonly batchGetCensorIdByCommentId: {
      readonly name: "BatchGetCensorIdByCommentId",
      readonly I: typeof BatchGetCensorIdByCommentIdReq,
      readonly O: typeof BatchGetCensorIdByCommentIdRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

