// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/comment/comment.proto (package step.raccoon.comment, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { CommentType } from "../common/types_pb.js";
import type { CommentItem } from "../common/comment_pb.js";
import type { Pagination } from "../common/utils_pb.js";

/**
 * @generated from message step.raccoon.comment.PublishCommentReq
 */
export declare class PublishCommentReq extends Message<PublishCommentReq> {
  /**
   * 评论组id
   *
   * @generated from field: string comment_group_id = 1;
   */
  commentGroupId: string;

  /**
   * 评论内容
   *
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * 图片列表
   *
   * @generated from field: repeated string images = 3;
   */
  images: string[];

  /**
   * 一级评论id
   *
   * @generated from field: string parent_comment_id = 4;
   */
  parentCommentId: string;

  /**
   * 回复的评论id，二级评论时回复
   *
   * @generated from field: string replied_comment_id = 5;
   */
  repliedCommentId: string;

  /**
   * 表情包id
   *
   * @generated from field: optional string emoji_id = 6;
   */
  emojiId?: string;

  /**
   * 被@的用户列表
   *
   * @generated from field: repeated step.raccoon.comment.AtUser at_users = 7;
   */
  atUsers: AtUser[];

  /**
   * 评论类型
   *
   * @generated from field: optional step.raccoon.common.CommentType comment_type = 8;
   */
  commentType?: CommentType;

  constructor(data?: PartialMessage<PublishCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.PublishCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishCommentReq;

  static equals(a: PublishCommentReq | PlainMessage<PublishCommentReq> | undefined, b: PublishCommentReq | PlainMessage<PublishCommentReq> | undefined): boolean;
}

/**
 * 被@的用户结构
 *
 * @generated from message step.raccoon.comment.AtUser
 */
export declare class AtUser extends Message<AtUser> {
  /**
   * 用户ID
   *
   * @generated from field: string uid = 1;
   */
  uid: string;

  /**
   * 用户名
   *
   * @generated from field: string user_name = 2;
   */
  userName: string;

  /**
   * 角色id
   *
   * @generated from field: string role_id = 3;
   */
  roleId: string;

  constructor(data?: PartialMessage<AtUser>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.AtUser";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AtUser;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AtUser;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AtUser;

  static equals(a: AtUser | PlainMessage<AtUser> | undefined, b: AtUser | PlainMessage<AtUser> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.PublishCommentRes
 */
export declare class PublishCommentRes extends Message<PublishCommentRes> {
  /**
   * 成功创建的评论id
   *
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  constructor(data?: PartialMessage<PublishCommentRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.PublishCommentRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishCommentRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishCommentRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishCommentRes;

  static equals(a: PublishCommentRes | PlainMessage<PublishCommentRes> | undefined, b: PublishCommentRes | PlainMessage<PublishCommentRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCommentsReq
 */
export declare class QueryCommentsReq extends Message<QueryCommentsReq> {
  /**
   * 评论组id
   *
   * @generated from field: string comment_group_id = 1;
   */
  commentGroupId: string;

  /**
   * 一级评论id，0=获取一级评论列表，非0=获取二级评论列表
   *
   * @generated from field: string parent_comment_id = 2;
   */
  parentCommentId: string;

  /**
   * 上一页的最后一个评论，0=第一条开始取评论
   *
   * @generated from field: string last_comment_id = 3;
   */
  lastCommentId: string;

  /**
   * 获取页码，默认为1
   *
   * @generated from field: int32 page = 4;
   */
  page: number;

  /**
   * 页码大小，默认10
   *
   * @generated from field: int32 size = 5;
   */
  size: number;

  constructor(data?: PartialMessage<QueryCommentsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCommentsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCommentsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCommentsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCommentsReq;

  static equals(a: QueryCommentsReq | PlainMessage<QueryCommentsReq> | undefined, b: QueryCommentsReq | PlainMessage<QueryCommentsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCommentsRes
 */
export declare class QueryCommentsRes extends Message<QueryCommentsRes> {
  /**
   * 评论列表
   *
   * @generated from field: repeated step.raccoon.common.CommentItem comments = 2;
   */
  comments: CommentItem[];

  constructor(data?: PartialMessage<QueryCommentsRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCommentsRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCommentsRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCommentsRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCommentsRes;

  static equals(a: QueryCommentsRes | PlainMessage<QueryCommentsRes> | undefined, b: QueryCommentsRes | PlainMessage<QueryCommentsRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCommentsByCursorReq
 */
export declare class QueryCommentsByCursorReq extends Message<QueryCommentsByCursorReq> {
  /**
   * 作品id
   *
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * 置顶的一级评论 如果有的话需要每一刷都带上来，去重
   *
   * @generated from field: string top_parent_comment_id = 2;
   */
  topParentCommentId: string;

  /**
   * 置顶的二级评论
   *
   * @generated from field: string top_second_comment_id = 3;
   */
  topSecondCommentId: string;

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 7;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QueryCommentsByCursorReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCommentsByCursorReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCommentsByCursorReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCommentsByCursorReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCommentsByCursorReq;

  static equals(a: QueryCommentsByCursorReq | PlainMessage<QueryCommentsByCursorReq> | undefined, b: QueryCommentsByCursorReq | PlainMessage<QueryCommentsByCursorReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QuerySubCommentsByCursorReq
 */
export declare class QuerySubCommentsByCursorReq extends Message<QuerySubCommentsByCursorReq> {
  /**
   * 作品id
   *
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * 一级评论id
   *
   * @generated from field: string parent_comment_id = 2;
   */
  parentCommentId: string;

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 10;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QuerySubCommentsByCursorReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QuerySubCommentsByCursorReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QuerySubCommentsByCursorReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QuerySubCommentsByCursorReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QuerySubCommentsByCursorReq;

  static equals(a: QuerySubCommentsByCursorReq | PlainMessage<QuerySubCommentsByCursorReq> | undefined, b: QuerySubCommentsByCursorReq | PlainMessage<QuerySubCommentsByCursorReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QuerySubCommentsByCursorRes
 */
export declare class QuerySubCommentsByCursorRes extends Message<QuerySubCommentsByCursorRes> {
  /**
   * 评论列表
   *
   * @generated from field: repeated step.raccoon.common.CommentItem comments = 1;
   */
  comments: CommentItem[];

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 2;
   */
  pagination?: Pagination;

  /**
   * 剩余的二级评论数量
   *
   * @generated from field: int32 remaining_cnt = 3;
   */
  remainingCnt: number;

  constructor(data?: PartialMessage<QuerySubCommentsByCursorRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QuerySubCommentsByCursorRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QuerySubCommentsByCursorRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QuerySubCommentsByCursorRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QuerySubCommentsByCursorRes;

  static equals(a: QuerySubCommentsByCursorRes | PlainMessage<QuerySubCommentsByCursorRes> | undefined, b: QuerySubCommentsByCursorRes | PlainMessage<QuerySubCommentsByCursorRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCommentsByCursorRes
 */
export declare class QueryCommentsByCursorRes extends Message<QueryCommentsByCursorRes> {
  /**
   * 评论列表
   *
   * @generated from field: repeated step.raccoon.common.CommentItem comments = 1;
   */
  comments: CommentItem[];

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 2;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QueryCommentsByCursorRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCommentsByCursorRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCommentsByCursorRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCommentsByCursorRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCommentsByCursorRes;

  static equals(a: QueryCommentsByCursorRes | PlainMessage<QueryCommentsByCursorRes> | undefined, b: QueryCommentsByCursorRes | PlainMessage<QueryCommentsByCursorRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.DeleteCommentReq
 */
export declare class DeleteCommentReq extends Message<DeleteCommentReq> {
  /**
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  constructor(data?: PartialMessage<DeleteCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.DeleteCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteCommentReq;

  static equals(a: DeleteCommentReq | PlainMessage<DeleteCommentReq> | undefined, b: DeleteCommentReq | PlainMessage<DeleteCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.LikeCommentReq
 */
export declare class LikeCommentReq extends Message<LikeCommentReq> {
  /**
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  /**
   * true=点赞，false=取消点赞
   *
   * @generated from field: bool like = 2;
   */
  like: boolean;

  constructor(data?: PartialMessage<LikeCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.LikeCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeCommentReq;

  static equals(a: LikeCommentReq | PlainMessage<LikeCommentReq> | undefined, b: LikeCommentReq | PlainMessage<LikeCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.ReportCommentReq
 */
export declare class ReportCommentReq extends Message<ReportCommentReq> {
  /**
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  /**
   * 举报类型
   *
   * @generated from field: string type = 2;
   */
  type: string;

  /**
   * 举报详情
   *
   * @generated from field: string reason = 3;
   */
  reason: string;

  /**
   * 一级评论id
   *
   * @generated from field: string parent_comment_id = 4;
   */
  parentCommentId: string;

  constructor(data?: PartialMessage<ReportCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.ReportCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReportCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReportCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReportCommentReq;

  static equals(a: ReportCommentReq | PlainMessage<ReportCommentReq> | undefined, b: ReportCommentReq | PlainMessage<ReportCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.LikeWorkReq
 */
export declare class LikeWorkReq extends Message<LikeWorkReq> {
  /**
   * 作品id
   *
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * true=点赞，false=取消点赞
   *
   * @generated from field: bool like = 2;
   */
  like: boolean;

  constructor(data?: PartialMessage<LikeWorkReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.LikeWorkReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeWorkReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeWorkReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeWorkReq;

  static equals(a: LikeWorkReq | PlainMessage<LikeWorkReq> | undefined, b: LikeWorkReq | PlainMessage<LikeWorkReq> | undefined): boolean;
}

