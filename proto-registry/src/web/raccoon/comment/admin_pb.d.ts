// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/comment/admin.proto (package step.raccoon.comment, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Pagination } from "../common/utils_pb.js";
import type { CensoredState } from "../common/state_pb.js";
import type { CommentType } from "../common/types_pb.js";

/**
 * @generated from enum step.raccoon.comment.BotCommentState
 */
export declare enum BotCommentState {
  /**
   * 待审核
   *
   * @generated from enum value: BOT_COMMENT_STATE_UN_PUBLISH = 0;
   */
  UN_PUBLISH = 0,

  /**
   * 审核完成，发布中 （待发布）
   *
   * @generated from enum value: BOT_COMMENT_STATE_PUBLISHING = 1;
   */
  PUBLISHING = 1,

  /**
   * 已发布
   *
   * @generated from enum value: BOT_COMMENT_STATE_ALREADY_PUBLISH = 2;
   */
  ALREADY_PUBLISH = 2,
}

/**
 * @generated from message step.raccoon.comment.DoGenerateBotCommentReq
 */
export declare class DoGenerateBotCommentReq extends Message<DoGenerateBotCommentReq> {
  /**
   * @generated from field: int64 start_time_sec = 1;
   */
  startTimeSec: bigint;

  /**
   * @generated from field: int64 end_time_sec = 2;
   */
  endTimeSec: bigint;

  /**
   * @generated from field: repeated int64 card_id = 3;
   */
  cardId: bigint[];

  constructor(data?: PartialMessage<DoGenerateBotCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.DoGenerateBotCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DoGenerateBotCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DoGenerateBotCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DoGenerateBotCommentReq;

  static equals(a: DoGenerateBotCommentReq | PlainMessage<DoGenerateBotCommentReq> | undefined, b: DoGenerateBotCommentReq | PlainMessage<DoGenerateBotCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.DoGenerateBotCommentRsp
 */
export declare class DoGenerateBotCommentRsp extends Message<DoGenerateBotCommentRsp> {
  constructor(data?: PartialMessage<DoGenerateBotCommentRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.DoGenerateBotCommentRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DoGenerateBotCommentRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DoGenerateBotCommentRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DoGenerateBotCommentRsp;

  static equals(a: DoGenerateBotCommentRsp | PlainMessage<DoGenerateBotCommentRsp> | undefined, b: DoGenerateBotCommentRsp | PlainMessage<DoGenerateBotCommentRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.AddWorkLikeStatReq
 */
export declare class AddWorkLikeStatReq extends Message<AddWorkLikeStatReq> {
  /**
   * 内容id
   *
   * @generated from field: string cardId = 1;
   */
  cardId: string;

  /**
   * 点赞的数量
   *
   * @generated from field: int32 likeCnt = 2;
   */
  likeCnt: number;

  constructor(data?: PartialMessage<AddWorkLikeStatReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.AddWorkLikeStatReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddWorkLikeStatReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddWorkLikeStatReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddWorkLikeStatReq;

  static equals(a: AddWorkLikeStatReq | PlainMessage<AddWorkLikeStatReq> | undefined, b: AddWorkLikeStatReq | PlainMessage<AddWorkLikeStatReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.AddWorkLikeStatRsp
 */
export declare class AddWorkLikeStatRsp extends Message<AddWorkLikeStatRsp> {
  constructor(data?: PartialMessage<AddWorkLikeStatRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.AddWorkLikeStatRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddWorkLikeStatRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddWorkLikeStatRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddWorkLikeStatRsp;

  static equals(a: AddWorkLikeStatRsp | PlainMessage<AddWorkLikeStatRsp> | undefined, b: AddWorkLikeStatRsp | PlainMessage<AddWorkLikeStatRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.BatchAddWorkLikeStatReq
 */
export declare class BatchAddWorkLikeStatReq extends Message<BatchAddWorkLikeStatReq> {
  /**
   * @generated from field: string file_key = 1;
   */
  fileKey: string;

  constructor(data?: PartialMessage<BatchAddWorkLikeStatReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchAddWorkLikeStatReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchAddWorkLikeStatReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchAddWorkLikeStatReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchAddWorkLikeStatReq;

  static equals(a: BatchAddWorkLikeStatReq | PlainMessage<BatchAddWorkLikeStatReq> | undefined, b: BatchAddWorkLikeStatReq | PlainMessage<BatchAddWorkLikeStatReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.BatchAddWorkLikeStatRsp
 */
export declare class BatchAddWorkLikeStatRsp extends Message<BatchAddWorkLikeStatRsp> {
  /**
   * @generated from field: repeated string failed_card_id_list = 1;
   */
  failedCardIdList: string[];

  constructor(data?: PartialMessage<BatchAddWorkLikeStatRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchAddWorkLikeStatRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchAddWorkLikeStatRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchAddWorkLikeStatRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchAddWorkLikeStatRsp;

  static equals(a: BatchAddWorkLikeStatRsp | PlainMessage<BatchAddWorkLikeStatRsp> | undefined, b: BatchAddWorkLikeStatRsp | PlainMessage<BatchAddWorkLikeStatRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.GetAdminCommentsReq
 */
export declare class GetAdminCommentsReq extends Message<GetAdminCommentsReq> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  /**
   * @generated from field: step.raccoon.comment.GetAdminCommentsQueryParam search = 2;
   */
  search?: GetAdminCommentsQueryParam;

  constructor(data?: PartialMessage<GetAdminCommentsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.GetAdminCommentsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAdminCommentsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAdminCommentsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAdminCommentsReq;

  static equals(a: GetAdminCommentsReq | PlainMessage<GetAdminCommentsReq> | undefined, b: GetAdminCommentsReq | PlainMessage<GetAdminCommentsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.GetAdminSubCommentsReq
 */
export declare class GetAdminSubCommentsReq extends Message<GetAdminSubCommentsReq> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  /**
   * 一级评论id
   *
   * @generated from field: string parent_comment_id = 2;
   */
  parentCommentId: string;

  constructor(data?: PartialMessage<GetAdminSubCommentsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.GetAdminSubCommentsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAdminSubCommentsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAdminSubCommentsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAdminSubCommentsReq;

  static equals(a: GetAdminSubCommentsReq | PlainMessage<GetAdminSubCommentsReq> | undefined, b: GetAdminSubCommentsReq | PlainMessage<GetAdminSubCommentsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.GetAdminCommentsQueryParam
 */
export declare class GetAdminCommentsQueryParam extends Message<GetAdminCommentsQueryParam> {
  /**
   * 评论的卡片id
   *
   * @generated from field: optional string card_id = 1;
   */
  cardId?: string;

  /**
   * 评论内容
   *
   * @generated from field: optional string comment_content = 2;
   */
  commentContent?: string;

  /**
   * 用户id或用户名
   *
   * @generated from field: optional string user = 3;
   */
  user?: string;

  constructor(data?: PartialMessage<GetAdminCommentsQueryParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.GetAdminCommentsQueryParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAdminCommentsQueryParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAdminCommentsQueryParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAdminCommentsQueryParam;

  static equals(a: GetAdminCommentsQueryParam | PlainMessage<GetAdminCommentsQueryParam> | undefined, b: GetAdminCommentsQueryParam | PlainMessage<GetAdminCommentsQueryParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.GetAdminCommentsRsp
 */
export declare class GetAdminCommentsRsp extends Message<GetAdminCommentsRsp> {
  /**
   * @generated from field: int64 total = 1;
   */
  total: bigint;

  /**
   * @generated from field: string next_cursor = 2;
   */
  nextCursor: string;

  /**
   * @generated from field: repeated step.raccoon.comment.AdminCommentInfo card_list = 3;
   */
  cardList: AdminCommentInfo[];

  constructor(data?: PartialMessage<GetAdminCommentsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.GetAdminCommentsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAdminCommentsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAdminCommentsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAdminCommentsRsp;

  static equals(a: GetAdminCommentsRsp | PlainMessage<GetAdminCommentsRsp> | undefined, b: GetAdminCommentsRsp | PlainMessage<GetAdminCommentsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.GetAdminSubCommentsRsp
 */
export declare class GetAdminSubCommentsRsp extends Message<GetAdminSubCommentsRsp> {
  /**
   * @generated from field: int64 total = 1;
   */
  total: bigint;

  /**
   * @generated from field: string next_cursor = 2;
   */
  nextCursor: string;

  /**
   * @generated from field: repeated step.raccoon.comment.AdminCommentInfo comment_list = 3;
   */
  commentList: AdminCommentInfo[];

  constructor(data?: PartialMessage<GetAdminSubCommentsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.GetAdminSubCommentsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAdminSubCommentsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAdminSubCommentsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAdminSubCommentsRsp;

  static equals(a: GetAdminSubCommentsRsp | PlainMessage<GetAdminSubCommentsRsp> | undefined, b: GetAdminSubCommentsRsp | PlainMessage<GetAdminSubCommentsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.AdminCommentInfo
 */
export declare class AdminCommentInfo extends Message<AdminCommentInfo> {
  /**
   * 评论id
   *
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  /**
   * 评论内容
   *
   * @generated from field: string comment_content = 2;
   */
  commentContent: string;

  /**
   * 内容id
   *
   * @generated from field: string card_id = 3;
   */
  cardId: string;

  /**
   * 内容title
   *
   * @generated from field: string card_title = 4;
   */
  cardTitle: string;

  /**
   * 点赞数
   *
   * @generated from field: int64 like_cnt = 5;
   */
  likeCnt: bigint;

  /**
   * 发布时间
   *
   * @generated from field: int64 publish_at = 6;
   */
  publishAt: bigint;

  /**
   * 审核状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored_state = 7;
   */
  censoredState: CensoredState;

  /**
   * 删除状态
   *
   * @generated from field: bool deleted = 8;
   */
  deleted: boolean;

  /**
   * 用户名
   *
   * @generated from field: string user_name = 9;
   */
  userName: string;

  /**
   * 用户id
   *
   * @generated from field: string uid = 10;
   */
  uid: string;

  /**
   * 总评论数量
   *
   * @generated from field: int64 total_comments = 11;
   */
  totalComments: bigint;

  /**
   * 回复的评论
   *
   * @generated from field: step.raccoon.comment.AdminCommentInfo replied_comment = 12;
   */
  repliedComment?: AdminCommentInfo;

  /**
   * 表情包图片链接
   *
   * @generated from field: string emoji_url = 13;
   */
  emojiUrl: string;

  /**
   * 评论类型
   *
   * @generated from field: step.raccoon.common.CommentType comment_type = 14;
   */
  commentType: CommentType;

  constructor(data?: PartialMessage<AdminCommentInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.AdminCommentInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminCommentInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminCommentInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminCommentInfo;

  static equals(a: AdminCommentInfo | PlainMessage<AdminCommentInfo> | undefined, b: AdminCommentInfo | PlainMessage<AdminCommentInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.GetAdminBotCommentsReq
 */
export declare class GetAdminBotCommentsReq extends Message<GetAdminBotCommentsReq> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  /**
   * @generated from field: step.raccoon.comment.GetAdminBotCommentsQueryParam search = 2;
   */
  search?: GetAdminBotCommentsQueryParam;

  constructor(data?: PartialMessage<GetAdminBotCommentsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.GetAdminBotCommentsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAdminBotCommentsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAdminBotCommentsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAdminBotCommentsReq;

  static equals(a: GetAdminBotCommentsReq | PlainMessage<GetAdminBotCommentsReq> | undefined, b: GetAdminBotCommentsReq | PlainMessage<GetAdminBotCommentsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.GetAdminBotCommentsQueryParam
 */
export declare class GetAdminBotCommentsQueryParam extends Message<GetAdminBotCommentsQueryParam> {
  /**
   * 评论的卡片id 必传
   *
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  constructor(data?: PartialMessage<GetAdminBotCommentsQueryParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.GetAdminBotCommentsQueryParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAdminBotCommentsQueryParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAdminBotCommentsQueryParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAdminBotCommentsQueryParam;

  static equals(a: GetAdminBotCommentsQueryParam | PlainMessage<GetAdminBotCommentsQueryParam> | undefined, b: GetAdminBotCommentsQueryParam | PlainMessage<GetAdminBotCommentsQueryParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.GetAdminBotCommentsRsp
 */
export declare class GetAdminBotCommentsRsp extends Message<GetAdminBotCommentsRsp> {
  /**
   * @generated from field: int64 total = 1;
   */
  total: bigint;

  /**
   * @generated from field: string next_cursor = 2;
   */
  nextCursor: string;

  /**
   * @generated from field: repeated step.raccoon.comment.AdminBotCommentInfo card_list = 3;
   */
  cardList: AdminBotCommentInfo[];

  constructor(data?: PartialMessage<GetAdminBotCommentsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.GetAdminBotCommentsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAdminBotCommentsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAdminBotCommentsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAdminBotCommentsRsp;

  static equals(a: GetAdminBotCommentsRsp | PlainMessage<GetAdminBotCommentsRsp> | undefined, b: GetAdminBotCommentsRsp | PlainMessage<GetAdminBotCommentsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.AdminBotCommentInfo
 */
export declare class AdminBotCommentInfo extends Message<AdminBotCommentInfo> {
  /**
   * 评论id
   *
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  /**
   * 水军评论内容
   *
   * @generated from field: string comment_content = 2;
   */
  commentContent: string;

  /**
   * 水军评论状态
   *
   * @generated from field: step.raccoon.comment.BotCommentState comment_state = 3;
   */
  commentState: BotCommentState;

  /**
   * 关联内容id
   *
   * @generated from field: string card_id = 4;
   */
  cardId: string;

  /**
   * 关联内容title
   *
   * @generated from field: string card_title = 5;
   */
  cardTitle: string;

  /**
   * 机器人类型名
   *
   * @generated from field: string bot_type_name = 6;
   */
  botTypeName: string;

  constructor(data?: PartialMessage<AdminBotCommentInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.AdminBotCommentInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminBotCommentInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminBotCommentInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminBotCommentInfo;

  static equals(a: AdminBotCommentInfo | PlainMessage<AdminBotCommentInfo> | undefined, b: AdminBotCommentInfo | PlainMessage<AdminBotCommentInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.CommentOperateFailInfo
 */
export declare class CommentOperateFailInfo extends Message<CommentOperateFailInfo> {
  /**
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  /**
   * @generated from field: string fail_reason = 2;
   */
  failReason: string;

  constructor(data?: PartialMessage<CommentOperateFailInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.CommentOperateFailInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CommentOperateFailInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CommentOperateFailInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CommentOperateFailInfo;

  static equals(a: CommentOperateFailInfo | PlainMessage<CommentOperateFailInfo> | undefined, b: CommentOperateFailInfo | PlainMessage<CommentOperateFailInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.DeleteAdminUserCommentReq
 */
export declare class DeleteAdminUserCommentReq extends Message<DeleteAdminUserCommentReq> {
  /**
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  /**
   * @generated from field: string remark = 2;
   */
  remark: string;

  constructor(data?: PartialMessage<DeleteAdminUserCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.DeleteAdminUserCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAdminUserCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAdminUserCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAdminUserCommentReq;

  static equals(a: DeleteAdminUserCommentReq | PlainMessage<DeleteAdminUserCommentReq> | undefined, b: DeleteAdminUserCommentReq | PlainMessage<DeleteAdminUserCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.DeleteAdminUserCommentRsp
 */
export declare class DeleteAdminUserCommentRsp extends Message<DeleteAdminUserCommentRsp> {
  /**
   * @generated from field: repeated step.raccoon.comment.CommentOperateFailInfo fail_list = 1;
   */
  failList: CommentOperateFailInfo[];

  constructor(data?: PartialMessage<DeleteAdminUserCommentRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.DeleteAdminUserCommentRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAdminUserCommentRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAdminUserCommentRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAdminUserCommentRsp;

  static equals(a: DeleteAdminUserCommentRsp | PlainMessage<DeleteAdminUserCommentRsp> | undefined, b: DeleteAdminUserCommentRsp | PlainMessage<DeleteAdminUserCommentRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.OnlineAdminUserCommentReq
 */
export declare class OnlineAdminUserCommentReq extends Message<OnlineAdminUserCommentReq> {
  /**
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  /**
   * @generated from field: string remark = 2;
   */
  remark: string;

  constructor(data?: PartialMessage<OnlineAdminUserCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.OnlineAdminUserCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnlineAdminUserCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnlineAdminUserCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnlineAdminUserCommentReq;

  static equals(a: OnlineAdminUserCommentReq | PlainMessage<OnlineAdminUserCommentReq> | undefined, b: OnlineAdminUserCommentReq | PlainMessage<OnlineAdminUserCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.OnlineAdminUserCommentRsp
 */
export declare class OnlineAdminUserCommentRsp extends Message<OnlineAdminUserCommentRsp> {
  /**
   * @generated from field: repeated step.raccoon.comment.CommentOperateFailInfo fail_list = 1;
   */
  failList: CommentOperateFailInfo[];

  constructor(data?: PartialMessage<OnlineAdminUserCommentRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.OnlineAdminUserCommentRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnlineAdminUserCommentRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnlineAdminUserCommentRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnlineAdminUserCommentRsp;

  static equals(a: OnlineAdminUserCommentRsp | PlainMessage<OnlineAdminUserCommentRsp> | undefined, b: OnlineAdminUserCommentRsp | PlainMessage<OnlineAdminUserCommentRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.PublishAdminBotCommentsReq
 */
export declare class PublishAdminBotCommentsReq extends Message<PublishAdminBotCommentsReq> {
  /**
   * @generated from field: repeated string comment_ids = 1;
   */
  commentIds: string[];

  constructor(data?: PartialMessage<PublishAdminBotCommentsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.PublishAdminBotCommentsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishAdminBotCommentsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishAdminBotCommentsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishAdminBotCommentsReq;

  static equals(a: PublishAdminBotCommentsReq | PlainMessage<PublishAdminBotCommentsReq> | undefined, b: PublishAdminBotCommentsReq | PlainMessage<PublishAdminBotCommentsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.PublishAdminBotCommentsRsp
 */
export declare class PublishAdminBotCommentsRsp extends Message<PublishAdminBotCommentsRsp> {
  /**
   * @generated from field: repeated step.raccoon.comment.CommentOperateFailInfo fail_list = 1;
   */
  failList: CommentOperateFailInfo[];

  constructor(data?: PartialMessage<PublishAdminBotCommentsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.PublishAdminBotCommentsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishAdminBotCommentsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishAdminBotCommentsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishAdminBotCommentsRsp;

  static equals(a: PublishAdminBotCommentsRsp | PlainMessage<PublishAdminBotCommentsRsp> | undefined, b: PublishAdminBotCommentsRsp | PlainMessage<PublishAdminBotCommentsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.PullBotCommentPublishRecordReq
 */
export declare class PullBotCommentPublishRecordReq extends Message<PullBotCommentPublishRecordReq> {
  constructor(data?: PartialMessage<PullBotCommentPublishRecordReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.PullBotCommentPublishRecordReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PullBotCommentPublishRecordReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PullBotCommentPublishRecordReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PullBotCommentPublishRecordReq;

  static equals(a: PullBotCommentPublishRecordReq | PlainMessage<PullBotCommentPublishRecordReq> | undefined, b: PullBotCommentPublishRecordReq | PlainMessage<PullBotCommentPublishRecordReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.PullBotCommentPublishRecordRsp
 */
export declare class PullBotCommentPublishRecordRsp extends Message<PullBotCommentPublishRecordRsp> {
  constructor(data?: PartialMessage<PullBotCommentPublishRecordRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.PullBotCommentPublishRecordRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PullBotCommentPublishRecordRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PullBotCommentPublishRecordRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PullBotCommentPublishRecordRsp;

  static equals(a: PullBotCommentPublishRecordRsp | PlainMessage<PullBotCommentPublishRecordRsp> | undefined, b: PullBotCommentPublishRecordRsp | PlainMessage<PullBotCommentPublishRecordRsp> | undefined): boolean;
}

