// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/comment/admin.proto (package step.raccoon.comment, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AddWorkLikeStatReq, AddWorkLikeStatRsp, BatchAddWorkLikeStatReq, BatchAddWorkLikeStatRsp, DeleteAdminUserCommentReq, DeleteAdminUserCommentRsp, DoGenerateBotCommentReq, DoGenerateBotCommentRsp, GetAdminBotCommentsReq, GetAdminBotCommentsRsp, GetAdminCommentsReq, GetAdminCommentsRsp, GetAdminSubCommentsReq, GetAdminSubCommentsRsp, OnlineAdminUserCommentReq, OnlineAdminUserCommentRsp, PublishAdminBotCommentsReq, PublishAdminBotCommentsRsp, PullBotCommentPublishRecordReq, PullBotCommentPublishRecordRsp } from "./admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.comment.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.comment.Admin",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.comment.Admin.GetAdminComments
     */
    readonly getAdminComments: {
      readonly name: "GetAdminComments",
      readonly I: typeof GetAdminCommentsReq,
      readonly O: typeof GetAdminCommentsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.comment.Admin.GetAdminBotComments
     */
    readonly getAdminBotComments: {
      readonly name: "GetAdminBotComments",
      readonly I: typeof GetAdminBotCommentsReq,
      readonly O: typeof GetAdminBotCommentsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.comment.Admin.DeleteAdminUserComment
     */
    readonly deleteAdminUserComment: {
      readonly name: "DeleteAdminUserComment",
      readonly I: typeof DeleteAdminUserCommentReq,
      readonly O: typeof DeleteAdminUserCommentRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.comment.Admin.OnlineAdminUserComment
     */
    readonly onlineAdminUserComment: {
      readonly name: "OnlineAdminUserComment",
      readonly I: typeof OnlineAdminUserCommentReq,
      readonly O: typeof OnlineAdminUserCommentRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.comment.Admin.PublishAdminBotComments
     */
    readonly publishAdminBotComments: {
      readonly name: "PublishAdminBotComments",
      readonly I: typeof PublishAdminBotCommentsReq,
      readonly O: typeof PublishAdminBotCommentsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.comment.Admin.AddWorkLikeStat
     */
    readonly addWorkLikeStat: {
      readonly name: "AddWorkLikeStat",
      readonly I: typeof AddWorkLikeStatReq,
      readonly O: typeof AddWorkLikeStatRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.comment.Admin.BatchAddWorkLikeStat
     */
    readonly batchAddWorkLikeStat: {
      readonly name: "BatchAddWorkLikeStat",
      readonly I: typeof BatchAddWorkLikeStatReq,
      readonly O: typeof BatchAddWorkLikeStatRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.comment.Admin.DoGenerateBotComment
     */
    readonly doGenerateBotComment: {
      readonly name: "DoGenerateBotComment",
      readonly I: typeof DoGenerateBotCommentReq,
      readonly O: typeof DoGenerateBotCommentRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.comment.Admin.GetAdminSubComments
     */
    readonly getAdminSubComments: {
      readonly name: "GetAdminSubComments",
      readonly I: typeof GetAdminSubCommentsReq,
      readonly O: typeof GetAdminSubCommentsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.comment.Admin.PullBotCommentPublishRecord
     */
    readonly pullBotCommentPublishRecord: {
      readonly name: "PullBotCommentPublishRecord",
      readonly I: typeof PullBotCommentPublishRecordReq,
      readonly O: typeof PullBotCommentPublishRecordRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

