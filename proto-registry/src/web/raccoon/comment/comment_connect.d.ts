// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/comment/comment.proto (package step.raccoon.comment, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { DeleteCommentReq, LikeCommentReq, LikeWorkReq, PublishCommentReq, PublishCommentRes, QueryCommentsByCursorReq, QueryCommentsByCursorRes, QueryCommentsReq, QueryCommentsRes, QuerySubCommentsByCursorReq, QuerySubCommentsByCursorRes, ReportCommentReq } from "./comment_pb.js";
import { Empty, MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.comment.Comment
 */
export declare const Comment: {
  readonly typeName: "step.raccoon.comment.Comment",
  readonly methods: {
    /**
     * 发布/回复评论
     *
     * @generated from rpc step.raccoon.comment.Comment.PublishComment
     */
    readonly publishComment: {
      readonly name: "PublishComment",
      readonly I: typeof PublishCommentReq,
      readonly O: typeof PublishCommentRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取评论列表
     *
     * @generated from rpc step.raccoon.comment.Comment.QueryComments
     */
    readonly queryComments: {
      readonly name: "QueryComments",
      readonly I: typeof QueryCommentsReq,
      readonly O: typeof QueryCommentsRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取评论列表v2
     *
     * @generated from rpc step.raccoon.comment.Comment.QueryCommentsByCursor
     */
    readonly queryCommentsByCursor: {
      readonly name: "QueryCommentsByCursor",
      readonly I: typeof QueryCommentsByCursorReq,
      readonly O: typeof QueryCommentsByCursorRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 删除个人评论
     *
     * @generated from rpc step.raccoon.comment.Comment.DeleteComment
     */
    readonly deleteComment: {
      readonly name: "DeleteComment",
      readonly I: typeof DeleteCommentReq,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 点赞/取消点赞评论
     *
     * @generated from rpc step.raccoon.comment.Comment.LikeComment
     */
    readonly likeComment: {
      readonly name: "LikeComment",
      readonly I: typeof LikeCommentReq,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 举报评论
     *
     * @generated from rpc step.raccoon.comment.Comment.ReportComment
     */
    readonly reportComment: {
      readonly name: "ReportComment",
      readonly I: typeof ReportCommentReq,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 点赞作品
     *
     * @generated from rpc step.raccoon.comment.Comment.LikeWork
     */
    readonly likeWork: {
      readonly name: "LikeWork",
      readonly I: typeof LikeWorkReq,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取二级评论列表
     *
     * @generated from rpc step.raccoon.comment.Comment.QuerySubCommentsByCursor
     */
    readonly querySubCommentsByCursor: {
      readonly name: "QuerySubCommentsByCursor",
      readonly I: typeof QuerySubCommentsByCursorReq,
      readonly O: typeof QuerySubCommentsByCursorRes,
      readonly kind: MethodKind.Unary,
    },
  }
};

