// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/comment/internal.proto (package step.raccoon.comment, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { CommentType, GameType } from "../common/types_pb.js";
import type { Pagination } from "../common/utils_pb.js";
import type { CommentItem } from "../common/comment_pb.js";
import type { GameTypeCopyCount } from "../common/stat_pb.js";
import type { CensoredState } from "../common/state_pb.js";

/**
 * @generated from message step.raccoon.comment.BatchGetCensorIdByCommentIdReq
 */
export declare class BatchGetCensorIdByCommentIdReq extends Message<BatchGetCensorIdByCommentIdReq> {
  /**
   * @generated from field: repeated int64 comment_ids = 1;
   */
  commentIds: bigint[];

  constructor(data?: PartialMessage<BatchGetCensorIdByCommentIdReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchGetCensorIdByCommentIdReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchGetCensorIdByCommentIdReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchGetCensorIdByCommentIdReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchGetCensorIdByCommentIdReq;

  static equals(a: BatchGetCensorIdByCommentIdReq | PlainMessage<BatchGetCensorIdByCommentIdReq> | undefined, b: BatchGetCensorIdByCommentIdReq | PlainMessage<BatchGetCensorIdByCommentIdReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.BatchGetCensorIdByCommentIdRsp
 */
export declare class BatchGetCensorIdByCommentIdRsp extends Message<BatchGetCensorIdByCommentIdRsp> {
  /**
   * key: comment_id,value: censor_id
   *
   * @generated from field: map<int64, string> censor_ids = 2;
   */
  censorIds: { [key: string]: string };

  constructor(data?: PartialMessage<BatchGetCensorIdByCommentIdRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchGetCensorIdByCommentIdRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchGetCensorIdByCommentIdRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchGetCensorIdByCommentIdRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchGetCensorIdByCommentIdRsp;

  static equals(a: BatchGetCensorIdByCommentIdRsp | PlainMessage<BatchGetCensorIdByCommentIdRsp> | undefined, b: BatchGetCensorIdByCommentIdRsp | PlainMessage<BatchGetCensorIdByCommentIdRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.CommentUserInfo
 */
export declare class CommentUserInfo extends Message<CommentUserInfo> {
  /**
   * 评论了作品的用户id，最多只返回10个
   *
   * @generated from field: repeated int64 comment_uids = 1;
   */
  commentUids: bigint[];

  constructor(data?: PartialMessage<CommentUserInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.CommentUserInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CommentUserInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CommentUserInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CommentUserInfo;

  static equals(a: CommentUserInfo | PlainMessage<CommentUserInfo> | undefined, b: CommentUserInfo | PlainMessage<CommentUserInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryWorkCommentUserInfoReq
 */
export declare class QueryWorkCommentUserInfoReq extends Message<QueryWorkCommentUserInfoReq> {
  /**
   * @generated from field: repeated int64 card_ids = 1;
   */
  cardIds: bigint[];

  /**
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  constructor(data?: PartialMessage<QueryWorkCommentUserInfoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryWorkCommentUserInfoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryWorkCommentUserInfoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryWorkCommentUserInfoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryWorkCommentUserInfoReq;

  static equals(a: QueryWorkCommentUserInfoReq | PlainMessage<QueryWorkCommentUserInfoReq> | undefined, b: QueryWorkCommentUserInfoReq | PlainMessage<QueryWorkCommentUserInfoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryWorkCommentUserInfoRsp
 */
export declare class QueryWorkCommentUserInfoRsp extends Message<QueryWorkCommentUserInfoRsp> {
  /**
   * @generated from field: map<int64, step.raccoon.comment.CommentUserInfo> user_info = 1;
   */
  userInfo: { [key: string]: CommentUserInfo };

  constructor(data?: PartialMessage<QueryWorkCommentUserInfoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryWorkCommentUserInfoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryWorkCommentUserInfoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryWorkCommentUserInfoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryWorkCommentUserInfoRsp;

  static equals(a: QueryWorkCommentUserInfoRsp | PlainMessage<QueryWorkCommentUserInfoRsp> | undefined, b: QueryWorkCommentUserInfoRsp | PlainMessage<QueryWorkCommentUserInfoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.SendCopyNoticeCommentReq
 */
export declare class SendCopyNoticeCommentReq extends Message<SendCopyNoticeCommentReq> {
  /**
   * 创作者uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 二创用户id
   *
   * @generated from field: int64 copied_uid = 2;
   */
  copiedUid: bigint;

  /**
   * 玩法类型
   *
   * @generated from field: step.raccoon.common.GameType game = 3;
   */
  game: GameType;

  /**
   * 源作品id
   *
   * @generated from field: int64 business_id = 4;
   */
  businessId: bigint;

  /**
   * 拍同款的作品id
   *
   * @generated from field: int64 copied_business_id = 5;
   */
  copiedBusinessId: bigint;

  constructor(data?: PartialMessage<SendCopyNoticeCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.SendCopyNoticeCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendCopyNoticeCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendCopyNoticeCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendCopyNoticeCommentReq;

  static equals(a: SendCopyNoticeCommentReq | PlainMessage<SendCopyNoticeCommentReq> | undefined, b: SendCopyNoticeCommentReq | PlainMessage<SendCopyNoticeCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.SendCopyNoticeCommentRsp
 */
export declare class SendCopyNoticeCommentRsp extends Message<SendCopyNoticeCommentRsp> {
  constructor(data?: PartialMessage<SendCopyNoticeCommentRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.SendCopyNoticeCommentRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendCopyNoticeCommentRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendCopyNoticeCommentRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendCopyNoticeCommentRsp;

  static equals(a: SendCopyNoticeCommentRsp | PlainMessage<SendCopyNoticeCommentRsp> | undefined, b: SendCopyNoticeCommentRsp | PlainMessage<SendCopyNoticeCommentRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QuerySubCommentReq
 */
export declare class QuerySubCommentReq extends Message<QuerySubCommentReq> {
  /**
   * 作品id
   *
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * 一级评论id
   *
   * @generated from field: int64 parent_comment_id = 2;
   */
  parentCommentId: bigint;

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 3;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QuerySubCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QuerySubCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QuerySubCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QuerySubCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QuerySubCommentReq;

  static equals(a: QuerySubCommentReq | PlainMessage<QuerySubCommentReq> | undefined, b: QuerySubCommentReq | PlainMessage<QuerySubCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QuerySubCommentRes
 */
export declare class QuerySubCommentRes extends Message<QuerySubCommentRes> {
  /**
   * @generated from field: repeated step.raccoon.common.CommentItem comments = 1;
   */
  comments: CommentItem[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 2;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QuerySubCommentRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QuerySubCommentRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QuerySubCommentRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QuerySubCommentRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QuerySubCommentRes;

  static equals(a: QuerySubCommentRes | PlainMessage<QuerySubCommentRes> | undefined, b: QuerySubCommentRes | PlainMessage<QuerySubCommentRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.PublishCommentInternalReq
 */
export declare class PublishCommentInternalReq extends Message<PublishCommentInternalReq> {
  /**
   * 评论组id
   *
   * @generated from field: string comment_group_id = 1;
   */
  commentGroupId: string;

  /**
   * 评论内容
   *
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * 图片列表
   *
   * @generated from field: repeated string images = 3;
   */
  images: string[];

  /**
   * 一级评论id
   *
   * @generated from field: string parent_comment_id = 4;
   */
  parentCommentId: string;

  /**
   * 回复的评论id，二级评论时回复
   *
   * @generated from field: string replied_comment_id = 5;
   */
  repliedCommentId: string;

  /**
   * 表情包id
   *
   * @generated from field: optional string emoji_id = 6;
   */
  emojiId?: string;

  /**
   * 评论uid
   *
   * @generated from field: int64 comment_uid = 7;
   */
  commentUid: bigint;

  /**
   * 是否机器人
   *
   * @generated from field: bool is_bot = 8;
   */
  isBot: boolean;

  /**
   * 特殊的评论类型
   *
   * @generated from field: step.raccoon.common.CommentType spec_comment_type = 9;
   */
  specCommentType: CommentType;

  constructor(data?: PartialMessage<PublishCommentInternalReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.PublishCommentInternalReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishCommentInternalReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishCommentInternalReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishCommentInternalReq;

  static equals(a: PublishCommentInternalReq | PlainMessage<PublishCommentInternalReq> | undefined, b: PublishCommentInternalReq | PlainMessage<PublishCommentInternalReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.PublishCommentInternalRes
 */
export declare class PublishCommentInternalRes extends Message<PublishCommentInternalRes> {
  /**
   * @generated from field: string comment_id = 1;
   */
  commentId: string;

  constructor(data?: PartialMessage<PublishCommentInternalRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.PublishCommentInternalRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishCommentInternalRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishCommentInternalRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishCommentInternalRes;

  static equals(a: PublishCommentInternalRes | PlainMessage<PublishCommentInternalRes> | undefined, b: PublishCommentInternalRes | PlainMessage<PublishCommentInternalRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.ReScanLatestCommentReq
 */
export declare class ReScanLatestCommentReq extends Message<ReScanLatestCommentReq> {
  /**
   * 天数
   *
   * @generated from field: int64 day = 1;
   */
  day: bigint;

  /**
   * 开始
   *
   * @generated from field: bool begin = 2;
   */
  begin: boolean;

  constructor(data?: PartialMessage<ReScanLatestCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.ReScanLatestCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReScanLatestCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReScanLatestCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReScanLatestCommentReq;

  static equals(a: ReScanLatestCommentReq | PlainMessage<ReScanLatestCommentReq> | undefined, b: ReScanLatestCommentReq | PlainMessage<ReScanLatestCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.ReScanLatestCommentRes
 */
export declare class ReScanLatestCommentRes extends Message<ReScanLatestCommentRes> {
  /**
   * 最后成功的id
   *
   * @generated from field: int64 latest_id = 1;
   */
  latestId: bigint;

  constructor(data?: PartialMessage<ReScanLatestCommentRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.ReScanLatestCommentRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReScanLatestCommentRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReScanLatestCommentRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReScanLatestCommentRes;

  static equals(a: ReScanLatestCommentRes | PlainMessage<ReScanLatestCommentRes> | undefined, b: ReScanLatestCommentRes | PlainMessage<ReScanLatestCommentRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryUserCommentCntReq
 */
export declare class QueryUserCommentCntReq extends Message<QueryUserCommentCntReq> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  constructor(data?: PartialMessage<QueryUserCommentCntReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryUserCommentCntReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserCommentCntReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserCommentCntReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserCommentCntReq;

  static equals(a: QueryUserCommentCntReq | PlainMessage<QueryUserCommentCntReq> | undefined, b: QueryUserCommentCntReq | PlainMessage<QueryUserCommentCntReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryUserCommentCntRsp
 */
export declare class QueryUserCommentCntRsp extends Message<QueryUserCommentCntRsp> {
  /**
   * @generated from field: int64 cnt = 1;
   */
  cnt: bigint;

  constructor(data?: PartialMessage<QueryUserCommentCntRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryUserCommentCntRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserCommentCntRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserCommentCntRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserCommentCntRsp;

  static equals(a: QueryUserCommentCntRsp | PlainMessage<QueryUserCommentCntRsp> | undefined, b: QueryUserCommentCntRsp | PlainMessage<QueryUserCommentCntRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCommentLikeUidsReq
 */
export declare class QueryCommentLikeUidsReq extends Message<QueryCommentLikeUidsReq> {
  /**
   * 评论id
   *
   * @generated from field: int64 comment_id = 1;
   */
  commentId: bigint;

  constructor(data?: PartialMessage<QueryCommentLikeUidsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCommentLikeUidsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCommentLikeUidsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCommentLikeUidsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCommentLikeUidsReq;

  static equals(a: QueryCommentLikeUidsReq | PlainMessage<QueryCommentLikeUidsReq> | undefined, b: QueryCommentLikeUidsReq | PlainMessage<QueryCommentLikeUidsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCommentLikeUidsRsp
 */
export declare class QueryCommentLikeUidsRsp extends Message<QueryCommentLikeUidsRsp> {
  /**
   * @generated from field: repeated int64 uids = 1;
   */
  uids: bigint[];

  constructor(data?: PartialMessage<QueryCommentLikeUidsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCommentLikeUidsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCommentLikeUidsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCommentLikeUidsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCommentLikeUidsRsp;

  static equals(a: QueryCommentLikeUidsRsp | PlainMessage<QueryCommentLikeUidsRsp> | undefined, b: QueryCommentLikeUidsRsp | PlainMessage<QueryCommentLikeUidsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCardTopCommentReq
 */
export declare class QueryCardTopCommentReq extends Message<QueryCardTopCommentReq> {
  /**
   * @generated from field: repeated int64 card_id = 1;
   */
  cardId: bigint[];

  /**
   * 未登录传0
   *
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  constructor(data?: PartialMessage<QueryCardTopCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCardTopCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCardTopCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCardTopCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCardTopCommentReq;

  static equals(a: QueryCardTopCommentReq | PlainMessage<QueryCardTopCommentReq> | undefined, b: QueryCardTopCommentReq | PlainMessage<QueryCardTopCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCardTopCommentRsp
 */
export declare class QueryCardTopCommentRsp extends Message<QueryCardTopCommentRsp> {
  /**
   * @generated from field: map<int64, step.raccoon.common.CommentItem> comments = 1;
   */
  comments: { [key: string]: CommentItem };

  constructor(data?: PartialMessage<QueryCardTopCommentRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCardTopCommentRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCardTopCommentRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCardTopCommentRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCardTopCommentRsp;

  static equals(a: QueryCardTopCommentRsp | PlainMessage<QueryCardTopCommentRsp> | undefined, b: QueryCardTopCommentRsp | PlainMessage<QueryCardTopCommentRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.ManageCommentReq
 */
export declare class ManageCommentReq extends Message<ManageCommentReq> {
  /**
   * 评论id
   *
   * @generated from field: int64 comment_id = 1;
   */
  commentId: bigint;

  constructor(data?: PartialMessage<ManageCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.ManageCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ManageCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ManageCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ManageCommentReq;

  static equals(a: ManageCommentReq | PlainMessage<ManageCommentReq> | undefined, b: ManageCommentReq | PlainMessage<ManageCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryWorkStatReq
 */
export declare class QueryWorkStatReq extends Message<QueryWorkStatReq> {
  /**
   * 业务内容的唯一id
   *
   * @generated from field: int64 business_id = 1;
   */
  businessId: bigint;

  /**
   * 浏览者的uid，用于获取是否浏览者是否点赞过
   *
   * @generated from field: int64 viewer_uid = 3;
   */
  viewerUid: bigint;

  constructor(data?: PartialMessage<QueryWorkStatReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryWorkStatReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryWorkStatReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryWorkStatReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryWorkStatReq;

  static equals(a: QueryWorkStatReq | PlainMessage<QueryWorkStatReq> | undefined, b: QueryWorkStatReq | PlainMessage<QueryWorkStatReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.WorkStat
 */
export declare class WorkStat extends Message<WorkStat> {
  /**
   * 业务内容的唯一id
   *
   * @generated from field: int64 business_id = 1;
   */
  businessId: bigint;

  /**
   * 点赞数量
   *
   * @generated from field: int64 likes = 2;
   */
  likes: bigint;

  /**
   * 评论数量
   *
   * @generated from field: int64 comments = 3;
   */
  comments: bigint;

  /**
   * 浏览者是否点赞过作品
   *
   * @generated from field: bool is_liked = 4;
   */
  isLiked: boolean;

  /**
   * 拍同款数量
   *
   * @generated from field: int64 copies = 5;
   */
  copies: bigint;

  constructor(data?: PartialMessage<WorkStat>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.WorkStat";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkStat;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkStat;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkStat;

  static equals(a: WorkStat | PlainMessage<WorkStat> | undefined, b: WorkStat | PlainMessage<WorkStat> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryWorkStatRes
 */
export declare class QueryWorkStatRes extends Message<QueryWorkStatRes> {
  /**
   * @generated from field: step.raccoon.comment.WorkStat stat = 1;
   */
  stat?: WorkStat;

  constructor(data?: PartialMessage<QueryWorkStatRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryWorkStatRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryWorkStatRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryWorkStatRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryWorkStatRes;

  static equals(a: QueryWorkStatRes | PlainMessage<QueryWorkStatRes> | undefined, b: QueryWorkStatRes | PlainMessage<QueryWorkStatRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.BatchQueryWorkStatReq
 */
export declare class BatchQueryWorkStatReq extends Message<BatchQueryWorkStatReq> {
  /**
   * @generated from field: repeated int64 business_ids = 1;
   */
  businessIds: bigint[];

  /**
   * @generated from field: int64 view_uid = 2;
   */
  viewUid: bigint;

  constructor(data?: PartialMessage<BatchQueryWorkStatReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchQueryWorkStatReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchQueryWorkStatReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchQueryWorkStatReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchQueryWorkStatReq;

  static equals(a: BatchQueryWorkStatReq | PlainMessage<BatchQueryWorkStatReq> | undefined, b: BatchQueryWorkStatReq | PlainMessage<BatchQueryWorkStatReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.BatchQueryWorkStatRes
 */
export declare class BatchQueryWorkStatRes extends Message<BatchQueryWorkStatRes> {
  /**
   * @generated from field: repeated step.raccoon.comment.WorkStat stats = 1;
   */
  stats: WorkStat[];

  constructor(data?: PartialMessage<BatchQueryWorkStatRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchQueryWorkStatRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchQueryWorkStatRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchQueryWorkStatRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchQueryWorkStatRes;

  static equals(a: BatchQueryWorkStatRes | PlainMessage<BatchQueryWorkStatRes> | undefined, b: BatchQueryWorkStatRes | PlainMessage<BatchQueryWorkStatRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryUserLikeWorksReq
 */
export declare class QueryUserLikeWorksReq extends Message<QueryUserLikeWorksReq> {
  /**
   * 用户uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 页码，从1开始
   *
   * @generated from field: int32 page = 2;
   */
  page: number;

  /**
   * 页大小
   *
   * @generated from field: int32 size = 3;
   */
  size: number;

  constructor(data?: PartialMessage<QueryUserLikeWorksReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryUserLikeWorksReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserLikeWorksReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserLikeWorksReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserLikeWorksReq;

  static equals(a: QueryUserLikeWorksReq | PlainMessage<QueryUserLikeWorksReq> | undefined, b: QueryUserLikeWorksReq | PlainMessage<QueryUserLikeWorksReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryUserLikeWorksRes
 */
export declare class QueryUserLikeWorksRes extends Message<QueryUserLikeWorksRes> {
  /**
   * 用户uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 页码，从1开始
   *
   * @generated from field: int32 page = 2;
   */
  page: number;

  /**
   * 页大小
   *
   * @generated from field: int32 size = 3;
   */
  size: number;

  /**
   * 总数
   *
   * @generated from field: int32 total = 4;
   */
  total: number;

  /**
   * 当前页数据
   *
   * @generated from field: repeated int64 card_ids = 5;
   */
  cardIds: bigint[];

  constructor(data?: PartialMessage<QueryUserLikeWorksRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryUserLikeWorksRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserLikeWorksRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserLikeWorksRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserLikeWorksRes;

  static equals(a: QueryUserLikeWorksRes | PlainMessage<QueryUserLikeWorksRes> | undefined, b: QueryUserLikeWorksRes | PlainMessage<QueryUserLikeWorksRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryWorkLikedByUsersReq
 */
export declare class QueryWorkLikedByUsersReq extends Message<QueryWorkLikedByUsersReq> {
  /**
   * @generated from field: int64 business_id = 1;
   */
  businessId: bigint;

  /**
   * 待检查的uid
   *
   * @generated from field: repeated int64 view_uids = 2;
   */
  viewUids: bigint[];

  constructor(data?: PartialMessage<QueryWorkLikedByUsersReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryWorkLikedByUsersReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryWorkLikedByUsersReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryWorkLikedByUsersReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryWorkLikedByUsersReq;

  static equals(a: QueryWorkLikedByUsersReq | PlainMessage<QueryWorkLikedByUsersReq> | undefined, b: QueryWorkLikedByUsersReq | PlainMessage<QueryWorkLikedByUsersReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryWorkLikedByUsersRes
 */
export declare class QueryWorkLikedByUsersRes extends Message<QueryWorkLikedByUsersRes> {
  /**
   * 点赞过的uid
   *
   * @generated from field: repeated int64 liked_uids = 1;
   */
  likedUids: bigint[];

  constructor(data?: PartialMessage<QueryWorkLikedByUsersRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryWorkLikedByUsersRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryWorkLikedByUsersRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryWorkLikedByUsersRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryWorkLikedByUsersRes;

  static equals(a: QueryWorkLikedByUsersRes | PlainMessage<QueryWorkLikedByUsersRes> | undefined, b: QueryWorkLikedByUsersRes | PlainMessage<QueryWorkLikedByUsersRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryWorkCommentedByUsersReq
 */
export declare class QueryWorkCommentedByUsersReq extends Message<QueryWorkCommentedByUsersReq> {
  /**
   * @generated from field: int64 business_id = 1;
   */
  businessId: bigint;

  /**
   * 待检查的uid
   *
   * @generated from field: repeated int64 view_uids = 2;
   */
  viewUids: bigint[];

  constructor(data?: PartialMessage<QueryWorkCommentedByUsersReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryWorkCommentedByUsersReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryWorkCommentedByUsersReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryWorkCommentedByUsersReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryWorkCommentedByUsersReq;

  static equals(a: QueryWorkCommentedByUsersReq | PlainMessage<QueryWorkCommentedByUsersReq> | undefined, b: QueryWorkCommentedByUsersReq | PlainMessage<QueryWorkCommentedByUsersReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryWorkCommentedByUsersRes
 */
export declare class QueryWorkCommentedByUsersRes extends Message<QueryWorkCommentedByUsersRes> {
  /**
   * 评论过的uid
   *
   * @generated from field: repeated int64 commented_uids = 1;
   */
  commentedUids: bigint[];

  constructor(data?: PartialMessage<QueryWorkCommentedByUsersRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryWorkCommentedByUsersRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryWorkCommentedByUsersRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryWorkCommentedByUsersRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryWorkCommentedByUsersRes;

  static equals(a: QueryWorkCommentedByUsersRes | PlainMessage<QueryWorkCommentedByUsersRes> | undefined, b: QueryWorkCommentedByUsersRes | PlainMessage<QueryWorkCommentedByUsersRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryUserTotalLikesReq
 */
export declare class QueryUserTotalLikesReq extends Message<QueryUserTotalLikesReq> {
  /**
   * 用户uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  constructor(data?: PartialMessage<QueryUserTotalLikesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryUserTotalLikesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserTotalLikesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserTotalLikesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserTotalLikesReq;

  static equals(a: QueryUserTotalLikesReq | PlainMessage<QueryUserTotalLikesReq> | undefined, b: QueryUserTotalLikesReq | PlainMessage<QueryUserTotalLikesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryUserTotalLikesRes
 */
export declare class QueryUserTotalLikesRes extends Message<QueryUserTotalLikesRes> {
  /**
   * 用户uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 用户总被点赞数，有一定时间缓存
   *
   * @generated from field: int64 total_likes = 2;
   */
  totalLikes: bigint;

  /**
   * 用户总被二创数
   *
   * @generated from field: int64 total_being_copieds = 3;
   */
  totalBeingCopieds: bigint;

  /**
   * 区分玩法的二创数统计
   *
   * @generated from field: repeated step.raccoon.common.GameTypeCopyCount game_type_being_copieds = 4;
   */
  gameTypeBeingCopieds: GameTypeCopyCount[];

  constructor(data?: PartialMessage<QueryUserTotalLikesRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryUserTotalLikesRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserTotalLikesRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserTotalLikesRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserTotalLikesRes;

  static equals(a: QueryUserTotalLikesRes | PlainMessage<QueryUserTotalLikesRes> | undefined, b: QueryUserTotalLikesRes | PlainMessage<QueryUserTotalLikesRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.IncUserBeCopiedsReq
 */
export declare class IncUserBeCopiedsReq extends Message<IncUserBeCopiedsReq> {
  /**
   * 创作者uid
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 二创用户id
   *
   * @generated from field: int64 copied_uid = 2;
   */
  copiedUid: bigint;

  /**
   * 玩法类型
   *
   * @generated from field: step.raccoon.common.GameType game = 3;
   */
  game: GameType;

  /**
   * 源作品id
   *
   * @generated from field: int64 business_id = 4;
   */
  businessId: bigint;

  /**
   * 拍同款的作品id
   *
   * @generated from field: int64 copied_business_id = 5;
   */
  copiedBusinessId: bigint;

  /**
   * 是否需要发拍同款站内信
   *
   * @generated from field: bool send_make_copy_inbox = 6;
   */
  sendMakeCopyInbox: boolean;

  constructor(data?: PartialMessage<IncUserBeCopiedsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.IncUserBeCopiedsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IncUserBeCopiedsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IncUserBeCopiedsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IncUserBeCopiedsReq;

  static equals(a: IncUserBeCopiedsReq | PlainMessage<IncUserBeCopiedsReq> | undefined, b: IncUserBeCopiedsReq | PlainMessage<IncUserBeCopiedsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.IncUserBeCopiedsRes
 */
export declare class IncUserBeCopiedsRes extends Message<IncUserBeCopiedsRes> {
  /**
   * 是否成功
   *
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<IncUserBeCopiedsRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.IncUserBeCopiedsRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IncUserBeCopiedsRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IncUserBeCopiedsRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IncUserBeCopiedsRes;

  static equals(a: IncUserBeCopiedsRes | PlainMessage<IncUserBeCopiedsRes> | undefined, b: IncUserBeCopiedsRes | PlainMessage<IncUserBeCopiedsRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.BatchQueryCommentLikeStatesReq
 */
export declare class BatchQueryCommentLikeStatesReq extends Message<BatchQueryCommentLikeStatesReq> {
  /**
   * 用户id
   *
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 评论列表
   *
   * @generated from field: repeated int64 comment_ids = 2;
   */
  commentIds: bigint[];

  constructor(data?: PartialMessage<BatchQueryCommentLikeStatesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchQueryCommentLikeStatesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchQueryCommentLikeStatesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchQueryCommentLikeStatesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchQueryCommentLikeStatesReq;

  static equals(a: BatchQueryCommentLikeStatesReq | PlainMessage<BatchQueryCommentLikeStatesReq> | undefined, b: BatchQueryCommentLikeStatesReq | PlainMessage<BatchQueryCommentLikeStatesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.BatchQueryCommentLikeStatesRes
 */
export declare class BatchQueryCommentLikeStatesRes extends Message<BatchQueryCommentLikeStatesRes> {
  /**
   * @generated from field: map<int64, bool> like_states = 1;
   */
  likeStates: { [key: string]: boolean };

  constructor(data?: PartialMessage<BatchQueryCommentLikeStatesRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchQueryCommentLikeStatesRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchQueryCommentLikeStatesRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchQueryCommentLikeStatesRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchQueryCommentLikeStatesRes;

  static equals(a: BatchQueryCommentLikeStatesRes | PlainMessage<BatchQueryCommentLikeStatesRes> | undefined, b: BatchQueryCommentLikeStatesRes | PlainMessage<BatchQueryCommentLikeStatesRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCommentByIdReq
 */
export declare class QueryCommentByIdReq extends Message<QueryCommentByIdReq> {
  /**
   * @generated from field: int64 comment_id = 1;
   */
  commentId: bigint;

  constructor(data?: PartialMessage<QueryCommentByIdReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCommentByIdReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCommentByIdReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCommentByIdReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCommentByIdReq;

  static equals(a: QueryCommentByIdReq | PlainMessage<QueryCommentByIdReq> | undefined, b: QueryCommentByIdReq | PlainMessage<QueryCommentByIdReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.BatchQueryCommentReq
 */
export declare class BatchQueryCommentReq extends Message<BatchQueryCommentReq> {
  /**
   * @generated from field: repeated int64 comment_ids = 1;
   */
  commentIds: bigint[];

  /**
   * 非必传,传了会有过滤逻辑
   *
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  constructor(data?: PartialMessage<BatchQueryCommentReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchQueryCommentReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchQueryCommentReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchQueryCommentReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchQueryCommentReq;

  static equals(a: BatchQueryCommentReq | PlainMessage<BatchQueryCommentReq> | undefined, b: BatchQueryCommentReq | PlainMessage<BatchQueryCommentReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.BatchQueryCommentRes
 */
export declare class BatchQueryCommentRes extends Message<BatchQueryCommentRes> {
  /**
   * @generated from field: map<int64, step.raccoon.comment.QueryCommentByIdRes> comments = 1;
   */
  comments: { [key: string]: QueryCommentByIdRes };

  constructor(data?: PartialMessage<BatchQueryCommentRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.BatchQueryCommentRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchQueryCommentRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchQueryCommentRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchQueryCommentRes;

  static equals(a: BatchQueryCommentRes | PlainMessage<BatchQueryCommentRes> | undefined, b: BatchQueryCommentRes | PlainMessage<BatchQueryCommentRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCommentByIdRes
 */
export declare class QueryCommentByIdRes extends Message<QueryCommentByIdRes> {
  /**
   * 评论id
   *
   * @generated from field: int64 comment_id = 1;
   */
  commentId: bigint;

  /**
   * 评论内容
   *
   * @generated from field: string comment_content = 2;
   */
  commentContent: string;

  /**
   * 点赞数
   *
   * @generated from field: int64 like_cnt = 3;
   */
  likeCnt: bigint;

  /**
   * 发布时间
   *
   * @generated from field: int64 publish_at = 4;
   */
  publishAt: bigint;

  /**
   * 审核状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored_state = 5;
   */
  censoredState: CensoredState;

  /**
   * 删除状态
   *
   * @generated from field: bool deleted = 6;
   */
  deleted: boolean;

  /**
   * 用户id
   *
   * @generated from field: int64 uid = 7;
   */
  uid: bigint;

  /**
   * 内容id
   *
   * @generated from field: int64 card_id = 8;
   */
  cardId: bigint;

  /**
   * 表情包id
   *
   * @generated from field: string emoji_id = 9;
   */
  emojiId: string;

  /**
   * 当前用户是否可见，不传uid默认为true
   *
   * @generated from field: bool can_view = 10;
   */
  canView: boolean;

  constructor(data?: PartialMessage<QueryCommentByIdRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCommentByIdRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCommentByIdRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCommentByIdRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCommentByIdRes;

  static equals(a: QueryCommentByIdRes | PlainMessage<QueryCommentByIdRes> | undefined, b: QueryCommentByIdRes | PlainMessage<QueryCommentByIdRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryUserCopyInfoReq
 */
export declare class QueryUserCopyInfoReq extends Message<QueryUserCopyInfoReq> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 2;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QueryUserCopyInfoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryUserCopyInfoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserCopyInfoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserCopyInfoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserCopyInfoReq;

  static equals(a: QueryUserCopyInfoReq | PlainMessage<QueryUserCopyInfoReq> | undefined, b: QueryUserCopyInfoReq | PlainMessage<QueryUserCopyInfoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.CardCopyInfo
 */
export declare class CardCopyInfo extends Message<CardCopyInfo> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * @generated from field: int64 copy_cnt = 2;
   */
  copyCnt: bigint;

  /**
   * @generated from field: repeated int64 copy_card_id_list = 3;
   */
  copyCardIdList: bigint[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 4;
   */
  pagination?: Pagination;

  /**
   * 拍同款人数
   *
   * @generated from field: int64 copy_user_cnt = 5;
   */
  copyUserCnt: bigint;

  constructor(data?: PartialMessage<CardCopyInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.CardCopyInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardCopyInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardCopyInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardCopyInfo;

  static equals(a: CardCopyInfo | PlainMessage<CardCopyInfo> | undefined, b: CardCopyInfo | PlainMessage<CardCopyInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryUserCopyInfoRsp
 */
export declare class QueryUserCopyInfoRsp extends Message<QueryUserCopyInfoRsp> {
  /**
   * @generated from field: repeated step.raccoon.comment.CardCopyInfo card_copy_info_list = 1;
   */
  cardCopyInfoList: CardCopyInfo[];

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 2;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QueryUserCopyInfoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryUserCopyInfoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryUserCopyInfoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryUserCopyInfoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryUserCopyInfoRsp;

  static equals(a: QueryUserCopyInfoRsp | PlainMessage<QueryUserCopyInfoRsp> | undefined, b: QueryUserCopyInfoRsp | PlainMessage<QueryUserCopyInfoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCardCopyInfoReq
 */
export declare class QueryCardCopyInfoReq extends Message<QueryCardCopyInfoReq> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 2;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QueryCardCopyInfoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCardCopyInfoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCardCopyInfoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCardCopyInfoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCardCopyInfoReq;

  static equals(a: QueryCardCopyInfoReq | PlainMessage<QueryCardCopyInfoReq> | undefined, b: QueryCardCopyInfoReq | PlainMessage<QueryCardCopyInfoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.comment.QueryCardCopyInfoRsp
 */
export declare class QueryCardCopyInfoRsp extends Message<QueryCardCopyInfoRsp> {
  /**
   * @generated from field: step.raccoon.comment.CardCopyInfo card_copy_info = 1;
   */
  cardCopyInfo?: CardCopyInfo;

  constructor(data?: PartialMessage<QueryCardCopyInfoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.comment.QueryCardCopyInfoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryCardCopyInfoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryCardCopyInfoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryCardCopyInfoRsp;

  static equals(a: QueryCardCopyInfoRsp | PlainMessage<QueryCardCopyInfoRsp> | undefined, b: QueryCardCopyInfoRsp | PlainMessage<QueryCardCopyInfoRsp> | undefined): boolean;
}

