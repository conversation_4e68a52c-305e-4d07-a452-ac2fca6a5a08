// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/asynccard/asynccard.proto (package step.raccoon.asynccard, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Pagination } from "../common/utils_pb.js";
import type { AsyncCardInfo } from "../common/asynccard_pb.js";

/**
 * @generated from message step.raccoon.asynccard.GetAsyncCardsReq
 */
export declare class GetAsyncCardsReq extends Message<GetAsyncCardsReq> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<GetAsyncCardsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.GetAsyncCardsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAsyncCardsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAsyncCardsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAsyncCardsReq;

  static equals(a: GetAsyncCardsReq | PlainMessage<GetAsyncCardsReq> | undefined, b: GetAsyncCardsReq | PlainMessage<GetAsyncCardsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.GetAsyncCardsRsp
 */
export declare class GetAsyncCardsRsp extends Message<GetAsyncCardsRsp> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  /**
   * @generated from field: repeated step.raccoon.common.AsyncCardInfo cards = 2;
   */
  cards: AsyncCardInfo[];

  constructor(data?: PartialMessage<GetAsyncCardsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.GetAsyncCardsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAsyncCardsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAsyncCardsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAsyncCardsRsp;

  static equals(a: GetAsyncCardsRsp | PlainMessage<GetAsyncCardsRsp> | undefined, b: GetAsyncCardsRsp | PlainMessage<GetAsyncCardsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.ReGenerateAsyncCardReq
 */
export declare class ReGenerateAsyncCardReq extends Message<ReGenerateAsyncCardReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  constructor(data?: PartialMessage<ReGenerateAsyncCardReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.ReGenerateAsyncCardReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReGenerateAsyncCardReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReGenerateAsyncCardReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReGenerateAsyncCardReq;

  static equals(a: ReGenerateAsyncCardReq | PlainMessage<ReGenerateAsyncCardReq> | undefined, b: ReGenerateAsyncCardReq | PlainMessage<ReGenerateAsyncCardReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.ReGenerateAsyncCardRsp
 */
export declare class ReGenerateAsyncCardRsp extends Message<ReGenerateAsyncCardRsp> {
  constructor(data?: PartialMessage<ReGenerateAsyncCardRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.ReGenerateAsyncCardRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReGenerateAsyncCardRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReGenerateAsyncCardRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReGenerateAsyncCardRsp;

  static equals(a: ReGenerateAsyncCardRsp | PlainMessage<ReGenerateAsyncCardRsp> | undefined, b: ReGenerateAsyncCardRsp | PlainMessage<ReGenerateAsyncCardRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.DeleteAsyncCardReq
 */
export declare class DeleteAsyncCardReq extends Message<DeleteAsyncCardReq> {
  /**
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  constructor(data?: PartialMessage<DeleteAsyncCardReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.DeleteAsyncCardReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAsyncCardReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAsyncCardReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAsyncCardReq;

  static equals(a: DeleteAsyncCardReq | PlainMessage<DeleteAsyncCardReq> | undefined, b: DeleteAsyncCardReq | PlainMessage<DeleteAsyncCardReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.DeleteAsyncCardRsp
 */
export declare class DeleteAsyncCardRsp extends Message<DeleteAsyncCardRsp> {
  constructor(data?: PartialMessage<DeleteAsyncCardRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.DeleteAsyncCardRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAsyncCardRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAsyncCardRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAsyncCardRsp;

  static equals(a: DeleteAsyncCardRsp | PlainMessage<DeleteAsyncCardRsp> | undefined, b: DeleteAsyncCardRsp | PlainMessage<DeleteAsyncCardRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.GetUserShowAsyncCardCntReq
 */
export declare class GetUserShowAsyncCardCntReq extends Message<GetUserShowAsyncCardCntReq> {
  constructor(data?: PartialMessage<GetUserShowAsyncCardCntReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.GetUserShowAsyncCardCntReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserShowAsyncCardCntReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserShowAsyncCardCntReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserShowAsyncCardCntReq;

  static equals(a: GetUserShowAsyncCardCntReq | PlainMessage<GetUserShowAsyncCardCntReq> | undefined, b: GetUserShowAsyncCardCntReq | PlainMessage<GetUserShowAsyncCardCntReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.GetUserShowAsyncCardCntRsp
 */
export declare class GetUserShowAsyncCardCntRsp extends Message<GetUserShowAsyncCardCntRsp> {
  /**
   * @generated from field: int64 cnt = 1;
   */
  cnt: bigint;

  constructor(data?: PartialMessage<GetUserShowAsyncCardCntRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.GetUserShowAsyncCardCntRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserShowAsyncCardCntRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserShowAsyncCardCntRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserShowAsyncCardCntRsp;

  static equals(a: GetUserShowAsyncCardCntRsp | PlainMessage<GetUserShowAsyncCardCntRsp> | undefined, b: GetUserShowAsyncCardCntRsp | PlainMessage<GetUserShowAsyncCardCntRsp> | undefined): boolean;
}

