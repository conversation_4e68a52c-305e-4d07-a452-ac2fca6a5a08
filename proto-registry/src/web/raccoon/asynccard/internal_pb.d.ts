// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/asynccard/internal.proto (package step.raccoon.asynccard, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { GameType } from "../common/types_pb.js";
import type { Pagination } from "../common/utils_pb.js";
import type { AsyncCardInfo, AsyncCardStatus } from "../common/asynccard_pb.js";

/**
 * @generated from message step.raccoon.asynccard.AcquireAsyncCardIdReq
 */
export declare class AcquireAsyncCardIdReq extends Message<AcquireAsyncCardIdReq> {
  constructor(data?: PartialMessage<AcquireAsyncCardIdReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.AcquireAsyncCardIdReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AcquireAsyncCardIdReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AcquireAsyncCardIdReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AcquireAsyncCardIdReq;

  static equals(a: AcquireAsyncCardIdReq | PlainMessage<AcquireAsyncCardIdReq> | undefined, b: AcquireAsyncCardIdReq | PlainMessage<AcquireAsyncCardIdReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.AcquireAsyncCardIdRsp
 */
export declare class AcquireAsyncCardIdRsp extends Message<AcquireAsyncCardIdRsp> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  constructor(data?: PartialMessage<AcquireAsyncCardIdRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.AcquireAsyncCardIdRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AcquireAsyncCardIdRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AcquireAsyncCardIdRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AcquireAsyncCardIdRsp;

  static equals(a: AcquireAsyncCardIdRsp | PlainMessage<AcquireAsyncCardIdRsp> | undefined, b: AcquireAsyncCardIdRsp | PlainMessage<AcquireAsyncCardIdRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.CreateAsyncCardReq
 */
export declare class CreateAsyncCardReq extends Message<CreateAsyncCardReq> {
  /**
   * 不传则生成
   *
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * 用户id
   *
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  /**
   * 玩法类型
   *
   * @generated from field: step.raccoon.common.GameType game = 3;
   */
  game: GameType;

  /**
   * 封面图
   *
   * @generated from field: string cover_image_id = 4;
   */
  coverImageId: string;

  /**
   * 业务id
   *
   * @generated from field: string biz_id = 5;
   */
  bizId: string;

  /**
   * 业务extra json/dict信息
   *
   * @generated from field: string extra = 6;
   */
  extra: string;

  constructor(data?: PartialMessage<CreateAsyncCardReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.CreateAsyncCardReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAsyncCardReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAsyncCardReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAsyncCardReq;

  static equals(a: CreateAsyncCardReq | PlainMessage<CreateAsyncCardReq> | undefined, b: CreateAsyncCardReq | PlainMessage<CreateAsyncCardReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.CreateAsyncCardRsp
 */
export declare class CreateAsyncCardRsp extends Message<CreateAsyncCardRsp> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  constructor(data?: PartialMessage<CreateAsyncCardRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.CreateAsyncCardRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAsyncCardRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAsyncCardRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAsyncCardRsp;

  static equals(a: CreateAsyncCardRsp | PlainMessage<CreateAsyncCardRsp> | undefined, b: CreateAsyncCardRsp | PlainMessage<CreateAsyncCardRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.QueryAsyncCardsReq
 */
export declare class QueryAsyncCardsReq extends Message<QueryAsyncCardsReq> {
  /**
   * @generated from field: int64 uid = 1;
   */
  uid: bigint;

  /**
   * 玩法类型
   *
   * @generated from field: repeated step.raccoon.common.GameType games = 2;
   */
  games: GameType[];

  /**
   * @generated from field: bool view_publish_card = 3;
   */
  viewPublishCard: boolean;

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 50;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QueryAsyncCardsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.QueryAsyncCardsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryAsyncCardsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryAsyncCardsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryAsyncCardsReq;

  static equals(a: QueryAsyncCardsReq | PlainMessage<QueryAsyncCardsReq> | undefined, b: QueryAsyncCardsReq | PlainMessage<QueryAsyncCardsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.QueryAsyncCardsRsp
 */
export declare class QueryAsyncCardsRsp extends Message<QueryAsyncCardsRsp> {
  /**
   * @generated from field: repeated step.raccoon.common.AsyncCardInfo cards = 1;
   */
  cards: AsyncCardInfo[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 50;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<QueryAsyncCardsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.QueryAsyncCardsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryAsyncCardsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryAsyncCardsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryAsyncCardsRsp;

  static equals(a: QueryAsyncCardsRsp | PlainMessage<QueryAsyncCardsRsp> | undefined, b: QueryAsyncCardsRsp | PlainMessage<QueryAsyncCardsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.UpdateAsyncCardStatusReq
 */
export declare class UpdateAsyncCardStatusReq extends Message<UpdateAsyncCardStatusReq> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * @generated from field: string biz_id = 2;
   */
  bizId: string;

  /**
   * @generated from field: int32 progress_rate = 3;
   */
  progressRate: number;

  /**
   * @generated from field: int32 left_seconds = 4;
   */
  leftSeconds: number;

  /**
   * @generated from field: step.raccoon.common.AsyncCardStatus status = 5;
   */
  status: AsyncCardStatus;

  /**
   * @generated from field: string fail_reason = 6;
   */
  failReason: string;

  /**
   * 业务extra json/dict信息
   *
   * @generated from field: string extra = 7;
   */
  extra: string;

  /**
   * 非空时将重新更新封面
   *
   * @generated from field: string cover_image_id = 8;
   */
  coverImageId: string;

  constructor(data?: PartialMessage<UpdateAsyncCardStatusReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.UpdateAsyncCardStatusReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAsyncCardStatusReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAsyncCardStatusReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAsyncCardStatusReq;

  static equals(a: UpdateAsyncCardStatusReq | PlainMessage<UpdateAsyncCardStatusReq> | undefined, b: UpdateAsyncCardStatusReq | PlainMessage<UpdateAsyncCardStatusReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.UpdateAsyncCardStatusRsp
 */
export declare class UpdateAsyncCardStatusRsp extends Message<UpdateAsyncCardStatusRsp> {
  constructor(data?: PartialMessage<UpdateAsyncCardStatusRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.UpdateAsyncCardStatusRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAsyncCardStatusRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAsyncCardStatusRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAsyncCardStatusRsp;

  static equals(a: UpdateAsyncCardStatusRsp | PlainMessage<UpdateAsyncCardStatusRsp> | undefined, b: UpdateAsyncCardStatusRsp | PlainMessage<UpdateAsyncCardStatusRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.UpdateAsyncCardBizIdReq
 */
export declare class UpdateAsyncCardBizIdReq extends Message<UpdateAsyncCardBizIdReq> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * @generated from field: string pre_biz_id = 2;
   */
  preBizId: string;

  /**
   * @generated from field: string new_biz_id = 3;
   */
  newBizId: string;

  /**
   * @generated from field: int32 progress_rate = 4;
   */
  progressRate: number;

  /**
   * @generated from field: int32 left_seconds = 5;
   */
  leftSeconds: number;

  /**
   * @generated from field: step.raccoon.common.AsyncCardStatus status = 6;
   */
  status: AsyncCardStatus;

  constructor(data?: PartialMessage<UpdateAsyncCardBizIdReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.UpdateAsyncCardBizIdReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAsyncCardBizIdReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAsyncCardBizIdReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAsyncCardBizIdReq;

  static equals(a: UpdateAsyncCardBizIdReq | PlainMessage<UpdateAsyncCardBizIdReq> | undefined, b: UpdateAsyncCardBizIdReq | PlainMessage<UpdateAsyncCardBizIdReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.UpdateAsyncCardBizIdRsp
 */
export declare class UpdateAsyncCardBizIdRsp extends Message<UpdateAsyncCardBizIdRsp> {
  constructor(data?: PartialMessage<UpdateAsyncCardBizIdRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.UpdateAsyncCardBizIdRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAsyncCardBizIdRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAsyncCardBizIdRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAsyncCardBizIdRsp;

  static equals(a: UpdateAsyncCardBizIdRsp | PlainMessage<UpdateAsyncCardBizIdRsp> | undefined, b: UpdateAsyncCardBizIdRsp | PlainMessage<UpdateAsyncCardBizIdRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.GetAsyncCardReq
 */
export declare class GetAsyncCardReq extends Message<GetAsyncCardReq> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * @generated from field: bool view_publish_card = 2;
   */
  viewPublishCard: boolean;

  constructor(data?: PartialMessage<GetAsyncCardReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.GetAsyncCardReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAsyncCardReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAsyncCardReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAsyncCardReq;

  static equals(a: GetAsyncCardReq | PlainMessage<GetAsyncCardReq> | undefined, b: GetAsyncCardReq | PlainMessage<GetAsyncCardReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.GetAsyncCardRsp
 */
export declare class GetAsyncCardRsp extends Message<GetAsyncCardRsp> {
  /**
   * @generated from field: step.raccoon.common.AsyncCardInfo card = 1;
   */
  card?: AsyncCardInfo;

  constructor(data?: PartialMessage<GetAsyncCardRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.GetAsyncCardRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAsyncCardRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAsyncCardRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAsyncCardRsp;

  static equals(a: GetAsyncCardRsp | PlainMessage<GetAsyncCardRsp> | undefined, b: GetAsyncCardRsp | PlainMessage<GetAsyncCardRsp> | undefined): boolean;
}

