// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/asynccard/asynccard.proto (package step.raccoon.asynccard, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { DeleteAsyncCardReq, DeleteAsyncCardRsp, GetAsyncCardsReq, GetAsyncCardsRsp, GetUserShowAsyncCardCntReq, GetUserShowAsyncCardCntRsp, ReGenerateAsyncCardReq, ReGenerateAsyncCardRsp } from "./asynccard_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.asynccard.AsyncCard
 */
export declare const AsyncCard: {
  readonly typeName: "step.raccoon.asynccard.AsyncCard",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.asynccard.AsyncCard.GetAsyncCards
     */
    readonly getAsyncCards: {
      readonly name: "GetAsyncCards",
      readonly I: typeof GetAsyncCardsReq,
      readonly O: typeof GetAsyncCardsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.asynccard.AsyncCard.ReGenerateAsyncCard
     */
    readonly reGenerateAsyncCard: {
      readonly name: "ReGenerateAsyncCard",
      readonly I: typeof ReGenerateAsyncCardReq,
      readonly O: typeof ReGenerateAsyncCardRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.asynccard.AsyncCard.DeleteAsyncCard
     */
    readonly deleteAsyncCard: {
      readonly name: "DeleteAsyncCard",
      readonly I: typeof DeleteAsyncCardReq,
      readonly O: typeof DeleteAsyncCardRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.asynccard.AsyncCard.GetUserShowAsyncCardCnt
     */
    readonly getUserShowAsyncCardCnt: {
      readonly name: "GetUserShowAsyncCardCnt",
      readonly I: typeof GetUserShowAsyncCardCntReq,
      readonly O: typeof GetUserShowAsyncCardCntRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

