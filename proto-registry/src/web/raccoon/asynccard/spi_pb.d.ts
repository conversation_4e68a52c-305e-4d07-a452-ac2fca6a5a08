// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/asynccard/spi.proto (package step.raccoon.asynccard, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { AsyncCardExtInfo, AsyncCardStatus } from "../common/asynccard_pb.js";

/**
 * @generated from message step.raccoon.asynccard.GetBizFeatureInfoReq
 */
export declare class GetBizFeatureInfoReq extends Message<GetBizFeatureInfoReq> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * @generated from field: string biz_id = 2;
   */
  bizId: string;

  constructor(data?: PartialMessage<GetBizFeatureInfoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.GetBizFeatureInfoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBizFeatureInfoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBizFeatureInfoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBizFeatureInfoReq;

  static equals(a: GetBizFeatureInfoReq | PlainMessage<GetBizFeatureInfoReq> | undefined, b: GetBizFeatureInfoReq | PlainMessage<GetBizFeatureInfoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.GetBizFeatureInfoRsp
 */
export declare class GetBizFeatureInfoRsp extends Message<GetBizFeatureInfoRsp> {
  /**
   * @generated from field: step.raccoon.common.AsyncCardExtInfo ext_info = 1;
   */
  extInfo?: AsyncCardExtInfo;

  constructor(data?: PartialMessage<GetBizFeatureInfoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.GetBizFeatureInfoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBizFeatureInfoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBizFeatureInfoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBizFeatureInfoRsp;

  static equals(a: GetBizFeatureInfoRsp | PlainMessage<GetBizFeatureInfoRsp> | undefined, b: GetBizFeatureInfoRsp | PlainMessage<GetBizFeatureInfoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.RegenerateReq
 */
export declare class RegenerateReq extends Message<RegenerateReq> {
  /**
   * @generated from field: int64 card_id = 1;
   */
  cardId: bigint;

  /**
   * @generated from field: string biz_id = 2;
   */
  bizId: string;

  constructor(data?: PartialMessage<RegenerateReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.RegenerateReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegenerateReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegenerateReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegenerateReq;

  static equals(a: RegenerateReq | PlainMessage<RegenerateReq> | undefined, b: RegenerateReq | PlainMessage<RegenerateReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.asynccard.RegenerateRsp
 */
export declare class RegenerateRsp extends Message<RegenerateRsp> {
  /**
   * @generated from field: string new_biz_id = 1;
   */
  newBizId: string;

  /**
   * @generated from field: int32 progress_rate = 2;
   */
  progressRate: number;

  /**
   * @generated from field: int32 left_seconds = 3;
   */
  leftSeconds: number;

  /**
   * @generated from field: step.raccoon.common.AsyncCardStatus status = 4;
   */
  status: AsyncCardStatus;

  constructor(data?: PartialMessage<RegenerateRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.asynccard.RegenerateRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegenerateRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegenerateRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegenerateRsp;

  static equals(a: RegenerateRsp | PlainMessage<RegenerateRsp> | undefined, b: RegenerateRsp | PlainMessage<RegenerateRsp> | undefined): boolean;
}

