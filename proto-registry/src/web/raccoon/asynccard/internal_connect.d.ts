// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/asynccard/internal.proto (package step.raccoon.asynccard, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AcquireAsyncCardIdReq, AcquireAsyncCardIdRsp, CreateAsyncCardReq, CreateAsyncCardRsp, GetAsyncCardReq, GetAsyncCardRsp, QueryAsyncCardsReq, QueryAsyncCardsRsp, UpdateAsyncCardBizIdReq, UpdateAsyncCardBizIdRsp, UpdateAsyncCardStatusReq, UpdateAsyncCardStatusRsp } from "./internal_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.asynccard.Internal
 */
export declare const Internal: {
  readonly typeName: "step.raccoon.asynccard.Internal",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.asynccard.Internal.AcquireAsyncCardId
     */
    readonly acquireAsyncCardId: {
      readonly name: "AcquireAsyncCardId",
      readonly I: typeof AcquireAsyncCardIdReq,
      readonly O: typeof AcquireAsyncCardIdRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.asynccard.Internal.CreateAsyncCard
     */
    readonly createAsyncCard: {
      readonly name: "CreateAsyncCard",
      readonly I: typeof CreateAsyncCardReq,
      readonly O: typeof CreateAsyncCardRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.asynccard.Internal.QueryAsyncCards
     */
    readonly queryAsyncCards: {
      readonly name: "QueryAsyncCards",
      readonly I: typeof QueryAsyncCardsReq,
      readonly O: typeof QueryAsyncCardsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.asynccard.Internal.GetAsyncCard
     */
    readonly getAsyncCard: {
      readonly name: "GetAsyncCard",
      readonly I: typeof GetAsyncCardReq,
      readonly O: typeof GetAsyncCardRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.asynccard.Internal.UpdateAsyncCardStatus
     */
    readonly updateAsyncCardStatus: {
      readonly name: "UpdateAsyncCardStatus",
      readonly I: typeof UpdateAsyncCardStatusReq,
      readonly O: typeof UpdateAsyncCardStatusRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.asynccard.Internal.UpdateAsyncCardBizId
     */
    readonly updateAsyncCardBizId: {
      readonly name: "UpdateAsyncCardBizId",
      readonly I: typeof UpdateAsyncCardBizIdReq,
      readonly O: typeof UpdateAsyncCardBizIdRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

