// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/asynccard/spi.proto (package step.raccoon.asynccard, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GetBizFeatureInfoReq, GetBizFeatureInfoRsp, RegenerateReq, RegenerateRsp } from "./spi_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.asynccard.AsyncCardSpi
 */
export declare const AsyncCardSpi: {
  readonly typeName: "step.raccoon.asynccard.AsyncCardSpi",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.asynccard.AsyncCardSpi.GetBizFeatureInfo
     */
    readonly getBizFeatureInfo: {
      readonly name: "GetBizFeatureInfo",
      readonly I: typeof GetBizFeatureInfoReq,
      readonly O: typeof GetBizFeatureInfoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.asynccard.AsyncCardSpi.Regenerate
     */
    readonly regenerate: {
      readonly name: "Regenerate",
      readonly I: typeof RegenerateReq,
      readonly O: typeof RegenerateRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

