// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/buildindex/buildindex.proto (package step.raccoon.buildindex, syntax proto3)
/* eslint-disable */
// @ts-nocheck

/**
 * @generated from service step.raccoon.buildindex.BuildIndex
 */
export declare const BuildIndex: {
  readonly typeName: "step.raccoon.buildindex.BuildIndex",
  readonly methods: {
  }
};

