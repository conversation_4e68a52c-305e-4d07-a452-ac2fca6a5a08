// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/video/video.proto (package step.raccoon.video, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { GameType } from "../common/types_pb.js";
import type { VideoExtraParam, VideoInfo, VideoResolution, VideoSize, VideoType } from "../common/video_pb.js";
import type { MediaOptions } from "../common/media_pb.js";

/**
 * @generated from message step.raccoon.video.CreateVideoReq
 */
export declare class CreateVideoReq extends Message<CreateVideoReq> {
  /**
   * 业务id
   *
   * @generated from field: string biz_id = 1;
   */
  bizId: string;

  /**
   * 用户id
   *
   * @generated from field: int64 uid = 2;
   */
  uid: bigint;

  /**
   * 玩法类型
   *
   * @generated from field: step.raccoon.common.GameType game = 3;
   */
  game: GameType;

  /**
   * 视频生成类型
   *
   * @generated from field: step.raccoon.common.VideoType video_type = 4;
   */
  videoType: VideoType;

  /**
   * 视频尺寸信息
   *
   * @generated from field: step.raccoon.common.VideoSize video_size = 5;
   */
  videoSize?: VideoSize;

  /**
   * 额外信息
   *
   * @generated from field: step.raccoon.common.VideoExtraParam extra_param = 6;
   */
  extraParam?: VideoExtraParam;

  constructor(data?: PartialMessage<CreateVideoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.CreateVideoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateVideoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateVideoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateVideoReq;

  static equals(a: CreateVideoReq | PlainMessage<CreateVideoReq> | undefined, b: CreateVideoReq | PlainMessage<CreateVideoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.CreateVideoRsp
 */
export declare class CreateVideoRsp extends Message<CreateVideoRsp> {
  /**
   * @generated from field: step.raccoon.common.VideoInfo video_info = 1;
   */
  videoInfo?: VideoInfo;

  constructor(data?: PartialMessage<CreateVideoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.CreateVideoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateVideoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateVideoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateVideoRsp;

  static equals(a: CreateVideoRsp | PlainMessage<CreateVideoRsp> | undefined, b: CreateVideoRsp | PlainMessage<CreateVideoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.GetVideoReq
 */
export declare class GetVideoReq extends Message<GetVideoReq> {
  /**
   * @generated from field: int64 video_id = 1;
   */
  videoId: bigint;

  /**
   * @generated from field: step.raccoon.video.VideoWithMark with_mark = 2;
   */
  withMark?: VideoWithMark;

  constructor(data?: PartialMessage<GetVideoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.GetVideoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVideoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVideoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVideoReq;

  static equals(a: GetVideoReq | PlainMessage<GetVideoReq> | undefined, b: GetVideoReq | PlainMessage<GetVideoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.GetVideoRsp
 */
export declare class GetVideoRsp extends Message<GetVideoRsp> {
  /**
   * @generated from field: step.raccoon.common.VideoInfo video_info = 1;
   */
  videoInfo?: VideoInfo;

  /**
   * @generated from field: optional step.raccoon.common.VideoExtraParam extra_param = 2;
   */
  extraParam?: VideoExtraParam;

  constructor(data?: PartialMessage<GetVideoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.GetVideoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVideoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVideoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVideoRsp;

  static equals(a: GetVideoRsp | PlainMessage<GetVideoRsp> | undefined, b: GetVideoRsp | PlainMessage<GetVideoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.GetVideosReq
 */
export declare class GetVideosReq extends Message<GetVideosReq> {
  /**
   * @generated from field: repeated int64 video_ids = 1;
   */
  videoIds: bigint[];

  /**
   * @generated from field: step.raccoon.video.VideoWithMark with_mark = 2;
   */
  withMark?: VideoWithMark;

  constructor(data?: PartialMessage<GetVideosReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.GetVideosReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVideosReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVideosReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVideosReq;

  static equals(a: GetVideosReq | PlainMessage<GetVideosReq> | undefined, b: GetVideosReq | PlainMessage<GetVideosReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.GetVideosRsp
 */
export declare class GetVideosRsp extends Message<GetVideosRsp> {
  /**
   * @generated from field: map<int64, step.raccoon.common.VideoInfo> video_infos = 1;
   */
  videoInfos: { [key: string]: VideoInfo };

  constructor(data?: PartialMessage<GetVideosRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.GetVideosRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVideosRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVideosRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVideosRsp;

  static equals(a: GetVideosRsp | PlainMessage<GetVideosRsp> | undefined, b: GetVideosRsp | PlainMessage<GetVideosRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.VideoWithMark
 */
export declare class VideoWithMark extends Message<VideoWithMark> {
  /**
   * @generated from field: bool with_mark = 1;
   */
  withMark: boolean;

  constructor(data?: PartialMessage<VideoWithMark>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.VideoWithMark";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VideoWithMark;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VideoWithMark;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VideoWithMark;

  static equals(a: VideoWithMark | PlainMessage<VideoWithMark> | undefined, b: VideoWithMark | PlainMessage<VideoWithMark> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.VideoWithBgm
 */
export declare class VideoWithBgm extends Message<VideoWithBgm> {
  /**
   * @generated from field: string bgm_audio_id = 1;
   */
  bgmAudioId: string;

  constructor(data?: PartialMessage<VideoWithBgm>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.VideoWithBgm";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VideoWithBgm;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VideoWithBgm;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VideoWithBgm;

  static equals(a: VideoWithBgm | PlainMessage<VideoWithBgm> | undefined, b: VideoWithBgm | PlainMessage<VideoWithBgm> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.ProcessVideoRsp
 */
export declare class ProcessVideoRsp extends Message<ProcessVideoRsp> {
  /**
   * @generated from field: step.raccoon.common.VideoInfo result_video_info = 1;
   */
  resultVideoInfo?: VideoInfo;

  constructor(data?: PartialMessage<ProcessVideoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.ProcessVideoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProcessVideoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProcessVideoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProcessVideoRsp;

  static equals(a: ProcessVideoRsp | PlainMessage<ProcessVideoRsp> | undefined, b: ProcessVideoRsp | PlainMessage<ProcessVideoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.AddMarkReq
 */
export declare class AddMarkReq extends Message<AddMarkReq> {
  /**
   * @generated from field: int64 origin_video_id = 1;
   */
  originVideoId: bigint;

  constructor(data?: PartialMessage<AddMarkReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.AddMarkReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddMarkReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddMarkReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddMarkReq;

  static equals(a: AddMarkReq | PlainMessage<AddMarkReq> | undefined, b: AddMarkReq | PlainMessage<AddMarkReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.AddMarkRsp
 */
export declare class AddMarkRsp extends Message<AddMarkRsp> {
  /**
   * @generated from field: step.raccoon.common.VideoInfo video_info = 1;
   */
  videoInfo?: VideoInfo;

  constructor(data?: PartialMessage<AddMarkRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.AddMarkRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddMarkRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddMarkRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddMarkRsp;

  static equals(a: AddMarkRsp | PlainMessage<AddMarkRsp> | undefined, b: AddMarkRsp | PlainMessage<AddMarkRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.AddBgmReq
 */
export declare class AddBgmReq extends Message<AddBgmReq> {
  /**
   * @generated from field: int64 origin_video_id = 1;
   */
  originVideoId: bigint;

  /**
   * @generated from field: string audio_id = 2;
   */
  audioId: string;

  /**
   * @generated from field: step.raccoon.common.MediaOptions options = 3;
   */
  options?: MediaOptions;

  constructor(data?: PartialMessage<AddBgmReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.AddBgmReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddBgmReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddBgmReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddBgmReq;

  static equals(a: AddBgmReq | PlainMessage<AddBgmReq> | undefined, b: AddBgmReq | PlainMessage<AddBgmReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.AddBgmRsp
 */
export declare class AddBgmRsp extends Message<AddBgmRsp> {
  /**
   * @generated from field: step.raccoon.common.VideoInfo video_info = 1;
   */
  videoInfo?: VideoInfo;

  constructor(data?: PartialMessage<AddBgmRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.AddBgmRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddBgmRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddBgmRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddBgmRsp;

  static equals(a: AddBgmRsp | PlainMessage<AddBgmRsp> | undefined, b: AddBgmRsp | PlainMessage<AddBgmRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.StartCreateVideoReq
 */
export declare class StartCreateVideoReq extends Message<StartCreateVideoReq> {
  /**
   * @generated from field: int64 video_id = 1;
   */
  videoId: bigint;

  /**
   * 是否进行模型服务异步生视频
   *
   * @generated from field: bool async = 2;
   */
  async: boolean;

  constructor(data?: PartialMessage<StartCreateVideoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.StartCreateVideoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StartCreateVideoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StartCreateVideoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StartCreateVideoReq;

  static equals(a: StartCreateVideoReq | PlainMessage<StartCreateVideoReq> | undefined, b: StartCreateVideoReq | PlainMessage<StartCreateVideoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.StartCreateVideoRsp
 */
export declare class StartCreateVideoRsp extends Message<StartCreateVideoRsp> {
  constructor(data?: PartialMessage<StartCreateVideoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.StartCreateVideoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StartCreateVideoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StartCreateVideoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StartCreateVideoRsp;

  static equals(a: StartCreateVideoRsp | PlainMessage<StartCreateVideoRsp> | undefined, b: StartCreateVideoRsp | PlainMessage<StartCreateVideoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.CancelCreateVideoReq
 */
export declare class CancelCreateVideoReq extends Message<CancelCreateVideoReq> {
  /**
   * @generated from field: int64 video_id = 1;
   */
  videoId: bigint;

  constructor(data?: PartialMessage<CancelCreateVideoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.CancelCreateVideoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CancelCreateVideoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CancelCreateVideoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CancelCreateVideoReq;

  static equals(a: CancelCreateVideoReq | PlainMessage<CancelCreateVideoReq> | undefined, b: CancelCreateVideoReq | PlainMessage<CancelCreateVideoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.CancelCreateVideoRsp
 */
export declare class CancelCreateVideoRsp extends Message<CancelCreateVideoRsp> {
  constructor(data?: PartialMessage<CancelCreateVideoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.CancelCreateVideoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CancelCreateVideoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CancelCreateVideoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CancelCreateVideoRsp;

  static equals(a: CancelCreateVideoRsp | PlainMessage<CancelCreateVideoRsp> | undefined, b: CancelCreateVideoRsp | PlainMessage<CancelCreateVideoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.AddDeriveReq
 */
export declare class AddDeriveReq extends Message<AddDeriveReq> {
  /**
   * @generated from field: int64 origin_video_id = 1;
   */
  originVideoId: bigint;

  /**
   * @generated from field: string derive_video_media_id = 2;
   */
  deriveVideoMediaId: string;

  constructor(data?: PartialMessage<AddDeriveReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.AddDeriveReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddDeriveReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddDeriveReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddDeriveReq;

  static equals(a: AddDeriveReq | PlainMessage<AddDeriveReq> | undefined, b: AddDeriveReq | PlainMessage<AddDeriveReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.AddDeriveRsp
 */
export declare class AddDeriveRsp extends Message<AddDeriveRsp> {
  /**
   * @generated from field: step.raccoon.common.VideoInfo video_info = 1;
   */
  videoInfo?: VideoInfo;

  constructor(data?: PartialMessage<AddDeriveRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.AddDeriveRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddDeriveRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddDeriveRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddDeriveRsp;

  static equals(a: AddDeriveRsp | PlainMessage<AddDeriveRsp> | undefined, b: AddDeriveRsp | PlainMessage<AddDeriveRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.TranscodeVideoParam
 */
export declare class TranscodeVideoParam extends Message<TranscodeVideoParam> {
  /**
   * 分辨率
   *
   * @generated from field: step.raccoon.common.VideoResolution resolution = 1;
   */
  resolution: VideoResolution;

  constructor(data?: PartialMessage<TranscodeVideoParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.TranscodeVideoParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TranscodeVideoParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TranscodeVideoParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TranscodeVideoParam;

  static equals(a: TranscodeVideoParam | PlainMessage<TranscodeVideoParam> | undefined, b: TranscodeVideoParam | PlainMessage<TranscodeVideoParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.TranscodeReq
 */
export declare class TranscodeReq extends Message<TranscodeReq> {
  /**
   * @generated from field: int64 video_id = 1;
   */
  videoId: bigint;

  /**
   * 转码参数
   *
   * @generated from field: repeated step.raccoon.video.TranscodeVideoParam params = 2;
   */
  params: TranscodeVideoParam[];

  constructor(data?: PartialMessage<TranscodeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.TranscodeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TranscodeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TranscodeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TranscodeReq;

  static equals(a: TranscodeReq | PlainMessage<TranscodeReq> | undefined, b: TranscodeReq | PlainMessage<TranscodeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.TranscodeRsp
 */
export declare class TranscodeRsp extends Message<TranscodeRsp> {
  /**
   * 任务id
   *
   * @generated from field: repeated string task_ids = 1;
   */
  taskIds: string[];

  constructor(data?: PartialMessage<TranscodeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.TranscodeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TranscodeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TranscodeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TranscodeRsp;

  static equals(a: TranscodeRsp | PlainMessage<TranscodeRsp> | undefined, b: TranscodeRsp | PlainMessage<TranscodeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.HandleOtakudanceReq
 */
export declare class HandleOtakudanceReq extends Message<HandleOtakudanceReq> {
  /**
   * base64的video二进制数据
   *
   * @generated from field: string video = 1;
   */
  video: string;

  /**
   * videogen 任务id
   *
   * @generated from field: string task_id = 2 [json_name = "task_id"];
   */
  taskId: string;

  /**
   * 结果，success-成功；fail-失败
   *
   * @generated from field: string status = 3;
   */
  status: string;

  constructor(data?: PartialMessage<HandleOtakudanceReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.HandleOtakudanceReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HandleOtakudanceReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HandleOtakudanceReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HandleOtakudanceReq;

  static equals(a: HandleOtakudanceReq | PlainMessage<HandleOtakudanceReq> | undefined, b: HandleOtakudanceReq | PlainMessage<HandleOtakudanceReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.video.HandleOtakudanceRsp
 */
export declare class HandleOtakudanceRsp extends Message<HandleOtakudanceRsp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<HandleOtakudanceRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.video.HandleOtakudanceRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HandleOtakudanceRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HandleOtakudanceRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HandleOtakudanceRsp;

  static equals(a: HandleOtakudanceRsp | PlainMessage<HandleOtakudanceRsp> | undefined, b: HandleOtakudanceRsp | PlainMessage<HandleOtakudanceRsp> | undefined): boolean;
}

