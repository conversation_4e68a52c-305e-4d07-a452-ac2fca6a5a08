// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/video/video.proto (package step.raccoon.video, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AddBgmReq, AddBgmRsp, AddDeriveReq, AddDeriveRsp, AddMarkReq, AddMarkRsp, CancelCreateVideoReq, CancelCreateVideoRsp, CreateVideoReq, CreateVideoRsp, GetVideoReq, GetVideoRsp, GetVideosReq, GetVideosRsp, HandleOtakudanceReq, HandleOtakudanceRsp, StartCreateVideoReq, StartCreateVideoRsp, TranscodeReq, TranscodeRsp } from "./video_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.video.Video
 */
export declare const Video: {
  readonly typeName: "step.raccoon.video.Video",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.video.Video.CreateVideo
     */
    readonly createVideo: {
      readonly name: "CreateVideo",
      readonly I: typeof CreateVideoReq,
      readonly O: typeof CreateVideoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.video.Video.StartCreateVideo
     */
    readonly startCreateVideo: {
      readonly name: "StartCreateVideo",
      readonly I: typeof StartCreateVideoReq,
      readonly O: typeof StartCreateVideoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.video.Video.CancelCreateVideo
     */
    readonly cancelCreateVideo: {
      readonly name: "CancelCreateVideo",
      readonly I: typeof CancelCreateVideoReq,
      readonly O: typeof CancelCreateVideoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.video.Video.GetVideo
     */
    readonly getVideo: {
      readonly name: "GetVideo",
      readonly I: typeof GetVideoReq,
      readonly O: typeof GetVideoRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.video.Video.GetVideos
     */
    readonly getVideos: {
      readonly name: "GetVideos",
      readonly I: typeof GetVideosReq,
      readonly O: typeof GetVideosRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.video.Video.AddMark
     */
    readonly addMark: {
      readonly name: "AddMark",
      readonly I: typeof AddMarkReq,
      readonly O: typeof AddMarkRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.video.Video.AddBgm
     */
    readonly addBgm: {
      readonly name: "AddBgm",
      readonly I: typeof AddBgmReq,
      readonly O: typeof AddBgmRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 业务自己实现的video派生，返回新的video信息
     *
     * @generated from rpc step.raccoon.video.Video.AddDerive
     */
    readonly addDerive: {
      readonly name: "AddDerive",
      readonly I: typeof AddDeriveReq,
      readonly O: typeof AddDeriveRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 转码
     *
     * @generated from rpc step.raccoon.video.Video.Transcode
     */
    readonly transcode: {
      readonly name: "Transcode",
      readonly I: typeof TranscodeReq,
      readonly O: typeof TranscodeRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

/**
 * 异步生视频的回调服务
 *
 * @generated from service step.raccoon.video.VideoAsyncCb
 */
export declare const VideoAsyncCb: {
  readonly typeName: "step.raccoon.video.VideoAsyncCb",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.video.VideoAsyncCb.HandleOtakudance
     */
    readonly handleOtakudance: {
      readonly name: "HandleOtakudance",
      readonly I: typeof HandleOtakudanceReq,
      readonly O: typeof HandleOtakudanceRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

