// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/admin/admin.proto (package step.raccoon.admin, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { AdminCode } from "../errorcode/errorcode_pb.js";

/**
 * @generated from message step.raccoon.admin.ErrorRes
 */
export declare class ErrorRes extends Message<ErrorRes> {
  /**
   * 错误状态码
   *
   * @generated from field: step.raccoon.errorcode.AdminCode code = 1;
   */
  code: AdminCode;

  /**
   * 错误信息
   *
   * @generated from field: string reason = 2;
   */
  reason: string;

  constructor(data?: PartialMessage<ErrorRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.admin.ErrorRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ErrorRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ErrorRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ErrorRes;

  static equals(a: ErrorRes | PlainMessage<ErrorRes> | undefined, b: ErrorRes | PlainMessage<ErrorRes> | undefined): boolean;
}

