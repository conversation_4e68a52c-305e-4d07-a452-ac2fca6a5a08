// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/usersphereinfo/usersphereinfo.proto (package step.raccoon.usersphereinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CreateUserWearAchievementReq, CreateUserWearAchievementRsp, DeleteUserWearPendantReq, DeleteUserWearPendantRsp, DelUserWearAchievementReq, DelUserWearAchievementRsp, GetAchievementsReq, GetAchievementsRsp, GetNextLevelDescReq, GetNextLevelDescRsp, GetPendantsReq, GetPendantsRsp, GetUpNewStatusReq, GetUpNewStatusRsp, GetUserFirstRewardObtainedReq, GetUserFirstRewardObtainedRsp, GetVisitedStatusReq, GetVisitedStatusRsp, UpdateUpNewStatusReq, UpdateUpNewStatusRsp, UpdateUserWearAchievementReq, UpdateUserWearAchievementRsp, UpdateUserWearPendantReq, UpdateUserWearPendantRsp, UpdateVisitStatusReq, UpdateVisitStatusRsp } from "./usersphereinfo_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * UserSphereInfo handles
 *
 * @generated from service step.raccoon.usersphereinfo.UserSphereInfo
 */
export declare const UserSphereInfo: {
  readonly typeName: "step.raccoon.usersphereinfo.UserSphereInfo",
  readonly methods: {
    /**
     * 获取挂件列表信息
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.GetPendants
     */
    readonly getPendants: {
      readonly name: "GetPendants",
      readonly I: typeof GetPendantsReq,
      readonly O: typeof GetPendantsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 切换挂件接口
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.UpdateUserWearPendant
     */
    readonly updateUserWearPendant: {
      readonly name: "UpdateUserWearPendant",
      readonly I: typeof UpdateUserWearPendantReq,
      readonly O: typeof UpdateUserWearPendantRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 卸下佩戴挂件
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.DeleteUserWearPendant
     */
    readonly deleteUserWearPendant: {
      readonly name: "DeleteUserWearPendant",
      readonly I: typeof DeleteUserWearPendantReq,
      readonly O: typeof DeleteUserWearPendantRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取是否有上新挂件
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.GetUpNewStatus
     */
    readonly getUpNewStatus: {
      readonly name: "GetUpNewStatus",
      readonly I: typeof GetUpNewStatusReq,
      readonly O: typeof GetUpNewStatusRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新上新挂件状态
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.UpdateUpNewStatus
     */
    readonly updateUpNewStatus: {
      readonly name: "UpdateUpNewStatus",
      readonly I: typeof UpdateUpNewStatusReq,
      readonly O: typeof UpdateUpNewStatusRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 用户是否访问过挂件页面
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.GetVisitedStatus
     */
    readonly getVisitedStatus: {
      readonly name: "GetVisitedStatus",
      readonly I: typeof GetVisitedStatusReq,
      readonly O: typeof GetVisitedStatusRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新用户访问发布页记录
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.UpdateVisitStatus
     */
    readonly updateVisitStatus: {
      readonly name: "UpdateVisitStatus",
      readonly I: typeof UpdateVisitStatusReq,
      readonly O: typeof UpdateVisitStatusRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 用户首次获取某种类型奖励结果
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.GetUserFirstRewardObtained
     */
    readonly getUserFirstRewardObtained: {
      readonly name: "GetUserFirstRewardObtained",
      readonly I: typeof GetUserFirstRewardObtainedReq,
      readonly O: typeof GetUserFirstRewardObtainedRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取称号列表信息
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.GetAchievements
     */
    readonly getAchievements: {
      readonly name: "GetAchievements",
      readonly I: typeof GetAchievementsReq,
      readonly O: typeof GetAchievementsRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 佩戴称号
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.CreateUserWearAchievement
     */
    readonly createUserWearAchievement: {
      readonly name: "CreateUserWearAchievement",
      readonly I: typeof CreateUserWearAchievementReq,
      readonly O: typeof CreateUserWearAchievementRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新称号
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.UpdateUserWearAchievement
     */
    readonly updateUserWearAchievement: {
      readonly name: "UpdateUserWearAchievement",
      readonly I: typeof UpdateUserWearAchievementReq,
      readonly O: typeof UpdateUserWearAchievementRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 卸下称号
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.DelUserWearAchievement
     */
    readonly delUserWearAchievement: {
      readonly name: "DelUserWearAchievement",
      readonly I: typeof DelUserWearAchievementReq,
      readonly O: typeof DelUserWearAchievementRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取下级称号的文案说明
     *
     * @generated from rpc step.raccoon.usersphereinfo.UserSphereInfo.GetNextLevelDesc
     */
    readonly getNextLevelDesc: {
      readonly name: "GetNextLevelDesc",
      readonly I: typeof GetNextLevelDescReq,
      readonly O: typeof GetNextLevelDescRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

