// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/usersphereinfo/usersphereinfo.proto (package step.raccoon.usersphereinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { FirstTriggerType, RewardType } from "../common/types_pb.js";
import type { AchievementInfo, AchievementType, Status } from "./common_pb.js";

/**
 * @generated from enum step.raccoon.usersphereinfo.PendantType
 */
export declare enum PendantType {
  /**
   * @generated from enum value: PENDANT_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: PENDANT_TYPE_NEW_PUBLISH_CARD = 1;
   */
  NEW_PUBLISH_CARD = 1,
}

/**
 * @generated from enum step.raccoon.usersphereinfo.VisitedStatus
 */
export declare enum VisitedStatus {
  /**
   * @generated from enum value: UNKNOWN_VISITED_STATUS = 0;
   */
  UNKNOWN_VISITED_STATUS = 0,

  /**
   * @generated from enum value: HAS_VISITED = 1;
   */
  HAS_VISITED = 1,

  /**
   * @generated from enum value: NO_VISITED = 2;
   */
  NO_VISITED = 2,
}

/**
 * @generated from enum step.raccoon.usersphereinfo.UpNewStatus
 */
export declare enum UpNewStatus {
  /**
   * @generated from enum value: UNKNOWN_UP_NEW_STATUS = 0;
   */
  UNKNOWN_UP_NEW_STATUS = 0,

  /**
   * 有上新挂件
   *
   * @generated from enum value: UP_NEW = 1;
   */
  UP_NEW = 1,

  /**
   * @generated from enum value: NO_UP_NEW = 2;
   */
  NO_UP_NEW = 2,
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateVisitStatusReq
 */
export declare class UpdateVisitStatusReq extends Message<UpdateVisitStatusReq> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.VisitedStatus visited_status = 1;
   */
  visitedStatus: VisitedStatus;

  constructor(data?: PartialMessage<UpdateVisitStatusReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateVisitStatusReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateVisitStatusReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateVisitStatusReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateVisitStatusReq;

  static equals(a: UpdateVisitStatusReq | PlainMessage<UpdateVisitStatusReq> | undefined, b: UpdateVisitStatusReq | PlainMessage<UpdateVisitStatusReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateVisitStatusRsp
 */
export declare class UpdateVisitStatusRsp extends Message<UpdateVisitStatusRsp> {
  /**
   * 是否访问过新挂件
   *
   * @generated from field: step.raccoon.usersphereinfo.VisitedStatus visited_status = 1;
   */
  visitedStatus: VisitedStatus;

  constructor(data?: PartialMessage<UpdateVisitStatusRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateVisitStatusRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateVisitStatusRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateVisitStatusRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateVisitStatusRsp;

  static equals(a: UpdateVisitStatusRsp | PlainMessage<UpdateVisitStatusRsp> | undefined, b: UpdateVisitStatusRsp | PlainMessage<UpdateVisitStatusRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetPendantsReq
 */
export declare class GetPendantsReq extends Message<GetPendantsReq> {
  constructor(data?: PartialMessage<GetPendantsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetPendantsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPendantsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPendantsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPendantsReq;

  static equals(a: GetPendantsReq | PlainMessage<GetPendantsReq> | undefined, b: GetPendantsReq | PlainMessage<GetPendantsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.RichPendantInfo
 */
export declare class RichPendantInfo extends Message<RichPendantInfo> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  /**
   * @generated from field: string pendant_url = 2;
   */
  pendantUrl: string;

  /**
   * 挂件名称
   *
   * @generated from field: string pendant_name = 3;
   */
  pendantName: string;

  /**
   * 解锁挂件所需条件
   *
   * @generated from field: string pendant_condition = 4;
   */
  pendantCondition: string;

  /**
   * 挂件的有效期文案
   *
   * @generated from field: string validity_period_desc = 5;
   */
  validityPeriodDesc: string;

  /**
   * 是否已解锁
   *
   * @generated from field: bool is_avaiable = 6;
   */
  isAvaiable: boolean;

  /**
   * 是否已过期
   *
   * @generated from field: bool is_expire = 7;
   */
  isExpire: boolean;

  /**
   * @generated from field: step.raccoon.usersphereinfo.PendantType pendant_type = 8;
   */
  pendantType: PendantType;

  /**
   * @generated from field: string button_content = 9;
   */
  buttonContent: string;

  constructor(data?: PartialMessage<RichPendantInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.RichPendantInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RichPendantInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RichPendantInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RichPendantInfo;

  static equals(a: RichPendantInfo | PlainMessage<RichPendantInfo> | undefined, b: RichPendantInfo | PlainMessage<RichPendantInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetPendantsRsp
 */
export declare class GetPendantsRsp extends Message<GetPendantsRsp> {
  /**
   * 挂件信息
   *
   * @generated from field: repeated step.raccoon.usersphereinfo.RichPendantInfo rich_pendant_infos = 1;
   */
  richPendantInfos: RichPendantInfo[];

  constructor(data?: PartialMessage<GetPendantsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetPendantsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPendantsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPendantsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPendantsRsp;

  static equals(a: GetPendantsRsp | PlainMessage<GetPendantsRsp> | undefined, b: GetPendantsRsp | PlainMessage<GetPendantsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateUserWearPendantReq
 */
export declare class UpdateUserWearPendantReq extends Message<UpdateUserWearPendantReq> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  constructor(data?: PartialMessage<UpdateUserWearPendantReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateUserWearPendantReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserWearPendantReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserWearPendantReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserWearPendantReq;

  static equals(a: UpdateUserWearPendantReq | PlainMessage<UpdateUserWearPendantReq> | undefined, b: UpdateUserWearPendantReq | PlainMessage<UpdateUserWearPendantReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateUserWearPendantRsp
 */
export declare class UpdateUserWearPendantRsp extends Message<UpdateUserWearPendantRsp> {
  constructor(data?: PartialMessage<UpdateUserWearPendantRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateUserWearPendantRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserWearPendantRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserWearPendantRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserWearPendantRsp;

  static equals(a: UpdateUserWearPendantRsp | PlainMessage<UpdateUserWearPendantRsp> | undefined, b: UpdateUserWearPendantRsp | PlainMessage<UpdateUserWearPendantRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetUpNewStatusReq
 */
export declare class GetUpNewStatusReq extends Message<GetUpNewStatusReq> {
  constructor(data?: PartialMessage<GetUpNewStatusReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetUpNewStatusReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUpNewStatusReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUpNewStatusReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUpNewStatusReq;

  static equals(a: GetUpNewStatusReq | PlainMessage<GetUpNewStatusReq> | undefined, b: GetUpNewStatusReq | PlainMessage<GetUpNewStatusReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetUpNewStatusRsp
 */
export declare class GetUpNewStatusRsp extends Message<GetUpNewStatusRsp> {
  /**
   * 是否有上架新挂件
   *
   * @generated from field: step.raccoon.usersphereinfo.UpNewStatus up_new_status = 1;
   */
  upNewStatus: UpNewStatus;

  constructor(data?: PartialMessage<GetUpNewStatusRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetUpNewStatusRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUpNewStatusRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUpNewStatusRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUpNewStatusRsp;

  static equals(a: GetUpNewStatusRsp | PlainMessage<GetUpNewStatusRsp> | undefined, b: GetUpNewStatusRsp | PlainMessage<GetUpNewStatusRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateUpNewStatusReq
 */
export declare class UpdateUpNewStatusReq extends Message<UpdateUpNewStatusReq> {
  constructor(data?: PartialMessage<UpdateUpNewStatusReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateUpNewStatusReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUpNewStatusReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUpNewStatusReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUpNewStatusReq;

  static equals(a: UpdateUpNewStatusReq | PlainMessage<UpdateUpNewStatusReq> | undefined, b: UpdateUpNewStatusReq | PlainMessage<UpdateUpNewStatusReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateUpNewStatusRsp
 */
export declare class UpdateUpNewStatusRsp extends Message<UpdateUpNewStatusRsp> {
  constructor(data?: PartialMessage<UpdateUpNewStatusRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateUpNewStatusRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUpNewStatusRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUpNewStatusRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUpNewStatusRsp;

  static equals(a: UpdateUpNewStatusRsp | PlainMessage<UpdateUpNewStatusRsp> | undefined, b: UpdateUpNewStatusRsp | PlainMessage<UpdateUpNewStatusRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetVisitedStatusReq
 */
export declare class GetVisitedStatusReq extends Message<GetVisitedStatusReq> {
  constructor(data?: PartialMessage<GetVisitedStatusReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetVisitedStatusReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVisitedStatusReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVisitedStatusReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVisitedStatusReq;

  static equals(a: GetVisitedStatusReq | PlainMessage<GetVisitedStatusReq> | undefined, b: GetVisitedStatusReq | PlainMessage<GetVisitedStatusReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetVisitedStatusRsp
 */
export declare class GetVisitedStatusRsp extends Message<GetVisitedStatusRsp> {
  /**
   * 是否访问过新挂件
   *
   * @generated from field: step.raccoon.usersphereinfo.VisitedStatus visited_status = 1;
   */
  visitedStatus: VisitedStatus;

  /**
   * @generated from field: step.raccoon.usersphereinfo.RichPendantInfo rich_pendant_info = 2;
   */
  richPendantInfo?: RichPendantInfo;

  constructor(data?: PartialMessage<GetVisitedStatusRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetVisitedStatusRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVisitedStatusRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVisitedStatusRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVisitedStatusRsp;

  static equals(a: GetVisitedStatusRsp | PlainMessage<GetVisitedStatusRsp> | undefined, b: GetVisitedStatusRsp | PlainMessage<GetVisitedStatusRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetUserFirstRewardObtainedReq
 */
export declare class GetUserFirstRewardObtainedReq extends Message<GetUserFirstRewardObtainedReq> {
  /**
   * 首次触发类型
   *
   * @generated from field: step.raccoon.common.FirstTriggerType first_trigger_type = 1;
   */
  firstTriggerType: FirstTriggerType;

  constructor(data?: PartialMessage<GetUserFirstRewardObtainedReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetUserFirstRewardObtainedReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserFirstRewardObtainedReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserFirstRewardObtainedReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserFirstRewardObtainedReq;

  static equals(a: GetUserFirstRewardObtainedReq | PlainMessage<GetUserFirstRewardObtainedReq> | undefined, b: GetUserFirstRewardObtainedReq | PlainMessage<GetUserFirstRewardObtainedReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetUserFirstRewardObtainedRsp
 */
export declare class GetUserFirstRewardObtainedRsp extends Message<GetUserFirstRewardObtainedRsp> {
  /**
   * @generated from field: string content = 1;
   */
  content: string;

  /**
   * 是否首次
   *
   * @generated from field: bool has_received_reward = 2;
   */
  hasReceivedReward: boolean;

  constructor(data?: PartialMessage<GetUserFirstRewardObtainedRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetUserFirstRewardObtainedRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserFirstRewardObtainedRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserFirstRewardObtainedRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserFirstRewardObtainedRsp;

  static equals(a: GetUserFirstRewardObtainedRsp | PlainMessage<GetUserFirstRewardObtainedRsp> | undefined, b: GetUserFirstRewardObtainedRsp | PlainMessage<GetUserFirstRewardObtainedRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.DeleteUserWearPendantReq
 */
export declare class DeleteUserWearPendantReq extends Message<DeleteUserWearPendantReq> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  constructor(data?: PartialMessage<DeleteUserWearPendantReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.DeleteUserWearPendantReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteUserWearPendantReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteUserWearPendantReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteUserWearPendantReq;

  static equals(a: DeleteUserWearPendantReq | PlainMessage<DeleteUserWearPendantReq> | undefined, b: DeleteUserWearPendantReq | PlainMessage<DeleteUserWearPendantReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.DeleteUserWearPendantRsp
 */
export declare class DeleteUserWearPendantRsp extends Message<DeleteUserWearPendantRsp> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  constructor(data?: PartialMessage<DeleteUserWearPendantRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.DeleteUserWearPendantRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteUserWearPendantRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteUserWearPendantRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteUserWearPendantRsp;

  static equals(a: DeleteUserWearPendantRsp | PlainMessage<DeleteUserWearPendantRsp> | undefined, b: DeleteUserWearPendantRsp | PlainMessage<DeleteUserWearPendantRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAchievementsReq
 */
export declare class GetAchievementsReq extends Message<GetAchievementsReq> {
  /**
   * 当前页最早的那条记录id
   *
   * @generated from field: optional string next_cursor = 1;
   */
  nextCursor?: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  constructor(data?: PartialMessage<GetAchievementsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAchievementsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAchievementsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAchievementsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAchievementsReq;

  static equals(a: GetAchievementsReq | PlainMessage<GetAchievementsReq> | undefined, b: GetAchievementsReq | PlainMessage<GetAchievementsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAchievementsRsp
 */
export declare class GetAchievementsRsp extends Message<GetAchievementsRsp> {
  /**
   * 称号信息
   *
   * @generated from field: repeated step.raccoon.usersphereinfo.AchievementInfo achievement_infos = 1;
   */
  achievementInfos: AchievementInfo[];

  /**
   * @generated from field: int32 total = 2;
   */
  total: number;

  /**
   * @generated from field: optional string next_cursor = 3;
   */
  nextCursor?: string;

  constructor(data?: PartialMessage<GetAchievementsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAchievementsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAchievementsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAchievementsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAchievementsRsp;

  static equals(a: GetAchievementsRsp | PlainMessage<GetAchievementsRsp> | undefined, b: GetAchievementsRsp | PlainMessage<GetAchievementsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateUserWearAchievementReq
 */
export declare class CreateUserWearAchievementReq extends Message<CreateUserWearAchievementReq> {
  /**
   * @generated from field: string achievement_id = 1;
   */
  achievementId: string;

  constructor(data?: PartialMessage<CreateUserWearAchievementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateUserWearAchievementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateUserWearAchievementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateUserWearAchievementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateUserWearAchievementReq;

  static equals(a: CreateUserWearAchievementReq | PlainMessage<CreateUserWearAchievementReq> | undefined, b: CreateUserWearAchievementReq | PlainMessage<CreateUserWearAchievementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateUserWearAchievementRsp
 */
export declare class CreateUserWearAchievementRsp extends Message<CreateUserWearAchievementRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AchievementInfo achievement_info = 1;
   */
  achievementInfo?: AchievementInfo;

  constructor(data?: PartialMessage<CreateUserWearAchievementRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateUserWearAchievementRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateUserWearAchievementRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateUserWearAchievementRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateUserWearAchievementRsp;

  static equals(a: CreateUserWearAchievementRsp | PlainMessage<CreateUserWearAchievementRsp> | undefined, b: CreateUserWearAchievementRsp | PlainMessage<CreateUserWearAchievementRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateUserWearAchievementReq
 */
export declare class UpdateUserWearAchievementReq extends Message<UpdateUserWearAchievementReq> {
  /**
   * @generated from field: string achievement_id = 1;
   */
  achievementId: string;

  /**
   * @generated from field: step.raccoon.usersphereinfo.Status status = 2;
   */
  status: Status;

  constructor(data?: PartialMessage<UpdateUserWearAchievementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateUserWearAchievementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserWearAchievementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserWearAchievementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserWearAchievementReq;

  static equals(a: UpdateUserWearAchievementReq | PlainMessage<UpdateUserWearAchievementReq> | undefined, b: UpdateUserWearAchievementReq | PlainMessage<UpdateUserWearAchievementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateUserWearAchievementRsp
 */
export declare class UpdateUserWearAchievementRsp extends Message<UpdateUserWearAchievementRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AchievementInfo achievement_info = 1;
   */
  achievementInfo?: AchievementInfo;

  /**
   * @generated from field: string msg = 2;
   */
  msg: string;

  constructor(data?: PartialMessage<UpdateUserWearAchievementRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateUserWearAchievementRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserWearAchievementRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserWearAchievementRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserWearAchievementRsp;

  static equals(a: UpdateUserWearAchievementRsp | PlainMessage<UpdateUserWearAchievementRsp> | undefined, b: UpdateUserWearAchievementRsp | PlainMessage<UpdateUserWearAchievementRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.DelUserWearAchievementReq
 */
export declare class DelUserWearAchievementReq extends Message<DelUserWearAchievementReq> {
  /**
   * @generated from field: string achievement_id = 1;
   */
  achievementId: string;

  constructor(data?: PartialMessage<DelUserWearAchievementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.DelUserWearAchievementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DelUserWearAchievementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DelUserWearAchievementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DelUserWearAchievementReq;

  static equals(a: DelUserWearAchievementReq | PlainMessage<DelUserWearAchievementReq> | undefined, b: DelUserWearAchievementReq | PlainMessage<DelUserWearAchievementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.DelUserWearAchievementRsp
 */
export declare class DelUserWearAchievementRsp extends Message<DelUserWearAchievementRsp> {
  /**
   * @generated from field: string msg = 1;
   */
  msg: string;

  constructor(data?: PartialMessage<DelUserWearAchievementRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.DelUserWearAchievementRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DelUserWearAchievementRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DelUserWearAchievementRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DelUserWearAchievementRsp;

  static equals(a: DelUserWearAchievementRsp | PlainMessage<DelUserWearAchievementRsp> | undefined, b: DelUserWearAchievementRsp | PlainMessage<DelUserWearAchievementRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetNextLevelDescReq
 */
export declare class GetNextLevelDescReq extends Message<GetNextLevelDescReq> {
  /**
   * @generated from field: string level = 1;
   */
  level: string;

  /**
   * @generated from field: step.raccoon.usersphereinfo.AchievementType achievement_type = 2;
   */
  achievementType: AchievementType;

  constructor(data?: PartialMessage<GetNextLevelDescReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetNextLevelDescReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetNextLevelDescReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetNextLevelDescReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetNextLevelDescReq;

  static equals(a: GetNextLevelDescReq | PlainMessage<GetNextLevelDescReq> | undefined, b: GetNextLevelDescReq | PlainMessage<GetNextLevelDescReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetNextLevelDescRsp
 */
export declare class GetNextLevelDescRsp extends Message<GetNextLevelDescRsp> {
  /**
   * @generated from field: repeated step.raccoon.usersphereinfo.GetNextLevelDescRsp.Reward reward_list = 1;
   */
  rewardList: GetNextLevelDescRsp_Reward[];

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: repeated string text = 3;
   */
  text: string[];

  /**
   * @generated from field: string next_level = 4;
   */
  nextLevel: string;

  constructor(data?: PartialMessage<GetNextLevelDescRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetNextLevelDescRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetNextLevelDescRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetNextLevelDescRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetNextLevelDescRsp;

  static equals(a: GetNextLevelDescRsp | PlainMessage<GetNextLevelDescRsp> | undefined, b: GetNextLevelDescRsp | PlainMessage<GetNextLevelDescRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetNextLevelDescRsp.Reward
 */
export declare class GetNextLevelDescRsp_Reward extends Message<GetNextLevelDescRsp_Reward> {
  /**
   * @generated from field: step.raccoon.common.RewardType reward_type = 1;
   */
  rewardType: RewardType;

  /**
   * @generated from field: int32 cnt = 2;
   */
  cnt: number;

  /**
   * @generated from field: string url = 3;
   */
  url: string;

  constructor(data?: PartialMessage<GetNextLevelDescRsp_Reward>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetNextLevelDescRsp.Reward";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetNextLevelDescRsp_Reward;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetNextLevelDescRsp_Reward;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetNextLevelDescRsp_Reward;

  static equals(a: GetNextLevelDescRsp_Reward | PlainMessage<GetNextLevelDescRsp_Reward> | undefined, b: GetNextLevelDescRsp_Reward | PlainMessage<GetNextLevelDescRsp_Reward> | undefined): boolean;
}

