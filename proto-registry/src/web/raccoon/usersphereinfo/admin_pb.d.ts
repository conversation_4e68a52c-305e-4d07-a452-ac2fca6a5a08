// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/usersphereinfo/admin.proto (package step.raccoon.usersphereinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { AchievementInfo, Status, ValidityPeriodType } from "./common_pb.js";
import type { Pagination } from "../common/utils_pb.js";

/**
 * @generated from enum step.raccoon.usersphereinfo.AvatarPendantStatus
 */
export declare enum AvatarPendantStatus {
  /**
   * @generated from enum value: AVATAR_PENDANT_STATUS_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: AVATAR_PENDANT_STATUS_ONLINE = 1;
   */
  ONLINE = 1,

  /**
   * @generated from enum value: AVATAR_PENDANT_STATUS_OFFLINE = 2;
   */
  OFFLINE = 2,
}

/**
 * @generated from message step.raccoon.usersphereinfo.AvatarPendant
 */
export declare class AvatarPendant extends Message<AvatarPendant> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  /**
   * 挂件名称
   *
   * @generated from field: string pendant_title = 2;
   */
  pendantTitle: string;

  /**
   * @generated from field: string pendant_type_id = 3;
   */
  pendantTypeId: string;

  /**
   * 挂件素材类型名称
   *
   * @generated from field: string pendant_type_name = 4;
   */
  pendantTypeName: string;

  /**
   * @generated from field: string pendant_url = 5;
   */
  pendantUrl: string;

  /**
   * 解锁条件
   *
   * @generated from field: string condition = 6;
   */
  condition: string;

  /**
   * 恭喜获得【新人报道】挂件及锂电池x200
   *
   * @generated from field: string toast_content = 7;
   */
  toastContent: string;

  /**
   * 发作品
   *
   * @generated from field: string button_content = 8;
   */
  buttonContent: string;

  /**
   * 有效期
   *
   * @generated from field: step.raccoon.usersphereinfo.ValidityPeriodType validity_period_type = 9;
   */
  validityPeriodType: ValidityPeriodType;

  /**
   * 过期时间: 秒级时间戳
   *
   * @generated from field: string expired_at = 10;
   */
  expiredAt: string;

  /**
   * @generated from field: step.raccoon.usersphereinfo.AvatarPendantStatus avatar_pendant_status = 11;
   */
  avatarPendantStatus: AvatarPendantStatus;

  /**
   * @generated from field: map<string, string> extra = 12;
   */
  extra: { [key: string]: string };

  /**
   * 创建时间
   *
   * @generated from field: string created_at = 13;
   */
  createdAt: string;

  /**
   * 挂件图片id 
   *
   * @generated from field: string pendant_image_id = 14;
   */
  pendantImageId: string;

  constructor(data?: PartialMessage<AvatarPendant>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AvatarPendant";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AvatarPendant;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AvatarPendant;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AvatarPendant;

  static equals(a: AvatarPendant | PlainMessage<AvatarPendant> | undefined, b: AvatarPendant | PlainMessage<AvatarPendant> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAvatarPendantReq
 */
export declare class GetAvatarPendantReq extends Message<GetAvatarPendantReq> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  constructor(data?: PartialMessage<GetAvatarPendantReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAvatarPendantReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAvatarPendantReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAvatarPendantReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAvatarPendantReq;

  static equals(a: GetAvatarPendantReq | PlainMessage<GetAvatarPendantReq> | undefined, b: GetAvatarPendantReq | PlainMessage<GetAvatarPendantReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAvatarPendantRsp
 */
export declare class GetAvatarPendantRsp extends Message<GetAvatarPendantRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AvatarPendant avatar_pendant = 1;
   */
  avatarPendant?: AvatarPendant;

  constructor(data?: PartialMessage<GetAvatarPendantRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAvatarPendantRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAvatarPendantRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAvatarPendantRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAvatarPendantRsp;

  static equals(a: GetAvatarPendantRsp | PlainMessage<GetAvatarPendantRsp> | undefined, b: GetAvatarPendantRsp | PlainMessage<GetAvatarPendantRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAvatarPendantsReq
 */
export declare class GetAvatarPendantsReq extends Message<GetAvatarPendantsReq> {
  /**
   * 当前页最早的那条记录id
   *
   * @generated from field: string next_cursor = 1;
   */
  nextCursor: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  constructor(data?: PartialMessage<GetAvatarPendantsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAvatarPendantsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAvatarPendantsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAvatarPendantsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAvatarPendantsReq;

  static equals(a: GetAvatarPendantsReq | PlainMessage<GetAvatarPendantsReq> | undefined, b: GetAvatarPendantsReq | PlainMessage<GetAvatarPendantsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAvatarPendantsRsp
 */
export declare class GetAvatarPendantsRsp extends Message<GetAvatarPendantsRsp> {
  /**
   * @generated from field: repeated step.raccoon.usersphereinfo.AvatarPendant avatar_pendants = 1;
   */
  avatarPendants: AvatarPendant[];

  /**
   * @generated from field: int32 total = 2;
   */
  total: number;

  /**
   * @generated from field: string next_cursor = 3;
   */
  nextCursor: string;

  constructor(data?: PartialMessage<GetAvatarPendantsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAvatarPendantsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAvatarPendantsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAvatarPendantsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAvatarPendantsRsp;

  static equals(a: GetAvatarPendantsRsp | PlainMessage<GetAvatarPendantsRsp> | undefined, b: GetAvatarPendantsRsp | PlainMessage<GetAvatarPendantsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateAvatarPendantReq
 */
export declare class UpdateAvatarPendantReq extends Message<UpdateAvatarPendantReq> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  /**
   * 挂件名称
   *
   * @generated from field: string pendant_title = 2;
   */
  pendantTitle: string;

  /**
   * 挂件图片id
   *
   * @generated from field: string pendant_image_id = 3;
   */
  pendantImageId: string;

  /**
   * 挂件素材类型id
   *
   * @generated from field: string pendant_type_id = 4;
   */
  pendantTypeId: string;

  /**
   * 挂件素材类型名称
   *
   * @generated from field: string pendant_type_name = 5;
   */
  pendantTypeName: string;

  /**
   * 解锁条件
   *
   * @generated from field: string condition = 6;
   */
  condition: string;

  /**
   * 恭喜获得【新人报道】挂件及锂电池x200
   *
   * @generated from field: string toast_content = 7;
   */
  toastContent: string;

  /**
   * 发作品
   *
   * @generated from field: string button_content = 8;
   */
  buttonContent: string;

  /**
   * 有效期
   *
   * @generated from field: step.raccoon.usersphereinfo.ValidityPeriodType validity_period_type = 9;
   */
  validityPeriodType: ValidityPeriodType;

  /**
   * @generated from field: map<string, string> extra = 10;
   */
  extra: { [key: string]: string };

  /**
   * 过期时间: 秒级时间戳
   *
   * @generated from field: string expired_at = 11;
   */
  expiredAt: string;

  /**
   * @generated from field: step.raccoon.usersphereinfo.AvatarPendantStatus avatar_pendant_status = 12;
   */
  avatarPendantStatus: AvatarPendantStatus;

  constructor(data?: PartialMessage<UpdateAvatarPendantReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateAvatarPendantReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAvatarPendantReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAvatarPendantReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAvatarPendantReq;

  static equals(a: UpdateAvatarPendantReq | PlainMessage<UpdateAvatarPendantReq> | undefined, b: UpdateAvatarPendantReq | PlainMessage<UpdateAvatarPendantReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateAvatarPendantRsp
 */
export declare class UpdateAvatarPendantRsp extends Message<UpdateAvatarPendantRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AvatarPendant avatar_pendant = 1;
   */
  avatarPendant?: AvatarPendant;

  constructor(data?: PartialMessage<UpdateAvatarPendantRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateAvatarPendantRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAvatarPendantRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAvatarPendantRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAvatarPendantRsp;

  static equals(a: UpdateAvatarPendantRsp | PlainMessage<UpdateAvatarPendantRsp> | undefined, b: UpdateAvatarPendantRsp | PlainMessage<UpdateAvatarPendantRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateAvatarPendantReq
 */
export declare class CreateAvatarPendantReq extends Message<CreateAvatarPendantReq> {
  /**
   * 挂件名称
   *
   * @generated from field: string pendant_title = 1;
   */
  pendantTitle: string;

  /**
   * 挂件图片id
   *
   * @generated from field: string pendant_image_id = 2;
   */
  pendantImageId: string;

  /**
   * 挂件素材类型id
   *
   * @generated from field: string pendant_type_id = 3;
   */
  pendantTypeId: string;

  /**
   * 挂件素材类型名称
   *
   * @generated from field: string pendant_type_name = 4;
   */
  pendantTypeName: string;

  /**
   * 解锁条件
   *
   * @generated from field: string condition = 5;
   */
  condition: string;

  /**
   * 恭喜获得【新人报道】挂件及锂电池x200
   *
   * @generated from field: string toast_content = 6;
   */
  toastContent: string;

  /**
   * 发作品
   *
   * @generated from field: string button_content = 7;
   */
  buttonContent: string;

  /**
   * 有效期
   *
   * @generated from field: step.raccoon.usersphereinfo.ValidityPeriodType validity_period_type = 8;
   */
  validityPeriodType: ValidityPeriodType;

  /**
   * @generated from field: map<string, string> extra = 9;
   */
  extra: { [key: string]: string };

  /**
   * 过期时间: 秒级时间戳
   *
   * @generated from field: string expired_at = 10;
   */
  expiredAt: string;

  constructor(data?: PartialMessage<CreateAvatarPendantReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateAvatarPendantReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAvatarPendantReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAvatarPendantReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAvatarPendantReq;

  static equals(a: CreateAvatarPendantReq | PlainMessage<CreateAvatarPendantReq> | undefined, b: CreateAvatarPendantReq | PlainMessage<CreateAvatarPendantReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateAvatarPendantRsp
 */
export declare class CreateAvatarPendantRsp extends Message<CreateAvatarPendantRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AvatarPendant avatar_pendant = 1;
   */
  avatarPendant?: AvatarPendant;

  constructor(data?: PartialMessage<CreateAvatarPendantRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateAvatarPendantRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAvatarPendantRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAvatarPendantRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAvatarPendantRsp;

  static equals(a: CreateAvatarPendantRsp | PlainMessage<CreateAvatarPendantRsp> | undefined, b: CreateAvatarPendantRsp | PlainMessage<CreateAvatarPendantRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.PublishPendantReq
 */
export declare class PublishPendantReq extends Message<PublishPendantReq> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  constructor(data?: PartialMessage<PublishPendantReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.PublishPendantReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishPendantReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishPendantReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishPendantReq;

  static equals(a: PublishPendantReq | PlainMessage<PublishPendantReq> | undefined, b: PublishPendantReq | PlainMessage<PublishPendantReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.PublishPendantRsp
 */
export declare class PublishPendantRsp extends Message<PublishPendantRsp> {
  constructor(data?: PartialMessage<PublishPendantRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.PublishPendantRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishPendantRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishPendantRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishPendantRsp;

  static equals(a: PublishPendantRsp | PlainMessage<PublishPendantRsp> | undefined, b: PublishPendantRsp | PlainMessage<PublishPendantRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UnPublishPendantReq
 */
export declare class UnPublishPendantReq extends Message<UnPublishPendantReq> {
  /**
   * @generated from field: string pendant_id = 1;
   */
  pendantId: string;

  constructor(data?: PartialMessage<UnPublishPendantReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UnPublishPendantReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnPublishPendantReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnPublishPendantReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnPublishPendantReq;

  static equals(a: UnPublishPendantReq | PlainMessage<UnPublishPendantReq> | undefined, b: UnPublishPendantReq | PlainMessage<UnPublishPendantReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UnPublishPendantRsp
 */
export declare class UnPublishPendantRsp extends Message<UnPublishPendantRsp> {
  constructor(data?: PartialMessage<UnPublishPendantRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UnPublishPendantRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnPublishPendantRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnPublishPendantRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnPublishPendantRsp;

  static equals(a: UnPublishPendantRsp | PlainMessage<UnPublishPendantRsp> | undefined, b: UnPublishPendantRsp | PlainMessage<UnPublishPendantRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.AllocatePendantReq
 */
export declare class AllocatePendantReq extends Message<AllocatePendantReq> {
  /**
   * @generated from field: string file_name = 1;
   */
  fileName: string;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: string file_url = 3;
   */
  fileUrl: string;

  /**
   * @generated from field: string operator = 4;
   */
  operator: string;

  constructor(data?: PartialMessage<AllocatePendantReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AllocatePendantReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AllocatePendantReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AllocatePendantReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AllocatePendantReq;

  static equals(a: AllocatePendantReq | PlainMessage<AllocatePendantReq> | undefined, b: AllocatePendantReq | PlainMessage<AllocatePendantReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.AllocatePendantRecord
 */
export declare class AllocatePendantRecord extends Message<AllocatePendantRecord> {
  /**
   * @generated from field: string allocate_id = 1;
   */
  allocateId: string;

  /**
   * @generated from field: string allocate_time = 2;
   */
  allocateTime: string;

  /**
   * @generated from field: string operator = 3;
   */
  operator: string;

  /**
   * @generated from field: string file_key = 4;
   */
  fileKey: string;

  /**
   * @generated from field: string file_url = 5;
   */
  fileUrl: string;

  /**
   * @generated from field: string file_name = 6;
   */
  fileName: string;

  constructor(data?: PartialMessage<AllocatePendantRecord>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AllocatePendantRecord";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AllocatePendantRecord;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AllocatePendantRecord;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AllocatePendantRecord;

  static equals(a: AllocatePendantRecord | PlainMessage<AllocatePendantRecord> | undefined, b: AllocatePendantRecord | PlainMessage<AllocatePendantRecord> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.AllocatePendantRsp
 */
export declare class AllocatePendantRsp extends Message<AllocatePendantRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AllocatePendantRecord allocate_pendant_record = 1;
   */
  allocatePendantRecord?: AllocatePendantRecord;

  constructor(data?: PartialMessage<AllocatePendantRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AllocatePendantRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AllocatePendantRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AllocatePendantRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AllocatePendantRsp;

  static equals(a: AllocatePendantRsp | PlainMessage<AllocatePendantRsp> | undefined, b: AllocatePendantRsp | PlainMessage<AllocatePendantRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAllocateRecordsReq
 */
export declare class GetAllocateRecordsReq extends Message<GetAllocateRecordsReq> {
  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<GetAllocateRecordsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAllocateRecordsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllocateRecordsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllocateRecordsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllocateRecordsReq;

  static equals(a: GetAllocateRecordsReq | PlainMessage<GetAllocateRecordsReq> | undefined, b: GetAllocateRecordsReq | PlainMessage<GetAllocateRecordsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAllocateRecordsRsp
 */
export declare class GetAllocateRecordsRsp extends Message<GetAllocateRecordsRsp> {
  /**
   * @generated from field: repeated step.raccoon.usersphereinfo.AllocatePendantRecord allocate_pendant_records = 1;
   */
  allocatePendantRecords: AllocatePendantRecord[];

  /**
   * @generated from field: int32 total = 2;
   */
  total: number;

  /**
   * @generated from field: string next_cursor = 3;
   */
  nextCursor: string;

  constructor(data?: PartialMessage<GetAllocateRecordsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAllocateRecordsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllocateRecordsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllocateRecordsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllocateRecordsRsp;

  static equals(a: GetAllocateRecordsRsp | PlainMessage<GetAllocateRecordsRsp> | undefined, b: GetAllocateRecordsRsp | PlainMessage<GetAllocateRecordsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateAvatarPendantTypeReq
 */
export declare class CreateAvatarPendantTypeReq extends Message<CreateAvatarPendantTypeReq> {
  /**
   * 挂件素材类型名称
   *
   * @generated from field: string pendant_type_name = 1;
   */
  pendantTypeName: string;

  /**
   * 挂件素材说明
   *
   * @generated from field: string pendant_type_desc = 2;
   */
  pendantTypeDesc: string;

  /**
   * @generated from field: string operator = 3;
   */
  operator: string;

  constructor(data?: PartialMessage<CreateAvatarPendantTypeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateAvatarPendantTypeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAvatarPendantTypeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAvatarPendantTypeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAvatarPendantTypeReq;

  static equals(a: CreateAvatarPendantTypeReq | PlainMessage<CreateAvatarPendantTypeReq> | undefined, b: CreateAvatarPendantTypeReq | PlainMessage<CreateAvatarPendantTypeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateAvatarPendantTypeRsp
 */
export declare class CreateAvatarPendantTypeRsp extends Message<CreateAvatarPendantTypeRsp> {
  /**
   * 挂件素材类型id
   *
   * @generated from field: step.raccoon.usersphereinfo.AvatarPendantTypeInfo avatar_pendant_type_info = 1;
   */
  avatarPendantTypeInfo?: AvatarPendantTypeInfo;

  constructor(data?: PartialMessage<CreateAvatarPendantTypeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateAvatarPendantTypeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAvatarPendantTypeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAvatarPendantTypeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAvatarPendantTypeRsp;

  static equals(a: CreateAvatarPendantTypeRsp | PlainMessage<CreateAvatarPendantTypeRsp> | undefined, b: CreateAvatarPendantTypeRsp | PlainMessage<CreateAvatarPendantTypeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateAvatarPendantTypeReq
 */
export declare class UpdateAvatarPendantTypeReq extends Message<UpdateAvatarPendantTypeReq> {
  /**
   * @generated from field: string pendant_type_id = 1;
   */
  pendantTypeId: string;

  /**
   * @generated from field: string pendant_type_name = 2;
   */
  pendantTypeName: string;

  /**
   * 挂件素材说明
   *
   * @generated from field: string pendant_type_desc = 3;
   */
  pendantTypeDesc: string;

  /**
   * @generated from field: string operator = 4;
   */
  operator: string;

  constructor(data?: PartialMessage<UpdateAvatarPendantTypeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateAvatarPendantTypeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAvatarPendantTypeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAvatarPendantTypeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAvatarPendantTypeReq;

  static equals(a: UpdateAvatarPendantTypeReq | PlainMessage<UpdateAvatarPendantTypeReq> | undefined, b: UpdateAvatarPendantTypeReq | PlainMessage<UpdateAvatarPendantTypeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateAvatarPendantTypeRsp
 */
export declare class UpdateAvatarPendantTypeRsp extends Message<UpdateAvatarPendantTypeRsp> {
  /**
   * 挂件素材类型id
   *
   * @generated from field: step.raccoon.usersphereinfo.AvatarPendantTypeInfo avatar_pendant_type_info = 1;
   */
  avatarPendantTypeInfo?: AvatarPendantTypeInfo;

  constructor(data?: PartialMessage<UpdateAvatarPendantTypeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateAvatarPendantTypeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAvatarPendantTypeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAvatarPendantTypeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAvatarPendantTypeRsp;

  static equals(a: UpdateAvatarPendantTypeRsp | PlainMessage<UpdateAvatarPendantTypeRsp> | undefined, b: UpdateAvatarPendantTypeRsp | PlainMessage<UpdateAvatarPendantTypeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAvatarPendantTypeReq
 */
export declare class GetAvatarPendantTypeReq extends Message<GetAvatarPendantTypeReq> {
  constructor(data?: PartialMessage<GetAvatarPendantTypeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAvatarPendantTypeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAvatarPendantTypeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAvatarPendantTypeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAvatarPendantTypeReq;

  static equals(a: GetAvatarPendantTypeReq | PlainMessage<GetAvatarPendantTypeReq> | undefined, b: GetAvatarPendantTypeReq | PlainMessage<GetAvatarPendantTypeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.AvatarPendantTypeInfo
 */
export declare class AvatarPendantTypeInfo extends Message<AvatarPendantTypeInfo> {
  /**
   * @generated from field: string pendant_type_id = 1;
   */
  pendantTypeId: string;

  /**
   * @generated from field: string pendant_type_name = 2;
   */
  pendantTypeName: string;

  /**
   * 挂件素材说明
   *
   * @generated from field: string pendant_type_desc = 3;
   */
  pendantTypeDesc: string;

  /**
   * @generated from field: string operator = 4;
   */
  operator: string;

  constructor(data?: PartialMessage<AvatarPendantTypeInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AvatarPendantTypeInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AvatarPendantTypeInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AvatarPendantTypeInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AvatarPendantTypeInfo;

  static equals(a: AvatarPendantTypeInfo | PlainMessage<AvatarPendantTypeInfo> | undefined, b: AvatarPendantTypeInfo | PlainMessage<AvatarPendantTypeInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAvatarPendantTypeRsp
 */
export declare class GetAvatarPendantTypeRsp extends Message<GetAvatarPendantTypeRsp> {
  /**
   * @generated from field: repeated step.raccoon.usersphereinfo.AvatarPendantTypeInfo avatar_pendant_type_infos = 1;
   */
  avatarPendantTypeInfos: AvatarPendantTypeInfo[];

  /**
   * @generated from field: int32 total = 2;
   */
  total: number;

  /**
   * @generated from field: string next_cursor = 3;
   */
  nextCursor: string;

  constructor(data?: PartialMessage<GetAvatarPendantTypeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAvatarPendantTypeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAvatarPendantTypeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAvatarPendantTypeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAvatarPendantTypeRsp;

  static equals(a: GetAvatarPendantTypeRsp | PlainMessage<GetAvatarPendantTypeRsp> | undefined, b: GetAvatarPendantTypeRsp | PlainMessage<GetAvatarPendantTypeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.AchievementTypeInfo
 */
export declare class AchievementTypeInfo extends Message<AchievementTypeInfo> {
  /**
   * @generated from field: string achievement_type_id = 1;
   */
  achievementTypeId: string;

  /**
   * @generated from field: string achievement_type_name = 2;
   */
  achievementTypeName: string;

  /**
   * 称号素材说明
   *
   * @generated from field: string achievement_type_desc = 3;
   */
  achievementTypeDesc: string;

  /**
   * @generated from field: string operator = 4;
   */
  operator: string;

  constructor(data?: PartialMessage<AchievementTypeInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AchievementTypeInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AchievementTypeInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AchievementTypeInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AchievementTypeInfo;

  static equals(a: AchievementTypeInfo | PlainMessage<AchievementTypeInfo> | undefined, b: AchievementTypeInfo | PlainMessage<AchievementTypeInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateAchievementTypeReq
 */
export declare class CreateAchievementTypeReq extends Message<CreateAchievementTypeReq> {
  /**
   * 称号素材类型名称
   *
   * @generated from field: string achievement_type_name = 1;
   */
  achievementTypeName: string;

  /**
   * 称号素材说明
   *
   * @generated from field: string achievement_type_desc = 2;
   */
  achievementTypeDesc: string;

  constructor(data?: PartialMessage<CreateAchievementTypeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateAchievementTypeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAchievementTypeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAchievementTypeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAchievementTypeReq;

  static equals(a: CreateAchievementTypeReq | PlainMessage<CreateAchievementTypeReq> | undefined, b: CreateAchievementTypeReq | PlainMessage<CreateAchievementTypeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateAchievementTypeRsp
 */
export declare class CreateAchievementTypeRsp extends Message<CreateAchievementTypeRsp> {
  /**
   * 称号素材类型id
   *
   * @generated from field: step.raccoon.usersphereinfo.AchievementTypeInfo achievement_type_info = 1;
   */
  achievementTypeInfo?: AchievementTypeInfo;

  constructor(data?: PartialMessage<CreateAchievementTypeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateAchievementTypeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAchievementTypeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAchievementTypeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAchievementTypeRsp;

  static equals(a: CreateAchievementTypeRsp | PlainMessage<CreateAchievementTypeRsp> | undefined, b: CreateAchievementTypeRsp | PlainMessage<CreateAchievementTypeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateAchievementTypeReq
 */
export declare class UpdateAchievementTypeReq extends Message<UpdateAchievementTypeReq> {
  /**
   * @generated from field: string achievement_type_id = 1;
   */
  achievementTypeId: string;

  /**
   * 称号素材类型名称
   *
   * @generated from field: string achievement_type_name = 2;
   */
  achievementTypeName: string;

  /**
   * 称号素材说明
   *
   * @generated from field: string achievement_type_desc = 3;
   */
  achievementTypeDesc: string;

  constructor(data?: PartialMessage<UpdateAchievementTypeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateAchievementTypeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAchievementTypeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAchievementTypeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAchievementTypeReq;

  static equals(a: UpdateAchievementTypeReq | PlainMessage<UpdateAchievementTypeReq> | undefined, b: UpdateAchievementTypeReq | PlainMessage<UpdateAchievementTypeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateAchievementTypeRsp
 */
export declare class UpdateAchievementTypeRsp extends Message<UpdateAchievementTypeRsp> {
  /**
   * 称号素材类型id
   *
   * @generated from field: step.raccoon.usersphereinfo.AchievementTypeInfo achievement_type_info = 1;
   */
  achievementTypeInfo?: AchievementTypeInfo;

  constructor(data?: PartialMessage<UpdateAchievementTypeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateAchievementTypeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAchievementTypeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAchievementTypeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAchievementTypeRsp;

  static equals(a: UpdateAchievementTypeRsp | PlainMessage<UpdateAchievementTypeRsp> | undefined, b: UpdateAchievementTypeRsp | PlainMessage<UpdateAchievementTypeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAchievementTypeReq
 */
export declare class GetAchievementTypeReq extends Message<GetAchievementTypeReq> {
  constructor(data?: PartialMessage<GetAchievementTypeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAchievementTypeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAchievementTypeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAchievementTypeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAchievementTypeReq;

  static equals(a: GetAchievementTypeReq | PlainMessage<GetAchievementTypeReq> | undefined, b: GetAchievementTypeReq | PlainMessage<GetAchievementTypeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAchievementTypeRsp
 */
export declare class GetAchievementTypeRsp extends Message<GetAchievementTypeRsp> {
  /**
   * @generated from field: repeated step.raccoon.usersphereinfo.AchievementTypeInfo achievement_type_infos = 1;
   */
  achievementTypeInfos: AchievementTypeInfo[];

  /**
   * @generated from field: int32 total = 2;
   */
  total: number;

  constructor(data?: PartialMessage<GetAchievementTypeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAchievementTypeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAchievementTypeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAchievementTypeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAchievementTypeRsp;

  static equals(a: GetAchievementTypeRsp | PlainMessage<GetAchievementTypeRsp> | undefined, b: GetAchievementTypeRsp | PlainMessage<GetAchievementTypeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateAchievementReq
 */
export declare class CreateAchievementReq extends Message<CreateAchievementReq> {
  /**
   * 称号名称
   *
   * @generated from field: string achievement_title = 1;
   */
  achievementTitle: string;

  /**
   * 称号素材类型id
   *
   * @generated from field: string achievement_type_id = 2;
   */
  achievementTypeId: string;

  /**
   * 是否为等级称号
   *
   * @generated from field: bool is_level = 3;
   */
  isLevel: boolean;

  /**
   * 等级
   *
   * @generated from field: string level = 4;
   */
  level: string;

  /**
   * icon图片id
   *
   * @generated from field: string achievement_icon_image_id = 5;
   */
  achievementIconImageId: string;

  /**
   * 称号图片id
   *
   * @generated from field: string achievement_image_id = 6;
   */
  achievementImageId: string;

  /**
   * 解锁条件
   *
   * @generated from field: string condition = 7;
   */
  condition: string;

  /**
   * 恭喜获得【评论狸】称号及锂电池x200
   *
   * @generated from field: string toast_content = 8;
   */
  toastContent: string;

  /**
   * 发作品
   *
   * @generated from field: string button_content = 9;
   */
  buttonContent: string;

  /**
   * 有效期
   *
   * @generated from field: step.raccoon.usersphereinfo.ValidityPeriodType validity_period_type = 10;
   */
  validityPeriodType: ValidityPeriodType;

  /**
   * @generated from field: map<string, string> extra = 11;
   */
  extra: { [key: string]: string };

  /**
   * 过期时间: 秒级时间戳
   *
   * @generated from field: string expired_at = 12;
   */
  expiredAt: string;

  /**
   * 跳转url
   *
   * @generated from field: string link_url = 13;
   */
  linkUrl: string;

  constructor(data?: PartialMessage<CreateAchievementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateAchievementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAchievementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAchievementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAchievementReq;

  static equals(a: CreateAchievementReq | PlainMessage<CreateAchievementReq> | undefined, b: CreateAchievementReq | PlainMessage<CreateAchievementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.CreateAchievementRsp
 */
export declare class CreateAchievementRsp extends Message<CreateAchievementRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AchievementInfo achievement_info = 1;
   */
  achievementInfo?: AchievementInfo;

  constructor(data?: PartialMessage<CreateAchievementRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.CreateAchievementRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAchievementRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAchievementRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAchievementRsp;

  static equals(a: CreateAchievementRsp | PlainMessage<CreateAchievementRsp> | undefined, b: CreateAchievementRsp | PlainMessage<CreateAchievementRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateAchievementReq
 */
export declare class UpdateAchievementReq extends Message<UpdateAchievementReq> {
  /**
   * @generated from field: optional string achievement_id = 1;
   */
  achievementId?: string;

  /**
   * 称号名称
   *
   * @generated from field: optional string achievement_title = 2;
   */
  achievementTitle?: string;

  /**
   * 称号素材类型id
   *
   * @generated from field: optional string achievement_type_id = 3;
   */
  achievementTypeId?: string;

  /**
   * 是否为等级称号
   *
   * @generated from field: optional bool is_level = 4;
   */
  isLevel?: boolean;

  /**
   * 等级
   *
   * @generated from field: optional string level = 5;
   */
  level?: string;

  /**
   * icon图片id
   *
   * @generated from field: optional string achievement_icon_image_id = 6;
   */
  achievementIconImageId?: string;

  /**
   * 称号图片id
   *
   * @generated from field: optional string achievement_image_id = 7;
   */
  achievementImageId?: string;

  /**
   * 解锁条件
   *
   * @generated from field: optional string condition = 8;
   */
  condition?: string;

  /**
   * 恭喜获得【评论狸】称号及锂电池x200
   *
   * @generated from field: optional string toast_content = 9;
   */
  toastContent?: string;

  /**
   * 发作品
   *
   * @generated from field: optional string button_content = 10;
   */
  buttonContent?: string;

  /**
   * 有效期
   *
   * @generated from field: optional step.raccoon.usersphereinfo.ValidityPeriodType validity_period_type = 11;
   */
  validityPeriodType?: ValidityPeriodType;

  /**
   * @generated from field: map<string, string> extra = 12;
   */
  extra: { [key: string]: string };

  /**
   * 过期时间: 秒级时间戳
   *
   * @generated from field: optional string expired_at = 13;
   */
  expiredAt?: string;

  /**
   * 跳转url
   *
   * @generated from field: optional string link_url = 14;
   */
  linkUrl?: string;

  /**
   * @generated from field: optional step.raccoon.usersphereinfo.Status status = 15;
   */
  status?: Status;

  constructor(data?: PartialMessage<UpdateAchievementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateAchievementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAchievementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAchievementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAchievementReq;

  static equals(a: UpdateAchievementReq | PlainMessage<UpdateAchievementReq> | undefined, b: UpdateAchievementReq | PlainMessage<UpdateAchievementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UpdateAchievementRsp
 */
export declare class UpdateAchievementRsp extends Message<UpdateAchievementRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AchievementInfo achievement_info = 1;
   */
  achievementInfo?: AchievementInfo;

  constructor(data?: PartialMessage<UpdateAchievementRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UpdateAchievementRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAchievementRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAchievementRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAchievementRsp;

  static equals(a: UpdateAchievementRsp | PlainMessage<UpdateAchievementRsp> | undefined, b: UpdateAchievementRsp | PlainMessage<UpdateAchievementRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAchievementReq
 */
export declare class GetAchievementReq extends Message<GetAchievementReq> {
  /**
   * @generated from field: string achievement_id = 1;
   */
  achievementId: string;

  constructor(data?: PartialMessage<GetAchievementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAchievementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAchievementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAchievementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAchievementReq;

  static equals(a: GetAchievementReq | PlainMessage<GetAchievementReq> | undefined, b: GetAchievementReq | PlainMessage<GetAchievementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAchievementRsp
 */
export declare class GetAchievementRsp extends Message<GetAchievementRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AchievementInfo achievement_info = 1;
   */
  achievementInfo?: AchievementInfo;

  constructor(data?: PartialMessage<GetAchievementRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAchievementRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAchievementRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAchievementRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAchievementRsp;

  static equals(a: GetAchievementRsp | PlainMessage<GetAchievementRsp> | undefined, b: GetAchievementRsp | PlainMessage<GetAchievementRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAvatarAchievementsReq
 */
export declare class GetAvatarAchievementsReq extends Message<GetAvatarAchievementsReq> {
  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<GetAvatarAchievementsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAvatarAchievementsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAvatarAchievementsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAvatarAchievementsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAvatarAchievementsReq;

  static equals(a: GetAvatarAchievementsReq | PlainMessage<GetAvatarAchievementsReq> | undefined, b: GetAvatarAchievementsReq | PlainMessage<GetAvatarAchievementsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAvatarAchievementsRsp
 */
export declare class GetAvatarAchievementsRsp extends Message<GetAvatarAchievementsRsp> {
  /**
   * @generated from field: repeated step.raccoon.usersphereinfo.AchievementInfo achievement_infos = 1;
   */
  achievementInfos: AchievementInfo[];

  /**
   * @generated from field: int32 total = 2;
   */
  total: number;

  /**
   * @generated from field: string next_cursor = 3;
   */
  nextCursor: string;

  constructor(data?: PartialMessage<GetAvatarAchievementsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAvatarAchievementsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAvatarAchievementsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAvatarAchievementsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAvatarAchievementsRsp;

  static equals(a: GetAvatarAchievementsRsp | PlainMessage<GetAvatarAchievementsRsp> | undefined, b: GetAvatarAchievementsRsp | PlainMessage<GetAvatarAchievementsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.AllocateAchievementReq
 */
export declare class AllocateAchievementReq extends Message<AllocateAchievementReq> {
  /**
   * @generated from field: string file_name = 1;
   */
  fileName: string;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: string file_url = 3;
   */
  fileUrl: string;

  constructor(data?: PartialMessage<AllocateAchievementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AllocateAchievementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AllocateAchievementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AllocateAchievementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AllocateAchievementReq;

  static equals(a: AllocateAchievementReq | PlainMessage<AllocateAchievementReq> | undefined, b: AllocateAchievementReq | PlainMessage<AllocateAchievementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.AchievementRecord
 */
export declare class AchievementRecord extends Message<AchievementRecord> {
  /**
   * @generated from field: string allocate_id = 1;
   */
  allocateId: string;

  /**
   * @generated from field: string allocate_time = 2;
   */
  allocateTime: string;

  /**
   * @generated from field: string file_key = 3;
   */
  fileKey: string;

  /**
   * @generated from field: string file_url = 4;
   */
  fileUrl: string;

  /**
   * @generated from field: string file_name = 5;
   */
  fileName: string;

  /**
   * @generated from field: string operator = 6;
   */
  operator: string;

  constructor(data?: PartialMessage<AchievementRecord>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AchievementRecord";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AchievementRecord;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AchievementRecord;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AchievementRecord;

  static equals(a: AchievementRecord | PlainMessage<AchievementRecord> | undefined, b: AchievementRecord | PlainMessage<AchievementRecord> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.AllocateAchievementRsp
 */
export declare class AllocateAchievementRsp extends Message<AllocateAchievementRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AchievementRecord allocate_achievement_record = 1;
   */
  allocateAchievementRecord?: AchievementRecord;

  constructor(data?: PartialMessage<AllocateAchievementRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AllocateAchievementRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AllocateAchievementRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AllocateAchievementRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AllocateAchievementRsp;

  static equals(a: AllocateAchievementRsp | PlainMessage<AllocateAchievementRsp> | undefined, b: AllocateAchievementRsp | PlainMessage<AllocateAchievementRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAllocateAchievementRecordsReq
 */
export declare class GetAllocateAchievementRecordsReq extends Message<GetAllocateAchievementRecordsReq> {
  /**
   * 当前页最早的那条记录id
   *
   * @generated from field: string next_cursor = 1;
   */
  nextCursor: string;

  /**
   * 分页大小
   *
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  constructor(data?: PartialMessage<GetAllocateAchievementRecordsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAllocateAchievementRecordsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllocateAchievementRecordsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllocateAchievementRecordsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllocateAchievementRecordsReq;

  static equals(a: GetAllocateAchievementRecordsReq | PlainMessage<GetAllocateAchievementRecordsReq> | undefined, b: GetAllocateAchievementRecordsReq | PlainMessage<GetAllocateAchievementRecordsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetAllocateAchievementRecordsRsp
 */
export declare class GetAllocateAchievementRecordsRsp extends Message<GetAllocateAchievementRecordsRsp> {
  /**
   * @generated from field: repeated step.raccoon.usersphereinfo.AchievementRecord allocate_achievement_records = 1;
   */
  allocateAchievementRecords: AchievementRecord[];

  /**
   * @generated from field: int32 total = 2;
   */
  total: number;

  /**
   * @generated from field: string next_cursor = 3;
   */
  nextCursor: string;

  constructor(data?: PartialMessage<GetAllocateAchievementRecordsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetAllocateAchievementRecordsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllocateAchievementRecordsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllocateAchievementRecordsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllocateAchievementRecordsRsp;

  static equals(a: GetAllocateAchievementRecordsRsp | PlainMessage<GetAllocateAchievementRecordsRsp> | undefined, b: GetAllocateAchievementRecordsRsp | PlainMessage<GetAllocateAchievementRecordsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UnAllocateAchievementReq
 */
export declare class UnAllocateAchievementReq extends Message<UnAllocateAchievementReq> {
  /**
   * @generated from field: string file_name = 1;
   */
  fileName: string;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: string file_url = 3;
   */
  fileUrl: string;

  constructor(data?: PartialMessage<UnAllocateAchievementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UnAllocateAchievementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnAllocateAchievementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnAllocateAchievementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnAllocateAchievementReq;

  static equals(a: UnAllocateAchievementReq | PlainMessage<UnAllocateAchievementReq> | undefined, b: UnAllocateAchievementReq | PlainMessage<UnAllocateAchievementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.UnAllocateAchievementRsp
 */
export declare class UnAllocateAchievementRsp extends Message<UnAllocateAchievementRsp> {
  /**
   * @generated from field: step.raccoon.usersphereinfo.AchievementRecord unallocate_achievement_record = 1;
   */
  unallocateAchievementRecord?: AchievementRecord;

  constructor(data?: PartialMessage<UnAllocateAchievementRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.UnAllocateAchievementRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnAllocateAchievementRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnAllocateAchievementRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnAllocateAchievementRsp;

  static equals(a: UnAllocateAchievementRsp | PlainMessage<UnAllocateAchievementRsp> | undefined, b: UnAllocateAchievementRsp | PlainMessage<UnAllocateAchievementRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetUnAllocateAchievementRecordsReq
 */
export declare class GetUnAllocateAchievementRecordsReq extends Message<GetUnAllocateAchievementRecordsReq> {
  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<GetUnAllocateAchievementRecordsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetUnAllocateAchievementRecordsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUnAllocateAchievementRecordsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUnAllocateAchievementRecordsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUnAllocateAchievementRecordsReq;

  static equals(a: GetUnAllocateAchievementRecordsReq | PlainMessage<GetUnAllocateAchievementRecordsReq> | undefined, b: GetUnAllocateAchievementRecordsReq | PlainMessage<GetUnAllocateAchievementRecordsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.GetUnAllocateAchievementRecordsRsp
 */
export declare class GetUnAllocateAchievementRecordsRsp extends Message<GetUnAllocateAchievementRecordsRsp> {
  /**
   * @generated from field: repeated step.raccoon.usersphereinfo.AchievementRecord allocate_achievement_records = 1;
   */
  allocateAchievementRecords: AchievementRecord[];

  /**
   * @generated from field: int32 total = 2;
   */
  total: number;

  /**
   * @generated from field: string next_cursor = 3;
   */
  nextCursor: string;

  constructor(data?: PartialMessage<GetUnAllocateAchievementRecordsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.GetUnAllocateAchievementRecordsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUnAllocateAchievementRecordsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUnAllocateAchievementRecordsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUnAllocateAchievementRecordsRsp;

  static equals(a: GetUnAllocateAchievementRecordsRsp | PlainMessage<GetUnAllocateAchievementRecordsRsp> | undefined, b: GetUnAllocateAchievementRecordsRsp | PlainMessage<GetUnAllocateAchievementRecordsRsp> | undefined): boolean;
}

