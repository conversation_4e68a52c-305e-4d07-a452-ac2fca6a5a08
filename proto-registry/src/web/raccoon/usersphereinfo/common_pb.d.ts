// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/usersphereinfo/common.proto (package step.raccoon.usersphereinfo, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum step.raccoon.usersphereinfo.ValidityPeriodType
 */
export declare enum ValidityPeriodType {
  /**
   * @generated from enum value: VALIDITY_PERIOD_UNKNOWN = 0;
   */
  VALIDITY_PERIOD_UNKNOWN = 0,

  /**
   * 永久
   *
   * @generated from enum value: VALIDITY_PERIOD_PERMANENT = 1;
   */
  VALIDITY_PERIOD_PERMANENT = 1,

  /**
   * @generated from enum value: VALIDITY_PERIOD_NO_PERMANENT = 2;
   */
  VALIDITY_PERIOD_NO_PERMANENT = 2,
}

/**
 * @generated from enum step.raccoon.usersphereinfo.AchievementType
 */
export declare enum AchievementType {
  /**
   * @generated from enum value: UNKNOWN_ACHIEVEMENT_TYPE = 0;
   */
  UNKNOWN_ACHIEVEMENT_TYPE = 0,

  /**
   * @generated from enum value: COMMENT_ACHIEVEMENT = 1;
   */
  COMMENT_ACHIEVEMENT = 1,

  /**
   * @generated from enum value: SHARE_ACHIEVEMENT = 2;
   */
  SHARE_ACHIEVEMENT = 2,

  /**
   * @generated from enum value: THUMBSUP_ACHIEVEMENT = 3;
   */
  THUMBSUP_ACHIEVEMENT = 3,

  /**
   * @generated from enum value: SUBMIT_ACHIEVEMENT = 4;
   */
  SUBMIT_ACHIEVEMENT = 4,

  /**
   * @generated from enum value: CREATE_ACHIEVEMENT = 5;
   */
  CREATE_ACHIEVEMENT = 5,

  /**
   * @generated from enum value: SMALL_LI_ACHIEVEMENT = 6;
   */
  SMALL_LI_ACHIEVEMENT = 6,
}

/**
 * @generated from enum step.raccoon.usersphereinfo.Status
 */
export declare enum Status {
  /**
   * @generated from enum value: STATUS_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: STATUS_ONLINE = 1;
   */
  ONLINE = 1,

  /**
   * @generated from enum value: STATUS_OFFLINE = 2;
   */
  OFFLINE = 2,
}

/**
 * @generated from message step.raccoon.usersphereinfo.TaskProgress
 */
export declare class TaskProgress extends Message<TaskProgress> {
  /**
   * 当前已完成次数
   *
   * @generated from field: int32 completed_cnt = 1;
   */
  completedCnt: number;

  /**
   * 目标需要完成的次数
   *
   * @generated from field: int32 target_cnt = 2;
   */
  targetCnt: number;

  constructor(data?: PartialMessage<TaskProgress>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.TaskProgress";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskProgress;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskProgress;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskProgress;

  static equals(a: TaskProgress | PlainMessage<TaskProgress> | undefined, b: TaskProgress | PlainMessage<TaskProgress> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.usersphereinfo.AchievementInfo
 */
export declare class AchievementInfo extends Message<AchievementInfo> {
  /**
   * @generated from field: string achievement_id = 1;
   */
  achievementId: string;

  /**
   * 称号名称
   *
   * @generated from field: string achievement_title = 2;
   */
  achievementTitle: string;

  /**
   * @generated from field: string achievement_type_id = 3;
   */
  achievementTypeId: string;

  /**
   * 是否等级称号
   *
   * @generated from field: bool is_level = 4;
   */
  isLevel: boolean;

  /**
   * @generated from field: string level = 5;
   */
  level: string;

  /**
   * 称号素材类型名称
   *
   * @generated from field: string achievement_type_name = 6;
   */
  achievementTypeName: string;

  /**
   * @generated from field: string achievement_icon_url = 7;
   */
  achievementIconUrl: string;

  /**
   * @generated from field: string achievement_url = 8;
   */
  achievementUrl: string;

  /**
   * 解锁条件
   *
   * @generated from field: string condition = 9;
   */
  condition: string;

  /**
   * @generated from field: string toast_content = 10;
   */
  toastContent: string;

  /**
   * 发作品
   *
   * @generated from field: string button_content = 11;
   */
  buttonContent: string;

  /**
   * 有效期
   *
   * @generated from field: step.raccoon.usersphereinfo.ValidityPeriodType validity_period_type = 12;
   */
  validityPeriodType: ValidityPeriodType;

  /**
   * 过期时间: 秒级时间戳
   *
   * @generated from field: string expired_at = 13;
   */
  expiredAt: string;

  /**
   * @generated from field: step.raccoon.usersphereinfo.Status status = 14;
   */
  status: Status;

  /**
   * @generated from field: map<string, string> extra = 15;
   */
  extra: { [key: string]: string };

  /**
   * 创建时间
   *
   * @generated from field: string created_at = 16;
   */
  createdAt: string;

  /**
   * 称号icon图片id
   *
   * @generated from field: string achievement_icon_image_id = 17;
   */
  achievementIconImageId: string;

  /**
   * 称号图片id
   *
   * @generated from field: string achievement_image_id = 18;
   */
  achievementImageId: string;

  /**
   * 跳转url
   *
   * @generated from field: string link_url = 19;
   */
  linkUrl: string;

  /**
   * @generated from field: string operator = 20;
   */
  operator: string;

  /**
   * 是否解锁
   *
   * @generated from field: bool is_avaiable = 21;
   */
  isAvaiable: boolean;

  /**
   * 是否过期
   *
   * @generated from field: bool is_expire = 22;
   */
  isExpire: boolean;

  /**
   * @generated from field: step.raccoon.usersphereinfo.AchievementType achievement_type = 23;
   */
  achievementType: AchievementType;

  /**
   * 任务进度
   *
   * @generated from field: step.raccoon.usersphereinfo.TaskProgress task_progress = 24;
   */
  taskProgress?: TaskProgress;

  constructor(data?: PartialMessage<AchievementInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.usersphereinfo.AchievementInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AchievementInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AchievementInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AchievementInfo;

  static equals(a: AchievementInfo | PlainMessage<AchievementInfo> | undefined, b: AchievementInfo | PlainMessage<AchievementInfo> | undefined): boolean;
}

