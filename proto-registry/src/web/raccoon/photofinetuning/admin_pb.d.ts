// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/photofinetuning/admin.proto (package step.raccoon.photofinetuning, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { PagePagination, QueryOrderItem } from "../common/utils_pb.js";

/**
 * @generated from enum step.raccoon.photofinetuning.SourceType
 */
export declare enum SourceType {
  /**
   * @generated from enum value: SOURCE_UNKOWN = 0;
   */
  SOURCE_UNKOWN = 0,

  /**
   * 系统自动生成
   *
   * @generated from enum value: SOURCE_AUTO = 1;
   */
  SOURCE_AUTO = 1,

  /**
   * 运营创建
   *
   * @generated from enum value: SOURCE_OPERATED = 2;
   */
  SOURCE_OPERATED = 2,
}

/**
 * @generated from message step.raccoon.photofinetuning.CreateElementReq
 */
export declare class CreateElementReq extends Message<CreateElementReq> {
  /**
   * 名称
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * 图片 media service id
   *
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  /**
   * 类型，必须为：action/cloth/scene
   *
   * @generated from field: string feature = 3;
   */
  feature: string;

  /**
   * 英文prompt
   *
   * @generated from field: string prompt = 4;
   */
  prompt: string;

  constructor(data?: PartialMessage<CreateElementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.CreateElementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateElementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateElementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateElementReq;

  static equals(a: CreateElementReq | PlainMessage<CreateElementReq> | undefined, b: CreateElementReq | PlainMessage<CreateElementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.CreateElementResp
 */
export declare class CreateElementResp extends Message<CreateElementResp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<CreateElementResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.CreateElementResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateElementResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateElementResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateElementResp;

  static equals(a: CreateElementResp | PlainMessage<CreateElementResp> | undefined, b: CreateElementResp | PlainMessage<CreateElementResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.UpdateElementReq
 */
export declare class UpdateElementReq extends Message<UpdateElementReq> {
  /**
   * 元素id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: optional string name = 2;
   */
  name?: string;

  /**
   * @generated from field: optional string image_id = 3;
   */
  imageId?: string;

  /**
   * @generated from field: optional string feature = 4;
   */
  feature?: string;

  /**
   * @generated from field: optional string prompt = 5;
   */
  prompt?: string;

  constructor(data?: PartialMessage<UpdateElementReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.UpdateElementReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateElementReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateElementReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateElementReq;

  static equals(a: UpdateElementReq | PlainMessage<UpdateElementReq> | undefined, b: UpdateElementReq | PlainMessage<UpdateElementReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.UpdateElementResp
 */
export declare class UpdateElementResp extends Message<UpdateElementResp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<UpdateElementResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.UpdateElementResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateElementResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateElementResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateElementResp;

  static equals(a: UpdateElementResp | PlainMessage<UpdateElementResp> | undefined, b: UpdateElementResp | PlainMessage<UpdateElementResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.UplineElementsReq
 */
export declare class UplineElementsReq extends Message<UplineElementsReq> {
  /**
   * @generated from field: repeated string ids = 1;
   */
  ids: string[];

  /**
   * true-上线，false-下线
   *
   * @generated from field: bool online = 2;
   */
  online: boolean;

  constructor(data?: PartialMessage<UplineElementsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.UplineElementsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UplineElementsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UplineElementsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UplineElementsReq;

  static equals(a: UplineElementsReq | PlainMessage<UplineElementsReq> | undefined, b: UplineElementsReq | PlainMessage<UplineElementsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.UplineElementsResp
 */
export declare class UplineElementsResp extends Message<UplineElementsResp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<UplineElementsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.UplineElementsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UplineElementsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UplineElementsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UplineElementsResp;

  static equals(a: UplineElementsResp | PlainMessage<UplineElementsResp> | undefined, b: UplineElementsResp | PlainMessage<UplineElementsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.QueryElementsReq
 */
export declare class QueryElementsReq extends Message<QueryElementsReq> {
  /**
   * @generated from field: optional string name = 1;
   */
  name?: string;

  /**
   * @generated from field: optional string feature = 2;
   */
  feature?: string;

  /**
   * @generated from field: optional step.raccoon.photofinetuning.SourceType source_type = 3;
   */
  sourceType?: SourceType;

  /**
   * @generated from field: step.raccoon.common.PagePagination page = 4;
   */
  page?: PagePagination;

  /**
   * @generated from field: optional bool online = 5;
   */
  online?: boolean;

  /**
   * 排序规则，支持"create_time", "use_count", "delete_state"
   *
   * @generated from field: repeated step.raccoon.common.QueryOrderItem order = 6;
   */
  order: QueryOrderItem[];

  constructor(data?: PartialMessage<QueryElementsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.QueryElementsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryElementsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryElementsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryElementsReq;

  static equals(a: QueryElementsReq | PlainMessage<QueryElementsReq> | undefined, b: QueryElementsReq | PlainMessage<QueryElementsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.ElementItem
 */
export declare class ElementItem extends Message<ElementItem> {
  /**
   * 元素id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 元素名称
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * 元素生图prompt
   *
   * @generated from field: string prompt = 3;
   */
  prompt: string;

  /**
   * 素材图片url
   *
   * @generated from field: string image_url = 4;
   */
  imageUrl: string;

  /**
   * 特征id
   *
   * @generated from field: string feature_id = 5;
   */
  featureId: string;

  /**
   * 来源
   *
   * @generated from field: step.raccoon.photofinetuning.SourceType source_type = 6;
   */
  sourceType: SourceType;

  /**
   * 使用次数
   *
   * @generated from field: int64 use_count = 7;
   */
  useCount: bigint;

  /**
   * 上下线状态
   *
   * @generated from field: bool onlined = 8;
   */
  onlined: boolean;

  /**
   * @generated from field: string create_time = 9;
   */
  createTime: string;

  /**
   * @generated from field: string update_time = 10;
   */
  updateTime: string;

  /**
   * 图片media service id
   *
   * @generated from field: string image_id = 11;
   */
  imageId: string;

  constructor(data?: PartialMessage<ElementItem>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.ElementItem";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ElementItem;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ElementItem;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ElementItem;

  static equals(a: ElementItem | PlainMessage<ElementItem> | undefined, b: ElementItem | PlainMessage<ElementItem> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.QueryElementsResp
 */
export declare class QueryElementsResp extends Message<QueryElementsResp> {
  /**
   * @generated from field: repeated step.raccoon.photofinetuning.ElementItem elements = 1;
   */
  elements: ElementItem[];

  /**
   * 请求page
   *
   * @generated from field: step.raccoon.common.PagePagination page = 2;
   */
  page?: PagePagination;

  /**
   * 总的数量
   *
   * @generated from field: int32 total = 3;
   */
  total: number;

  constructor(data?: PartialMessage<QueryElementsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.QueryElementsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryElementsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryElementsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryElementsResp;

  static equals(a: QueryElementsResp | PlainMessage<QueryElementsResp> | undefined, b: QueryElementsResp | PlainMessage<QueryElementsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.ElementConfig
 */
export declare class ElementConfig extends Message<ElementConfig> {
  /**
   * @generated from field: string feature = 1;
   */
  feature: string;

  /**
   * @generated from field: repeated string ids = 2;
   */
  ids: string[];

  /**
   * @generated from field: string oprator = 3;
   */
  oprator: string;

  /**
   * @generated from field: string create_time = 4;
   */
  createTime: string;

  /**
   * @generated from field: string update_time = 5;
   */
  updateTime: string;

  constructor(data?: PartialMessage<ElementConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.ElementConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ElementConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ElementConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ElementConfig;

  static equals(a: ElementConfig | PlainMessage<ElementConfig> | undefined, b: ElementConfig | PlainMessage<ElementConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.UpdateElementConfigReq
 */
export declare class UpdateElementConfigReq extends Message<UpdateElementConfigReq> {
  /**
   * 必须为cloth/scene/action
   *
   * @generated from field: string feature = 1;
   */
  feature: string;

  /**
   * @generated from field: repeated string ids = 2;
   */
  ids: string[];

  /**
   * @generated from field: string operator = 3;
   */
  operator: string;

  constructor(data?: PartialMessage<UpdateElementConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.UpdateElementConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateElementConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateElementConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateElementConfigReq;

  static equals(a: UpdateElementConfigReq | PlainMessage<UpdateElementConfigReq> | undefined, b: UpdateElementConfigReq | PlainMessage<UpdateElementConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.UpdateElementConfigResp
 */
export declare class UpdateElementConfigResp extends Message<UpdateElementConfigResp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<UpdateElementConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.UpdateElementConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateElementConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateElementConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateElementConfigResp;

  static equals(a: UpdateElementConfigResp | PlainMessage<UpdateElementConfigResp> | undefined, b: UpdateElementConfigResp | PlainMessage<UpdateElementConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.GetElementConfigsReq
 */
export declare class GetElementConfigsReq extends Message<GetElementConfigsReq> {
  constructor(data?: PartialMessage<GetElementConfigsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.GetElementConfigsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetElementConfigsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetElementConfigsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetElementConfigsReq;

  static equals(a: GetElementConfigsReq | PlainMessage<GetElementConfigsReq> | undefined, b: GetElementConfigsReq | PlainMessage<GetElementConfigsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.GetElementConfigsResp
 */
export declare class GetElementConfigsResp extends Message<GetElementConfigsResp> {
  /**
   * @generated from field: repeated step.raccoon.photofinetuning.ElementConfig configs = 1;
   */
  configs: ElementConfig[];

  constructor(data?: PartialMessage<GetElementConfigsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.GetElementConfigsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetElementConfigsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetElementConfigsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetElementConfigsResp;

  static equals(a: GetElementConfigsResp | PlainMessage<GetElementConfigsResp> | undefined, b: GetElementConfigsResp | PlainMessage<GetElementConfigsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.PreviewElementConfigReq
 */
export declare class PreviewElementConfigReq extends Message<PreviewElementConfigReq> {
  /**
   * @generated from field: string feature = 1;
   */
  feature: string;

  constructor(data?: PartialMessage<PreviewElementConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.PreviewElementConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PreviewElementConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PreviewElementConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PreviewElementConfigReq;

  static equals(a: PreviewElementConfigReq | PlainMessage<PreviewElementConfigReq> | undefined, b: PreviewElementConfigReq | PlainMessage<PreviewElementConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.photofinetuning.PreviewElementConfigResp
 */
export declare class PreviewElementConfigResp extends Message<PreviewElementConfigResp> {
  /**
   * @generated from field: repeated step.raccoon.photofinetuning.ElementItem elements = 1;
   */
  elements: ElementItem[];

  constructor(data?: PartialMessage<PreviewElementConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.photofinetuning.PreviewElementConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PreviewElementConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PreviewElementConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PreviewElementConfigResp;

  static equals(a: PreviewElementConfigResp | PlainMessage<PreviewElementConfigResp> | undefined, b: PreviewElementConfigResp | PlainMessage<PreviewElementConfigResp> | undefined): boolean;
}

