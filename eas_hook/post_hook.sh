#!/bin/bash

echo "run stp-eas post-install"
stp-eas post-install

echo "modify appInfo.swift"

if [[ ${BUILD_PLATFORM} != ios ]] || [[ "${BUILD_PROFILE}" != production* ]] && [[ "${BUILD_PROFILE}" != adhoc* ]]; then
  echo "不满足修改要求"
  exit 0
fi

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
IOS_APP_INFO_PATH="${SCRIPT_DIR}/../ios/Pods/AppInfo/Source/AppInfo.swift"

if [ ! -f "${IOS_APP_INFO_PATH}" ];then
  echo "${IOS_APP_INFO_PATH}不存在"
  exit 0
fi

tmp_file="${IOS_APP_INFO_PATH}.tmp"
sed "s/\"AppStore\"/\"${IOS_CHANNEL}\"/g" "${IOS_APP_INFO_PATH}" > "${tmp_file}"
cat "${tmp_file}"
chmod +w "${IOS_APP_INFO_PATH}"
cp "${tmp_file}" "${IOS_APP_INFO_PATH}" 
rm "${tmp_file}"

