import VideoFlow from '@/src/bizComponents/livePhotoScreen/VideoFlow';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useParams } from '@/src/hooks/useParams';
import { VideoFlowCommentContext } from '@/src/store/video-flow-comment';
import { VideoFLowScene } from '@/src/types';

export const VideoFlowScreen = () => {
  const params = useParams<{
    cardId?: string;
    videoScene?: VideoFLowScene;
    id: string;
    showCommentInput?: string;
  }>();
  const {
    videoScene = VideoFLowScene.DISCOVER,
    cardId,
    id,
    showCommentInput = false
  } = params;

  return (
    <PagePerformance pathname="short-video/[id]">
      <VideoFlowCommentContext>
        <VideoFlow
          videoScene={videoScene}
          // windowSize={Number(windowSize)}
          firstCardId={id || cardId}
          showCommentInput={showCommentInput}
        />
      </VideoFlowCommentContext>
    </PagePerformance>
  );
};

export default VideoFlowScreen;
