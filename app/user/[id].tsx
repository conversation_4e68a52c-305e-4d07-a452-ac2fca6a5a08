import { UserScreen } from '@/src/bizComponents/userScreen';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useParams } from '@/src/hooks/useParams';

export default function Feed() {
  const params = useParams<{
    profilePageTab?: string;
    profileRefresh?: string;
    profileUpdateTimestamp?: string;
  }>();

  return (
    <PagePerformance pathname="user/[id]">
      <UserScreen
        pageTab={params.profilePageTab}
        refresh={params.profileRefresh}
        timestamp={params.profileUpdateTimestamp}
      />
    </PagePerformance>
  );
}
