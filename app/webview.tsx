import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Pressable, Text, TouchableOpacity } from 'react-native';
import { WebView, WebViewProps } from 'react-native-webview';
import { useWebviewMagicVideo } from '@/src/bizComponents/webviewScreen/hooks/webviewMagicVideo.hook';
import { useWebviewPublish } from '@/src/bizComponents/webviewScreen/hooks/webviewPublish.hook';
import { useWebviewRefreshToken } from '@/src/bizComponents/webviewScreen/hooks/webviewRefreshToken.hook';
import {
  WebviewReportState,
  useWebviewReport
} from '@/src/bizComponents/webviewScreen/hooks/webviewReport.hook';
import { Icon, hideLoading, showToast } from '@/src/components';
import { AiTag } from '@/src/components/aiTag';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { hideShare, showCommonShare, showShare } from '@/src/components/share';
import { shareCompPresets } from '@/src/components/share/channelConfig';
import { ChannelConfig, ShareCompPreset } from '@/src/components/share/typings';
import { genNonCardSharePostImageItemConfig } from '@/src/components/shareButton/CommonShareButton';
import { RN_EVENTS, WEBVIEW_EVENTS } from '@/src/constants';
import { useAuthState } from '@/src/hooks/useAuthState';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useScreenCapture } from '@/src/hooks/useScreenCapture';
import { useWebviewBridge } from '@/src/hooks/useWebviewBridge';
import { useAuthStore } from '@/src/store/authInfo';
import { useDecisionStore } from '@/src/store/decision';
import { useStorageStore } from '@/src/store/storage';
import { darkTheme, typography } from '@/src/theme';
import { getThemeColor } from '@/src/theme/colors/common';
import { Theme } from '@/src/theme/colors/type';
import { $USE_FONT } from '@/src/theme/variable';
import {
  GameType,
  ShareImageType,
  ShareInfo,
  ShareTemplateName
} from '@/src/types';
import { isIos } from '@/src/utils';
import { StyleSheet } from '@/src/utils/StyleSheet';
import { Source, getGlobalCommonParams } from '@/src/utils/report';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { Screen, ScreenProps } from '@Components/screen';
import { Webview } from '@Components/webview';
import { useParams } from '../src/hooks/useParams';
import { ShareIcon } from '@/assets/image/svg';
import { useShallow } from 'zustand/react/shallow';

export type WebviewScreenParams = {
  url: string;
  title: string;
  shareParams?: string;
  searchParams?: string;
  screenParams?: string;
  webviewParams?: string;
  preloadScene?: string;
  withAiTag?: string;
  darkThemeStatus?: string;
  theme?: Theme;
};

export enum WebviewShareScene {
  DECISION_H5 = 'decision_h5',
  DECISION_H5_IMAGE = 'decision_h5_image'
}

export enum PreloadScene {
  DECISION = 'decision'
}

export interface WebviewShareParams {
  gameType?: GameType;
  scene?: WebviewShareScene;
  shareInfo?: Partial<ShareInfo>;
}

export default function WebviewScreen() {
  const {
    url = '',
    withAiTag = '0',
    title = '',
    shareParams,
    searchParams,
    screenParams = '',
    webviewParams = '',
    preloadScene = '',
    theme = Theme.LIGHT,
    darkThemeStatus
  } = useParams<WebviewScreenParams>();

  const [showTitle, setShowTitle] = useState(title);
  const [shareData, setShareDta] = useState(shareParams);
  const [screenProps, setScreenProps] = useState<Partial<ScreenProps>>({});
  const [webviewProps, setWebviewProps] = useState<Partial<WebViewProps>>({});
  const [backgroundColor, setBackgroundColor] = useState('');
  const [isLoaded, setIsLoaded] = useState(false);

  const webviewRef: React.MutableRefObject<unknown> = useRef(null);
  const {
    injectedJavaScript,
    onMessage,
    addWebviewBridgeListener,
    removeWebviewBridgeListener,
    triggerWebviewBridgeEvent
  } = useWebviewBridge(webviewRef as React.MutableRefObject<WebView>);

  const { handleWebviewMagicVideo } = useWebviewMagicVideo();

  const { reportArgs, handleWebviewReportRegister, handleWebviewReport } =
    useWebviewReport();

  const { handleRefreshToken } = useWebviewRefreshToken();

  const { loginIntercept, getUserAuthState } = useAuthState();
  const { go2Create } = useChangeRoute();
  const { handleWebviewPublish } = useWebviewPublish();

  useEffect(() => {
    if (screenParams) {
      console.log('screenParams===>', JSON.parse(screenParams));
      const { backgroundColor, ...screenProps } = JSON.parse(screenParams);
      setScreenProps(screenProps);
      if (backgroundColor) {
        setBackgroundColor(backgroundColor);
      }
    }
    if (webviewParams) {
      console.log('webviewParams===>', JSON.parse(webviewParams));
      setWebviewProps(JSON.parse(webviewParams));
    }
    addWebviewBridgeListener(WEBVIEW_EVENTS.LOGIN, (arg?: any) => {
      loginIntercept(() => {}, arg);
    });
    addWebviewBridgeListener(WEBVIEW_EVENTS.TAKE_PHOTO, arg => {
      const { photoId, source, keyword, url, gameType } = arg || {};
      const keywordInString =
        typeof keyword === 'object' ? keyword?.join?.(',') : '';
      go2Create({
        gameType: gameType || GameType.DRAWING,
        gameParams: {
          photoId: photoId,
          photoUrl: url
        },
        params: {
          source: source || Source.DRAWING_WITH_PROMPT,
          keyword: keywordInString
        },
        loginOptions: arg
      });
    });
    addWebviewBridgeListener(WEBVIEW_EVENTS.ROUTER_PUSH, arg => {
      const { pathname, params = {} } = arg || {};
      if (pathname) {
        router.navigate({
          pathname,
          params
        });
      }
    });
    addWebviewBridgeListener(WEBVIEW_EVENTS.ROUTER_REPLACE, arg => {
      const { pathname, params } = arg || {};
      if (pathname) {
        router.replace({
          pathname,
          params
        });
      }
    });
    addWebviewBridgeListener(
      WEBVIEW_EVENTS.MAGIC_VIDEO,
      handleWebviewMagicVideo
    );
    addWebviewBridgeListener(WEBVIEW_EVENTS.SHARE_INFO_REGISTER, arg => {
      const { shareParams: sharePayload } = arg || {};
      if (sharePayload) {
        setShareDta(sharePayload);
      }
    });
    addWebviewBridgeListener(
      WEBVIEW_EVENTS.SHARE_INFO_REGISTER_V2,
      (arg: WebviewShareParams) => {
        const { shareInfo, gameType = GameType.DRAWING, scene } = arg || {};

        if (!shareInfo) {
          showToast('分享失败，请重试');
          return;
        }

        let config: ChannelConfig[][] | undefined;
        const shareInfoFulfilled: ShareInfo = {
          ...shareInfo,
          title: shareInfo.title || '',
          description: shareInfo.description || '',
          images: shareInfo.images || [],
          imageIndex: (shareInfo.imageIndex || 0) + 1,
          url: shareInfo.url || ''
        };

        const posterShareParams = {
          shareInfo: shareInfoFulfilled,
          type: ShareImageType.image,
          shareTemplateName: ShareTemplateName.default,
          extra: '{}',
          theme: Theme.DARK
        };

        if (scene === WebviewShareScene.DECISION_H5_IMAGE) {
          showShare(posterShareParams);
          return;
        }

        if (scene === WebviewShareScene.DECISION_H5) {
          const shareConfig = [
            ...shareCompPresets[ShareCompPreset.DECISION_H5_SHARE]
          ];
          const operationConfig = [
            ...shareCompPresets[ShareCompPreset.DECISION_H5_OPERATION],
            genNonCardSharePostImageItemConfig({
              getShareInfo: () => posterShareParams,
              theme: Theme.DARK
            })
          ];
          config = [shareConfig, operationConfig];
        }

        showCommonShare({
          theme: Theme.DARK,
          getShareInfo: () => shareInfoFulfilled,
          compConfigs: config
        });
      }
    );
    addWebviewBridgeListener(WEBVIEW_EVENTS.WEBVIEW_INFO, arg => {
      const { title: webviewTitle } = arg || {};
      if (webviewTitle) {
        console.log(webviewTitle, 'webviewTitle==');
        setShowTitle(webviewTitle);
      }
    });
    addWebviewBridgeListener(WEBVIEW_EVENTS.ROUTER_BACK, arg => {
      safeGoBack();
    });
    addWebviewBridgeListener(WEBVIEW_EVENTS.PUBLISH, handleWebviewPublish);
    addWebviewBridgeListener(
      WEBVIEW_EVENTS.REPORT,
      handleWebviewReportRegister
    );
    addWebviewBridgeListener(WEBVIEW_EVENTS.REFRESH_TOKEN, handleRefreshToken);
    addWebviewBridgeListener('test', args => {
      console.log('addWebviewBridgeListener test===>', args);
    });
  }, []);

  const headerRight = () => {
    let shareInfo: any = shareData;
    if (typeof shareData === 'string') {
      try {
        shareInfo = JSON.parse(shareData);
      } catch {
        shareInfo = '';
      }
    }
    if (!shareInfo) return null;

    const handleShare = () => {
      showShare({
        shareInfo: shareInfo,
        allowShareImage: false,
        type: ShareImageType.common,
        theme: Theme.LIGHT,
        shareTemplateName: ShareTemplateName.detail,
        channelPreset: ShareCompPreset.CONTENT_ACTIVITY
      });
    };

    return (
      <Pressable onPress={handleShare}>
        <ShareIcon
          color={getThemeColor(Theme.LIGHT).fontColor}
          width={24}
          height={24}
        />
      </Pressable>
    );
  };

  useEffect(() => {
    return () => {
      hideShare();
    };
  }, []);

  useEffect(() => {
    return () => {
      if (reportArgs?.current?.state === WebviewReportState.UNMOUNT) {
        handleWebviewReport({});
      }
    };
  }, []);

  const linkUrl = useMemo(() => {
    let result = url;
    if (searchParams) {
      try {
        result = url + '?' + decodeURIComponent(searchParams);
      } catch (err) {
        result = url;
      }
    }
    return result;
  }, [url, searchParams]);

  const { token, uid } = useAuthStore(
    useShallow(state => ({
      token: state.token,
      uid: state.uid
    }))
  );

  const carryInfo = useMemo(
    () => ({
      hasLogin: !!uid,
      uid,
      token
    }),
    [uid, token]
  );

  const onScreenCapture = useMemoizedFn(() => {
    triggerWebviewBridgeEvent(RN_EVENTS.SCREEN_CAPTURE, {});
  });
  useScreenCapture(true, onScreenCapture);

  useEffect(() => {
    triggerWebviewBridgeEvent(RN_EVENTS.AUTH_INFO, carryInfo);
  }, [carryInfo]);

  const onLoad = useMemoizedFn(() => {
    triggerWebviewBridgeEvent(RN_EVENTS.AUTH_INFO, carryInfo);
    triggerWebviewBridgeEvent(RN_EVENTS.REPORT_PARAMS, getGlobalCommonParams());
    setIsLoaded(true);
    // const preloadedGameData = useDecisionStore.getState().preloadedGameData;
    // triggerWebviewBridgeEvent(RN_EVENTS.DECISION_DATA, preloadedGameData);
  });

  // 不在副作用里获取是为了防止webview页面加载完成时preloadData还未返回。
  const { preloadedGameData } = useDecisionStore(
    useShallow(state => ({
      preloadedGameData: state.preloadDataset?.[state?.activeInstanceId ?? '']
    }))
  );
  useEffect(() => {
    // console.log('### preloadedGameData', preloadedGameData, preloadScene);
    if (
      preloadScene === PreloadScene.DECISION &&
      preloadedGameData &&
      isLoaded
    ) {
      triggerWebviewBridgeEvent(RN_EVENTS.DECISION_DATA, preloadedGameData);
    }
  }, [preloadScene, preloadedGameData, isLoaded]);

  const restOptions = useMemo(() => {
    if (!darkThemeStatus) {
      return {};
    }
    return {
      headerTitle: () => {
        return (
          <Text
            style={$USE_FONT(
              darkTheme.text.primary,
              typography.fonts.pingfangSC.normal,
              16,
              undefined,
              isIos ? '500' : 'bold',
              undefined
            )}
          >
            {showTitle}
          </Text>
        );
      },
      headerLeft: () => {
        return (
          <TouchableOpacity onPress={safeGoBack}>
            <Icon
              icon="back"
              size={24}
              style={{
                tintColor: '#fff'
              }}
            />
          </TouchableOpacity>
        );
      }
    };
  }, [darkTheme]);

  const themeColor = useMemo(() => {
    return theme === 'light' ? StyleSheet.lightTheme : StyleSheet.darkTheme;
  }, [theme]);

  return (
    <PagePerformance pathname="webview">
      <Screen
        theme={theme}
        withWaterMark={withAiTag === '1'}
        title={showTitle}
        screenStyle={{
          backgroundColor: backgroundColor || themeColor.background.page
        }}
        titleStyle={{ flexBasis: 280 }}
        headerRight={headerRight}
        style={backgroundColor ? { backgroundColor } : null}
        {...screenProps}
        {...restOptions}
      >
        <Webview
          url={linkUrl}
          ref={webviewRef}
          onLoad={onLoad}
          onMessage={onMessage}
          injectedJavaScriptBeforeContentLoaded={injectedJavaScript}
          injectedJavaScriptBeforeContentLoadedForMainFrameOnly={false}
          injectedJavaScriptObject={carryInfo} // 这里将登录信息传递给 WebView
          style={
            backgroundColor
              ? { backgroundColor: 'rgba(0,0,0,0)' }
              : darkThemeStatus
                ? { backgroundColor: '#16161A' }
                : null
          }
          showLoading={false}
          {...webviewProps}
        />
      </Screen>
    </PagePerformance>
  );
}
