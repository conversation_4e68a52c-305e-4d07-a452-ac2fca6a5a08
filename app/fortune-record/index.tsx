import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { Fragment, useEffect, useState } from 'react';
import { Platform, ScrollView, Text, View } from 'react-native';
import { getRaffleRecord } from '@/src/api/reward';
import { hideLoading, showLoading } from '@/src/components';
import { Image, Screen, showToast } from '@/src/components';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { FortuneEvent, FortuneEventBus } from '@/src/components/fortune/event';
import Button, { EButtonType } from '@/src/components/v2/button';
import { typography } from '@/src/theme';
import { darkColors, darkTheme } from '@/src/theme';
import { $USE_FONT } from '@/src/theme/variable';
import { StyleSheet } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { requestWithTimeout } from '@/src/utils/requestWithTimeout';
import {
  FillInRecipientInfoType,
  RewardType,
  WinRes,
  WinType
} from '@/proto-registry/src/web/raccoon/reward/reward_pb';

const bg = require('@Assets/image/forture/record-bg.png');
export default function FortuneRecord() {
  const [winRes, setWinRes] = useState<WinRes[]>([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    (async () => {
      await loadData();
      setInitialLoading(false);
    })();
    reportExpo('draw_page_record', { module: 'feed' });
    FortuneEventBus.on(FortuneEvent.SAVE_ADDRESS_SUCCESS, updateAddressInfo);
    return () => {
      FortuneEventBus.off(FortuneEvent.SAVE_ADDRESS_SUCCESS, updateAddressInfo);
    };
  }, []);

  const updateAddressInfo = useMemoizedFn(
    (params: { raffleRecordId: string }) => {
      const targetIndex = winRes.findIndex(
        item => item.raffleRecordId === params.raffleRecordId
      );
      if (targetIndex !== -1) {
        winRes[targetIndex].fillInRecipientInfoType =
          FillInRecipientInfoType.YES;
        setWinRes([...winRes]);
      }
    }
  );

  const loadData = useMemoizedFn(async () => {
    showLoading();
    try {
      const res = await requestWithTimeout(getRaffleRecord(), {
        timeout: 5000
      });
      const { winReses } = res;
      setWinRes(winReses);
      setHasError(false);
    } catch (error) {
      showToast('加载失败，请重试~');
      setHasError(true);
    } finally {
      hideLoading();
    }
  });

  const renderContent = () => {
    if (initialLoading) return null;
    if (hasError) {
      return (
        <EmptyPlaceHolder
          style={{ height: 400 }}
          buttonText="立即刷新"
          button={true}
          buttonStyle={{
            marginTop: 24
          }}
          onButtonPress={() => {
            loadData();
          }}
        >
          小狸走丢了，请刷新页面
        </EmptyPlaceHolder>
      );
    }
    if (winRes.length === 0) {
      return (
        <EmptyPlaceHolder
          style={{ height: 400 }}
          buttonText="立即抽奖"
          button={true}
          buttonStyle={{
            marginTop: 24
          }}
          onButtonPress={() => {
            router.back();
            FortuneEventBus.emit(FortuneEvent.OPEN_MODAL);
            reportClick('draw_page_record', {
              module: 'feed',
              type: 0
            });
          }}
        >
          暂未参与抽奖哦~快去看看吧！
        </EmptyPlaceHolder>
      );
    }
    return <RewardList winRes={winRes} />;
  };

  return (
    <PagePerformance pathname="lottery-record/index">
      <Screen
        theme="dark"
        title="抽奖记录"
        preset="auto"
        safeAreaEdges={['top', 'bottom']}
        backgroundView={
          <Fragment>
            <View
              style={[
                { backgroundColor: 'rgba(22, 22, 26, 1)' },
                StyleSheet.absoluteFill
              ]}
            />
            <Image source={bg} style={[StyleSheet.absoluteFill]} />
          </Fragment>
        }
      >
        <View style={{ flex: 1 }}>{renderContent()}</View>
      </Screen>
    </PagePerformance>
  );
}

function RewardList({ winRes }: { winRes: WinRes[] }) {
  const rewardList = winRes.filter(item => item.winType === WinType.WIN);
  if (rewardList.length) {
    return (
      <ScrollView
        contentContainerStyle={{
          gap: 20,
          paddingHorizontal: 16,
          paddingVertical: 20
        }}
        showsVerticalScrollIndicator={false}
      >
        {rewardList.map((item, index) => {
          const { rewardInfo: reward, fillInRecipientInfoType } = item;

          const needAddAddress = reward?.rewardType === RewardType.ENTITY;

          return (
            <View key={index} style={styles.rewardItem}>
              <View style={styles.rewardContent}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignSelf: 'flex-start'
                  }}
                >
                  <Text style={styles.rewardTitle}>您获得的奖品是</Text>
                  <Text style={styles.rewardName}>{reward?.name}</Text>
                </View>
                {needAddAddress &&
                  fillInRecipientInfoType === FillInRecipientInfoType.NO && (
                    <Button
                      type={EButtonType.NORMAL}
                      $customBtnStyle={styles.addressButton}
                      onPress={() => {
                        reportClick('draw_page_record', {
                          module: 'feed',
                          type: 1
                        });
                        router.navigate({
                          pathname: '/receive-address',
                          params: {
                            raffleRecordId: item.raffleRecordId
                          }
                        });
                      }}
                    >
                      <View
                        style={[
                          StyleSheet.rowStyle,
                          { justifyContent: 'center' }
                        ]}
                      >
                        <Text style={styles.addressButtonText}>填写地址</Text>
                      </View>
                    </Button>
                  )}
                {needAddAddress &&
                  fillInRecipientInfoType === FillInRecipientInfoType.YES && (
                    <Button
                      type={EButtonType.NORMAL}
                      $customBtnStyle={styles.addressButtonDisabled}
                      disabled
                    >
                      <View
                        style={[
                          StyleSheet.rowStyle,
                          { justifyContent: 'center' }
                        ]}
                      >
                        <Text style={styles.addressButtonTextDisabled}>
                          地址已填写
                        </Text>
                      </View>
                    </Button>
                  )}
              </View>

              <View style={styles.rewardRight}>
                {reward?.imgUrl && (
                  <Image
                    source={reward.imgUrl}
                    style={styles.rewardImage}
                    contentFit="contain"
                  />
                )}
              </View>
            </View>
          );
        })}
      </ScrollView>
    );
  }
  return null;
}

const styles = StyleSheet.create({
  emptyText: {
    ...$USE_FONT(
      darkTheme.text.disabled,
      typography.fonts.pingfangSC.normal,
      14,
      'normal',
      '400',
      19.6
    ),
    paddingHorizontal: 32,
    marginTop: 20,
    marginBottom: 12
  },
  rewardItem: {
    backgroundColor: darkColors.white[50],
    borderRadius: 10,
    padding: 12,
    flexDirection: 'row'
  },
  rewardContent: {
    flexDirection: 'column',
    justifyContent: 'center',
    flex: 1
  },
  rewardTitle: $USE_FONT(
    darkColors.white[900],
    typography.fonts.pingfangSC.normal,
    14,
    'normal',
    '500',
    19.6
  ),
  rewardName: {
    ...$USE_FONT(
      'rgba(255, 106, 59, 1)',
      'YeZiGongChangAoYeHei',
      15,
      'normal',
      '500',
      19.6
    ),
    marginLeft: 2,
    top: Platform.OS === 'ios' ? 0 : 3
  },
  rewardRight: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center'
  },
  rewardImage: {
    width: 80,
    height: 80
  },
  addressButton: {
    width: 112,
    height: 36,
    backgroundColor: 'rgba(255, 106, 59, 1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 14
  },
  addressButtonDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    width: 112,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: 'rgba(232, 232, 232, 0.3)',
    marginTop: 14
  },
  addressButtonText: $USE_FONT(
    darkColors.white[1000],
    typography.fonts.pingfangSC.normal,
    13,
    'normal',
    '500',
    19.6
  ),
  addressButtonTextDisabled: $USE_FONT(
    darkColors.white[300],
    typography.fonts.pingfangSC.normal,
    13,
    'normal',
    '500',
    19.6
  )
});
