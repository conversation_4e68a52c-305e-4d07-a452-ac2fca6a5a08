import { router } from 'expo-router';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { ImageStyle, TextStyle, View } from 'react-native';
import { useEllipsisDots } from '@/src/bizComponents/soulmakerScreen/genImage/ellipisis';
import { Image, Screen, Text, showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { ImageCard } from '@/src/components/imageCard';
import { PrimaryButton } from '@/src/components/primaryButton';
import { LOADING_SYMBOL } from '@/src/constants';
import { selectState } from '@/src/store/_utils';
import { useMemeStore } from '@/src/store/meme';
import { centerStyle } from '@/src/theme';
import { StyleSheet } from '@/src/utils';
import { getRemoteAssets } from '@/src/utils/getRemoteAssets';
import { reportClick, reportExpo } from '@/src/utils/report';
import {
  CensoredState,
  GeneState
} from '@/proto-registry/src/web/raccoon/common/state_pb';
import { Video } from '@step.ai/expo-av';
import TransparentVideo from '@step.ai/react-native-transparent-video';

const PROGRESS_VIDEO = require('@Assets/mp4/alpha/progress.mp4');

const BG_IMG = require('@Assets/playground/meme-publish-bg.png');

const LOADING_VIDEO = require('@Assets/mp4/role-loading.mp4');
const ERROR_IMAGE = require('@Assets/playground/error-image.png');

const MemeEffect = () => {
  const isCanceled = useRef(false);
  const [showProgress, setShowProgress] = useState(true);
  const videoRef = useRef<TransparentVideo>(null);
  const { currentPhoto } = useMemeStore(state =>
    selectState(state, ['currentPhoto'])
  );
  useEffect(() => {
    reportExpo('meme_loading');
    setTimeout(() => {
      videoRef.current?.pause();
    }, 3200);
  }, []);

  const realShowProgress =
    showProgress &&
    (currentPhoto === LOADING_SYMBOL ||
      currentPhoto.cencorState === CensoredState.CENSORED_PROCESSING ||
      currentPhoto.cencorState === CensoredState.CENSORED_PASSED);

  useEffect(() => {
    if (realShowProgress) {
      setTimeout(() => {
        videoRef.current?.pause();
      }, 3200);
    }
  }, [realShowProgress]);
  useEffect(() => {
    if (
      currentPhoto !== LOADING_SYMBOL &&
      currentPhoto.cencorState === CensoredState.CENSORED_PASSED &&
      currentPhoto.geneState === GeneState.GENE_DONE
    ) {
      videoRef.current?.play();
      setTimeout(() => {
        if (!isCanceled.current) {
          setShowProgress(false);
        }
      }, 500);
      setTimeout(() => {
        if (!isCanceled.current) {
          router.replace({
            pathname: '/memePublish',
            params: {
              photoId: currentPhoto?.photoId ?? ''
            }
          });
        }
      }, 2000);
    }
  }, [currentPhoto]);
  const showRegen =
    currentPhoto !== LOADING_SYMBOL &&
    (CensoredState.CENSORED_BLOCKED === currentPhoto.cencorState ||
      GeneState.GENE_EXCEPTIONED === currentPhoto.geneState);
  return (
    <PagePerformance pathname="meme/effect">
      <Screen
        backButton
        theme="dark"
        title={'游乐园'}
        backgroundView={
          <Image source={BG_IMG} style={[StyleSheet.absoluteFill]} />
        }
        onBack={() => {
          isCanceled.current = true;
          reportClick('meme_loading_back');
          router.back();
        }}
        contentContainerStyle={centerStyle}
      >
        <View>
          <ImageCard
            loadingVideo={LOADING_VIDEO}
            errorImage={ERROR_IMAGE}
            photo={currentPhoto === LOADING_SYMBOL ? null : currentPhoto}
            style={showRegen ? $errorStyle : $cardStyle}
          ></ImageCard>
          {realShowProgress ? <ProgressBar ref={videoRef}></ProgressBar> : null}
        </View>
        {showRegen && (
          <>
            <Text style={$textStyle}>{'这张不小心被炖糊了'}</Text>
            <PrimaryButton
              onPress={() => {
                useMemeStore.getState().genMemePhotos({});
                setShowProgress(true);
              }}
              width={144}
            >
              {'重新生成'}
            </PrimaryButton>
          </>
        )}
      </Screen>
    </PagePerformance>
  );
};

const ProgressBar = forwardRef<TransparentVideo>((_, ref) => {
  const dots = useEllipsisDots();
  return (
    <View style={[StyleSheet.absoluteFill, centerStyle]}>
      <TransparentVideo
        loop={false}
        ref={ref}
        source={PROGRESS_VIDEO}
        style={{ width: 154, height: 154 }}
      ></TransparentVideo>
      <Text style={$progressTextStyle}>{'正在制作中' + dots}</Text>
    </View>
  );
});

ProgressBar.displayName = 'ProgressBar';

const $cardStyle: ImageStyle = { width: 344, height: 458, borderRadius: 20 };

const $errorStyle: ImageStyle = {
  width: 140,
  height: 130
};

const $textStyle: TextStyle = {
  marginBottom: 20,
  fontWeight: '600',
  fontSize: 16,
  lineHeight: 22.4,
  color: 'rgba(255, 255, 255, 0.4)'
};

const $progressTextStyle: TextStyle = {
  marginTop: 20,
  fontWeight: '500',
  fontSize: 17,
  lineHeight: 18,
  color: 'rgba(255, 255, 255, 1)'
};

export default MemeEffect;
