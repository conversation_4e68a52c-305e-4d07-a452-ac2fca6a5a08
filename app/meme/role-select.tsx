import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect } from 'react';
import { MemeRoleSelector } from '@/src/bizComponents/playgoundScreen/meme/MemeRoleSelect';
import { Image, Screen, showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { IPRoleSelector } from '@/src/components/makePhoto/roleSelector/IPRoleSelector';
import { selectState } from '@/src/store/_utils';
import { useMemeStore } from '@/src/store/meme';
import { GameType, InvokeType, PlainType, RoleInfo } from '@/src/types';
import { StyleSheet, arabToChinese } from '@/src/utils';
import { getEmptyRoleInfo } from '@/src/utils/formatRole';
import { reportClick } from '@/src/utils/report';
import { CrefSearchArea } from '../make-photo/cref-searcharea';
import { CensoredState } from '@/proto-registry/src/web/raccoon/common/state_pb';
import { RoleState } from '@/proto-registry/src/web/raccoon/common/state_pb';
import { MemeTemplate } from '@/proto-registry/src/web/raccoon/meme/meme_pb';

const BG_IMG = require('@Assets/makephoto/role_select_bg.png');

const MemeRoleSelect = () => {
  const { roles, memeTemplate, setRoles, genMemePhotos, setState } =
    useMemeStore(state =>
      selectState(state, [
        'roles',
        'memeTemplate',
        'setRoles',
        'genMemePhotos',
        'setState'
      ])
    );

  useEffect(() => {
    // console.log('# roles: ', roles.length, roles, memeTemplate);
    const { id: emptyId } = getEmptyRoleInfo();
    if (roles.some(r => r.id === emptyId)) {
      const omittedRoles = roles.filter(r => r.id !== emptyId);
      setRoles(omittedRoles);
    }
  }, [roles]);

  const roleCount = (memeTemplate as PlainType<MemeTemplate>)?.roleCount ?? 1;
  const enableSubmit = roles.length === roleCount;
  const initialIP =
    roles[0]?.roleSetId || roles?.[0]?.roleSet?.originBrandId || 100;
  const headerLeft = useMemoizedFn(() => <CrefSearchArea rightWidth={70} />);
  const onRolePress = useMemoizedFn((role?: RoleInfo) => {
    reportClick('meme_char_select', { module: 'park' });
    if (!role) return;

    const index = roles.findIndex(r => r.id === role.id);
    if (index !== -1) {
      roles.splice(index, 1);
      setRoles([...roles]);
      return;
    }
    if (roleCount === 1) {
      setRoles([role]);
      return;
    } else if (roles.length >= roleCount) {
      showToast(`最多只能选择${arabToChinese(roleCount)}名角色哦～`);
      return;
    }
    setRoles([...roles, role]);
  });
  const onSubmit = useMemoizedFn(() => {
    reportClick('meme_char_choose', { module: 'park' });
    setState({
      title: '',
      story: ''
    });
    genMemePhotos({});
    router.navigate('/meme/effect');
  });

  return (
    <PagePerformance pathname="meme/role-select">
      <Screen
        backButton
        safeAreaEdges={['top']}
        theme="dark"
        screenStyle={{ backgroundColor: '#16161A' }}
        headerLeft={headerLeft}
      >
        <IPRoleSelector
          initIp={initialIP}
          gameType={GameType.MEME}
          role1={roles.length > 0 ? roles[0] : undefined}
          onPressRole1={onRolePress}
          handleSubmit={onSubmit}
          submitText={
            enableSubmit
              ? '选好了，梗图启动！'
              : `选择${arabToChinese(roleCount)}个角色`
          }
        />
      </Screen>
    </PagePerformance>
  );
};

export default MemeRoleSelect;
