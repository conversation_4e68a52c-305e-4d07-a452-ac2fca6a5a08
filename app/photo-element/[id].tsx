import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import PagerView from 'react-native-pager-view';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { PhotoFinetuningClient } from '@/src/api/fineTunning';
import { RecommendSecondaryTab } from '@/src/bizComponents/feedScreen/recommendSecondaryTab';
import { NestedScrollView } from '@/src/bizComponents/nestedScrollView';
import {
  ElementFeed,
  FETCH_TYPE
} from '@/src/bizComponents/photoElementScreen/ElementFeed';
import {
  ElementInfo,
  ElementInfoThumbnail
} from '@/src/bizComponents/photoElementScreen/ElementInfo';
import { Button, Image, Screen, showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { PrimaryButton } from '@/src/components/primaryButton';
import { LOGIN_SCENE } from '@/src/constants';
import { useAuthState, useSafeBottomArea } from '@/src/hooks';
import { PhotoInfo } from '@/src/store/detail';
import { useKitchenStore } from '@/src/store/kitchen';
import { useRoleStore } from '@/src/store/role';
import { darkTheme } from '@/src/theme';
import { $flex } from '@/src/theme/variable';
import { dp2px } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { useParams } from '../../src/hooks/useParams';
import {
  FeatureElement,
  ImageElementAnchors
} from '@/proto-registry/src/web/raccoon/common/element_pb';

const elementDetailBg = require('@Assets/image/photo-element/element-detail-bg.png');

type TabItemInfo = {
  key: string;
  title: string;
  type: FETCH_TYPE;
};

const routes = [
  {
    key: '0',
    type: FETCH_TYPE.ALL,
    title: '全部作品'
  },
  {
    key: '1',
    type: FETCH_TYPE.MINE,
    title: '我的作品'
  }
];

function PhotoElement() {
  /** states & refs */
  const localParams = useParams();
  const { id, name, cover, fid } = localParams;

  const sheetRef = useRef<NestedScrollView>(null);
  const pagerViewRef = useRef<PagerView>(null);

  const [curTabIdx, setCurTabIdx] = useState<string>('0');
  const [info, setInfo] = useState<FeatureElement | undefined>();

  const safeBottom = useSafeBottomArea(20);
  const { loginIntercept } = useAuthState();
  /** states & refs --end */

  /** style */
  const hasSlideUp = useSharedValue(false);
  const $headerStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(hasSlideUp?.value ? 1 : 0),
      pointerEvents: hasSlideUp?.value ? 'auto' : 'none'
    };
  });
  /** style --end */

  /** handlers */
  const init = useMemoizedFn(async (id: string) => {
    try {
      const res = await PhotoFinetuningClient.getElement({
        elementId: id
      });
      setInfo(res.result);
      reportExpo('expo', {
        element_type: res.result?.featureId || '',
        element_name: res.result?.name || ''
      });
    } catch (error) {
      //
    }
  });

  const onPressGo2Kitchen = useMemoizedFn(async () => {
    if (!info) {
      showToast('请重试');
      return;
    }

    reportClick('button', {
      button_type: 1
    });

    loginIntercept(
      async () => {
        try {
          const emptyRole = await useRoleStore
            .getState()
            .getRoleDetail('nil_v2');
          const defaultRole = emptyRole;

          const featureId = info.featureId || (fid as string) || '';
          useKitchenStore.getState().updateRefInfo({
            refCardId: '',
            refPhotos: [],
            refEleInfos: {
              elements: {
                [info.id]: info
              },
              anchors: {
                ['default' as string]: {
                  eles: [
                    {
                      eid: info.id,
                      fid: info.featureId
                    }
                  ]
                }
              }
            } as unknown as ImageElementAnchors,
            refProto: []
          });

          useKitchenStore.getState().updateState({
            select: featureId,
            selectPhoto: 'default',
            selectRoles: defaultRole ? { default: defaultRole } : {},
            selectEleInfos: {
              ['default' as string]: {
                [featureId]: info
              }
            }
          });
          router.navigate({
            pathname: '/kitchen',
            params: {
              source: 'element_detail'
            }
          });
        } catch (error) {
          showToast('哎呀失败了，请重试');
          console.log('==lin==error', error);
        }
      },
      { scene: LOGIN_SCENE.KITCHEN }
    );
  });
  /** handlers --end */

  /** effects */
  useEffect(() => {
    if (id) {
      init(id as string);
    }
  }, [id]);
  /** effects --end */

  return (
    <PagePerformance pathname="photo-element/[id]/index">
      <Screen
        theme="dark"
        backgroundView={
          <Image
            source={elementDetailBg}
            style={{
              width: '100%',
              height: dp2px(300),
              position: 'absolute',
              top: 0,
              left: 0
            }}
          />
        }
        wholePageStyle={{
          backgroundColor: darkTheme.background.page
        }}
        safeAreaEdges={['top']}
        headerLeft={() => (
          <ElementInfoThumbnail
            style={$headerStyle}
            id={id as string}
            name={info?.name || (name as string)}
            cover={info?.imageUrl || (cover as string)}
          />
        )}
      >
        <View style={$flex}>
          <NestedScrollView
            ref={sheetRef}
            hasSlideUp={hasSlideUp}
            // preserve space for sticky tab
            topPreserveInset={46}
            headerComponent={
              <View>
                <ElementInfo
                  id={id as string}
                  name={info?.name || (name as string)}
                  cover={info?.imageUrl || (cover as string)}
                  onPress={onPressGo2Kitchen}
                />
                <RecommendSecondaryTab<TabItemInfo>
                  enableExpand={false}
                  activeIndex={curTabIdx}
                  list={routes}
                  onPress={(key, item) => {
                    pagerViewRef.current?.setPage(Number(key));
                  }}
                />
              </View>
            }
          >
            <PagerView
              ref={pagerViewRef}
              style={{ flex: 1 }}
              onPageSelected={e => {
                setCurTabIdx(e.nativeEvent.position.toString());
              }}
              overdrag
            >
              <ElementFeed
                eleInfo={info}
                id={id as string}
                type={FETCH_TYPE.ALL}
                isActive={routes[curTabIdx]?.type === FETCH_TYPE.ALL}
              />
              <ElementFeed
                eleInfo={info}
                id={id as string}
                type={FETCH_TYPE.MINE}
                isActive={routes[curTabIdx]?.type === FETCH_TYPE.MINE}
              />
            </PagerView>
          </NestedScrollView>
        </View>
        <Animated.View
          style={[
            {
              position: 'absolute',
              bottom: safeBottom,
              width: '100%',
              alignItems: 'center',
              justifyContent: 'center',
              pointerEvents: 'box-none'
            },
            $headerStyle
          ]}
        >
          <PrimaryButton
            usingGradient={false}
            onPress={onPressGo2Kitchen}
            style={{
              width: 161,
              borderRadius: 50
            }}
          >
            用Ta生成
          </PrimaryButton>
        </Animated.View>
      </Screen>
    </PagePerformance>
  );
}

export default PhotoElement;
