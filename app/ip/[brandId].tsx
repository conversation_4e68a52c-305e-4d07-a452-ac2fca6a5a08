import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import { StyleProp, TextStyle, View, ViewStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PagerView from 'react-native-pager-view';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SubscribeBrandClient } from '@/src/api/subscribe';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import { RecommendSecondaryTab } from '@/src/bizComponents/feedScreen/recommendSecondaryTab';
import { RecSceneName } from '@/src/bizComponents/feedScreen/type';
import { CellCardScene } from '@/src/bizComponents/feedcard/types';
import { GlobalPublishButton } from '@/src/bizComponents/globalPublish/globalPublishButton';
import { NestedScrollView } from '@/src/bizComponents/nestedScrollView';
import { TopicDetailBg } from '@/src/bizComponents/topicDetailScreen/TopicDetailBg';
import { TopicDetailHeader } from '@/src/bizComponents/topicDetailScreen/TopicDetailHeader';
import { TopicInfo } from '@/src/bizComponents/topicDetailScreen/TopicDetailInfo';
import { getIpTagColor } from '@/src/bizComponents/topicTag';
import {
  Button,
  Header,
  Icon,
  Image,
  Screen,
  Text,
  showToast
} from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { RequestScene } from '@/src/components/infiniteList/typing';
import { PresetButton } from '@/src/components/primaryButton/PresetButton';
import { ButtonPreset } from '@/src/components/primaryButton/PresetButton/typing';
import { PublishEntry } from '@/src/components/publishEntry';
import { WaterFall2 } from '@/src/components/waterfall/WaterFall2';
import { useRequestFeed } from '@/src/components/waterfall/useRequsetFeed';
import { IP_GUIMIE, IP_IMAGE_BACK, IP_MASK } from '@/src/constants';
import {
  useAuthState,
  useSafeAreaInsetsStyle,
  useSafeBottomArea
} from '@/src/hooks';
import { useAuthStore } from '@/src/store/authInfo';
import { useBrandStore } from '@/src/store/brand';
import { typography } from '@/src/theme';
import { CommonColor } from '@/src/theme/colors/common';
import { $flex, $relative } from '@/src/theme/variable';
import { BrandInfo } from '@/src/types';
import { lightenColor, opacityColor } from '@/src/utils/color';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { formatTosUrl } from '@/src/utils/getTosUrl';
import { reportClick, setIpOrTopicTabForPostReport } from '@/src/utils/report';
import { BbsPageType } from '@/src/utils/report/type';
import { useParams } from '../../src/hooks/useParams';
import TopicScene, { TOPIC_FETCH_TYPE } from '../topic-page/[id]/topic-scene';
import { useShallow } from 'zustand/react/shallow';

const ERROR_CODE_DUPLICATE_SUBSCRIPTION = 21002;

type TabItemInfo = {
  key: string;
  title: string;
  type: TOPIC_FETCH_TYPE;
};

const routes: TabItemInfo[] = [
  {
    key: '0',
    type: TOPIC_FETCH_TYPE.IP_RECOMMEND,
    title: '作品'
  },
  {
    key: '1',
    type: TOPIC_FETCH_TYPE.IP_DISCUSS,
    title: '讨论区'
  }
];

export default function IpDetailScreen() {
  const brandId = parseInt(useParams().brandId as string, 10);
  const [branInfo, setBrandInfo] = useState<BrandInfo | undefined>();
  const [pendingId, setPendingId] = useState<string | undefined>('');
  const [showGuide, setShowGuide] = useState(true);

  const $safePaddingBottom = useSafeBottomArea();
  const [curTabIdx, setCurTabIdx] = useState<string>('0');
  const pagerViewRef = useRef<PagerView>(null);

  const { uid } = useAuthStore(
    useShallow(state => ({
      uid: state.uid
    }))
  );
  const { loginIntercept } = useAuthState();

  const sheetRef = useRef<NestedScrollView>(null);
  const hasSlideUp = useSharedValue(false);

  const { getBrandInfo } = useBrandStore(
    useShallow(state => ({
      getBrandInfo: state.getBrandInfo,
      brandInfos: state.brandInfos
    }))
  );

  useEffect(() => {
    if (typeof brandId !== 'number') return router.back();
    getBrandInfo(brandId, Boolean(branInfo)).then(branInfo => {
      setBrandInfo(branInfo);
    });
  }, [brandId, uid]);

  const { run: debounceShowGuide } = useDebounceFn(
    () => {
      setShowGuide(true);
    },
    {
      wait: 1000
    }
  );

  const onScroll = useMemoizedFn(() => {
    setShowGuide(false);
    debounceShowGuide();
  });

  return (
    <PagePerformance pathname="ip/[brandId]">
      <Screen
        theme="dark"
        backgroundView={
          <TopicDetailBg
            themeColor={getIpTagColor(branInfo?.displayName)}
            isIp
          />
        }
        safeAreaEdges={['top']}
        headerShown={false}
      >
        <TopicDetailHeader branInfo={branInfo} hasSlideUp={hasSlideUp} />
        <View style={$flex}>
          <NestedScrollView
            ref={sheetRef}
            hasSlideUp={hasSlideUp}
            topPreserveInset={46}
            headerComponent={
              <View>
                <TopicInfo branInfo={branInfo} />
                <RecommendSecondaryTab<TabItemInfo>
                  enableExpand={false}
                  activeIndex={curTabIdx}
                  list={routes}
                  onPress={(key, item: TabItemInfo) => {
                    pagerViewRef.current?.setPage(Number(key));
                    if (item.type === TOPIC_FETCH_TYPE.IP_DISCUSS) {
                      setIpOrTopicTabForPostReport('ip_discuss');
                    }
                  }}
                />
              </View>
            }
          >
            <PagerView
              ref={pagerViewRef}
              style={{ flex: 1 }}
              onPageSelected={e => {
                setCurTabIdx(e.nativeEvent.position.toString());
              }}
              overdrag
            >
              <TopicScene
                id={branInfo?.brand.toString() || ''}
                type={TOPIC_FETCH_TYPE.IP_RECOMMEND}
                isActive={
                  routes[curTabIdx]?.type === TOPIC_FETCH_TYPE.IP_RECOMMEND
                }
              />
              <TopicScene
                id={branInfo?.brand.toString() || ''}
                onScroll={onScroll}
                type={TOPIC_FETCH_TYPE.IP_DISCUSS}
                pendingId={pendingId}
                isActive={
                  routes[curTabIdx]?.type === TOPIC_FETCH_TYPE.IP_DISCUSS
                }
              />
            </PagerView>
          </NestedScrollView>
        </View>

        {curTabIdx === '1' ? (
          <GlobalPublishButton
            showGuide={showGuide}
            pageType={BbsPageType.ip}
            publishProps={{
              showTab: true,
              mode: 'newPost',
              asyncIps: branInfo ? [branInfo] : undefined,
              onPublishCallback: ({ cardId }) => {
                setPendingId(cardId);
              }
            }}
          />
        ) : (
          <PublishEntry
            config={{
              priorIp: brandId
            }}
            renderCustomAffix={onClick => {
              return (
                <View style={$publishContainer}>
                  <Button
                    style={$publishButton}
                    textStyle={$publishButtonText}
                    onPress={onClick}
                    iconText="icon_add"
                    iconStyle={{
                      tintColor: $publishButtonText.color,
                      marginRight: 4
                    }}
                    iconSize={12}
                  >
                    去发布
                  </Button>
                </View>
              );
            }}
          />
        )}
      </Screen>
    </PagePerformance>
  );
}

// 仅IP页使用，后续再挪动
const IPBrand = ({
  uri,
  color = '#264D2A',
  size = 118,
  style
}: {
  uri: string;
  color: string;
  size?: number;
  style?: StyleProp<ViewStyle>;
}) => {
  const height = (64 / 118) * size;
  const iconHeight = (138 / 118) * size;
  const iconWidth = (126 / 118) * size;
  const offset = (-4 / 118) * size;
  return (
    <View
      style={[
        {
          width: size
        },
        style
      ]}
    >
      <Image
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          height: height,
          width: size
        }}
        source={IP_IMAGE_BACK}
        tintColor={color}
      ></Image>
      <Image
        source={formatTosUrl(uri, { size: 'size4' }) || IP_GUIMIE}
        style={{
          left: offset,
          width: iconWidth,
          height: iconHeight
        }}
      ></Image>
    </View>
  );
};

const $publishButton: ViewStyle = {
  width: 142,
  height: 36,
  backgroundColor: CommonColor.brand1,
  borderRadius: 18,
  paddingVertical: 0
};

const $publishButtonText: TextStyle = {
  color: CommonColor.white,
  fontSize: 14,
  lineHeight: 20,
  fontWeight: '600'
};

const $publishContainer: ViewStyle = {
  position: 'absolute',
  bottom: 50,
  width: '100%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 1
};

const $headerIpBrandStyle: ViewStyle = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  flexDirection: 'row',
  marginLeft: 8
};

const $headerBrandStyle: ViewStyle = {
  position: 'relative',
  top: -4
};

const $headerIpName: TextStyle = {
  fontSize: 14,
  fontWeight: '600',
  lineHeight: 20,
  marginLeft: 8
};
