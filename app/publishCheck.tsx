import { useEffect } from 'react';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { Text } from '@/src/components/text';
import { Screen } from '@Components/screen';
import { useParams } from '../src/hooks/useParams';
import { PublishSingleton } from '@step.ai/publish-module';

export default function PublishCheck() {
  const localParams = useParams();

  useEffect(() => {
    if (Object.keys(localParams).length !== 0) {
      PublishSingleton.showCheckPage(localParams);
    }

    return () => {
      PublishSingleton.hideCheckPage();
    };
  }, [localParams]);

  return (
    <PagePerformance pathname="publishcheck">
      <Screen title="PublishCheck">
        <Text>Check Parmas: {JSON.stringify(localParams)}</Text>
      </Screen>
    </PagePerformance>
  );
}
