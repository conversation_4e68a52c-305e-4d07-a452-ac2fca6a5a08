import { useMemoizedFn } from 'ahooks';
import { useFocusEffect } from 'expo-router';
import React, { useCallback, useEffect } from 'react';
import { View } from 'react-native';
import PagerView from 'react-native-pager-view';
import { runOnUI, withTiming } from 'react-native-reanimated';
import { usePKResultPager } from '@/src/bizComponents/pkResultScreen/_hooks/pkResultPager.hook';
import { PkResultBackgroundView } from '@/src/bizComponents/pkResultScreen/components/pkResultBg';
import PkNominatePage from '@/src/bizComponents/pkResultScreen/pkNominatePage';
import PKResultPage from '@/src/bizComponents/pkResultScreen/pkResultPage';
import PKResultTab from '@/src/bizComponents/pkResultScreen/pkResultTab';
import { usePKExpoParams } from '@/src/bizComponents/pkScreen/_hooks/pkExpo';
import { PKMain } from '@/src/bizComponents/pkScreen/main';
import PKComment from '@/src/bizComponents/pkScreen/pkComment';
import PKShare from '@/src/bizComponents/pkScreen/pkShare';
import { endPK, initRoundResult } from '@/src/bizComponents/pkScreen/utilts';
import { Screen } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useAuthState } from '@/src/hooks';
import { usePKStore } from '@/src/store/pk';
import { useCreatePkResultStore } from '@/src/store/pk/pkResult';
import { catchErrorLog } from '@/src/utils/error-log';
import PkRankPage from '../../src/bizComponents/pkResultScreen/pkRankPage/index';
import { useParams } from '../../src/hooks/useParams';
import { useShallow } from 'zustand/react/shallow';

export default function PKResult() {
  const { id } = useParams();

  const { isPKEnd, pkCreatorUid } = usePKStore(
    useShallow(state => ({
      isPKEnd: state.isPKEnd,
      pkCreatorUid: state.pkCreatorUid
    }))
  );

  const { getUserAuthState } = useAuthState();

  const {
    pagerViewRef,
    animatedIndictor,
    curTabIdx,
    setCurTabIdx,
    handlePagerViewScroll
  } = usePKResultPager();

  const handleStart = useMemoizedFn(async () => {
    try {
      const isLogin = await getUserAuthState();

      if (isLogin) {
        const res = await usePKStore
          .getState()
          .startPK({ cardId: id as string });

        // 卡片的isPlayed未更新的场景兜底
        if (res?.latestFull?.length && !isPKEnd) {
          await endPK({ cardId: id as string });
        }
        initRoundResult();
      }

      // 初始化分享数据（未登陆使用isLogin）
      usePKStore.getState().initPkShareData({ cardId: id as string }, isLogin);
    } catch (e) {
      catchErrorLog('handleStartPk', e);
    }
  });

  // 清空定时器
  useEffect(() => {
    if (curTabIdx !== 0) {
      useCreatePkResultStore.getState().closeTimer();
    }
  }, [curTabIdx]);

  // 初始化数据
  useFocusEffect(
    useCallback(() => {
      handleStart();
    }, [])
  );

  usePKExpoParams({ cardId: id as string });

  // 清空数据
  useEffect(() => {
    return () => {
      usePKStore.getState().reset();
      useCreatePkResultStore.getState().reset();
    };
  }, []);

  return (
    <PagePerformance pathname="pk/[id]">
      <Screen
        theme="dark"
        backgroundView={
          <PkResultBackgroundView isPKEnd={isPKEnd} curTabIdx={curTabIdx} />
        }
        safeAreaEdges={['top']}
        title=""
        headerStyle={{ backgroundColor: 'transparent' }}
        headerTitle={() =>
          isPKEnd && (
            <PKResultTab
              animatedIndictor={animatedIndictor}
              curTabIdx={curTabIdx}
              onPress={nextTab => {
                pagerViewRef.current?.setPage(nextTab);
              }}
            />
          )
        }
        headerRight={() => (
          <PKShare detailId={id as string} authorId={pkCreatorUid} />
        )}
      >
        {isPKEnd ? (
          <View style={{ flex: 1 }}>
            <PagerView
              ref={pagerViewRef}
              style={{
                flex: 1
              }}
              scrollEnabled
              overdrag
              orientation="horizontal"
              onPageSelected={e => {
                setCurTabIdx(e.nativeEvent.position);
              }}
              onPageScroll={({ nativeEvent }) => {
                runOnUI(handlePagerViewScroll)(animatedIndictor, nativeEvent);
              }}
            >
              {/* <PKResultPage
                onTimeout={() => {
                  pagerViewRef.current?.setPage(1);
                  // setCurTabIdx(1);
                  // animatedIndictor.value = withTiming(1);
                }}
              /> */}
              <PkRankPage />
              <PkNominatePage />
            </PagerView>
          </View>
        ) : (
          <PKMain />
        )}
        <PKComment />
      </Screen>
    </PagePerformance>
  );
}
