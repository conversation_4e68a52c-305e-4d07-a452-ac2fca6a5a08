import dayjs from 'dayjs';
import { router } from 'expo-router';
import { Pressable, Text, TextStyle, View, ViewStyle } from 'react-native';
import ImageBox from '@/src/bizComponents/magic-video/imageBox';
import { UserPageTab } from '@/src/bizComponents/userScreen/constants';
import StatusCode from '@/src/bizComponents/videoMagic/statusCode';
import { Go2HomeScene, useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useLiveStore } from '@/src/store/live';
import { useMagicVideoEditStore } from '@/src/store/video-magic';
import { typography } from '@/src/theme';
import { $USE_FONT } from '@/src/theme/variable';
import { TabItemType } from '@/src/types';
import { isIos } from '@/src/utils';
import { reportClick } from '@/src/utils/report';
import {
  AsyncCardInfo,
  AsyncCardStatus
} from '@/proto-registry/src/web/raccoon/common/asynccard_pb';
import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
import { useShallow } from 'zustand/react/shallow';

export interface IHistoryItemProps {
  item: AsyncCardInfo;
  index: number;
}

export const AsyncCardStatusText = {
  [AsyncCardStatus.FAIL]: '生成失败',
  [AsyncCardStatus.PROCESSING]: '视频生成中',
  [AsyncCardStatus.SUC]: '生成了一个视频',
  [AsyncCardStatus.PUBLISH]: '生成了一个视频'
};

export default function HistoryItem({ item, index }: IHistoryItemProps) {
  const { syncHistoryExtInfo } = useMagicVideoEditStore(
    useShallow(state => ({
      syncHistoryExtInfo: state.syncHistoryExtInfo
    }))
  );
  const { go2HomePage } = useChangeRoute();

  const diffHistory = (item: AsyncCardInfo) => {
    reportClick('record_button', {
      templateid: (item.extInfo?.value?.value as ReimagineExtInfo)?.templateId,
      recordid: (item.extInfo?.value?.value as ReimagineExtInfo)?.reimagineId,
      videoid: (item.extInfo?.value?.value as ReimagineExtInfo)?.videoUrl
    });

    switch (item.status) {
      case AsyncCardStatus.FAIL: {
        go2HomePage({
          tab: TabItemType.PROFILE,
          pageTab: UserPageTab.SECRET,
          refresh: true,
          scene: Go2HomeScene.VIDEO_GENERATING
        });
        break;
      }
      case AsyncCardStatus.PROCESSING: {
        go2HomePage({
          tab: TabItemType.PROFILE,
          pageTab: UserPageTab.SECRET,
          refresh: true,
          scene: Go2HomeScene.VIDEO_GENERATING
        });
        break;
      }
      case AsyncCardStatus.SUC:
      case AsyncCardStatus.PUBLISH: {
        const reImagineInfo = item?.extInfo?.value.value as ReimagineExtInfo;
        syncHistoryExtInfo(reImagineInfo);

        router.navigate({
          pathname: '/magic-video-publish',
          params: {
            id: item.cardId,
            video_cover: reImagineInfo?.videoCoverImgUrl,
            video_url: reImagineInfo?.videoUrl,
            from_history: '1',
            templte_type: reImagineInfo?.templateType
          }
        });
        break;
      }
    }
  };

  const { liveCardMap } = useLiveStore(
    useShallow(state => ({
      liveCardMap: state.liveCardMap
    }))
  );

  return (
    <Pressable onPress={e => diffHistory(item)}>
      <View style={$history}>
        <View
          style={{
            width: 96,
            height: 71,
            backgroundColor: '#fff',
            borderRadius: 12
          }}
        >
          <ImageBox
            source={item.coverImgUrl}
            hiddenDelete
            status={item.status}
            resizeMode="cover"
            progress={
              liveCardMap?.[item.cardId]?.progress || item?.progress || 0
            }
            parentPress={() => diffHistory(item)}
          />
        </View>
        <View style={$detail}>
          <Text style={$result}>{AsyncCardStatusText[item.status]}</Text>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 12
            }}
          >
            {item.status !== AsyncCardStatus.SUC &&
            item.status !== AsyncCardStatus.PUBLISH ? (
              <View
                style={{
                  marginRight: 4.5,
                  justifyContent: 'center',
                  alignItems: 'flex-start'
                }}
              >
                <StatusCode
                  type={item.status || AsyncCardStatus.FAIL}
                  size={9}
                />
              </View>
            ) : null}
            {item.status === AsyncCardStatus.FAIL ? (
              <Text
                style={[
                  $resultTime,
                  {
                    opacity: 0.7
                  }
                ]}
              >
                {item.failReason}
              </Text>
            ) : (
              <Text
                style={[
                  $resultTime,
                  {
                    opacity: 0.7
                  }
                ]}
              >
                {dayjs(
                  (item.extInfo?.value?.value as ReimagineExtInfo)?.createTime
                ).format('YYYY-MM-DD HH:mm')}
              </Text>
            )}
          </View>
        </View>
      </View>
    </Pressable>
  );
}

const $history: ViewStyle = {
  paddingLeft: 16,
  paddingRight: 44,
  height: 71,
  width: '100%',
  marginVertical: 12,
  flexDirection: 'row',
  borderWidth: 0
};

const $detail: ViewStyle = {
  marginLeft: 16,
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'flex-start'
};

const $result: TextStyle = $USE_FONT(
  '#fff',
  typography.fonts.pingfangSC.normal,
  15,
  'normal',
  isIos ? '600' : 'bold',
  undefined
);
const $resultTime: TextStyle = $USE_FONT(
  '#fff',
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  '400',
  undefined
);
