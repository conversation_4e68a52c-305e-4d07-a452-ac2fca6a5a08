import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import { RefreshControl, TouchableOpacity, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { feedClient } from '@/src/api';
import {
  WINDOW_HEIGHT,
  WINDOW_WIDTH
} from '@/src/bizComponents/nestedScrollView';
import { Icon, hideLoading } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { GameType } from '@/src/types';
import { isIos } from '@/src/utils';
import { reportExpo } from '@/src/utils/report';
import { Screen } from '@Components/screen';
import {
  AsyncCardInfo,
  AsyncCardStatus
} from '@/proto-registry/src/web/raccoon/common/asynccard_pb';
import { Pagination } from '@/proto-registry/src/web/raccoon/common/utils_pb';
import HistoryItem from './history-item';

export enum EVideoHistoryStatus {
  'PROGRESS' = 'PROGRESS',
  'FINISHED' = 'FINISHED',
  'ERROR' = 'ERROR'
}

export type TVideoHistory = {
  status: EVideoHistoryStatus;
  id: string;
  errorContent: string;
  content: string;
  generateTime: number;
};

export default function MagicVideoHistory() {
  const flatlistRef = useRef<FlatList>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const nextPageToken = useRef<Partial<Pagination>>({
    size: 30,
    cursor: ''
  });

  useEffect(() => {
    reportExpo('expo', undefined, true);
  }, []);

  const resetToken = () => {
    nextPageToken.current = {
      size: 30,
      cursor: ''
    };
  };

  const [historyList, setHistoryList] = useState<AsyncCardInfo[]>([]);

  const flush = async () => {
    console.log('flush it');
  };

  const [isEmpty, setIsEmpty] = useState(false);

  const flushNextPage = async () => {
    if (nextPageToken?.current?.nextCursor === '') {
      return;
    }

    console.log('flush next page');
    // 先 mock roleList
    try {
      const res = await feedClient.userHistoryAsyncCards({
        pagination: nextPageToken.current,
        gameTypes: [GameType.REIMAGINE]
      });

      if (!res.cards.length) {
        setIsEmpty(true);
      } else {
        setIsEmpty(false);
      }

      nextPageToken.current = {
        ...nextPageToken.current,
        cursor: res.pagination?.nextCursor, // 用于下次请求
        nextCursor: res.pagination?.nextCursor
      };

      console.log(res, 'resssssss');

      setHistoryList([...historyList, ...res.cards]);
    } catch (e) {
      console.log(e, 'get history list error');
      // error report
    } finally {
      hideLoading();
    }
  };

  return (
    <PagePerformance pathname="magic-video-history/index">
      <Screen
        title="生成历史"
        theme="dark"
        backButton={false}
        headerLeft={() => (
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
          >
            <Icon
              icon="back"
              size={24}
              style={{
                tintColor: '#fff'
              }}
            />
          </TouchableOpacity>
        )}
        safeAreaEdges={['top', 'bottom']}
        screenStyle={{
          backgroundColor: '#000'
        }}
      >
        <View
          style={{
            flex: 1
          }}
        >
          <FlatList
            horizontal={false}
            initialNumToRender={10}
            contentContainerStyle={{
              minHeight: WINDOW_HEIGHT,
              height: 'auto',
              width: '100%'
            }}
            ref={flatlistRef}
            data={historyList}
            scrollEventThrottle={16}
            renderToHardwareTextureAndroid
            onEndReachedThreshold={50}
            onEndReached={flushNextPage}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={async () => {
                  try {
                    setIsRefreshing(true);
                    await flush();
                  } finally {
                    setIsRefreshing(false);
                  }
                }}
              />
            }
            renderItem={({
              item,
              index
            }: {
              item: AsyncCardInfo;
              index: number;
            }) => <HistoryItem item={item} index={index} key={index} />}
            keyExtractor={item => item.cardId}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          />
        </View>
      </Screen>
    </PagePerformance>
  );
}
