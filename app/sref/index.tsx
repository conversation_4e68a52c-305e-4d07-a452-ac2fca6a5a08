import { useThrottleFn } from 'ahooks';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  Pressable,
  ScrollView,
  Text,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming
} from 'react-native-reanimated';
import { Image, Screen } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import CreditCas, {
  CREDIT_LIMIT,
  CREDIT_TYPE,
  MINUS_BORDER_THEME2,
  MINUS_THEME2,
  PLUS_BORDER_THEME2,
  PLUS_THEME2,
  PURE_BORDER_MINUS2,
  PURE_BORDER_PLUS2
} from '@/src/components/credit-cas';
import {
  $selectedTextStyle,
  $tabTitleTextStyle
} from '@/src/components/makePhoto/bottomTab';
import {
  UploadSrefImage,
  UploadSrefImageHandle
} from '@/src/components/sref/UploadSrefImage';
import { SrefEvents } from '@/src/components/sref/constant';
import { Tabs } from '@/src/components/tabs';
import { useScreenSize } from '@/src/hooks';
import { useOneRunning } from '@/src/hooks/useOneRunning';
import { useAuthStore } from '@/src/store/authInfo';
import { useCreditStore } from '@/src/store/credit';
import { useMakePhotoStoreV2 } from '@/src/store/makePhotoV2';
import { useSrefStore } from '@/src/store/sref';
import { rowStyle, typography } from '@/src/theme';
import { hex } from '@/src/theme';
import { dp2px } from '@/src/utils';
import { reportClick } from '@/src/utils/report';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { useParams } from '../../src/hooks/useParams';
import { Photo } from '@/proto-registry/src/web/raccoon/makephoto/makephoto_pb';
import { StrokeText } from '@charmy.tech/react-native-stroke-text';
import { useShallow } from 'zustand/react/shallow';

const BG_IMG = require('@Assets/makephoto/bgv3.png');
const NO_UPLOAD = require('@Assets/image/sref/no-upload-image.png');

const tableItems = [
  {
    label: '发现',
    title: '发现',
    key: '发现'
  },
  {
    label: '_',
    title: '我的',
    key: '我的'
  }
];

const $renderItemStyle: ViewStyle = {
  width: dp2px(165),
  height: dp2px(200),
  marginVertical: 6,
  overflow: 'hidden',
  alignItems: 'center'
};

const $noImageTextStyle: TextStyle = {
  color: '#CDEAFF',
  fontSize: 14,
  fontFamily: typography.fonts.pingfangSC.normal,
  fontWeight: '500',
  opacity: 0.4
  // top: -60
};

const $buttonText: TextStyle = {
  color: 'white',
  fontSize: 12,
  fontFamily: typography.fonts.pingfangSC.normal,
  fontWeight: '600'
};

const $imageContainer: ViewStyle = {
  width: dp2px(165),
  height: dp2px(200),
  marginVertical: 6,
  overflow: 'hidden',
  alignItems: 'center'
};

export default function Sref() {
  const { totalCredits } = useCreditStore(
    useShallow(state => ({
      totalCredits: state.totalCredits
    }))
  );

  const { direct } = useParams<{
    direct?: 'true' | 'false';
  }>();

  const {
    srefPhotos,
    mySrefPhotos,
    originFetchSrefPhotos,
    originFetchMySrefPhotos,
    resetMySrefPhotos
  } = useSrefStore(
    useShallow(state => ({
      srefPhotos: state.srefPhotos,
      mySrefPhotos: state.mySrefPhotos,
      originFetchSrefPhotos: state.fetchSrefPhotos,
      originFetchMySrefPhotos: state.fetchMySrefPhotos,
      resetMySrefPhotos: state.resetMySrefPhotos
    }))
  );

  const fetchSrefPhotos = useOneRunning(originFetchSrefPhotos);
  const fetchMySrefPhotos = useOneRunning(originFetchMySrefPhotos);

  const { uid = '' } = useAuthStore(
    useShallow(state => ({
      uid: state.uid
    }))
  );

  const [currentTab, setCurrentTab] = useState(tableItems[0].key);
  // const [collapse, setCollapse] = useState(false);

  const { run: throttledOnPress } = useThrottleFn(onPress, {
    wait: 3000,
    leading: true,
    trailing: false
  });

  // const collapse = useRef(false);
  const interrupted = useRef(false);
  const uploadRef = useRef<UploadSrefImageHandle>(null);
  const translateY = useSharedValue(0);
  const { width: screenWidth, height: screenHeight } = useScreenSize('window');
  const collapse = useSharedValue(0);

  const loadingElements = useMemo(() => {
    const loadingLength = srefPhotos.length < 8 ? 8 - srefPhotos.length : 0;
    return new Array(loadingLength)
      .fill(null)
      .map((_, index) => (
        <View
          key={index}
          style={[
            $imageContainer,
            { borderRadius: 6, backgroundColor: 'rgba(115, 82, 80, 1)' }
          ]}
        ></View>
      ));
  }, [srefPhotos]);

  const renderList =
    currentTab === tableItems[0].key ? srefPhotos : mySrefPhotos;

  const onMomentumScrollEnd = (
    event: NativeSyntheticEvent<NativeScrollEvent>
  ) => {
    // 兼容安卓机复原折叠
    const { contentOffset } = event.nativeEvent;
    // if (contentOffset.y === 0) {
    //   uploadRef.current?.changeExpand(false, () => {
    //     setCollapse(false);
    //     translateY.value = 0;
    //   });
    // }
  };

  // const $tabStyles = useAnimatedStyle(() => ({
  //   display: collapse.value === 1 ? undefined : 'none',
  //   opacity: collapse.value,
  //   transform: [
  //     {
  //       scaleY: collapse.value === 1 ? 1 : 0
  //     }
  //   ]
  // }));

  // const $heightStyle = useAnimatedStyle(() => ({
  //   position: 'absolute',
  //   bottom: 0,
  //   transform: [
  //     {
  //       scaleY: translateY.value ? 175 : 0
  //     }
  //   ],
  //   height: 1,
  //   width: '100%',
  //   backgroundColor: '#543C3E'
  // }));

  useEffect(() => {
    resetMySrefPhotos();
  }, [uid]);

  useEffect(() => {
    fetchSrefPhotos();
    fetchMySrefPhotos();
  }, []);

  return (
    <PagePerformance pathname="sref/index">
      <Screen
        title="自定义"
        theme="dark"
        safeAreaEdges={['top']}
        onBack={() => {
          interrupted.current = true;
          if (direct === 'true') {
            router.replace('/make-photo');
          } else {
            safeGoBack();
          }
        }}
        backgroundView={
          <Image
            style={{
              position: 'absolute',
              top: 0,
              left: -1,
              bottom: 0,
              right: -1
            }}
            source={BG_IMG}
          ></Image>
        }
        headerLeft={() => {
          const moreThanLimit = totalCredits >= CREDIT_LIMIT;
          const theme = moreThanLimit ? CREDIT_TYPE.PLUS : CREDIT_TYPE.MINUS;
          return (
            <View style={{ position: 'absolute', left: 32 }}>
              <CreditCas
                theme={theme}
                text={`${totalCredits}`}
                borderColors={
                  moreThanLimit ? PLUS_BORDER_THEME2 : MINUS_BORDER_THEME2
                }
                insetsColors={moreThanLimit ? PLUS_THEME2 : MINUS_THEME2}
                $customTextStyle={{
                  color: '#fff',
                  fontSize: 12,
                  lineHeight: 22
                }}
                pureBorderColor={
                  moreThanLimit ? PURE_BORDER_PLUS2 : PURE_BORDER_MINUS2
                }
                size={20}
                hasPad
              ></CreditCas>
            </View>
          );
        }}
        headerRight={() => (
          <UploadSrefImage
            collapse={collapse}
            small={true}
            // isAnimating={isAnimating}
            onSuccess={item => {
              if (interrupted.current) return;
              throttledOnPress(item);
            }}
            ref={uploadRef}
          ></UploadSrefImage>
        )}
        contentContainerStyle={{
          flex: 1
        }}
      >
        <ScrollView
          onScroll={onScroll}
          onMomentumScrollEnd={onMomentumScrollEnd}
          scrollEventThrottle={100}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
        >
          <View
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              minHeight: screenHeight
            }}
          >
            <UploadSrefImage
              collapse={collapse}
              // isAnimating={isAnimating}
              onSuccess={item => {
                if (interrupted.current) return;
                throttledOnPress(item);
              }}
              ref={uploadRef}
            ></UploadSrefImage>
            <Tabs
              tabBarStyle={{ ...rowStyle, paddingHorizontal: 0 }}
              items={tableItems}
              current={currentTab}
              tabGap={0}
              onPressTab={(_, key) => {
                reportClick(
                  key === tableItems[0].key
                    ? SrefEvents.sref_styletab
                    : SrefEvents.sref_mystyletab
                );
                setCurrentTab(key!);
              }}
              itemTextStyle={$tabTitleTextStyle}
              tabWidth={screenWidth / 2}
              itemStyle={{
                height: 40
              }}
              renderIndicator={({ tabWidth, animatedStyle }) => (
                <Animated.View
                  style={[
                    {
                      borderBottomColor: '#FC968F',
                      width: tabWidth,
                      position: 'absolute',
                      bottom: -2,
                      borderBottomWidth: 2
                    },
                    animatedStyle
                  ]}
                />
              )}
            />
            <View
              style={{
                width: '100%',
                backgroundColor: '#543C3E',
                flex: 1
                // position: 'relative'
              }}
            >
              {currentTab === tableItems[1].key && renderList.length === 0 && (
                <View
                  style={{
                    width: '100%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    position: 'relative',
                    top: 100
                  }}
                >
                  <Image
                    style={{ width: 120, height: 120 }}
                    source={NO_UPLOAD}
                  ></Image>
                  <Text style={$noImageTextStyle}>
                    {'暂时还没有上传图片哩～'}
                  </Text>
                </View>
              )}

              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  // ...rowStyle,
                  flexWrap: 'wrap',
                  width: '100%',
                  paddingHorizontal: 15,
                  paddingVertical: 6,
                  justifyContent: 'space-between'
                }}
              >
                {renderList.map(item => (
                  <View key={item.photoId} style={$renderItemStyle}>
                    <Image
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'cover',
                        borderRadius: 6
                      }}
                      tosSize="size2"
                      source={item.url}
                    ></Image>
                    {currentTab === tableItems[0].key && item.desc && (
                      <View
                        style={[
                          {
                            position: 'absolute',
                            bottom: 55,
                            transform: [{ skewX: '-10deg' }]
                          }
                        ]}
                      >
                        <StrokeText
                          numberOfLines={1}
                          text={item.desc}
                          strokeWidth={2}
                          fontSize={16}
                          color="#ffffff"
                          fontFamily={typography.fonts.baba.bold}
                          strokeColor="#000000"
                        ></StrokeText>
                      </View>
                    )}
                    <Pressable
                      onPress={() => {
                        reportClick(SrefEvents.sref_mystyle_card);
                        throttledOnPress(item);
                      }}
                      style={{
                        position: 'absolute',
                        bottom: 16,
                        borderColor: 'rgba(254, 231, 218, 0.40)',
                        borderWidth: 1,
                        borderRadius: 99,
                        width: 104,
                        height: 30,
                        overflow: 'hidden'
                      }}
                    >
                      <BlurView
                        intensity={10}
                        style={{
                          width: '100%',
                          height: '100%',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backgroundColor: 'rgba(0, 0, 0, 0.2)'
                        }}
                      >
                        <Text style={$buttonText}>{'使用'}</Text>
                      </BlurView>
                    </Pressable>
                  </View>
                ))}
                {currentTab === tableItems[0].key && loadingElements}
              </View>
            </View>
          </View>
        </ScrollView>
      </Screen>
    </PagePerformance>
  );

  function onScroll(event: NativeSyntheticEvent<NativeScrollEvent>) {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const offsetY = contentOffset.y;
    const contentHeight = contentSize.height;
    const scrollHeight = layoutMeasurement.height;

    if (scrollHeight + offsetY > contentHeight - 200) {
      currentTab === tableItems[0].key
        ? fetchSrefPhotos()
        : fetchMySrefPhotos();
    }

    if (event.nativeEvent.contentOffset.y > 150) {
      collapse.value = withTiming(1, { duration: 300 });
    } else {
      collapse.value = withTiming(0, { duration: 300 });
    }
  }

  function onPress(item: Photo) {
    const { setSref, setStyle } = useMakePhotoStoreV2.getState();
    setSref({
      srefPhotoId: item.photoId,
      url: item.url,
      desc: item.desc || '自定义风格'
    });
    setStyle('sref');
    interrupted.current = true;
    if (direct === 'true') {
      router.replace('/make-photo');
    } else {
      safeGoBack();
    }
  }
}
