import { View } from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets
} from 'react-native-safe-area-context';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import ShareActivity from '@/src/components/shareActivity';
import { useSafeAreaInsetsStyle } from '@/src/hooks';
import { Screen } from '@Components/screen';

export default function Activity() {
  const insets = useSafeAreaInsets();
  return (
    <PagePerformance pathname="share-activity/index">
      <View
        style={[
          { position: 'relative', flex: 1, overflow: 'hidden' },
          { paddingTop: insets.top, backgroundColor: '#FABE00' }
        ]}
      >
        <ShareActivity />
      </View>
    </PagePerformance>
  );
}
