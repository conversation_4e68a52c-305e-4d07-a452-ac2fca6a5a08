import { useEffect, useState } from 'react';
import { ScrollView, View, ViewStyle } from 'react-native';
import { IOScrollView } from 'react-native-intersection-observer';
import { feedClient } from '@/src/api';
import { IpSection } from '@/src/bizComponents/parallelWorld/brandWorldList';
import { Icon, Image, Screen } from '@/src/components';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { uploadGameUser } from '@/src/components/publishEntry/entryBadge';
import {
  SkeletonCircle,
  SkeletonColumn,
  SkeletonRow,
  SkeletonSpan
} from '@/src/components/skeletion';
import { useSafeBottomArea } from '@/src/hooks';
import { useParams } from '@/src/hooks/useParams';
import { CommonColor } from '@/src/theme/colors/common';
import { Theme } from '@/src/theme/colors/type';
import { GameType } from '@/src/types';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { reportExpo } from '@/src/utils/report';
import { Text } from '@Components/text';
import { AllRootWorldsResponse_Brand } from '@step.ai/proto-gen/raccoon/query/query_pb';

export const PAGE_BG = require('@Assets/image/parallel-world/center-bg.png');

export default function ParallelWorldCenter() {
  const { priorIp } = useParams<{
    priorIp?: string;
  }>();

  const [wpConfig, setWpConfig] = useState<
    AllRootWorldsResponse_Brand[] | null
  >();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const $bottom = useSafeBottomArea();

  const initPageData = async () => {
    setLoading(true);
    setError(false);

    try {
      const res = await feedClient.allRootWorlds({});
      const brands = res.brands as AllRootWorldsResponse_Brand[];

      if (priorIp) {
        const index = brands.findIndex(
          item => item.brandInfo?.brand.toString() === priorIp
        );
        if (index !== -1) {
          const prior = brands.splice(index, 1);
          brands.unshift(prior[0]);
        }
      }

      setWpConfig(res.brands);
    } catch (error) {
      errorReport(
        'parallelWorldGetAllRootWorldsError',
        ReportError.PARALLEL_WORLD,
        error
      );
      setError(true);
    }
    setLoading(false);
  };

  useEffect(() => {
    initPageData();
    uploadGameUser(GameType.WORLD);
    setTimeout(() => {
      reportExpo('page_expo', { module: 'world' });
    });
  }, []);

  return (
    <PagePerformance pathname="parallel-world/center">
      <Screen
        // ScrollViewComp={IOScrollView}
        theme="dark"
        preset="fixed"
        safeAreaEdges={['top']}
        headerTitle={() => (
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: 4,
              alignItems: 'center'
            }}
          >
            <Icon icon="pw_icon_white" size={18} />
            <Text
              preset="bold"
              size="md"
              numberOfLines={1}
              style={{ color: CommonColor.white }}
            >
              平行世界
            </Text>
          </View>
        )}
        headerStyle={{
          borderBottomWidth: 0
        }}
        backgroundView={
          <Image
            source={PAGE_BG}
            style={{
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              position: 'absolute'
            }}
          />
        }
      >
        {error ? (
          <View style={$pageContainerStyle}>
            <EmptyPlaceHolder theme={Theme.DARK}>找不到啦</EmptyPlaceHolder>
          </View>
        ) : loading ? (
          <View style={$pageContainerStyle}>
            <ParallelWorldCenterSkeleton />
          </View>
        ) : wpConfig?.length ? (
          <IOScrollView
            showsVerticalScrollIndicator={false}
            style={[$pageContainerStyle]}
          >
            <View
              style={[
                {
                  width: '100%',
                  marginBottom: $bottom
                }
              ]}
            >
              {wpConfig.map(item => (
                <IpSection key={item.brandInfo?.brand} brand={item} />
              ))}
            </View>
          </IOScrollView>
        ) : (
          <View style={$pageContainerStyle}>
            <EmptyPlaceHolder theme={Theme.DARK}>暂无内容</EmptyPlaceHolder>
          </View>
        )}
      </Screen>
    </PagePerformance>
  );
}

function ParallelWorldCenterSkeleton() {
  return (
    <SkeletonColumn style={$skeletonContainerStyle} repeat={3} gap={50}>
      <SkeletonColumn>
        <SkeletonRow>
          <SkeletonCircle theme={Theme.DARK} size={26} />
          <SkeletonSpan theme={Theme.DARK} height={26} width={98} radius={4} />
        </SkeletonRow>
        <SkeletonRow repeat={4}>
          <SkeletonColumn>
            <SkeletonSpan
              theme={Theme.DARK}
              height={130}
              width={98}
              radius={4}
            />
            <SkeletonSpan
              theme={Theme.DARK}
              height={20}
              width={98}
              radius={4}
            />
          </SkeletonColumn>
        </SkeletonRow>
      </SkeletonColumn>
    </SkeletonColumn>
  );
}

const $pageContainerStyle: ViewStyle = {
  paddingTop: 10,
  width: '100%',
  height: '100%'
};

const $skeletonContainerStyle: ViewStyle = {
  paddingLeft: 20
};
