import React, { memo, useEffect, useRef, useState } from 'react';
import { TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSharedValue } from 'react-native-reanimated';
import TarotEntry from '@/src/bizComponents/tarot/tarotEntry';
import { BannerPart } from '@/src/bizComponents/welfare/bannerPart';
import { LotteryPart } from '@/src/bizComponents/welfare/lotteryPart';
import { PrizePool } from '@/src/bizComponents/welfare/lotteryPart/prizePool';
import { SIGN_PART_RESOURCES } from '@/src/bizComponents/welfare/resource';
import { SignPart } from '@/src/bizComponents/welfare/signPart';
import { TaskPart } from '@/src/bizComponents/welfare/taskPart';
import { Icon } from '@/src/components';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { Image } from '@/src/components/image';
import { hideModal } from '@/src/components/popup/ModalGlobal';
import { usePersistFn } from '@/src/hooks';
import { useCreditStore } from '@/src/store/credit';
import { useWelfareStore } from '@/src/store/welfare';
import { darkTheme, typography } from '@/src/theme';
import { Theme } from '@/src/theme/colors/type';
import { $USE_FONT } from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { clearTimeoutWorklet, setTimeoutWorklet } from '@/src/utils/worklet';
import { Screen } from '@Components/screen';
import { Text } from '@Components/text';
import { useParams } from '../../src/hooks/useParams';
import { EAnimateByReward, RewardAnimation } from '../gacha/rewardAnimation';
import NetInfo from '@react-native-community/netinfo';
import { useIsFocused } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';

export const RedDot = memo(() => {
  return <View style={$dot} />;
});

export default function Welfare() {
  useEffect(() => {
    reportExpo('welfare', { module: 'welfare' }, true);
    useCreditStore.getState().getBannerInfo();
    init();
  }, []);

  const { init, signBoardInfo, gachaPlayStatus } = useWelfareStore(
    useShallow(s => ({
      init: s.init,
      signBoardInfo: s.signBoardInfo,
      gachaPlayStatus: s.gachaPlayStatus
    }))
  );

  const { anchorTask } = useParams();

  const welfareRef = useRef<ScrollView>(null);

  const $welfareTimeout = useSharedValue(0);
  useEffect(() => {
    if (anchorTask) {
      setTimeoutWorklet(
        $welfareTimeout,
        () => {
          welfareRef?.current?.scrollTo({
            y: 250,
            animated: true
          });
        },
        0
      );
    }

    return () => {
      clearTimeoutWorklet($welfareTimeout);
    };
  }, [anchorTask]);

  const [hasNetWork, setHasNetWork] = useState(true);

  const checkHasNetWork = usePersistFn(async () => {
    const state = await NetInfo.fetch('');
    if (!state.isConnected) {
      setHasNetWork(false);
    } else {
      setHasNetWork(true);
    }
  });

  useEffect(() => {
    checkHasNetWork();

    return () => {
      hideModal();
    };
  }, []);

  return (
    <PagePerformance pathname="welfare/index">
      <View
        style={{
          flex: 1,
          backgroundColor: darkTheme.background.page
        }}
      >
        <Screen
          backButton={false}
          theme={Theme.DARK}
          headerLeft={() => (
            <TouchableOpacity
              onPress={() => {
                reportClick('back_button', { module: 'welfare' });
                safeGoBack();
              }}
            >
              <Icon
                icon="back"
                size={24}
                style={{
                  tintColor: '#fff'
                }}
              />
            </TouchableOpacity>
          )}
          headerTitle={() => <Text style={$pageTitle}>活动中心</Text>}
          headerStyle={{
            justifyContent: 'space-between'
          }}
          headerRight={() => <TarotEntry />}
          style={{ flex: 1 }}
          safeAreaEdges={['top']}
        >
          {hasNetWork ? (
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                paddingTop: 16,
                paddingBottom: 0
              }}
              ref={welfareRef}
            >
              {signBoardInfo?.task ? (
                <>
                  <SignPart />
                  <View
                    style={{
                      transform: [
                        {
                          translateY: -20
                        }
                      ]
                    }}
                  >
                    <LotteryPart />
                    <PrizePool />
                    <TaskPart />
                  </View>
                </>
              ) : null}
              <View
                style={{
                  transform: [
                    {
                      translateY: -20
                    }
                  ]
                }}
              >
                <BannerPart />
                <View
                  style={{
                    width: '100%',
                    height: 60,
                    alignItems: 'center',
                    marginTop: 0
                  }}
                >
                  <Image
                    source={SIGN_PART_RESOURCES['FOOTER_UNDERLINE']}
                    tosSize="size1"
                    style={{
                      width: 158,
                      height: 60
                    }}
                  />
                </View>
              </View>
            </ScrollView>
          ) : (
            <EmptyPlaceHolder
              buttonText="重试"
              button={true}
              onButtonPress={() => {
                init();
                checkHasNetWork();
              }}
            >
              小狸走丢了
            </EmptyPlaceHolder>
          )}
        </Screen>
        {gachaPlayStatus ? (
          <RewardAnimation type={EAnimateByReward.TASK} />
        ) : null}
      </View>
    </PagePerformance>
  );
}

const $pageTitle: TextStyle = $USE_FONT(
  darkTheme.text.primary,
  typography.fonts.pingfangSC.normal,
  16,
  'normal',
  isIos ? '600' : 'bold',
  undefined
);

const $dot: ViewStyle = {
  position: 'absolute',
  backgroundColor: '#FF3B3B',
  width: 8,
  height: 8,
  borderRadius: 4,
  right: 2,
  top: 0,
  borderWidth: 2,
  borderColor: darkTheme.background.page
};
