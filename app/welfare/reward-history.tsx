import { router } from 'expo-router';
import { useEffect } from 'react';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { WelfareHistoryRecord } from '@/src/bizComponents/historyRecord';
import { ERewardHistoryPage } from '@/src/bizComponents/historyRecord/const';
import { Image, ImageStyle, Screen, Text } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { darkTheme } from '@/src/theme';
import { $absoluteFull, $flexCenter } from '@/src/theme/variable';
import { reportClick, reportExpo } from '@/src/utils/report';

export default function WelfareRewardHistory() {
  useEffect(() => {
    reportExpo('welfare_view_rewards', { module: 'welfare' }, true);
  }, []);

  return (
    <PagePerformance pathname="welfare/reward-history">
      <View
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: darkTheme.background.page
        }}
      >
        <Screen
          theme="dark"
          title="奖励记录"
          safeAreaEdges={['top']}
          backgroundView={
            <Image
              cache
              style={$absoluteFull as ImageStyle}
              source="https://resource.lipuhome.com/resource/img/prod/20250228/d8b9e782352f2709b542a797bf39618b.png"
            />
          }
          onBack={() => {
            reportClick('invite_reward_details', {
              module: 'invite',
              type: 0
            });
            router.back();
          }}
          style={{
            height: '100%',
            width: '100%'
          }}
        >
          <View style={{ flex: 1 }}>
            <WelfareHistoryRecord page={ERewardHistoryPage.WELFARE} />
          </View>
        </Screen>
      </View>
    </PagePerformance>
  );
}
