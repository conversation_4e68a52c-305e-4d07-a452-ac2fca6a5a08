import { useMemoizedFn } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';
import { Dimensions, Keyboard, LayoutChangeEvent, Text, TextInput, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, { runOnJS, useAnimatedGestureHandler, useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import { clamp } from '@/src/bizComponents/nestedScrollView/utilities/clamp';
import { PhotoText, useMakePhotoStoreV2 } from '@/src/store/makePhotoV2';
import { StyleSheet } from '@Utils/StyleSheet';
import { useShallow } from 'zustand/react/shallow';


interface Position {
  x: number;
  y: number;
  scale: number;
  rotate: number;
}

interface DraggableInputProps extends PhotoText {
  containerWidth?: number;
  containerHeight?: number;
  onTextChange?: (text: string) => void;
  onPositionChange?: (position: Position) => void;
}

interface DraggableInputContainerProps {
  index: number;
}

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const DraggableInput: React.FC<DraggableInputProps> = ({
  text = '',
  id,
  color,
  fontFamily,
  position,
  onTextChange,
  onPositionChange,
  containerWidth = SCREEN_WIDTH,
  containerHeight = SCREEN_HEIGHT
}) => {
  const { x, y, scale, rotate } = position;

  const [value, setValue] = useState(text);
  const inputRef = useRef<TextInput>(null);
  const [inputWidth, setInputWidth] = useState(100);
  const [inputHeight, setInputHeight] = useState(44);

  // 拖拽
  const translateX = useSharedValue(x);
  const translateY = useSharedValue(y);
  const prevTranslationX = useSharedValue(x);
  const prevTranslationY = useSharedValue(y);
  // 缩放
  const shareScale = useSharedValue(scale);
  const startScale = useSharedValue(scale);
  // 旋转
  const angle = useSharedValue(rotate);
  const startAngle = useSharedValue(rotate);

  useEffect(() => {}, []);

  // 保存位置信息
  const savePosition = (
    x: number,
    y: number,
    currentScale: number,
    rotate: number
  ) => {
    if (onPositionChange) {
      onPositionChange({
        x: x,
        y: y,
        scale: currentScale,
        rotate: rotate
      });
    }
  };

  const drag = Gesture.Pan()
    .onStart(() => {
      prevTranslationX.value = translateX.value;
      prevTranslationY.value = translateY.value;
    })
    .onUpdate(event => {
      translateX.value = clamp(
        prevTranslationX.value + event.translationX,
        -1 * containerWidth,
        containerWidth
      );

      translateY.value = clamp(
        prevTranslationY.value + event.translationY,
        -1 * containerHeight,
        containerWidth
      );
    })
    .onEnd(() => {
      runOnJS(savePosition)(
        translateX.value,
        translateY.value,
        shareScale.value,
        angle.value
      );
    });

  const pinch = Gesture.Pinch()
    .onStart(() => {
      startScale.value = shareScale.value;
    })
    .onUpdate(event => {
      shareScale.value = clamp(
        startScale.value * event.scale,
        0.5,
        Math.min(containerWidth / 100, containerHeight / 100)
      );
    })
    .onEnd(() => {
      runOnJS(savePosition)(
        translateX.value,
        translateY.value,
        shareScale.value,
        angle.value
      );
    });

  const rotation = Gesture.Rotation()
    .onStart(() => {
      startAngle.value = angle.value;
    })
    .onUpdate(event => {
      angle.value = startAngle.value + event.rotation;
    })
    .onEnd(() => {
      runOnJS(savePosition)(
        translateX.value,
        translateY.value,
        shareScale.value,
        angle.value
      );
    });

  // 动画样式
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: shareScale.value },
        { rotate: `${angle.value}rad` }
      ]
    };
  });

  const composed = Gesture.Race(drag, pinch, rotation);

  // 处理输入框大小变化
  const onLayout = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setInputWidth(width);
    setInputHeight(height);
  };

  return (
    <GestureDetector gesture={composed}>
      <Animated.View
        style={[
          {
            position: 'absolute',
            left: '50%',
            top: '50%'
          },
          animatedStyle
        ]}
      >
        <TextInput
          value={value}
          ref={inputRef}
          onChangeText={text => {
            setValue(text);
            onTextChange?.(text);
          }}
          onLayout={onLayout}
          style={[
            {
              height: 44,
              width: 100,
              paddingLeft: 16,
              paddingRight: 40,
              borderRadius: 8,
              backgroundColor: 'transparent',
              fontSize: 14,
              fontFamily: fontFamily,
              fontWeight: '500',
              color: color,
              borderWidth: 1,
              borderColor: StyleSheet.currentColors.white
            }
          ]}
        />
      </Animated.View>
    </GestureDetector>
  );
};


const DraggableInputContainer: React.FC<DraggableInputContainerProps> = ({
  index = 0
}: {
  index: number;
}) => {
  const { photoTextGroups, addGroupText, updateGroupText } =
    useMakePhotoStoreV2(
      useShallow(state => ({
        photoTextGroups: state.photoTextGroups,
        addGroupText: state.addGroupText,
        updateGroupText: state.updateGroupText
      }))
    );

  const photoTextGroup = photoTextGroups[index];

  const onPositionChange = useMemoizedFn(
    (position: Position, textIndex: number) => {
      updateGroupText(index, textIndex, {
        position
      });
    }
  );

  const onTextChange = useMemoizedFn((text: string, textIndex: number) => {
    updateGroupText(index, textIndex, {
      text
    });
  });

  return (
    <View
      style={[
        StyleSheet.absoluteFill,
        {
          backgroundColor: '#000'
        }
      ]}
    >
      {photoTextGroup.map((item, textIndex) => (
        <DraggableInput
          key={item.id}
          onPositionChange={position => {
            onPositionChange(position, textIndex);
          }}
          onTextChange={text => {
            onTextChange(text, textIndex);
          }}
          containerHeight={100}
          containerWidth={100}
          {...item}
        />
      ))}

      <TouchableOpacity
        style={{
          position: 'absolute',
          bottom: 100,
          left: 100
        }}
        onPress={() => {
          addGroupText(index);
        }}
      >
        <Text
          style={{
            color: '#fff',
            position: 'absolute'
          }}
        >
          添加文本
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={{
          position: 'absolute',
          bottom: 100,
          left: 200
        }}
        onPress={() => {
          updateGroupText(index, 0, {
            color: 'red'
          });
        }}
      >
        <Text
          style={{
            color: '#fff',
            position: 'absolute'
          }}
        >
          修改颜色
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default DraggableInputContainer;