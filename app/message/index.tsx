import { router } from 'expo-router';
import { useEffect } from 'react';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { Theme } from '@/src/theme/colors/type';
import { TabItemType } from '@/src/types';
import { StyleSheet } from '@/src/utils';
import { Screen } from '@Components/screen';

export default function Message() {
  useEffect(() => {
    setTimeout(() => {
      router.replace({
        pathname: '/feed',
        params: {
          tab: TabItemType.MESSSGE
        }
      });
    });
  }, []);

  return (
    <PagePerformance pathname="message/index">
      <Screen
        theme="dark"
        screenStyle={{
          backgroundColor: StyleSheet.darkTheme.background.page
        }}
      >
        <EmptyPlaceHolder
          type="userProductDark"
          style={{ flex: 1 }}
          theme={Theme.DARK}
        >
          准备前往新页面！
        </EmptyPlaceHolder>
      </Screen>
    </PagePerformance>
  );
}
