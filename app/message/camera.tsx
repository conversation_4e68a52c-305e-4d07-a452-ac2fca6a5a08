
import { View, StyleSheet } from 'react-native';
import { Screen } from '@Components/screen';
import { Text } from '@Components/text';
import { CommonColor } from '@/src/theme/colors/common';
import SubCategoryMessage from '@/src/bizComponents/messageScreen/SubCategoryMessage';
import { InboxTab } from '@step.ai/proto-gen/raccoon/inbox/inbox_pb';
export default function CameraMessagesScreen() {
  return (
    <SubCategoryMessage title="做同款" type={InboxTab.InboxTabMakeCopy} />
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: CommonColor.white,
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  }
});
