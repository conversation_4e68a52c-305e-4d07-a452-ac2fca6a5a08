import { View } from 'react-native';
import { Text } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { CommonColor } from '@/src/theme/colors/common';
import { EmptyPlaceHolder } from '@Components/Empty';
import { Screen } from '@Components/screen';
import { StyleSheet } from '@Utils/StyleSheet';
import { useParams } from '../../src/hooks/useParams';

export default function ExptyPage() {
  const { type, text } = useParams();
  return (
    <PagePerformance pathname="empty-page/index">
      <Screen
        title=""
        theme="dark"
        screenStyle={{
          backgroundColor: StyleSheet.darkTheme.background.page
        }}
      >
        <View style={{ width: '100%', height: '100%' }}>
          <View style={{ zIndex: 99 }}>
            <EmptyPlaceHolder
              type={type === 'user' ? 'otherCancelDark' : 'recommendDark'}
            >
              {type === 'user'
                ? '来晚了，用户注销了'
                : text || '来晚了，作品消失啦'}
            </EmptyPlaceHolder>
          </View>
        </View>
      </Screen>
    </PagePerformance>
  );
}
