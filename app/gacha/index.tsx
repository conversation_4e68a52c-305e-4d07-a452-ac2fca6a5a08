import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { SvgUri } from 'react-native-svg';
import { SCREEN_WIDTH } from '@/src/bizComponents/nestedScrollView';
import { SIGN_PART_RESOURCES } from '@/src/bizComponents/welfare/resource';
import { Icon } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { Image, ImageStyle } from '@/src/components/image';
import { showCommonShare } from '@/src/components/share';
import { shareCompPresets } from '@/src/components/share/channelConfig';
import { ShareCompPreset } from '@/src/components/share/typings';
import { WELFARE_RULE_URL } from '@/src/constants';
import { usePersistFn } from '@/src/hooks';
import { useWelfareStore } from '@/src/store/welfare';
import { darkTheme, typography } from '@/src/theme';
import { Theme } from '@/src/theme/colors/type';
import {
  $USE_FONT,
  $flex,
  $flexCenter,
  $flexHCenter
} from '@/src/theme/variable';
import { ShareInfo } from '@/src/types';
import { dp2px, isIos } from '@/src/utils';
import { WaterMarkType } from '@/src/utils/getWaterMark';
import { reportExpo } from '@/src/utils/report';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { Screen, ScreenProps } from '@Components/screen';
import { useShallow } from 'zustand/react/shallow';
// import { Text } from '@Components/text';
import { GachaRobox } from './gachaRobox';
import { PoolBank } from './poolBank';
import { EAnimateByReward, RewardAnimation } from './rewardAnimation';

export default function Gacha() {
  const getDetailShareInfo: () => ShareInfo = useMemoizedFn(() => {
    return {
      title: '快来抽奖',
      description: '赢取 9999 电池',
      url: 'https://m.lipuhome.com/',
      images: [''],
      imageIndex: 0,
      reportParams: {
        module: 'welfare'
      }
    };
  });

  const { gachaPlayStatus } = useWelfareStore(
    useShallow(s => ({
      gachaPlayStatus: s.gachaPlayStatus
    }))
  );

  const shareGacha = usePersistFn(() => {
    // TODO：唤起分享
    showCommonShare({
      getShareInfo: getDetailShareInfo,
      theme: Theme.DARK,
      waterMarkType: WaterMarkType.NO_WMK,
      compConfigs: [shareCompPresets[ShareCompPreset.WELFARE_GACHA]]
    });
  });

  const enterGachaRule = usePersistFn(() => {
    reportExpo('welfare_lottery_rule');
    const screenParams = JSON.stringify({
      screenStyle: {
        backgroundColor: '#16161A'
      },
      backButton: false,
      theme: Theme.DARK
    } as Partial<ScreenProps>);
    router.navigate({
      pathname: '/webview',
      params: {
        url: WELFARE_RULE_URL,
        darkThemeStatus: 'turnon',
        title: '抽奖规则',
        screenParams: screenParams
      }
    });
  });

  const maxBattery = 9999;

  const gachaRef = useRef<ScrollView>(null);

  useEffect(() => {
    reportExpo(
      'welfare_lottery',
      {
        module: 'welfare'
      },
      true
    );
  }, []);

  return (
    <PagePerformance pathname="welfare/index">
      <View
        style={{
          flex: 1,
          backgroundColor: '#381e07'
        }}
      >
        <Image
          source={SIGN_PART_RESOURCES['GACHA_TOP_BG']}
          tosSize="size1"
          style={$topBg}
          contentFit="fill"
        />
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          ref={gachaRef}
        >
          <Screen
            backButton={false}
            headerLeft={() => (
              <TouchableOpacity onPress={safeGoBack}>
                <Icon
                  icon="back"
                  size={24}
                  style={{
                    tintColor: '#fff'
                  }}
                />
              </TouchableOpacity>
            )}
            headerStyle={{
              justifyContent: 'space-between'
              // zIndex: -1,
              // position: 'absolute'
            }}
            style={{ flex: 1 }}
            safeAreaEdges={['top']}
            theme={Theme.DARK}
          >
            <View style={[$flexCenter, { justifyContent: 'flex-end' }]}>
              <View style={$flexHCenter}>
                <Text style={$mainTitle}>{'抽奖赢 '}</Text>
                <View>
                  <Text style={[$mainTitle, $bold]}>{maxBattery}</Text>
                  <SvgUri
                    uri={SIGN_PART_RESOURCES['SVG_GACHA_MAX']}
                    width={34}
                    height={17}
                    style={{
                      position: 'absolute',
                      top: -17,
                      right: -34
                    }}
                  />
                </View>
                <Text style={$mainTitle}>{' 狸电池'} </Text>
              </View>
              <View>
                <Text style={$subTitle}>更有限量实物周边</Text>
                <Image
                  source={SIGN_PART_RESOURCES['GACHA_TITLE_BG']}
                  style={{
                    width: 271,
                    height: dp2px(34),
                    position: 'absolute',
                    bottom: dp2px(-4),
                    zIndex: -1
                  }}
                  contentFit="contain"
                  tosSize="size1"
                />
              </View>
            </View>
            <GachaRobox />
            <PoolBank />

            <TouchableOpacity
              style={[$ruleStiff, $flexCenter]}
              onPress={enterGachaRule}
            >
              <Text style={$ruleStiffText}>抽奖规则</Text>
            </TouchableOpacity>
          </Screen>
        </ScrollView>

        <Image
          source={SIGN_PART_RESOURCES['GACHA_BOTTOM_BG']}
          tosSize="size1"
          style={$bottomBg}
          contentFit="fill"
        />

        {gachaPlayStatus ? (
          <RewardAnimation type={EAnimateByReward.GACHA} />
        ) : null}
      </View>
    </PagePerformance>
  );
}

const $mainTitle: TextStyle = $USE_FONT(
  darkTheme.text.solid,
  typography.fonts.feed,
  32,
  'normal',
  '400',
  48
);

const $bottomBg: ImageStyle = {
  position: 'absolute',
  zIndex: -1,
  width: SCREEN_WIDTH,
  height: 515,
  bottom: 0
};

const $subTitle: TextStyle = $USE_FONT(
  darkTheme.text.solid,
  typography.fonts.feed,
  24,
  'normal',
  '400',
  34
);

const $bold: TextStyle = {
  color: '#FF642B',
  fontStyle: isIos ? 'italic' : 'normal',
  fontSize: 48
};

const $subBold: TextStyle = {
  color: '#FF642B',
  fontStyle: isIos ? 'italic' : 'normal',
  fontSize: 24
};

const $ruleStiff: ViewStyle = {
  position: 'absolute',
  right: 0,
  top: 51,
  backgroundColor: '#FFC95B',
  borderTopLeftRadius: 6,
  borderBottomLeftRadius: 6,
  height: 64,
  paddingRight: 0,
  paddingLeft: 8
};

const $ruleStiffText: TextStyle = {
  width: dp2px(18),
  fontFamily: typography.fonts.feed,
  fontWeight: '400',
  fontSize: 13,
  lineHeight: 13
};

const $topBg: ImageStyle = {
  position: 'absolute',
  zIndex: -1,
  width: SCREEN_WIDTH,
  height: 758
};
