import dayjs from 'dayjs';
import { router } from 'expo-router';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import {
  FlatList,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { SvgUri } from 'react-native-svg';
import { SCREEN_WIDTH } from '@/src/bizComponents/nestedScrollView';
import { ActProgress } from '@/src/bizComponents/welfare/actProgress';
import { SIGN_PART_RESOURCES } from '@/src/bizComponents/welfare/resource';
import { Image, ImageStyle } from '@/src/components/image';
import { useWelfareStore } from '@/src/store/welfare';
import { darkTheme, typography } from '@/src/theme';
import {
  $USE_FONT,
  $flexCenter,
  $flexHBetween,
  $flexHCenter
} from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { DrawCheckInTargetType } from '@/proto-registry/src/web/raccoon/bonus/common_pb';
import { useShallow } from 'zustand/react/shallow';

export const PoolBank = memo(() => {
  const { rewardPool, gachaCount } = useWelfareStore(
    useShallow(s => ({
      rewardPool: s.rewardPool,
      gachaCount: s.gachaCount
    }))
  );

  const { gachaTaskInfo } = useWelfareStore(
    useShallow(s => ({
      gachaTaskInfo: s.gachaTaskInfo
    }))
  );

  const lotterys = useMemo(() => {
    return gachaTaskInfo.drawTasks || [];
  }, [gachaTaskInfo]);

  const pools = useMemo(() => rewardPool?.rewards, [rewardPool]);

  return (
    <View
      style={{
        zIndex: -1
      }}
    >
      <Image
        source={SIGN_PART_RESOURCES['GACHA_BOTTOM_BG']}
        tosSize="size1"
        style={$bottomBg}
        contentFit="fill"
      />
      <View
        style={[
          {
            paddingHorizontal: 16
          },
          $flexHBetween
        ]}
      >
        <SvgUri
          uri={SIGN_PART_RESOURCES['SVG_GACHA_GIFT_TEXT']}
          width={44}
          height={18}
        />
        <TouchableOpacity
          style={$flexHCenter}
          onPress={() => {
            //  TODO：跳转
            router.navigate({
              pathname: '/welfare/reward-history'
            });
          }}
        >
          <Text
            style={[
              $recordText,
              {
                marginRight: 4,
                opacity: 0.6
              }
            ]}
          >
            奖励记录
          </Text>
          <SvgUri
            uri={SIGN_PART_RESOURCES['SVG_GACHA_ARROW']}
            width={10}
            height={10}
          />
        </TouchableOpacity>
      </View>
      <View
        style={{
          paddingHorizontal: 16,
          flexDirection: 'row',
          gap: 7
        }}
      >
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          data={pools}
          contentContainerStyle={{
            gap: 8
          }}
          renderItem={({ item, index }) => {
            return (
              <View
                style={[
                  $flexCenter,
                  {
                    width: 85,
                    height: 101,
                    marginTop: 20
                  }
                ]}
                key={index}
              >
                <Image
                  source={item?.imageUrl}
                  tosSize="size4"
                  style={{
                    width: 40,
                    height: 40,
                    marginBottom: 6
                  }}
                />
                <Text
                  style={[
                    $giftAmount,
                    {
                      opacity: 0.9
                    }
                  ]}
                >{`×${item?.count}`}</Text>
                <Image
                  source={SIGN_PART_RESOURCES['GACHA_CARD_BG']}
                  tosSize="size4"
                  style={{
                    position: 'absolute',
                    width: 85,
                    height: 101,
                    zIndex: -1
                  }}
                />
              </View>
            );
          }}
        />
      </View>
      <View style={[$taskContainer, $flexCenter]}>
        <Image
          source={SIGN_PART_RESOURCES['GACHA_TASK_BG']}
          tosSize="size1"
          style={$taskBg}
          contentFit="fill"
        />
        <View
          style={[
            $flexHBetween,
            {
              width: '100%',
              paddingHorizontal: 16
            }
          ]}
        >
          <Text style={$tipText}>签到抽奖赢好礼</Text>
          <Text
            style={[
              $dayText,
              {
                opacity: 0.5
              }
            ]}
          >
            每月1日0点更新
          </Text>
        </View>

        <View
          style={[
            $flexHBetween,
            {
              width: '100%',
              paddingHorizontal: 16,
              marginTop: 12
            }
          ]}
        >
          <FlatList
            horizontal
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            data={lotterys}
            contentContainerStyle={{
              gap: 8
            }}
            renderItem={({ item, index }) => {
              const hasAchieved =
                item?.targets[0].targetCount === item?.processes[0]?.count;
              const isInit = item?.processes[0]?.count === 0;
              const isExpired =
                item?.targets[0]?.targetType ===
                  DrawCheckInTargetType.FIXED_DATE &&
                dayjs().get('date') > item?.targets[0]?.fixedDate;
              return (
                <View key={index} style={{ alignItems: 'center' }}>
                  <View style={[$lotteryBox, $flexHCenter]}>
                    <Image
                      source={item.targets[0]?.reward?.imageUrl}
                      tosSize="size6"
                      style={{
                        width: 42,
                        height: 42
                      }}
                      contentFit="contain"
                    />
                    <View
                      style={{
                        marginLeft: 4
                      }}
                    >
                      <Text
                        style={[
                          $num,
                          {
                            opacity: 0.9,
                            marginRight: 2,
                            maxWidth: 50
                          }
                        ]}
                        numberOfLines={1}
                      >{`x${item.targets[0]?.reward?.count}`}</Text>
                    </View>
                  </View>
                  <Text
                    style={[
                      $lotteryNeedText,
                      {
                        textAlign: 'center',
                        marginVertical: 8
                      }
                    ]}
                  >
                    {item.name}
                  </Text>
                  <View>
                    {!hasAchieved && !isInit ? (
                      <ActProgress
                        current={item?.processes[0]?.count}
                        total={item?.targets[0]?.targetCount}
                      />
                    ) : (
                      <View
                        style={[
                          $progressContainer,
                          $flexCenter,
                          {
                            opacity: isInit ? 1 : 0.5
                          }
                        ]}
                      >
                        <Text
                          style={[
                            $progressText,
                            {
                              color:
                                isInit && !isExpired ? '#5AC8EA' : '#5A7F9E'
                            }
                          ]}
                        >
                          {isInit
                            ? isExpired
                              ? '已过期'
                              : '未完成'
                            : '已完成'}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              );
            }}
          />
        </View>
      </View>
    </View>
  );
});

const $bottomBg: ImageStyle = {
  position: 'absolute',
  zIndex: -1,
  width: SCREEN_WIDTH,
  height: 515,
  top: -120
};

const $recordText: TextStyle = $USE_FONT(
  '#FFCB92',
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  isIos ? '500' : 'bold',
  undefined
);

const $tipText: TextStyle = $USE_FONT(
  '#EAB984',
  typography.fonts.pingfangSC.normal,
  16,
  'normal',
  isIos ? '600' : 'bold',
  18
);

const $dayText: TextStyle = $USE_FONT(
  darkTheme.text.primary,
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  isIos ? '600' : 'bold',
  16
);

const $giftAmount: TextStyle = $USE_FONT(
  '#FA853A',
  undefined,
  16,
  'italic',
  isIos ? '700' : 'bold',
  18
);

const $taskBg: ImageStyle = {
  width: SCREEN_WIDTH - 16 * 2,
  height: 180,
  position: 'absolute'
};

const $taskContainer: ViewStyle = {
  marginTop: 20,
  paddingHorizontal: 16,
  height: 180,
  marginBottom: 52,
  paddingVertical: 21.5,
  justifyContent: 'flex-start'
};

const $num: TextStyle = $USE_FONT(
  '#5ED9FF',
  undefined,
  16,
  isIos ? 'italic' : 'normal',
  isIos ? '900' : 'bold',
  18
);

const $progressText: TextStyle = $USE_FONT(
  '#5A7F9E',
  typography.fonts.pingfangSC.normal,
  10,
  'normal',
  isIos ? '600' : 'bold',
  undefined
);

const $progressContainer: ViewStyle = {
  backgroundColor: '#80E1FF14',
  width: 74,
  height: 16,
  borderRadius: 14,
  opacity: 0.5
};

const $lotteryBox: ViewStyle = {
  // width: 97,
  height: 58,
  backgroundColor: '#FFFFFF1A',
  borderColor: '#FFFFFF0F',
  borderWidth: 1,
  borderRadius: 12,
  justifyContent: 'center',
  paddingHorizontal: 12,
  paddingVertical: 8
};

const $lotteryNeedText: TextStyle = $USE_FONT(
  darkTheme.text.primary,
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  isIos ? '500' : 'bold',
  18
);
