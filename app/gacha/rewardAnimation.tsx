import Animated<PERSON>ottieView from 'lottie-react-native';
import { memo, useEffect, useMemo, useState } from 'react';
import { TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSequence,
  withTiming
} from 'react-native-reanimated';
import { SvgUri } from 'react-native-svg';
import {
  SCREEN_HEIGHT,
  SCREEN_WIDTH
} from '@/src/bizComponents/nestedScrollView';
import { SIGN_PART_RESOURCES } from '@/src/bizComponents/welfare/resource';
import { usePersistFn } from '@/src/hooks';
import { useWelfareStore } from '@/src/store/welfare';
import { darkTheme, typography } from '@/src/theme';
import { $USE_FONT, $absoluteFull, $flexCenter } from '@/src/theme/variable';
import { RewardType } from '@/src/types';
import { isIos } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { Image } from '@Components/image';
import { Text } from '@Components/text';
import FLOWER_LOTTIE from '@Assets/lottie/goods/flowers.json';
import { useShallow } from 'zustand/react/shallow';

export type TRewardDetail = {
  url: string;
  count: number;
  type: RewardType;
};

export enum EAnimateByReward {
  TASK = 'task',
  GACHA = 'gacha'
}

export const RewardAnimation = memo(({ type }: { type: EAnimateByReward }) => {
  const $modalOpacity = useSharedValue(0);
  const $modalScale = useSharedValue(1.25);
  const $contentScale = useSharedValue(0.75);
  const $ribbonScale = useSharedValue(1.3);

  const {
    updateGachaPlayStatus,
    gachaPlayStatus,
    rewardDetail,
    syncRewardDetail
  } = useWelfareStore(
    useShallow(s => ({
      updateGachaPlayStatus: s.updateGachaPlayStatus,
      gachaPlayStatus: s.gachaPlayStatus,
      rewardDetail: s.rewardDetail,
      syncRewardDetail: s.syncRewardDetail
    }))
  );

  const [canBeClick, setCanBeClick] = useState(false);
  const [flowerPlay, setFlowerPlay] = useState(false);

  useEffect(() => {
    return () => {
      // 重置
      syncRewardDetail({});
      updateGachaPlayStatus(false);
    };
  }, []);

  useEffect(() => {
    if (type === EAnimateByReward.GACHA) {
      reportExpo('welfare_lottery_window', { module: 'welfare', type }, true);
    }
  }, [type]);

  const $modalAnimate = useAnimatedStyle(() => ({
    opacity: $modalOpacity.value,
    transform: [
      {
        scale: $modalScale.value
      }
    ]
  }));

  const $contentAnimate = useAnimatedStyle(() => ({
    opacity: $modalOpacity.value,
    transform: [
      {
        scale: $contentScale.value
      }
    ]
  }));

  const $ribbonAnimate = useAnimatedStyle(() => ({
    opacity: $modalOpacity.value,
    transform: [
      {
        scale: $ribbonScale.value
      }
    ]
  }));

  const closeModal = usePersistFn(() => {
    reportClick('welfare_lottery_window', { module: 'welfare' });
    updateGachaPlayStatus(false);
    $modalScale.value = withTiming(1.25, {
      duration: 500
    });
    $contentScale.value = withTiming(0.75, {
      duration: 500
    });
    $ribbonScale.value = withTiming(1.3, {
      duration: 500
    });
    $modalOpacity.value = 0;
    setCanBeClick(false);
  });

  const delayMs = useMemo(
    () => (type === EAnimateByReward.TASK ? 0 : 1500),
    [type]
  );

  const delayEaseOut = useMemo(
    () =>
      withDelay(
        delayMs,
        withTiming(1, {
          duration: 500
        })
      ),
    [delayMs]
  );

  useEffect(() => {
    if (gachaPlayStatus) {
      $modalScale.value = delayEaseOut;
      $contentScale.value = delayEaseOut;
      $ribbonScale.value = delayEaseOut;
      $modalOpacity.value = withDelay(
        delayMs,
        withTiming(
          1,
          {
            duration: 500
          },
          () => {
            runOnJS(setCanBeClick)(true);
          }
        )
      );
      setFlowerPlay(true);
    } else {
      $modalOpacity.value = 0;
    }
  }, [gachaPlayStatus, delayMs]);

  return (
    <Animated.View
      style={[
        $absoluteFull,
        $modalAnimate,
        {
          pointerEvents: canBeClick ? 'auto' : 'none'
        }
      ]}
    >
      <View style={$mask} />
      <Image
        source={SIGN_PART_RESOURCES['GACHA_REWARD_TRANSITION']}
        tosSize="size1"
        contentFit="contain"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          top: -32
        }}
      />

      <AnimatedLottieView
        style={$flowerLottie}
        source={FLOWER_LOTTIE}
        loop={false}
        autoPlay={flowerPlay}
        onAnimationFinish={() => {
          setFlowerPlay(false);
        }}
      />

      <Animated.View
        style={[
          $absoluteFull,
          $flexCenter,
          {
            top: -300,
            left: 16
          },
          $ribbonAnimate
        ]}
      >
        <Image
          source={SIGN_PART_RESOURCES['GACHA_RIBBON']}
          tosSize="size1"
          style={{
            width: 287,
            height: 163
          }}
        />
      </Animated.View>

      <Animated.View
        style={[
          { width: '100%', height: '100%' },
          $contentAnimate,
          $flexCenter
        ]}
      >
        <Image
          tosSize="size1"
          style={{
            width: 167,
            height: 45,
            marginTop: 188
          }}
          source={SIGN_PART_RESOURCES['GACHA_REWARD_TITLE']}
        />
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginVertical: 48
          }}
        >
          <Image
            tosSize="size4"
            style={{
              width: 68,
              height: 68,
              marginRight: 9
            }}
            source={rewardDetail?.url}
          />
          <Text
            style={[
              $rewardText,
              {
                verticalAlign: 'bottom'
              }
            ]}
          >{`x${rewardDetail?.count}`}</Text>
        </View>
        <TouchableOpacity
          onPress={closeModal}
          activeOpacity={0.75}
          style={{
            marginTop: 96
          }}
        >
          <Image
            tosSize="size1"
            style={{
              width: 189,
              height: 54
            }}
            source={SIGN_PART_RESOURCES['GACHA_REWARD_BUTTON']}
          />
        </TouchableOpacity>

        <SvgUri
          uri={SIGN_PART_RESOURCES['SVG_MODAL_CLOSE']}
          width={30}
          height={30}
          style={{
            marginTop: 104
          }}
          onPress={closeModal}
        />
      </Animated.View>
    </Animated.View>
  );
});

const $flowerLottie: ViewStyle = {
  position: 'absolute',
  bottom: 0,
  left: 0,
  height: '100%'
};

const $rewardText: TextStyle = $USE_FONT(
  darkTheme.text.primary,
  typography.fonts.Barlow.BoldItalic,
  42,
  isIos ? 'italic' : 'normal',
  isIos ? '600' : 'bold',
  50
);

const $mask: ViewStyle = {
  backgroundColor: '#000000CC',
  position: 'absolute',
  width: SCREEN_WIDTH,
  height: SCREEN_HEIGHT,
  top: 0,
  left: 0
};
