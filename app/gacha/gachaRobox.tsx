import { useThrottleFn } from 'ahooks';
import { memo, useEffect, useRef } from 'react';
import {
  ImageStyle,
  Pressable,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming
} from 'react-native-reanimated';
import { doDraw } from '@/src/api/welfare';
import { SCREEN_WIDTH } from '@/src/bizComponents/nestedScrollView';
import { SIGN_PART_RESOURCES } from '@/src/bizComponents/welfare/resource';
import { showToast } from '@/src/components';
import { usePersistFn } from '@/src/hooks';
import { useWelfareStore } from '@/src/store/welfare';
import { typography } from '@/src/theme';
import { $USE_FONT } from '@/src/theme/variable';
import { dp2px, isIos } from '@/src/utils';
import { checkNetWork } from '@/src/utils/device/network';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { reportClick, reportExpo } from '@/src/utils/report';
import { Image } from '@Components/image';
import { Text } from '@Components/text';
import GACHA_VIDEO from '@Assets/mp4/alpha/gacha.mp4';
import NetInfo from '@react-native-community/netinfo';
import { useIsFocused } from '@react-navigation/native';
import TransparentVideo from '@step.ai/react-native-transparent-video';
import { useShallow } from 'zustand/react/shallow';

const roboxRatio = 505 / 356;

export const GachaRobox = memo(() => {
  const gachaRef = useRef<TransparentVideo>(null);
  const beFocus = useIsFocused();

  useEffect(() => {
    console.log(gachaRef.current, beFocus, 'looookkk');
    if (gachaRef.current && beFocus) {
      gachaRef.current?.seek(0);
    }
  }, [beFocus]);

  const {
    updateGachaPlayStatus,
    gachaCount,
    initBackPack,
    syncRewardDetail,
    initGacha
  } = useWelfareStore(
    useShallow(s => ({
      updateGachaPlayStatus: s.updateGachaPlayStatus,
      gachaCount: s.gachaCount,
      initBackPack: s.initBackPack,
      syncRewardDetail: s.syncRewardDetail,
      initGacha: s.initGacha
    }))
  );

  const ticketMinus = usePersistFn(async () => {
    reportClick('welfare_lottery_button');
    if (gachaCount <= 0) {
      showToast('抽奖券不够啦，多签到攒券吧～');
      return;
    }
    const state = await NetInfo.fetch('');
    if (!state.isConnected) {
      showToast('小狸走丢了');
      return;
    }
    const res = await doDraw();

    if (res.reward) {
      console.log(res.reward, 'res.reward===');
      syncRewardDetail({
        url: res.reward?.imageUrl,
        type: res.reward?.rewardType,
        count: res.reward?.count
      });
      initBackPack();
      initGacha();
      gachaRef.current?.play();
      updateGachaPlayStatus(true);
      $placeHolderGachaOpacity.value = withDelay(
        1500,
        withTiming(1, {
          duration: 500
        })
      );
    } else {
      showToast('抽奖失败');
    }
  });

  const $placeHolderGachaOpacity = useSharedValue(0);
  const $placeHolderGachaAnimate = useAnimatedStyle(() => ({
    opacity: $placeHolderGachaOpacity.value
  }));

  const { run: debounceDoGacha } = useThrottleFn(ticketMinus, {
    wait: 2500
  });

  return (
    <View
      style={{
        height: (SCREEN_WIDTH - 16) * roboxRatio,
        paddingHorizontal: 16,
        alignItems: 'center'
      }}
    >
      {/* 扭蛋机 */}
      {beFocus ? (
        <TransparentVideo
          loop={false}
          isAutoPlay={false}
          ref={gachaRef}
          source={GACHA_VIDEO}
          onEnd={() => {
            $placeHolderGachaOpacity.value = withTiming(0, {
              duration: 250
            });
            gachaRef.current?.seek(0);
          }}
          style={[
            $gachaVideo,
            {
              opacity: 1
            }
          ]}
        />
      ) : null}

      {/* 扭蛋机占位 1.5s 出 */}
      <Animated.View style={[$placeHolderGachaAnimate, $staticRobox]}>
        <Image
          source={SIGN_PART_RESOURCES['GACHA_ROBOX_BG']}
          style={[{ flex: 1 }]}
          tosSize="size1"
        />
      </Animated.View>

      {/* 抽奖券占位 */}
      <View style={$ticketArea}>
        <Text style={$ticketTitle}>我的抽奖券</Text>
        <View
          style={{
            alignItems: 'center'
          }}
        >
          <Image
            source={SIGN_PART_RESOURCES['GACHA_TICKET']}
            tosSize="size10"
            style={{
              width: 30,
              height: 30,
              marginRight: 6
            }}
          />
          <Text
            style={$ticketNum}
            numberOfLines={1}
            ellipsizeMode="tail"
          >{`x${gachaCount}`}</Text>
        </View>
      </View>

      {/* 虚拟扭蛋按钮 */}
      <Pressable style={$hiddenButton} onPress={debounceDoGacha} />
    </View>
  );
});

const $gachaVideo: ViewStyle = {
  width: SCREEN_WIDTH - 16,
  height: (SCREEN_WIDTH - 16) * roboxRatio,
  opacity: 0.2
};

const $hiddenButton: ViewStyle = {
  position: 'absolute',
  width: dp2px(120),
  height: dp2px(120),
  borderRadius: dp2px(60),
  left: SCREEN_WIDTH / 2 - dp2px(60),
  bottom: dp2px(20),
  zIndex: 100
};

const $staticRobox: ImageStyle = {
  position: 'absolute',
  zIndex: 10,
  width: SCREEN_WIDTH - dp2px(40),
  height: (SCREEN_WIDTH - dp2px(40)) * roboxRatio,
  transform: [
    {
      translateY: dp2px(16)
    }
  ]
};

const $ticketArea: ViewStyle = {
  position: 'absolute',
  justifyContent: 'space-between',
  minWidth: dp2px(63),
  bottom: isIos ? dp2px(58) : dp2px(50),
  left: dp2px(54)
};

const $ticketNum: TextStyle = $USE_FONT(
  '#FF6A3B',
  typography.fonts.Barlow.ExtraBold,
  14,
  'normal',
  '800',
  18
);

const $ticketTitle: TextStyle = $USE_FONT(
  '#794324',
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  isIos ? '600' : 'bold',
  18
);
