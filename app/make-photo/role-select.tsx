import { router } from 'expo-router';
import { View } from 'react-native';
import { Screen } from '@/src/components';
import { MakePhotoEvents } from '@/src/components/makePhoto/constant';
import { RoleSelector } from '@/src/components/makePhoto/roleSelector';
import { Switch } from '@/src/components/switch';
import { PlayType, useMakePhotoStoreV2 } from '@/src/store/makePhotoV2';
import { reportClick } from '@/src/utils/report';
import { useShallow } from 'zustand/react/shallow';
import { CrefSearchArea } from './cref-searcharea';

export default function MakePhotoRoleSelect() {
  const {
    palyType,
    role1,
    role2,
    selectRole,
    setRoleType,
    setUseDouble,
    useDouble
  } = useMakePhotoStoreV2(
    useShallow(state => ({
      useDouble: state.useDouble,
      role1: state.role1,
      role2: state.role2,
      selectRole: state.selectRole,
      palyType: state.playType,
      setRoleType: state.setRoleType,
      setUseDouble: state.setUseDouble
    }))
  );
  const screenHeaderRight = () =>
    palyType === PlayType.drawing && (
      <Switch
        value={useDouble}
        onPress={() => {
          reportClick(
            useDouble
              ? MakePhotoEvents['1playerselect']
              : MakePhotoEvents['2playerselect'],
            { module: 'create' }
          );
          setUseDouble(!useDouble);
        }}
        trackColor={{ true: '#FF6A3B', false: '#FFFFFF33' }}
        trackText={{ false: '单人', true: '双人' }}
      />
    );

  return (
    <Screen
      theme="dark"
      safeAreaEdges={['top']}
      onBack={onBack}
      headerRight={screenHeaderRight}
      screenStyle={{ backgroundColor: '#16161A' }}
    >
      <View
        style={{
          position: 'absolute',
          top: -44,
          left: 42,
          zIndex: 1000
        }}
      >
        <CrefSearchArea />
      </View>
      <RoleSelector onBack={selectConfirmBack} />
    </Screen>
  );
  function selectConfirmBack() {
    router.back();
  }
  function onBack() {
    if (!role2) {
      const tempRole1 = role1;
      setRoleType(0);
      setUseDouble(false);
      if (tempRole1) {
        selectRole(tempRole1);
      }
    }
    router.back();
  }
}
