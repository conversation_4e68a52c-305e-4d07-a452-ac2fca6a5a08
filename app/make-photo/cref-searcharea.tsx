import { useMemoizedFn } from 'ahooks';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { memo } from 'react';
import {
  GestureResponderEvent,
  KeyboardAvoidingView,
  StyleProp,
  ViewStyle
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  withTiming
} from 'react-native-reanimated';
import SearchBar from '@/src/bizComponents/search/search-bar';
import { useSearchStore } from '@/src/store/search';
import { $CREF_SEARCH_COLORS, $flexHBetween } from '@/src/theme/variable';
import { getScreenSize, isIos } from '@/src/utils';
import { clickEffect } from '@/src/utils/clickeffect';
import { reportClick } from '@/src/utils/report';
import { useShallow } from 'zustand/react/shallow';

export const CrefSearchArea = memo(
  ({ rightWidth = 145 }: { rightWidth?: number }) => {
    const onFocus = useMemoizedFn(
      (e: GestureResponderEvent, keywords: string) => {
        clickEffect();
        reportClick('search_button', { module: 'role_search' });
        useSearchStore.getState().updateLastPlaceText(keywords);

        router.navigate({
          pathname: '/cref-search'
        });
      }
    );

    const { placeTexts } = useSearchStore(
      useShallow(state => ({
        placeTexts: state.initPlaceTexts
      }))
    );

    const windowWidth = getScreenSize('width');

    const $searchBarAnime = useAnimatedStyle(() => ({
      width: withTiming(windowWidth - 145, { duration: 250 }),
      height: withTiming(36, { duration: 250 })
    }));

    return (
      <Animated.View
        id="cref-search-area"
        style={[
          $searchArea,
          $flexHBetween,
          {
            justifyContent: 'flex-start',
            flexDirection: 'row'
          }
        ]}
      >
        <KeyboardAvoidingView behavior={isIos ? 'height' : undefined}>
          <SearchBar
            hiddenRight
            hiddenBack
            autoFocus={false}
            onParentFocus={onFocus}
            canbeEdit={false}
            verticalSlide={false}
            clearIcon="cref_search_clear"
            searchIcon="cref_search"
            placeHolderContent="来搜索更多角色吧～"
            searchColors={{
              selectionColor: $CREF_SEARCH_COLORS.white_40,
              placeholderTextColor: $CREF_SEARCH_COLORS.white_40,
              cursorColor: $CREF_SEARCH_COLORS.white,
              searchTextColor: $CREF_SEARCH_COLORS.white
            }}
            overrideSearchBar
            searchbarStyle={$searchBarAnime}
            $customStyle={{
              width: windowWidth - rightWidth,
              alignContent: 'center',
              backgroundColor: '#232329',
              marginLeft: -8,
              transform: [
                {
                  translateY: -4
                }
              ]
            }}
          />
        </KeyboardAvoidingView>
      </Animated.View>
    );
  }
);

const $searchArea: ViewStyle = {
  height: 30,
  marginTop: 8,
  width: '100%'
};
