import { useEffect, useState } from 'react';
import { usePerformanceStore } from '@/src/store/performance';

export const usePerformance = () => {
  const [lcp, setLcp] = useState(0);
  useEffect(() => {
    async function calLcpPerformance() {
      Promise.all(usePerformanceStore.getState().initMakePhotoLcp()).finally(
        () => {
          setLcp(Date.now());
        }
      );
    }
    calLcpPerformance();
  }, []);

  return {
    lcp
  };
};
