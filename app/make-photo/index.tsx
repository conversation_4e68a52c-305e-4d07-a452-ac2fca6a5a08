import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { BackHandler, TextStyle, View, ViewStyle } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  ABTestEvent,
  ABTestResource,
  FirstTriggerType,
  showNoviceGuideByServer
} from '@/src/api/abTest';
import { AlbumSheet } from '@/src/components/album';
import { Button } from '@/src/components/button';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { BottomPannel } from '@/src/components/makePhoto/bottomPannel';
import { EmojiPannel } from '@/src/components/makePhoto/emojiPannel';
import Clip from '@/src/components/makePhoto/pannel/Clip';
import ThinkingBtn from '@/src/components/makePhoto/pannel/ThinkingBtn';
import PromptPanel from '@/src/components/makePhoto/promptPannel';
import { RoleSelector } from '@/src/components/makePhoto/roleSelector';
import { uploadGameUser } from '@/src/components/publishEntry/entryBadge';
import { Switch } from '@/src/components/switch';
import { MAKE_PHOTO_PORTAL } from '@/src/constants';
import { useAndroidBackHandler, usePersistFn } from '@/src/hooks';
import { EditPageState, useMakePhotoEdit } from '@/src/store/makePhotoEdit';
import {
  PageState,
  PlayType,
  useMakePhotoStoreV2
} from '@/src/store/makePhotoV2';
import { usePublishStore } from '@/src/store/publish';
import { useResourceStore } from '@/src/store/resource';
import { useRoleStore } from '@/src/store/role';
import { darkTheme, fullStyle, typography } from '@/src/theme';
import { $USE_FONT, $Z_INDEXES } from '@/src/theme/variable';
import { GameType } from '@/src/types';
import {
  addCommonReportParams,
  getPageName,
  reportClick,
  reportExpo,
  reportMakePhotoTrack
} from '@/src/utils/report';
import { setTimeoutWorklet } from '@/src/utils/worklet';
import { Image } from '@Components/image';
import { ElementSuffix, MakePhotoEvents } from '@Components/makePhoto/constant';
import { LoadingView, LoadingViewRef } from '@Components/makePhoto/loadingView';
import { Pannel } from '@Components/makePhoto/pannel';
import { PreviewView } from '@Components/makePhoto/previewView';
import { showConfirm } from '@Components/popup/confirmModalGlobal/Confirm';
import { Screen } from '@Components/screen';
import { StyleSheet } from '@Utils/StyleSheet';
import { useParams } from '../../src/hooks/useParams';
import { PortalHost } from '@gorhom/portal';
import {
  AVPlaybackStatus,
  AVPlaybackStatusSuccess,
  ResizeMode,
  Video
} from '@step.ai/expo-av';
import { useShallow } from 'zustand/react/shallow';
import { CrefSearchArea } from './cref-searcharea';
import { usePerformance } from './usePerformance';

const BG_IMG = require('@Assets/makephoto/role_select_bg.png');

function MakePhoto() {
  const {
    pageState,
    palyType,
    role1,
    role2,
    currentSlot,
    makePhotoId,
    setUseDouble,
    useDouble,
    inputVisible
  } = useMakePhotoStoreV2(
    useShallow(state => ({
      currentSlot: state.currentSlot,
      pageState: state.pageState,
      palyType: state.playType,
      role1: state.role1,
      role2: state.role2,
      makePhotoId: state.makePhotoId,
      useDouble: state.useDouble,
      setUseDouble: state.setUseDouble,
      style: state.style,
      setStyle: state.setStyle,
      inputVisible: state.inputVisible
    }))
  );
  const { lcp } = usePerformance();
  const [hasOpenKeyboard, setOpenKeyboard] = useState(false);
  const [showAlbum, setShowAlbum] = useState(false);
  const { ip, role, keyword, playScene } = useParams();
  const [videoShow, setVideoShow] = useState(false);
  const videoRef = useRef<Video>(null);

  const { roles, findRole } = useRoleStore(
    useShallow(state => ({
      roles: state.roles,
      findRole: state.findRole
    }))
  );

  const guideVideoResource = useMemo(() => {
    return useResourceStore
      .getState()
      .getVideoResource(ABTestResource[ABTestEvent.ANIMATION]);
  }, []);

  useEffect(() => {
    if (roles && ip) {
      useMakePhotoStoreV2.getState().setIp(Number(ip));

      if (role) {
        findRole({ id: role as string }).then(roleInfo => {
          useMakePhotoStoreV2.getState().changePageState(PageState.diy);
          if (roleInfo) {
            useMakePhotoStoreV2.getState().selectRole(roleInfo);
          }
        });
      } else {
        useMakePhotoStoreV2.getState().changePageState(PageState.diy);
      }
    }
  }, [ip, roles, role]);

  useEffect(() => {
    useMakePhotoStoreV2.getState().setState({
      keyword: String(keyword || '')
    });
  }, [keyword]);

  useEffect(() => {
    if (playScene === PlayType.emoji) {
      useMakePhotoStoreV2.getState().setState({
        playType: PlayType.emoji
      });
    }
  }, [playScene]);

  const handleBack = () => {
    const currentPage = getPageName();
    if (currentPage !== 'create') return;
    onBack();
    return true;
  };
  useAndroidBackHandler(handleBack);

  useEffect(() => {
    handleNoticeGuide();
    reportExpo('create_page', {
      module: 'create',
      palyType,
      ip,
      role,
      keyword,
      playScene
    });
  }, []);

  const handleNoticeGuide = useMemoizedFn(async () => {
    showNoviceGuideByServer({
      triggerType: FirstTriggerType.DRAWING_ENTER,
      sceneId: 'newUserLanding',
      variant: 'animation',
      successCallback: () => {
        setVideoShow(true);
        reportExpo('guide', { module: 'create' });
        // 触发引导
        setTimeout(() => {
          videoRef.current?.playAsync();
        });
      }
    });
  });

  useEffect(() => {
    uploadGameUser(GameType.DRAWING);
    addCommonReportParams('makephoto', { makephoto_id: makePhotoId });
    // // 首次上报延迟100ms
    // setTimeout(() => {
    //   addCommonReportParams('makephoto', { makephoto_id: makePhotoId });
    //   reportMakePhotoTrack(
    //     MakePhotoEvents.enter_paint,
    //     pageState,
    //     ElementSuffix.page_view,
    //     {
    //       source: sourceMap[from as Source]
    //     }
    //   );
    // }, 100);
  }, []);
  useEffect(() => {
    switch (pageState) {
      case PageState.diy: {
        reportMakePhotoTrack(
          MakePhotoEvents.prompt,
          PageState.promptselect,
          ElementSuffix.page_view,
          {
            // source: sourceMap[from as Source]
          }
        );
        break;
      }
      default: {
        reportMakePhotoTrack(
          MakePhotoEvents.enter_paint,
          pageState,
          ElementSuffix.page_view,
          {
            // source: sourceMap[from as Source]
          }
        );
      }
    }
  }, [pageState]);

  const loadingViewRef = useRef<LoadingViewRef>(null);

  const title = useMemo(() => {
    return '炖图';
  }, [pageState, currentSlot]);
  useEffect(() => {
    if (inputVisible) {
      setOpenKeyboard(true);
    }
  }, [inputVisible]);

  const onBack = usePersistFn(() => {
    console.log('onBack----');
    const { pageState, changePageState } = useMakePhotoStoreV2.getState();
    const { editPageState } = useMakePhotoEdit.getState();
    if (
      pageState === PageState.preview ||
      pageState === PageState.styleselect
    ) {
      if (
        pageState === PageState.preview &&
        editPageState === EditPageState.editing
      ) {
        useMakePhotoEdit.getState().setState({
          editPageState: EditPageState.beforePublish,
          backFromText: true
        });
        useMakePhotoEdit.getState().resetGroupTexts();
        return;
      }
      if (palyType === PlayType.drawing) {
        loadingViewRef.current?.reset();
        useMakePhotoEdit.getState().reset();
        setTimeout(() => {
          changePageState(PageState.diy);
        }, 200); // 这里是为了等photoLoading状态更新
      }
    } else {
      showConfirm({
        title: '确定退出炖图吗？',
        confirmText: '确认退出',
        onConfirm: ({ close }) => {
          close();
          router.back();
        }
      });
    }
  });

  const renderContent = () => {
    if (palyType === PlayType.emoji) {
      return <EmojiPannel title={title} />;
    }

    return (
      <>
        {<Pannel title={title} />}
        <LoadingView ref={loadingViewRef} onBack={onBack} />
        {<PromptPanel />}
        {!inputVisible &&
          pageState !== PageState.effect &&
          pageState !== PageState.preview && (
            <BottomPannel hasOpenKeyboard={hasOpenKeyboard} />
          )}
        <Clip />
        {/* 炖图配词 */}
        <ThinkingBtn />
        {pageState === PageState.preview && (
          <PreviewView
            onBack={onBack}
            showAlbum={showAlbumFunc}
            resetLoading={resetLoading}
          />
        )}
      </>
    );
  };

  const handlePlaybackStatusUpdate = (status: AVPlaybackStatus) => {
    if ((status as AVPlaybackStatusSuccess).didJustFinish) {
      setVideoShow(false);
    }
  };

  return (
    <PagePerformance pathname="make-photo/index" lcp={lcp}>
      {videoShow && (
        <View style={[{ zIndex: $Z_INDEXES.z1000 }, StyleSheet.absoluteFill]}>
          <Button
            style={$skipBtn}
            textStyle={$skipTextStyle}
            onPress={() => {
              setVideoShow(false);
              reportClick('guide_skip');
            }}
          >
            跳过
          </Button>
          <Video
            ref={videoRef}
            isLooping={false}
            source={guideVideoResource}
            resizeMode={ResizeMode.STRETCH}
            style={fullStyle}
            onPlaybackStatusUpdate={handlePlaybackStatusUpdate}
            onError={() => {
              setVideoShow(false);
              if (videoRef.current) {
                videoRef.current.stopAsync();
              }
            }}
          />
        </View>
      )}
      {renderContent()}
      {showAlbum && (
        <AlbumSheet
          isVisible={showAlbum}
          onClose={() => {
            setShowAlbum(false);
          }}
          resetLoading={resetLoading}
        />
      )}
      <PortalHost name={MAKE_PHOTO_PORTAL} />
    </PagePerformance>
  );

  function resetLoading() {
    loadingViewRef.current?.reset();
  }

  function showAlbumFunc() {
    usePublishStore.getState().getAlbumPhotos(true);
    usePublishStore.getState().getHistoryPhotos(true);
    reportMakePhotoTrack(
      MakePhotoEvents.album_click,
      PageState.preview,
      ElementSuffix.album
    );
    setShowAlbum(true);
  }
}

export default memo(MakePhoto);

const st = StyleSheet.create({
  backgroundViewImg: {
    position: 'absolute',
    top: 0,
    left: -1,
    bottom: 0,
    right: -1,
    backgroundColor: darkTheme.background.page
  }
});

const $skipBtn: ViewStyle = {
  paddingHorizontal: 19,
  paddingVertical: 6,
  zIndex: $Z_INDEXES.z1000 + 1,
  position: 'absolute',
  top: 66,
  right: 12,
  backgroundColor: 'transparent',
  borderColor: 'rgba(255, 255, 255, 0.2)',
  height: 32,
  borderRadius: 30
};

const $skipTextStyle: TextStyle = $USE_FONT(
  'rgba(255, 255, 255, 0.6)',
  typography.fonts.pingfangSC.normal,
  14,
  'normal',
  '500',
  19.6
);
