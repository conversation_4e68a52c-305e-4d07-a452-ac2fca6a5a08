import { router } from 'expo-router';
import React, { useEffect } from 'react';
import { Pressable, Text, View } from 'react-native';
import { ResourceClient } from '@/src/api/config';
import { decisionColors } from '@/src/bizComponents/decision/constants';
import DecisionList from '@/src/bizComponents/decision/decisionList';
import { showToast } from '@/src/components';
import { Screen } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { GameType } from '@/src/types';
import { reportClick, reportExpo } from '@Utils/report';
import NetInfo from '@react-native-community/netinfo';

export default function DecisionEntry() {
  /**
   * 点击「征集脑洞」跳转
   */
  const handleGoToCollection = async () => {
    const state = await NetInfo.fetch();
    if (!state.isConnected) {
      showToast('网络错误，请检查您的网络连接后重试');
      return;
    }

    reportClick('create_button', { module: 'decisionlist' });

    // 如果网络正常，才进行路由跳转
    router.navigate({
      pathname: '/webview',
      params: {
        url: 'https://wvixbzgc0u7.feishu.cn/share/base/form/shrcn1v3gh7iu0usE2SZFDMhJHg',
        title: '征集脑洞'
      }
    });
  };

  useEffect(() => {
    setTimeout(() => {
      reportExpo('expo', { module: 'decisionlist' });
    });
  }, []);

  useEffect(() => {
    // 上报正在游玩，用于统计游玩人数
    ResourceClient.uploadGameUser({ gameType: GameType.INSTANCE });
  }, []);

  return (
    <PagePerformance pathname="decision/entry">
      <Screen
        theme="dark"
        screenStyle={{ backgroundColor: decisionColors.bg }}
        backgroundView={<View style={{ backgroundColor: decisionColors.bg }} />}
        safeAreaEdges={['top']}
        title="脑洞闯关"
        headerStyle={{ backgroundColor: 'transparent' }}
        /** 「征集脑洞」 */
        headerRight={() => (
          <Pressable onPress={handleGoToCollection}>
            <Text
              style={{
                fontFamily: 'PingFang SC',
                fontSize: 14,
                fontWeight: '600',
                lineHeight: 22,
                color: '#F5BE5F'
              }}
            >
              征集脑洞
            </Text>
          </Pressable>
        )}
      >
        {/* 决策列表 */}
        <DecisionList />
      </Screen>
    </PagePerformance>
  );
}
