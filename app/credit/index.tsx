import {
  useDebounce,
  useDebounceFn,
  useMemoizedFn,
  useThrottleFn
} from 'ahooks';
import { Href, useRouter } from 'expo-router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  ImageStyle,
  LayoutChangeEvent,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Pressable,
  ScrollView,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { Icon } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { CREDIT_LIMIT } from '@/src/components/credit-cas';
import RankHistory from '@/src/components/rank-history';
import DotMarquee from '@/src/components/v2/dot-marquee';
import { useScreenSize } from '@/src/hooks';
import { useParams } from '@/src/hooks/useParams';
import { SwitchName, useControlStore } from '@/src/store/control';
import { useCreditStore } from '@/src/store/credit';
import { useRoleHomeStore } from '@/src/store/roleHome';
import { darkTheme, typography } from '@/src/theme';
import { $Z_INDEXES } from '@/src/theme/variable';
import { StyleSheet, dp2px } from '@/src/utils';
import { formatTosUrl } from '@/src/utils/getTosUrl';
import { reportClick, reportDiy, reportExpo } from '@/src/utils/report';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { Image } from '@Components/image';
import {
  hideConfirm,
  showConfirm
} from '@Components/popup/confirmModalGlobal/Confirm';
import { Screen } from '@Components/screen';
import { useShallow } from 'zustand/react/shallow';

const LIGHTNING = require('@Assets/image/credit/lightning.png');
const BATTERY = require('@Assets/image/credit/battery.png');
const FULL_BATTERY = require('@Assets/image/credit/full-battery.png');
const NO_BATTERY = require('@Assets/image/credit/no-battery.png');
const HALO = require('@Assets/image/credit/halo.png');
const THRESHOLD = 30;

export default function Credit() {
  const router = useRouter();
  const screen = useScreenSize();
  const [holderIndex, setHolderIndex] = useState(0);
  const [layoutWidth, setLayoutWidth] = useState(0);
  const [autoLoop, setAutoLoop] = useState(true);
  const autoLoopTimer = useRef<NodeJS.Timeout>();
  const reportCreativeRef = useRef(false);
  const { isFromGift } = useParams();

  useEffect(() => {
    reportDiy('credit', 'all-expo');
    useCreditStore.getState().getBannerInfo();
    return () => {
      hideConfirm();
    };
  }, []);

  useEffect(() => {
    if (layoutWidth) {
      holderX.value = withTiming(-1 * layoutWidth * holderIndex, {
        duration: 250
      });
    }
  }, [layoutWidth, holderIndex]);

  const goRule = () => {
    reportClick('rule-entrance_button', { module: 'credit' });
    showConfirm({
      title: '狸电池规则',
      modalStyle: {
        width: screen.width < 375 ? dp2px(screen.width - 72) : 303
      },
      content: (
        <View
          style={{
            paddingTop: 10,
            width: '100%',
            flexDirection: 'column'
          }}
        >
          <Text style={[$ruleContent, $bold]}>1、狸电池有什么用途？</Text>
          <Text style={$ruleContent}>
            狸电池可以用来参与炖图、表情包、梗图、动态LIVE等玩法。更多功能和玩法即将上线！
          </Text>
          <Text style={[$ruleContent, $bold]}>2、如何获得狸电池？</Text>
          <Text style={$ruleContent}>
            每日登录、充电站可以获得大量狸电池，详情在狸电站-马上充电查看。
          </Text>
          <Text style={$ruleContent}>
            你的作品被玩同款、被评论、被点赞，也会获得狸电池奖励哦。
          </Text>
          <Text style={[$ruleContent, $bold]}>3、每日登录赠送规则</Text>
          <Text style={$ruleContent}>
            狸电池余额不足200时，每日登录赠送200狸电池；狸电池余额在200至400之间，每日登录赠送100狸电池；狸电池余额超过400时，则不再赠送。
          </Text>
        </View>
      ),
      confirmText: '知道了',
      cancelText: '#hiddenCancel#',
      onConfirm: ({ close }) => {
        close();
      },
      maskClose: true
    });
  };

  const { totalCredits, banner } = useCreditStore(
    useShallow(state => ({
      totalCredits: state.totalCredits,
      banner: state.bannerInfo
    }))
  );

  const effectBanner = useMemo(
    () =>
      banner?.filter(item => {
        const { landingPage, elements } = item || {};
        const { media } = (elements && elements[0]) || {};
        const { url } = media || {};
        return landingPage && url;
      }) || [],
    [banner]
  );

  useEffect(() => {
    if (effectBanner.length && !reportCreativeRef.current) {
      reportCreativeRef.current = true;
      reportExpo('banner_expo', {
        module: 'credit'
      });
    }
  }, [effectBanner]);

  const holderX = useSharedValue(0);
  const holderOffseX = useSharedValue(0);

  const $holderTransStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: holderX.value
      }
    ]
  }));

  const indexChange = useMemoizedFn((index: number) => {
    setHolderIndex(index);
  });

  const getWidth = (e: LayoutChangeEvent) => {
    setLayoutWidth(e.nativeEvent.layout.width);
  };

  const onPanGestureEnd = () => {
    if (autoLoopTimer.current) {
      clearTimeout(autoLoopTimer.current);
      autoLoopTimer.current = undefined;
    }
    autoLoopTimer.current = setTimeout(() => {
      setAutoLoop(true);
    }, 3000);
  };

  const panGesture = Gesture.Pan()
    .activeOffsetX([-10, 10])
    .failOffsetY([-10, 10])
    .onStart(e => {
      holderOffseX.value = e.translationX;
      runOnJS(setAutoLoop)(false);
    })
    .onUpdate(e => {
      holderOffseX.value = e.translationX;
    })
    .onEnd(() => {
      if (holderOffseX.value < -THRESHOLD) {
        runOnJS(indexChange)((holderIndex + 1) % effectBanner.length);
      } else if (holderOffseX.value > THRESHOLD) {
        runOnJS(indexChange)(
          holderIndex === 0 ? effectBanner.length - 1 : holderIndex - 1
        );
      }
      runOnJS(onPanGestureEnd)();
      holderOffseX.value = 0;
    });

  const renderBanner = () => {
    if (!effectBanner.length) {
      return null;
    }

    const operationView = (
      <View style={$bannerWrapper}>
        <Animated.View
          style={[$holderTransStyle, $holderWrapper]}
          onLayout={getWidth}
        >
          {effectBanner.map((item, index) => (
            <Pressable
              key={index}
              style={$banner}
              onPress={() => {
                const { landingPage, creativeId } = item || {};
                reportClick('banner_button', {
                  module: 'credit',
                  pic_order: index,
                  picid: item.elements?.[0]?.media?.url || '',
                  resourceid: creativeId
                });
                if (landingPage) {
                  router.navigate(landingPage as Href<string>);
                }
              }}
            >
              <Image
                style={$bannerImg}
                source={formatTosUrl(item.elements?.[0]?.media?.url || '', {
                  size: 'size1'
                })}
              />
            </Pressable>
          ))}
        </Animated.View>
        {effectBanner.length > 1 ? (
          <View style={$dotWrapper}>
            <DotMarquee
              autoLoop={autoLoop}
              length={effectBanner.length}
              defaultColor="rgba(255, 255, 255, 0.3)"
              defaultDotWidth={5}
              defaultdotHeight={5}
              defaultdotRadius={100}
              highlightColor="rgba(255, 255, 255, 0.8)"
              highlightDotWidth={5}
              highlightDotHeight={5}
              highlightDotRadius={100}
              activeIndex={holderIndex}
              handleIndexChange={indexChange}
            />
          </View>
        ) : null}
      </View>
    );

    return (
      <>
        {effectBanner.length === 1 ? (
          <GestureDetector gesture={Gesture.Native()}>
            {operationView}
          </GestureDetector>
        ) : (
          <GestureDetector gesture={panGesture}>
            {operationView}
          </GestureDetector>
        )}
      </>
    );
  };

  const { checkIsOpen } = useControlStore(
    useShallow(state => ({
      checkIsOpen: state.checkIsOpen
    }))
  );

  const taskSwitchOpen = useMemo(
    () => checkIsOpen(SwitchName.ENABLE_BATTERY_TASK),
    [checkIsOpen]
  );

  return (
    <PagePerformance pathname="credit/index">
      <Screen
        title="狸电站"
        theme="dark"
        safeAreaEdges={['bottom', 'top']}
        screenStyle={{
          backgroundColor: '#121212'
        }}
        backButton={false}
        headerRight={() =>
          taskSwitchOpen ? (
            <TouchableOpacity onPressIn={goRule}>
              <Text style={$ruleText}>规则</Text>
            </TouchableOpacity>
          ) : null
        }
        headerLeft={() => (
          <TouchableOpacity
            onPress={() => {
              if (isFromGift) {
                useRoleHomeStore.getState().setState({
                  giftModalVis: true
                });
              }
              safeGoBack();
            }}
          >
            <Icon
              icon="back"
              size={24}
              style={{
                tintColor: '#fff'
              }}
            />
          </TouchableOpacity>
        )}
      >
        <View style={{ pointerEvents: 'none' }}>
          <Image
            style={[
              $lightning,
              {
                top: dp2px(-96),
                left: dp2px(-12)
              }
            ]}
            source={LIGHTNING}
            contentFit="fill"
          />
        </View>
        <View>
          <View style={$mine} key={'mine'}>
            {totalCredits < CREDIT_LIMIT ? (
              <Icon icon="credit_minus" />
            ) : (
              <Icon icon="credit_plus" />
            )}
            <Text style={$mineText}>我的狸电池</Text>
          </View>
          <Text
            style={[
              $total,
              {
                color: totalCredits < CREDIT_LIMIT ? '#FF6A3B' : '#11BB2C'
              }
            ]}
            key={'totalCredits'}
          >
            {totalCredits}
          </Text>
          <View
            style={[
              {
                top: dp2px(-82),
                left: 0,
                pointerEvents: 'none'
              }
            ]}
          >
            <Image
              source={
                totalCredits <= 60
                  ? NO_BATTERY
                  : totalCredits >= 300
                    ? FULL_BATTERY
                    : BATTERY
              }
              contentFit="fill"
              style={$battery}
            />
            <Image style={[$halo]} source={HALO} contentFit="fill" />
          </View>
          {taskSwitchOpen ? renderBanner() : null}
          <View style={{ paddingHorizontal: 16, marginBottom: 40 }}>
            <RankHistory />
          </View>
        </View>
      </Screen>
    </PagePerformance>
  );
}

const $ruleText: TextStyle = {
  color: '#FFF',
  fontFamily: typography.fonts.pingfangSC.normal,
  fontSize: 16,
  fontWeight: '500',
  lineHeight: 22
};

const $ruleContent: TextStyle = {
  color: darkTheme.text.primary,
  fontFamily: typography.fonts.pingfangSC.normal,
  fontSize: 13,
  fontWeight: '400',
  lineHeight: 22
};

const $lightning: ImageStyle = {
  width: dp2px(236),
  height: dp2px(313),
  position: 'absolute'
};

const $halo: ImageStyle = {
  width: dp2px(261),
  height: dp2px(261),
  position: 'absolute',
  bottom: dp2px(-172),
  right: dp2px(-21),
  borderRadius: 261,
  zIndex: $Z_INDEXES.zm1
};

const $mine: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
  paddingLeft: dp2px(30),
  paddingTop: dp2px(42)
};

const $mineText: TextStyle = {
  color: 'rgba(255, 255, 255, 0.54)',
  fontFamily: typography.fonts.pingfangSC.normal,
  fontSize: 18,
  fontWeight: '500',
  marginLeft: dp2px(7)
};

const $battery: ImageStyle = {
  position: 'absolute',
  width: dp2px(150),
  height: dp2px(150),
  right: dp2px(23),
  bottom: dp2px(-92)
};

const $total: TextStyle = {
  color: StyleSheet.currentColors.brand1,
  fontFamily: typography.fonts.wishcard,
  fontSize: 38,
  fontWeight: '400',
  paddingLeft: dp2px(30),
  marginBottom: dp2px(26)
};

const $bold: TextStyle = {
  fontWeight: '800',
  marginTop: 10
};

const $banner: ViewStyle = {
  paddingHorizontal: 16,
  height: '100%',
  width: '100%'
};

const $bannerImg: ImageStyle = {
  width: '100%',
  height: '100%',
  resizeMode: 'cover',
  borderRadius: 16
};

const $bannerWrapper: ViewStyle = {
  height: 90,
  width: '100%',
  marginBottom: 16,
  position: 'relative',
  alignItems: 'center'
};

const $holderWrapper: ViewStyle = {
  flexDirection: 'row',
  width: '100%',
  height: '100%'
};

const $dotWrapper: ViewStyle = {
  position: 'absolute',
  bottom: 6,
  height: 5,
  zIndex: $Z_INDEXES.z20
};
