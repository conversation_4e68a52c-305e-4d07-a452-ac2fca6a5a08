import { router } from 'expo-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Keyboard, Text, TextStyle, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import { ttsGenerateStream } from '@/src/api/magicvideo';
import ScriptMask from '@/src/bizComponents/magic-video/scriptMask';
import { safeScriptForbidden } from '@/src/bizComponents/magic-video/toast';
import TTSBang from '@/src/bizComponents/magic-video/ttsBang';
import { EScriptLineStatus } from '@/src/bizComponents/magic-video/type';
import VideoBox from '@/src/bizComponents/magic-video/videoBox';
import {
  SCREEN_HEIGHT,
  WINDOW_WIDTH
} from '@/src/bizComponents/nestedScrollView';
import { TTSPanelSheet } from '@/src/bizComponents/videoMagic/ttsPanel';
import { hideToast, showToast } from '@/src/components';
import { CustomBlurView } from '@/src/components/image/CustomBlurView';
import Button, { EButtonType } from '@/src/components/v2/button';
import { TTSPannelItem, useMagicVideoEditStore } from '@/src/store/video-magic';
import { typography } from '@/src/theme';
import { $USE_FONT, $Z_INDEXES } from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { Image } from '@Components/image';
import { Screen } from '@Components/screen';
import IOS_BG from '@Assets/image/video-magic/ios_bg.png';
import { PartialMessage } from '../../src/types';
import {
  TtsLine,
  TtsRole
} from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
import { CommonCode } from '@/proto-registry/src/web/raccoon/errorcode/errorcode_pb';
import { ResizeMode } from '@step.ai/expo-av';
import { useShallow } from 'zustand/react/shallow';

type IScriptVideoItem = {
  initTTSText: string;
  initTTSRole: string;
  ttsStatus: EScriptLineStatus;
  maskText: string;
} & PartialMessage<TtsLine>;

export default function ScriptVideo() {
  const [scripts, setScripts] = useState<Partial<IScriptVideoItem>[]>([]);
  const [maskTexts, setMaskTexts] = useState<Partial<IScriptVideoItem>[]>([]);

  const [videoDuration, setVideoDuration] = useState(0);
  const [omitIndex, setOmitIndex] = useState(-1); // 内部控件进度
  const [segIndex, setSegIndex] = useState(0);
  const [videoPauseSignal, setVideoPauseSignal] = useState(0);
  const [videoPlaySignal, setVideoPlaySignal] = useState(0);
  const [segPlay, setSegPlay] = useState(true);
  const [maskTTSName, setMaskTTSName] = useState('');
  const [maskTTSRole, setMaskTTSRole] = useState('');
  const [editIndex, setEditIndex] = useState(-1);

  const updateMaskTTSName = (item: TTSPannelItem | PartialMessage<TtsRole>) => {
    return (
      [...(scriptLines.roles || []), ...ttsList]?.find(
        r => r?.ttsRole === item?.ttsRole
      )?.ttsName || ''
    );
  };

  useEffect(() => {
    if (scriptLines?.roles?.length) {
      const ttsName = updateMaskTTSName(scripts[editIndex]);
      setMaskTTSName(ttsName);
      setMaskTTSRole(scripts[editIndex]?.ttsRole || '');
    }
  }, [scripts, editIndex]);

  const {
    syncScriptTTSPanelVisible,
    ttsScriptPanelVisible,
    ttsList,
    syncFromTemplate,
    syncMaskScriptVisible,
    maskScriptShow,
    curPreviewItem,
    modMediaSource,
    scriptLines,
    currentTemplateId,
    mediaSource,
    scriptInitTemplate
  } = useMagicVideoEditStore(
    useShallow(state => ({
      syncScriptTTSPanelVisible: state.syncScriptTTSPanelVisible,
      ttsScriptPanelVisible: state.ttsScriptPanelVisible,
      ttsList: state.ttsList,
      syncFromTemplate: state.syncFromTemplate,
      syncMaskScriptVisible: state.syncMaskScriptVisible,
      maskScriptShow: state.maskScriptShow,
      curPreviewItem: state.curPreviewItem,
      modMediaSource: state.modMediaSource,
      scriptLines: state.scriptLines,
      currentTemplateId: state.currentTemplateId,
      mediaSource: state.mediaSource,
      scriptInitTemplate: state.scriptInitTemplate
    }))
  );

  useEffect(() => {
    setScripts(
      scriptLines?.lines?.map(sl => {
        return {
          ...sl,
          initTTSText: sl.text,
          initTTSRole: sl.ttsRole,
          ttsStatus: EScriptLineStatus.DONE
        };
      }) || []
    );
    setMaskTexts(
      scriptLines?.lines?.map(sl => {
        return {
          maskText: ''
        };
      }) || []
    );
  }, [scriptLines]);

  const isDiffOrigin = useMemo(() => {
    return scripts.some(s => {
      return s.initTTSText !== s.text || s.initTTSRole !== s.ttsRole;
    });
  }, [scripts]);

  const hasGenerate = useMemo(() => {
    return scripts.some(s => {
      return s.ttsStatus === EScriptLineStatus.LOADING;
    });
  }, [scripts]);

  useEffect(() => {
    // 曝光
    reportExpo(
      'expo',
      {
        changetype: 'word'
      },
      true
    );
  }, []);

  useEffect(() => {
    ttsScriptPanelVisible && useMagicVideoEditStore.getState().initTTSPannel();
  }, [ttsScriptPanelVisible]);

  const modScripts = (index, struct) => {
    setScripts(beforeData => {
      console.log(struct, ' struct====');
      const copyData = JSON.parse(JSON.stringify(beforeData));
      copyData[index] = {
        ...copyData[index],
        ...struct
      };
      return copyData;
    });
  };

  const generateTTS = () => {
    reportClick('publish_text', {
      changetype: 'word',
      templateid: currentTemplateId
    });
    hideToast();

    if (maskTexts[editIndex]?.maskText?.trim() === '') {
      showToast('输入的字数还不够哦');
      return;
    }
    if (
      scripts[editIndex]?.text?.trim() === maskTexts[editIndex].maskText &&
      scripts[editIndex].initTTSRole === maskTTSRole
    ) {
      showToast('你还没魔改台词哦');
      return;
    }

    if (
      (maskTexts[editIndex].maskText?.length || 0) <
      (scripts[editIndex]?.initTTSText?.length || 0) - 2
    ) {
      showToast('输入的字数还不够哦');
      return;
    }

    if (
      (maskTexts[editIndex].maskText?.length || 0) >
      (scripts[editIndex]?.initTTSText?.length || 0) + 2
    ) {
      showToast('输入的字数太多了哦');
      return;
    }

    const oldTTSRole = scripts[editIndex]?.ttsRole;
    modScripts(editIndex, {
      ttsStatus: EScriptLineStatus.LOADING,
      text: maskTexts[editIndex].maskText,
      ttsRole: maskTTSRole
    });

    let ttsSegs: string[] = [];

    let lineId: string | undefined = undefined;
    if (maskTTSRole && scriptLines?.roles) {
      const originRoles = scriptLines.roles?.map(r => r.ttsRole);
      if (originRoles.includes(maskTTSRole)) {
        lineId = scriptLines?.lineId;
      }
    }

    ttsGenerateStream(
      {
        ttsTone: maskTTSRole,
        ttsText: maskTexts[editIndex].maskText,
        lineId: lineId
      },
      res => {
        if (res.errorCode === 0 && res.total) {
          const progress = Math.floor(((res.index + 1) / res.total) * 100);
          if (!ttsSegs.includes(res?.ttsUrl)) {
            ttsSegs = [...ttsSegs, res.ttsUrl];
          }
          //   setTtsGenerateProgress(progress);
          console.log(
            ttsSegs[0],
            ' ttsSegs[0]',
            maskTexts[editIndex].maskText,
            res?.duration
          );
          modScripts(editIndex, {
            ttsProgress: progress,
            audioUrl: ttsSegs[0],
            ttsRole: maskTTSRole,
            text: maskTexts[editIndex].maskText,
            audioDuration: res?.duration
          });

          console.log(progress, 'progress===');

          if (progress === 100) {
            modScripts(editIndex, {
              ttsStatus: EScriptLineStatus.DONE
            });
          }
        } else if (res.errorCode !== 0) {
          console.log(res.errorCode, '=== generate tts code');
          if (res.errorCode === CommonCode.COMMON_ERR_LEGAL_RISK) {
            safeScriptForbidden();
          } else {
            showToast('服务异常，请稍候重试', 5000);
          }
          console.log('error tts normal', res.errorMsg);
          modScripts(editIndex, {
            ttsStatus: EScriptLineStatus.DONE,
            text: scripts[editIndex].text,
            ttsRole: oldTTSRole
          });
        }
      },
      err => {
        console.log(
          err,
          'error tts catch',
          JSON.stringify({
            ttsTone: scripts[editIndex].ttsRole || '',
            ttsText: maskTexts[editIndex].maskText,
            lineId: lineId
          })
        );
        safeScriptForbidden();
        modScripts(editIndex, {
          ttsStatus: EScriptLineStatus.DONE,
          errorCode: 10005,
          text: scripts[editIndex].text,
          ttsRole: oldTTSRole
        });
      }
    );

    Keyboard.dismiss();
    syncMaskScriptVisible(false);
  };

  const scriptMaskTextChange = (text: string) => {
    const copyData = JSON.parse(JSON.stringify(maskTexts));
    copyData[editIndex] = {
      maskText: text
    };
    setMaskTexts(copyData);
  };

  const recallKeyboard = () => {
    syncMaskScriptVisible(true);
    setEditIndex(editIndex);
  };

  const chooseRole = item => {
    setMaskTTSName(item?.ttsName);
    setMaskTTSRole(item?.ttsRole);
    syncScriptTTSPanelVisible(false);
    recallKeyboard();
  };

  return (
    <>
      <Screen
        title="魔改台词"
        headerShown={true}
        theme="dark"
        backButton={true}
        onBack={() => {
          reportClick('return_button', {
            module: 'videoedit',
            changetype: 'word',
            templateid: currentTemplateId
          });
          router.back();
        }}
        safeAreaEdges={['top']}
        style={{
          flex: 1
        }}
        backgroundView={
          <View
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              zIndex: -1
            }}
          >
            <LinearGradient
              style={{
                position: 'absolute',
                width: '100%',
                top: 375,
                height: SCREEN_HEIGHT - 375,
                zIndex: -101
              }}
              colors={['#1C1D22', '#18181C']}
              locations={[0, 1]}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
            />

            <Image
              source={IOS_BG}
              style={{
                position: 'absolute',
                width: '100%',
                height: 375,
                zIndex: -100
              }}
            />
            <CustomBlurView
              ios={{
                blurMount: 50,
                blurType: 'dark'
              }}
              useBlurView={isIos}
              android={{
                style: {
                  backgroundColor: '#18181C'
                }
              }}
              style={{
                position: 'absolute',
                width: '100%',
                height: 375,
                zIndex: -98
              }}
            />
            <LinearGradient
              style={{
                position: 'absolute',
                width: '100%',
                top: 0,
                height: 391,
                zIndex: -97
              }}
              colors={['#1c1d2200', '#1C1D22']}
              locations={[0, 1]}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
            />
          </View>
        }
      >
        <View
          style={{
            justifyContent: 'flex-start',
            alignItems: 'center',
            height: 400
          }}
        >
          <View
            style={{
              width: WINDOW_WIDTH - 16 * 2,
              height: (WINDOW_WIDTH - 16 * 2) / (4 / 3)
            }}
            key={'script-tts'}
          >
            <VideoBox
              width={WINDOW_WIDTH - 16 * 2}
              height={(WINDOW_WIDTH - 16 * 2) / (4 / 3)}
              assets={scriptLines?.rawVideoUrl || ''}
              showDelete={false}
              autoPlay={true}
              isLoop
              seekMode
              muted
              coverFit={ResizeMode.COVER}
              emitDuration={setVideoDuration}
              syncControlPlayStatus={(isPlay, index) => {
                setSegIndex(index);
                setSegPlay(isPlay);
                setOmitIndex(-1);
                reportClick('lines_play', {
                  module: 'videoedit',
                  changetype: 'word',
                  templateid: currentTemplateId,
                  clicktype: 0
                });
              }}
              syncLoadTTSIndex={index => {
                setSegIndex(index);
                setOmitIndex(-1);
              }}
              omitIndex={omitIndex}
              videoPauseSignal={videoPauseSignal}
              videoPlaySignal={videoPlaySignal}
              ttsLines={scripts}
            />
          </View>

          <View
            style={{
              width: '100%',
              justifyContent: 'flex-start',
              position: 'relative',
              marginTop: 16
            }}
            key={'script-masl'}
          >
            <LinearGradient
              colors={['rgba(24, 24, 28, 0)', '#18181C']}
              locations={[0, 1]}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              style={{
                position: 'absolute',
                bottom: 0,
                width: '100%',
                height: 30,
                zIndex: $Z_INDEXES.z1000,
                pointerEvents: 'none'
              }}
            />
            <FlatList
              data={scripts}
              bounces={false}
              showsVerticalScrollIndicator={false}
              style={{ height: 328 }}
              keyExtractor={(item, index) =>
                (item?.audioId || '' + index).toString()
              }
              renderItem={({
                item,
                index
              }: {
                item: Partial<IScriptVideoItem>;
                index: number;
              }) => {
                return (
                  <View key={item?.text + '' + index}>
                    <TTSBang
                      showSink={index === segIndex}
                      status={item.ttsStatus!}
                      text={
                        ([...(scriptLines?.roles || []), ...ttsList]?.find(
                          r => r?.ttsRole === item.ttsRole
                        )?.ttsName || '') +
                        '-' +
                        (item.text || '')
                      }
                      ttsOnPlay={false}
                      ttsUrl={undefined}
                      fullbodyPress
                      pressCallback={isPlay => {
                        reportClick('lines_play', {
                          module: 'videoedit',
                          changetype: 'word',
                          templateid: currentTemplateId,
                          clicktype: 1
                        });
                        setOmitIndex(index);
                      }}
                      activeContainerStyle={{
                        backgroundColor: '#1C1D22',
                        borderWidth: 0
                      }}
                      editScriptCallback={() => {
                        setVideoPauseSignal(s => s + 1);
                        syncMaskScriptVisible(true);
                        setEditIndex(index);
                        reportClick('modify_dialogue', {
                          changetype: 'word',
                          templateid: currentTemplateId
                        });
                      }}
                      handleColor={'transparent'}
                    />
                  </View>
                );
              }}
            />
          </View>

          <View key={'script-op'}>
            <Button
              type={EButtonType.LINEAR}
              linearColors={['#06EDB0', '#ADFFE9']}
              style={{
                width: Math.min(WINDOW_WIDTH - 32, 343),
                height: 54,
                paddingHorizontal: 0,
                marginTop: 10,
                marginBottom: 20,
                opacity: isDiffOrigin && !hasGenerate ? 1 : 0.75
              }}
              disabled={hasGenerate}
              onPress={() => {
                if (!isDiffOrigin) {
                  showToast('你还没魔改台词哦');
                } else {
                  const modifyLines = {
                    ...scriptLines,
                    lines: scripts
                  };

                  const findIndex = mediaSource.findIndex(m => m.lines);
                  console.log(mediaSource, 'mediaSource template');
                  if (findIndex > -1) {
                    modMediaSource(findIndex, {
                      lines: modifyLines
                    });
                  }

                  reportClick('linse_create_button', {
                    changetype: 'word',
                    templateid: currentTemplateId
                  });
                  router.navigate({
                    pathname: '/magic-video-edit',
                    params: {
                      from: 'script-tts'
                    }
                  });
                }
              }}
            >
              <Text style={$saveScript}>保存台词</Text>
            </Button>
          </View>
        </View>

        {ttsScriptPanelVisible ? (
          <TTSPanelSheet
            visible={ttsScriptPanelVisible}
            onClose={() => {
              syncScriptTTSPanelVisible(false);
              recallKeyboard();
            }}
            ttsList={ttsList}
            noRandom
            noRandomRole={scripts[editIndex]?.initTTSRole}
            $containerStyle={{
              height: 399,
              backgroundColor: '#1D1D1D'
            }}
            onChoose={chooseRole}
            extraRoles={scriptLines?.roles}
            portalName="script-video"
          />
        ) : null}
      </Screen>
      <ScriptMask
        ttsName={maskTTSName}
        ttsText={maskTexts[editIndex]?.maskText || ''}
        hiddenDelete
        submitCallback={generateTTS}
        textChangeCallback={scriptMaskTextChange}
        placeholderText={scripts?.[editIndex]?.initTTSText || ''}
        lessLimit={
          (scriptInitTemplate?.lines?.lines?.[editIndex]?.text?.length || 0) - 2
        }
        moreLimit={
          (scriptInitTemplate?.lines?.lines?.[editIndex]?.text?.length || 0) + 2
        }
        maxLimit={100}
        from={'word'}
      />
    </>
  );
}

const $saveScript: TextStyle = $USE_FONT(
  '#0B0503',
  typography.fonts.pingfangSC.normal,
  15,
  'normal',
  isIos ? '600' : 'bold',
  undefined
);
