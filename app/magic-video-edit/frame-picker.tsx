import { router } from 'expo-router';
import { useState } from 'react';
import { Image as RNImage, Text, TouchableOpacity, View } from 'react-native';
import TipOnce, { ETipOnce } from '@/src/bizComponents/magic-video/tipOnce';
import { WINDOW_WIDTH } from '@/src/bizComponents/nestedScrollView';
import { FrameSegPicker } from '@/src/bizComponents/videoMagic/frameSegPicker';
import { Icon } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import Button, { EButtonType } from '@/src/components/v2/button';
import { useMagicVideoEditStore } from '@/src/store/video-magic';
import { typography } from '@/src/theme';
import {
  $USE_FONT,
  $Z_INDEXES,
  $flexCenter,
  $flexColumn
} from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { Image } from '@Components/image';
import { Screen } from '@Components/screen';
import IOS_BG from '@Assets/image/video-magic/ios_bg.png';
import ImageBox from '../../src/bizComponents/magic-video/imageBox';
import { useParams } from '../../src/hooks/useParams';
import { useShallow } from 'zustand/react/shallow';

export default function FramePickerPage() {
  const { reimagineId } = useParams();

  const { frameCoverPath } = useMagicVideoEditStore(
    useShallow(state => ({
      frameCoverPath: state.frameCoverPath
    }))
  );

  return (
    <PagePerformance pathname="magic-video-edit/frame-picker">
      <Screen
        title="选择封面"
        theme="dark"
        backButton={false}
        headerLeft={() => (
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
          >
            <Icon icon="close" size={24} />
          </TouchableOpacity>
        )}
        headerRight={() => {
          return (
            <Button
              style={{
                borderRadius: 100,
                backgroundColor: '#FF6A3B',
                width: 57,
                height: 26,
                justifyContent: 'center'
              }}
              type={EButtonType.NORMAL}
              onPress={() => {
                router.back();
              }}
            >
              <Text
                style={$USE_FONT(
                  '#fff',
                  typography.fonts.pingfangSC.normal,
                  12,
                  'normal',
                  isIos ? '600' : 'bold',
                  undefined
                )}
              >
                完成
              </Text>
            </Button>
          );
        }}
        safeAreaEdges={['top']}
        backgroundView={
          <Image
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              zIndex: $Z_INDEXES.zm1
            }}
            source={IOS_BG}
          />
        }
      >
        <View style={{ flex: 1, alignItems: 'center' }}>
          <ImageBox
            width={WINDOW_WIDTH}
            height={WINDOW_WIDTH / (4 / 3)}
            hiddenDelete
            source={frameCoverPath?.path}
            $customStyle={{ marginBottom: 162, borderRadius: 16 }}
          />
          <FrameSegPicker
            width={WINDOW_WIDTH - 48}
            height={50}
            reimagineId={reimagineId as string}
          />
          <View
            style={[
              $flexColumn,
              $flexCenter,
              {
                marginBottom: 56,
                marginTop: 51
              }
            ]}
          >
            <TipOnce type={ETipOnce.DRAG_COVER} />
          </View>
        </View>
      </Screen>
    </PagePerformance>
  );
}
