import { router } from 'expo-router';
import Animated<PERSON><PERSON><PERSON><PERSON>iew from 'lottie-react-native';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Keyboard,
  Text,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSequence,
  withSpring,
  withTiming
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ttsGenerateStream } from '@/src/api/magicvideo';
import DraggieStoryboard from '@/src/bizComponents/magic-video/draggieStoryboard';
import ImageBox from '@/src/bizComponents/magic-video/imageBox';
import MagicBox from '@/src/bizComponents/magic-video/magicBox';
import ScriptMask from '@/src/bizComponents/magic-video/scriptMask';
import {
  removeLineScript,
  returnPrevPage,
  safePicForbidden,
  safeScriptForbidden
} from '@/src/bizComponents/magic-video/toast';
import { EScriptLineStatus } from '@/src/bizComponents/magic-video/type';
import {
  SCREEN_HEIGHT,
  WINDOW_WIDTH
} from '@/src/bizComponents/nestedScrollView';
import GenerateButton from '@/src/bizComponents/videoMagic/button/generateButton';
import { GalleryPanelSheet } from '@/src/bizComponents/videoMagic/galleryPanel';
import { TTSPanelSheet } from '@/src/bizComponents/videoMagic/ttsPanel';
import { getRandomRole } from '@/src/bizComponents/videoMagic/ttsPanel/randomTTS';
import { Icon, showToast } from '@/src/components';
import { AlbumSheet } from '@/src/components/album';
import { AlbumFromType } from '@/src/components/album/const';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import Button, { EButtonType } from '@/src/components/v2/button';
import { Toast, ToastMethods } from '@/src/components/v2/toast';
import { usePerformanceStore } from '@/src/store/performance';
import { useStorageStore } from '@/src/store/storage';
import { useMagicVideoEditStore } from '@/src/store/video-magic';
import { spacing, typography } from '@/src/theme';
import {
  $USE_FONT,
  $Z_INDEXES,
  $flexCenter,
  $flexRow
} from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { getMediaLibraryPermissionsAsync } from '@/src/utils/image';
import { reportClick, reportExpo } from '@/src/utils/report';
import { Image, ImageStyle } from '@Components/image';
import { Screen } from '@Components/screen';
import IOS_BG from '@Assets/image/video-magic/ios_bg.png';
import GENERATE_LOADING from '@Assets/lottie/magic-video/generate.json';
import VideoBox from '../../src/bizComponents/magic-video/videoBox';
import { useParams } from '../../src/hooks/useParams';
import { MediaType } from '@/proto-registry/src/web/raccoon/common/media_pb';
import { PlotType } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
import { CommonCode } from '@/proto-registry/src/web/raccoon/errorcode/errorcode_pb';
import { useIsFocused } from '@react-navigation/native';
import { usePersistFn } from '@step.ai/devtools/src/hooks/usePersistFn';
import { ResizeMode } from '@step.ai/expo-av';
import { useShallow } from 'zustand/react/shallow';

const PPT_ARROW = require('@Assets/image/video-magic/ppt-arrow.png');
const PPT_SECRET = require('@Assets/image/video-magic/ppt-secret.png');
const PPT_TITLE = require('@Assets/image/video-magic/ppt-title.png');

export default function MagicVideoEdit() {
  const toastRef = useRef<ToastMethods>();

  const {
    enterSource,
    templateid: templateId,
    recordid: recordId
  } = useParams();

  useEffect(() => {
    reportExpo(
      'expo',
      {
        source: enterSource,
        templateid: templateId,
        recordid: recordId
      },
      true
    );
  }, []);

  const {
    reimagineId,
    chooseId,
    albumPanelVisible,
    syncAlbumPanelVisible,
    mineAlbumPanelVisible,
    syncMineAlbumPanelVisible,
    ttsPanelVisible,
    syncTTSPanelVisible,
    mediaSource,
    ttsList,
    modMediaSource,
    syncAutoKeyboardShow,
    currentTemplateId,
    syncMaskScriptVisible,
    syncPromptAutoFocus,
    videoGenerating
  } = useMagicVideoEditStore(
    useShallow(state => ({
      reimagineId: state.reimagineId,
      chooseId: state.chooseId,
      syncChooseId: state.syncChooseId,
      setBgmPannelVisible: state.setBgmPannelVisible,
      bgmPannelVisible: state.bgmPannelVisible,
      albumPanelVisible: state.albumPanelVisible,
      syncAlbumPanelVisible: state.syncAlbumPanelVisible,
      mineAlbumPanelVisible: state.mineAlbumPanelVisible,
      syncMineAlbumPanelVisible: state.syncMineAlbumPanelVisible,
      ttsPanelVisible: state.ttsPanelVisible,
      syncTTSPanelVisible: state.syncTTSPanelVisible,
      mediaSource: state.mediaSource,
      initTTSPannel: state.initTTSPannel,
      ttsList: state.ttsList,
      scriptScrollOffsetY: state.scriptScrollOffsetY,
      flushStoryBoards: state.flushStoryBoards,
      ttsScriptShow: state.ttsScriptShow,
      modMediaSource: state.modMediaSource,
      syncAutoKeyboardShow: state.syncAutoKeyboardShow,
      currentTemplateId: state.currentTemplateId,
      syncMaskScriptVisible: state.syncMaskScriptVisible,
      syncPromptAutoFocus: state.syncPromptAutoFocus,
      videoGenerating: state.videoGenerating
    }))
  );

  const store = useStorageStore();
  const __setStorage = store.__setStorage;

  console.log(ttsList, 'ttsList=====');

  useEffect(() => {
    const checkPermission = async () => {
      await getMediaLibraryPermissionsAsync();
    };
    checkPermission();
  }, []);

  const isVideoBox =
    (mediaSource?.[chooseId]?.hasGuide &&
      mediaSource?.[chooseId]?.type === PlotType.Image2Video) ||
    mediaSource?.[chooseId]?.type === PlotType.RawVideo;

  const safeTop = useSafeAreaInsets().top;

  /** 打开图集 */
  const localMineCall = () => {
    syncMineAlbumPanelVisible(true);
    usePerformanceStore.getState().recordStart('make_photo_photo_set_render', {
      performance_type: 'render',
      performance_key: AlbumFromType.MAGIC_VIDEO_EDIT
    });
  };

  const $shareToastOpacity = useSharedValue(0);
  const $shareToastTrans = useSharedValue(-142);

  const $customToastAnimate = useAnimatedStyle(() => ({
    opacity: $shareToastOpacity.value,
    transform: [
      {
        translateY: $shareToastTrans.value
      }
    ]
  }));

  useEffect(() => {
    $shareToastOpacity.value = withSequence(
      withTiming(1, {
        duration: 500
      }),
      withDelay(
        5000,
        withTiming(0, {
          duration: 500
        })
      )
    );
    $shareToastTrans.value = withSequence(
      withTiming(42, {
        duration: 320
      }),
      withDelay(
        5000,
        withTiming(
          -142,
          {
            duration: 320
          },
          () => {
            runOnJS(__setStorage)({ magicVideoToast: true });
          }
        )
      )
    );
  }, []);

  const editRef = useRef<ScrollView>(null);

  const isNotSafePictureCode = useMemo(() => {
    return mediaSource?.some(
      ms =>
        ms?.errorCode === CommonCode.COMMON_ERR_LEGAL_RISK &&
        ms.errorMediaType === MediaType.IMAGE
    );
  }, [mediaSource]);

  useEffect(() => {
    if (isNotSafePictureCode) {
      safePicForbidden(() => {
        syncAlbumPanelVisible(true);
      });
    }
  }, [isNotSafePictureCode]);

  const isFirstEmptyImage = useMemo(
    () => mediaSource.length === 0,
    [mediaSource]
  );

  const isFocus = useIsFocused();

  const $shareOriginOpacity = useSharedValue(0);
  const $shareArrowOpacity = useSharedValue(0);
  const $shareSecretOpacity = useSharedValue(0);
  const $shareTitleOpacity = useSharedValue(0);
  const $generateOpacity = useSharedValue(0);

  useEffect(() => {
    if (videoGenerating) {
      $generateOpacity.value = withSpring(1, {
        damping: 80
      });
    } else {
      $generateOpacity.value = withSpring(0, {
        damping: 80
      });
    }
  }, [videoGenerating]);

  const doPPTWork = usePersistFn(async () => {
    if (useStorageStore.getState().magicVideoGuideCount >= 3) return;
    const finishedAnimation = async () => {
      if (!isFocus) {
        return;
      }
      setCanPlayPPT(false);
      __setStorage({
        magicVideoGuideCount:
          useStorageStore.getState().magicVideoGuideCount + 1
      });
    };

    $shareOriginOpacity.value = withSpring(1, {
      mass: 0.8,
      stiffness: 20
    });
    $shareArrowOpacity.value = withDelay(
      750,
      withSpring(1, {
        mass: 0.8,
        stiffness: 20
      })
    );
    $shareSecretOpacity.value = withDelay(
      1050,
      withSpring(1, {
        mass: 0.8,
        stiffness: 20
      })
    );
    $shareTitleOpacity.value = withDelay(
      2000,
      withSpring(
        1,
        {
          mass: 0.8,
          stiffness: 20
        },
        () => {
          runOnJS(finishedAnimation)();
        }
      )
    );
  });

  const $originAnimateStyle = useAnimatedStyle(() => ({
    opacity: $shareOriginOpacity.value
  }));
  const $arrowAnimateStyle = useAnimatedStyle(() => ({
    opacity: $shareArrowOpacity.value
  }));
  const $secretAnimateStyle = useAnimatedStyle(() => ({
    opacity: $shareSecretOpacity.value
  }));
  const $titleAnimateStyle = useAnimatedStyle(() => ({
    opacity: $shareTitleOpacity.value
  }));
  const $loadingAnimateStyle = useAnimatedStyle(() => ({
    opacity: $generateOpacity.value
  }));

  const isLessCount = useStorageStore.getState().magicVideoGuideCount < 3;

  const [canPlayPPT, setCanPlayPPT] = useState(
    isLessCount &&
      useMagicVideoEditStore.getState().mediaSource[chooseId]?.imageUrl &&
      !useMagicVideoEditStore.getState().mediaSource?.some(i => i.lines?.lines)
  );

  useEffect(() => {
    canPlayPPT && doPPTWork();
  }, [canPlayPPT]);

  useEffect(() => {
    useMagicVideoEditStore.getState().initTTSPannel();
  }, []);

  const hasGenerate = useMemo(
    () => mediaSource?.length && mediaSource?.some(m => !m?.hasGuide),
    [mediaSource]
  );

  const deleteTTSScript = randomRole => {
    Keyboard.dismiss();
    syncMaskScriptVisible(false);
    modMediaSource(chooseId, {
      ttsRole: randomRole,
      ttsText: '',
      ttsUrl: [],
      ttsStatus: EScriptLineStatus.NOT_EDIT
    });
  };

  const deleteScript = () => {
    const randomRole = getRandomRole(ttsList);
    removeLineScript(({ close }) => {
      deleteTTSScript(randomRole);
      close();
    });
  };

  const generateTTS = () => {
    if (!mediaSource?.[chooseId]?.ttsText) {
      return;
    }
    if (mediaSource[chooseId]?.ttsText?.trim() === '') {
      showToast('请不要输入空字符串哦');
      return;
    }

    Keyboard.dismiss();
    syncMaskScriptVisible(false);
    modMediaSource(chooseId, {
      ttsStatus: EScriptLineStatus.LOADING
    });

    let ttsSegs: string[] = [];
    ttsGenerateStream(
      {
        ttsTone: mediaSource[chooseId].ttsRole || '',
        ttsText: mediaSource[chooseId].ttsText,
        ttsVersion: ''
      },
      res => {
        console.log(
          res,
          'tts resresres',
          mediaSource[chooseId].ttsRole,
          mediaSource[chooseId].ttsRole || ''
        );
        if (res.errorCode === 0 && res.total) {
          reportClick('dialogue_produce', {
            module: 'videoedit',
            templateid: currentTemplateId,
            recordid: reimagineId,
            ttsid: mediaSource[chooseId].ttsId,
            dialoguetext: mediaSource[chooseId].ttsText
          });
          const progress = Math.floor(((res.index + 1) / res.total) * 100);
          if (!ttsSegs.includes(res?.ttsUrl)) {
            ttsSegs = [...ttsSegs, res.ttsUrl];
          }
          //   setTtsGenerateProgress(progress);
          modMediaSource(chooseId, {
            ttsProgress: progress,
            ttsUrl: ttsSegs,
            ttsText: mediaSource[chooseId].ttsText,
            ttsRole: mediaSource[chooseId].ttsRole || ''
          });

          if (progress === 100) {
            modMediaSource(chooseId, {
              ttsStatus: EScriptLineStatus.DONE
            });
          }
        } else if (res.errorCode !== 0) {
          console.log(res.errorCode, '=== generate tts code');
          if (res.errorCode === CommonCode.COMMON_ERR_LEGAL_RISK) {
            safeScriptForbidden();
          } else {
            showToast('服务异常，请稍候重试', 5000);
          }
          console.log('error tts normal', res.errorMsg);
          modMediaSource(chooseId, {
            ttsStatus: EScriptLineStatus.NOT_EDIT
          });
        }
      },
      err => {
        console.log(err, 'error tts catch');
        safeScriptForbidden();
        modMediaSource(chooseId, {
          ttsStatus: EScriptLineStatus.NOT_EDIT,
          errorCode: 10005
        });
      }
    );
  };

  const scriptMaskTextChange = (text: string) => {
    modMediaSource(chooseId, {
      ttsText: text
    });
  };

  const [videoPauseSignal, setVideoPauseSignal] = useState(0);
  const [videoPlaySignal, setVideoPlaySignal] = useState(0);

  useEffect(() => {
    if (albumPanelVisible) {
      setVideoPauseSignal(p => p + 1);
    } else {
      setVideoPlaySignal(p => p + 1);
    }
  }, [albumPanelVisible]);

  useEffect(() => {
    if (videoGenerating) {
      setVideoPauseSignal(p => p + 1);
    }
  }, [videoGenerating]);

  console.log(
    ttsList?.find(t => t.ttsRole === mediaSource[chooseId]?.ttsRole),
    'ttsList?.find(t => t.ttsRole === mediaSource[chooseId]?.ttsRole)',
    ttsPanelVisible
  );

  const initRandomRole = useMemo(() => getRandomRole(ttsList), [ttsList]);

  return (
    <View style={{ flex: 1, backgroundColor: '#000000' }}>
      <PagePerformance pathname="magic-video-edit/index">
        {canPlayPPT ? (
          <Animated.View style={[$pptContainer, $flexCenter]}>
            <Animated.View style={[$ppt, $originAnimateStyle]}>
              <View style={[pptMain, $flexRow, $flexCenter]}>
                <Animated.View
                  style={[$originAnimateStyle, pptCoverImgContainer]}
                >
                  <Image
                    source={mediaSource[chooseId]?.imageUrl}
                    style={pptCoverImg}
                    tosSize="size3"
                    cache
                  />
                </Animated.View>
                <Animated.View style={[$arrowAnimateStyle, pptArrowContainer]}>
                  <Image
                    source={PPT_ARROW}
                    tosSize="size3"
                    style={{
                      resizeMode: ResizeMode.COVER,
                      width: '100%',
                      height: '100%'
                    }}
                  />
                </Animated.View>
                <Animated.View
                  style={[$secretAnimateStyle, pptCoverImgContainer]}
                >
                  <Image
                    source={PPT_SECRET}
                    style={pptCoverImg}
                    tosSize="size3"
                  />
                </Animated.View>
              </View>
              <Animated.View
                style={[
                  $titleAnimateStyle,
                  {
                    width: 200,
                    height: 48,
                    marginTop: 28
                  }
                ]}
              >
                <Image source={PPT_TITLE} style={imgTitle} />
              </Animated.View>
            </Animated.View>
          </Animated.View>
        ) : null}

        <Screen
          headerShown={false}
          theme="dark"
          backButton={true}
          safeAreaEdges={['top']}
          backgroundView={
            <Image
              source={IOS_BG}
              style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                zIndex: -1
              }}
              transition={{
                duration: 200
              }}
            />
          }
          style={{
            overflow: 'hidden',
            position: 'relative'
          }}
        >
          <View style={$header}>
            <TouchableOpacity
              onPress={() => {
                returnPrevPage(({ close }) => {
                  close();
                  router.back();
                });
              }}
            >
              <Icon
                icon="back"
                size={24}
                style={{
                  tintColor: '#fff'
                }}
              />
            </TouchableOpacity>
            <Text
              style={$USE_FONT(
                '#fff',
                typography.fonts.pingfangSC.normal,
                16,
                'normal',
                isIos ? '600' : 'bold',
                undefined
              )}
            >
              爆改视频
            </Text>
            <View style={{ width: 20, height: 20, opacity: 0 }} />
          </View>

          <ScrollView
            ref={editRef}
            showsVerticalScrollIndicator={false}
            style={{
              maxHeight: SCREEN_HEIGHT - safeTop,
              width: '100%',
              borderRadius: 16,
              overflow: 'hidden'
            }}
            bounces={false}
            keyboardShouldPersistTaps={'handled'}
          >
            <View
              style={[
                {
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '100%',
                  height: SCREEN_HEIGHT - safeTop,
                  position: 'relative'
                }
              ]}
            >
              <View
                style={[
                  $maskWrapper,
                  {
                    width: WINDOW_WIDTH - 16 * 2,
                    height: 400,
                    marginHorizontal: 0,
                    paddingTop: 8
                  }
                ]}
              >
                {isVideoBox ? (
                  <View style={{ marginTop: safeTop + 8 }}>
                    <VideoBox
                      videoPauseSignal={videoPauseSignal}
                      videoPlaySignal={videoPlaySignal}
                      videoReplaySignal={
                        chooseId + mediaSource?.[chooseId]?.localSortId
                      }
                      width={WINDOW_WIDTH - 16 * 2}
                      height={(WINDOW_WIDTH - 16 * 2) / (4 / 3)}
                      assets={
                        mediaSource?.[chooseId]?.lines?.lines
                          ? mediaSource?.[chooseId]?.lines?.rawVideoUrl
                          : mediaSource?.[chooseId]?.videoUrl ||
                            mediaSource?.[chooseId]?.videoAsset?.uri
                      }
                      muted={!isVideoBox}
                      showDelete={true}
                      autoPlay={true}
                      seekMode
                      showControl
                      $videoStyle={{
                        width: WINDOW_WIDTH - 16 * 2,
                        height: (WINDOW_WIDTH - 16 * 2) / (4 / 3)
                      }}
                      posterCover={mediaSource?.[chooseId]?.imageUrl}
                      ttsLines={
                        mediaSource?.[chooseId]?.lines?.lines?.map(sl => {
                          return {
                            ...sl,
                            initTTSText: sl.text,
                            ttsStatus: EScriptLineStatus.DONE
                          };
                        }) || []
                      }
                    />
                  </View>
                ) : (
                  <>
                    {isFirstEmptyImage ? (
                      <>
                        <ImageBox
                          width={WINDOW_WIDTH - 16 * 2}
                          height={(WINDOW_WIDTH - 16 * 2) / (4 / 3)}
                          source={mediaSource?.[chooseId]?.imageUrl}
                          hiddenDelete={true}
                          resizeMode="contain"
                          parentPress={() => {
                            syncAlbumPanelVisible(true);
                          }}
                        />
                        <Text
                          style={[
                            {
                              marginTop: 20
                            },
                            $USE_FONT(
                              '#fff',
                              typography.fonts.pingfangSC.normal,
                              12,
                              'normal',
                              isIos ? '500' : 'bold',
                              undefined
                            ),
                            { opacity: 0.4 }
                          ]}
                        >
                          添加分镜图片可以使用图片生成视频分镜哦
                        </Text>
                      </>
                    ) : null}
                  </>
                )}
                {mediaSource?.[chooseId]?.type === PlotType.Image2Video &&
                mediaSource?.[chooseId]?.imageUrl &&
                !mediaSource?.[chooseId]?.hasGuide ? (
                  <MagicBox />
                ) : mediaSource?.[chooseId]?.hasGuide &&
                  mediaSource?.[chooseId]?.imageUrl &&
                  mediaSource?.[chooseId]?.type === PlotType.Image2Video ? (
                  <Button
                    type={EButtonType.LINEAR}
                    linearColors={['#06EDB0', '#ADFFE9']}
                    style={{
                      width: 158,
                      paddingHorizontal: 0,
                      position: 'absolute',
                      left: -37,
                      right: 0,
                      alignItems: 'center'
                    }}
                    $customBtnStyle={{
                      height: 44
                    }}
                    onPress={() => {
                      modMediaSource(chooseId, {
                        hasGuide: false
                      });
                      syncPromptAutoFocus();
                      console.log(
                        mediaSource?.map(i => i.hasGuide),
                        'guidetest'
                      );
                      syncAutoKeyboardShow(true);
                    }}
                  >
                    <View style={[$flexCenter, $flexRow]}>
                      <Icon
                        size={16}
                        icon="video_magic_tts_edit"
                        color={'#000'}
                        style={{
                          marginRight: 10
                        }}
                      />
                      <Text
                        style={$USE_FONT(
                          '#010101',
                          typography.fonts.pingfangSC.normal,
                          15,
                          'normal',
                          isIos ? '600' : 'bold',
                          undefined
                        )}
                      >
                        改它
                      </Text>
                    </View>
                  </Button>
                ) : null}
              </View>

              <View
                style={{
                  width: '100%',
                  position: 'absolute',
                  bottom: 0,
                  paddingBottom: 40,
                  backgroundColor: '#161616',
                  height: 220
                }}
              >
                <DraggieStoryboard />
                <View style={{ marginTop: 12, alignItems: 'center' }}>
                  {hasGenerate ? <GenerateButton /> : null}
                </View>
              </View>
            </View>

            {!store.magicVideoToast && (
              <Animated.View
                style={[
                  $customToastAnimate,
                  {
                    position: 'absolute',
                    top: 10,
                    left: 0,
                    right: 0,
                    marginHorizontal: 16,
                    borderRadius: 100,
                    backgroundColor: '#000',
                    height: 42,
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: $Z_INDEXES.z500,
                    pointerEvents: 'none'
                  }
                ]}
              >
                <Text
                  style={$USE_FONT(
                    '#fff',
                    typography.fonts.pingfangSC.normal,
                    14,
                    'normal',
                    '400',
                    undefined
                  )}
                >
                  为防止素材丢失，编辑过程中尽量不要离开APP哦
                </Text>
              </Animated.View>
            )}
            {ttsPanelVisible ? (
              <TTSPanelSheet
                visible={ttsPanelVisible}
                onClose={() => {
                  syncTTSPanelVisible(false);
                  syncMaskScriptVisible(true);
                }}
                portalName={'magic-video'}
                ttsList={ttsList}
                $containerStyle={{
                  height: 399,
                  backgroundColor: '#1D1D1D'
                }}
                onChoose={item => {
                  modMediaSource(chooseId, item);
                  syncTTSPanelVisible(false);
                  syncMaskScriptVisible(true);
                }}
              />
            ) : null}
            <GalleryPanelSheet
              localMineCall={localMineCall}
              $containerStyle={{
                backgroundColor: '#1D1D1D',
                height: 550,
                paddingLeft: 16,
                marginBottom: 31,
                zIndex: 0
              }}
              visible={albumPanelVisible}
              onClose={() => {
                syncAlbumPanelVisible(false);
              }}
            />
            <AlbumSheet
              isSingleMode
              isVisible={mineAlbumPanelVisible}
              maxLen={1}
              onClose={() => {
                syncMineAlbumPanelVisible(false);
              }}
              callWhere={AlbumFromType.MAGIC_VIDEO_EDIT}
            />
            <Toast ref={toastRef} />
          </ScrollView>
        </Screen>

        <ScriptMask
          ttsName={
            ttsList?.find(t => t.ttsRole === mediaSource[chooseId]?.ttsRole)
              ?.ttsName || initRandomRole
          }
          ttsText={mediaSource?.[chooseId]?.ttsText || ''}
          deleteCallback={deleteScript}
          submitCallback={generateTTS}
          textChangeCallback={scriptMaskTextChange}
        />
        <Animated.View
          style={[
            $videoGenerate,
            $flexCenter,
            { pointerEvents: videoGenerating ? 'auto' : 'none' },
            $loadingAnimateStyle
          ]}
        >
          <View
            style={[
              {
                borderRadius: 20,
                padding: 32,
                backgroundColor: '#1A1A1A'
              },
              $flexCenter
            ]}
          >
            <AnimatedLottieView
              source={GENERATE_LOADING}
              autoPlay={videoGenerating}
              loop={false}
              style={{ width: 110, height: 110, marginBottom: 12 }}
              onAnimationFinish={() => {}}
            />
            <Text
              style={$USE_FONT(
                '#fff',
                typography.fonts.pingfangSC.normal,
                14,
                'normal',
                isIos ? '700' : 'bold',
                undefined
              )}
            >
              视频合成中，请耐心等待
            </Text>
          </View>
        </Animated.View>
      </PagePerformance>
    </View>
  );
}

const $maskWrapper: ViewStyle = {
  position: 'relative',
  alignItems: 'center',
  justifyContent: 'center',
  marginHorizontal: 8,
  paddingTop: 8,
  borderRadius: 16
};

const $ppt: ViewStyle = {
  position: 'absolute',
  width: '100%',
  height: 241,
  borderRadius: 16,
  alignItems: 'center',
  justifyContent: 'center'
};

const pptMain = {
  gap: 11
};

const pptCoverImgContainer = {
  width: 106,
  height: 78,
  borderRadius: 10
};

const pptCoverImg: ImageStyle = {
  resizeMode: ResizeMode.COVER,
  width: '100%',
  height: '100%',
  borderRadius: 10
};

const pptArrowContainer = {
  width: 24,
  height: 24
};

const imgTitle: ImageStyle = {
  resizeMode: ResizeMode.COVER,
  width: 181,
  height: 48
};

const $pptContainer: ViewStyle = {
  position: 'absolute',
  zIndex: 1000,
  width: '100%',
  height: '100%',
  backgroundColor: '#00000099',
  top: 0,
  bottom: 0,
  left: 0,
  right: 0
};

const $header: ViewStyle = {
  position: 'absolute',
  height: 54,
  width: WINDOW_WIDTH,
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  borderBottomColor: 'rgba(210,210,210,1)',
  zIndex: $Z_INDEXES.z100,
  backgroundColor: 'transparent'
};

const $videoGenerate: ViewStyle = {
  position: 'absolute',
  zIndex: 1000,
  width: '100%',
  height: '100%'
};
