import { useDebounceFn } from 'ahooks';
import { ImagePickerAsset } from 'expo-image-picker';
import { router } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Pressable,
  Image as RNImage,
  Text,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming
} from 'react-native-reanimated';
import { uploadCropImage } from '@/src/api/magicvideo';
import TipOnce, { ETipOnce } from '@/src/bizComponents/magic-video/tipOnce';
import { WINDOW_WIDTH } from '@/src/bizComponents/nestedScrollView';
import CropEditor, {
  TImageSource
} from '@/src/bizComponents/videoMagic/crop-editor';
import { hideLoading } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import But<PERSON>, { EButtonType } from '@/src/components/v2/button';
import { useMagicVideoEditStore } from '@/src/store/video-magic';
import { typography } from '@/src/theme';
import {
  $USE_FONT,
  $Z_INDEXES,
  $flexCenter,
  $flexColumn,
  $flexHBetween,
  $flexRow
} from '@/src/theme/variable';
import { reportClick } from '@/src/utils/report';
import { Image } from '@Components/image';
import { PlotType } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
import { useShallow } from 'zustand/react/shallow';

type TCropFunc = (quality: number) => Promise<{
  uri: TImageSource;
  width: number;
  height: number;
  base64?: string;
}>;

export default function ImageClipPage() {
  const {
    clipOriginPath,
    addMediaSource,
    mediaSource,
    currentTemplateId,
    reimagineId
  } = useMagicVideoEditStore(
    useShallow(state => ({
      clipOriginPath: state.clipOriginPath,
      addMediaSource: state.addMediaSource,
      mediaSource: state.mediaSource,
      currentTemplateId: state.currentTemplateId,
      reimagineId: state.reimagineId
    }))
  );

  const $shareOpacity = useSharedValue(0);
  const $animateClip = useAnimatedStyle(() => ({
    opacity: $shareOpacity.value
  }));

  const cropThis = useRef<TCropFunc>();
  const [isCropping, setIsCropping] = useState(false);

  const [hasResult, setHasResult] = useState(false);

  const cropImage = useCallback(async () => {
    console.log(Date.now(), '===crop start');
    if (cropThis.current && !hasResult && isCropping) {
      const { uri, width, height, base64 } = await cropThis.current(1);
      console.log(width, height, 'crop image success');

      if (base64) {
        console.log(Date.now(), '===crop mid');
        const res = await uploadCropImage({
          imageBase64: base64
        });
        console.log(Date.now(), '===crop end');
        console.log(res, 'uploadCropImage1===');
        reportClick('storyboard_add', {
          module: 'videoedit',
          templateid: currentTemplateId,
          recordid: reimagineId,
          storyboardstatus: 'custom_picture'
        });

        if (!res?.imageUrl) {
          setIsCropping(false);
        } else {
          addMediaSource({
            localSortId: mediaSource.length,
            imageId: res?.imageId,
            imageUrl: res?.imageUrl, // 应为上传的 path,
            type: PlotType.Image2Video
          });

          setIsCropping(false);
          setHasResult(true);
          hideLoading();
        }
      }
    }
  }, [isCropping]);

  useEffect(() => {
    hasResult && router.back();
  }, [hasResult]);

  const { run: takeShot } = useDebounceFn(cropImage, {
    wait: 1000
  });

  const [originImageSize, setOriginImageSize] = useState({
    width: 0,
    height: 0
  });

  const [scale, setScale] = useState(1);
  const [ratio, setRatio] = useState(1);

  return (
    <PagePerformance pathname="magic-video-edit/clip">
      <View
        style={[
          $flexCenter,
          {
            backgroundColor: '#000',
            width: '100%',
            height: '100%',
            position: 'relative'
          }
        ]}
      >
        {clipOriginPath ? (
          <Image
            source={{
              uri: (clipOriginPath as ImagePickerAsset)?.uri
            }}
            tosSize={'sizeOrigin'}
            style={{
              width: originImageSize.width || WINDOW_WIDTH,
              height: originImageSize.height || WINDOW_WIDTH / (4 / 3),
              position: 'absolute',
              zIndex: $Z_INDEXES.zm1,
              opacity: 0
            }}
            onLoad={(e, duration) => {
              const width = e?.source?.width || 0;
              const height = e?.source?.height || 0;
              setOriginImageSize({
                width: width,
                height: height
              });
              console.log(
                width,
                height,
                'origin width height',
                clipOriginPath as ImagePickerAsset
              );
              const rate = width / WINDOW_WIDTH;
              console.log(width / WINDOW_WIDTH < 1, 'width rate  < 1 ');
              const minSegX = width / WINDOW_WIDTH < 1;
              const minSegY = height / (WINDOW_WIDTH / (4 / 3)) < 1;
              setScale(minSegX || minSegY ? 1 / rate : rate);
              setRatio(width / height);
              $shareOpacity.value = withDelay(
                500,
                withTiming(1, {
                  duration: 250
                })
              );
            }}
          />
        ) : null}

        {originImageSize.width && clipOriginPath ? (
          <Animated.View style={$animateClip}>
            <CropEditor
              source={clipOriginPath}
              cropShape={'rect'}
              width={WINDOW_WIDTH}
              height={WINDOW_WIDTH}
              cropArea={{
                width: WINDOW_WIDTH, // required
                height: WINDOW_WIDTH / (4 / 3) // required,
              }}
              borderWidth={1}
              backgroundColor={'#000000B2'}
              resizeMode={'contain'}
              onCrop={cropCallback => {
                cropThis.current = cropCallback;
              }}
              scale={scale}
              originImageSize={originImageSize}
            />
          </Animated.View>
        ) : null}

        {originImageSize?.width && clipOriginPath ? (
          <Pressable
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              width: WINDOW_WIDTH,
              height: 210,
              zIndex: $Z_INDEXES.z100,
              alignItems: 'center'
            }}
          >
            <View
              style={[
                $flexColumn,
                $flexCenter,
                {
                  marginBottom: 56
                }
              ]}
            >
              <TipOnce type={ETipOnce.PINCH_COVER} />
            </View>
            <Animated.View
              style={[
                $flexRow,
                $flexHBetween,
                {
                  width: '100%',
                  paddingHorizontal: 24
                },
                $animateClip
              ]}
            >
              <Button
                type={EButtonType.NORMAL}
                style={$handleButton}
                onPress={() => {
                  router.back();
                }}
              >
                <Text style={$handleText}>取消</Text>
              </Button>
              <Button
                type={EButtonType.NORMAL}
                onPress={() => {
                  console.log(Date.now(), '===crop before');
                  if (!isCropping) {
                    takeShot();
                    setIsCropping(true);
                  }
                }}
              >
                <Text style={$handleText}>确定</Text>
              </Button>
            </Animated.View>
          </Pressable>
        ) : null}
      </View>
    </PagePerformance>
  );
}

const $handleButton: ViewStyle = {
  width: 80,
  height: 40,
  borderRadius: 100
};

const $handleText: TextStyle = $USE_FONT(
  '#FFFFFF',
  typography.fonts.pingfangSC.normal,
  16,
  'normal',
  '500',
  undefined
);
