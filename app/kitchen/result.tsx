import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import LottieView from 'lottie-react-native';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  ImageStyle,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import Carousel, { ICarouselInstance } from 'react-native-reanimated-carousel';
import { SharedElement } from 'react-navigation-shared-element';
import { PersistPhotos } from '@/src/api/makephotov2';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import ToastInner from '@/src/bizComponents/credit/toast';
import { ProgressBar } from '@/src/bizComponents/goods/generate/Progress';
import {
  Button,
  Image,
  LOTTIE_LOADING,
  Screen,
  hideLoading,
  showLoading,
  showToast
} from '@/src/components';
import {
  CommentEvent,
  CommentEventBus
} from '@/src/components/comment/eventbus';
import { InputType } from '@/src/components/comment/typing';
import { Credits } from '@/src/components/makePhoto/previewView/components/Credits';
import {
  PHOTO_ITEM_HEIGHT,
  PhotoPreview
} from '@/src/components/makePhoto/previewView/components/PhotoPreview';
import { SaveToAlbum } from '@/src/components/makePhoto/previewView/components/SaveToAlbumButton';
import { showConfirm } from '@/src/components/popup/confirmModalGlobal/Confirm';
import { PrimaryButton } from '@/src/components/primaryButton';
import CreditWrapper from '@/src/components/v2/credit-wrapper';
import { useSafeBottomArea } from '@/src/hooks';
import { showNoBatteryTip } from '@/src/hooks/useCheckCredit';
import { useCurrentPageKey } from '@/src/hooks/useCurrentPageKey';
import { useOneRunning } from '@/src/hooks/useOneRunning';
import { useCommentStore } from '@/src/store/comment';
import { GenerateResult, useKitchenStore } from '@/src/store/kitchen';
import { useMakePhotoStoreV2 } from '@/src/store/makePhotoV2';
import { AlbumType, usePublishStore } from '@/src/store/publish';
import { useTopicStore } from '@/src/store/topic';
import {
  centerStyle,
  darkTheme,
  fullStyle,
  imageFullStyle,
  typography
} from '@/src/theme';
import { Theme } from '@/src/theme/colors/type';
import { $Z_INDEXES, $flexCenter } from '@/src/theme/variable';
import { GameType, InvokeType, RoleInfo } from '@/src/types';
import { dp2px, getScreenSize } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { StyleSheet } from '@Utils/StyleSheet';
import { CensoredState } from '@/proto-registry/src/web/raccoon/common/state_pb';
import { PointsCode } from '@/proto-registry/src/web/raccoon/errorcode/errorcode_pb';
import { CommonActions, useNavigation } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';

const NEW_REGEN_BTN = require('@Assets/makephoto/new-regen-btn.png');
const loadingBg = require('@Assets/image/kitchen/loading-bg.png');

function KitchenResult() {
  const {
    allGenerateResult,
    loading,
    focusPhoto,
    isRetry,
    isGeneratingError,
    isGeneratingDone,
    displayPhoto,
    currentRole,
    refCardId,
    refTopics
  } = useKitchenStore(
    useShallow(state => {
      const selectedPhoto = state.refPhotos.find(
        item => item.photoId === state.selectPhoto
      );
      const currentSelectRole: RoleInfo | undefined =
        state.selectRoles?.[state.selectPhoto];

      return {
        loading: state.generating,
        allGenerateResult: state.generateResult,
        focusPhoto: state.focusPhoto,
        isRetry: state.isRetry,
        isGeneratingError: state.isGeneratingError,
        isGeneratingDone: state.isGeneratingDone,
        displayPhoto: selectedPhoto,
        currentRole: currentSelectRole,
        refCardId: state.refCardId,
        refTopics: state.refTopics
      };
    })
  );

  const generateResult = useMemo(() => {
    if (!isGeneratingDone) {
      return allGenerateResult;
    } else {
      return allGenerateResult.filter(item => item.url && item.photoId);
    }
  }, [allGenerateResult, isGeneratingDone]);

  const [process, setProcess] = useState(0);
  const [resultSize, setResultSize] = useState<{
    width?: number;
    height?: number;
  }>({});
  const calcPhotoSizeRef = useRef<string>('');

  const saveToAlbumHistory = useRef<string[]>([]);
  const carouselRef = useRef<ICarouselInstance>(null);
  const navigation = useNavigation();
  const prevPageKey = useCurrentPageKey(2);
  const safeBottom = useSafeBottomArea(30);

  const showLoadingView = loading || isGeneratingError;

  const focusIndex = useMemo(() => {
    const index = generateResult.findIndex(
      item => item.photoId === focusPhoto?.photoId
    );
    return Math.max(index, 0);
  }, [generateResult, focusPhoto]);

  const onSaveToAlbum = useMemoizedFn(async () => {
    const changeRecord = useKitchenStore.getState().calcIsChange();
    reportClick('button', {
      button_type: 1,
      ...changeRecord
    });

    if (focusPhoto?.photoId) {
      saveToAlbumHistory.current.push(focusPhoto.photoId);
    }
    if (focusPhoto?.cencorState === CensoredState.CENSORED_BLOCKED) {
      showToast('呜呜小狸炖糊了，无法保存哦');
      return;
    }

    if (!focusPhoto?.photoId) {
      showToast('保存失败，请重试');
      return;
    }

    showLoading();
    PersistPhotos({ photoIds: [focusPhoto?.photoId] })
      .then(res => {
        const { ok } = res || {};
        showToast('保存成功');
        hideLoading();
      })
      .catch(e => {
        showToast('保存失败');
        hideLoading();
      });
  });

  const onPressReGenerate = useMemoizedFn(async () => {
    const changeRecord = useKitchenStore.getState().calcIsChange();

    reportClick('button', {
      button_type: 2,
      ...changeRecord
    });
    useKitchenStore.getState().reGeneratePhoto(focusIndex);
  });

  const onPressPublish = useMemoizedFn(async () => {
    const changeRecord = useKitchenStore.getState().calcIsChange();

    reportClick('button', {
      button_type: 4,
      ...changeRecord
    });

    const publishPhotos = generateResult.map((item, index) => {
      return {
        ...item,
        gameType: GameType.PHOTO_FINETUNING,
        albumType: AlbumType.history,
        num: index
      };
    });
    usePublishStore.getState().setPhotos(publishPhotos);
    // useMakePhotoStoreV2.getState().setTopics(refTopics || []);
    useTopicStore.getState().setDefaultTopics(refTopics ?? []);

    router.navigate({
      pathname: '/publish',
      params: {
        photoId: publishPhotos[0]?.photoId || '',
        source: 'element_generate_result'
      }
    });
  });

  const onPressBack = useMemoizedFn(() => {
    if (loading) {
      showConfirm({
        title: '确定退出吗？',
        content: '退出生成后，可能造成图片丢失',
        theme: Theme.DARK,
        confirmText: '确认退出',
        onConfirm: ({ close }) => {
          close();
          router.back();
        }
      });
    } else {
      router.back();
    }
  });

  const renderItem = (data: { item: GenerateResult; index: number }) => {
    const { item, index } = data || {};
    const renderCard = () => {
      return (
        <View
          style={[
            $flexCenter,
            {
              overflow: 'hidden',
              borderRadius: 18
            }
          ]}
          collapsable={false}
        >
          {resultSize.height ? (
            <Image
              key={item.photoId}
              source={item?.url || ''}
              tosSize="size1"
              style={[
                {
                  width: resultSize.width,
                  height: resultSize.height,
                  overflow: 'hidden',
                  borderRadius: 24,
                  backgroundColor: darkTheme.background.skeleton40
                }
              ]}
              contentFit={'contain'}
            />
          ) : null}

          <View
            style={[
              $regenWrap,
              {
                right: 0
              }
            ]}
          >
            <CreditWrapper
              gameType={GameType.PHOTO_FINETUNING}
              invokeType={InvokeType.INVOKE_FINETUNING_REGEN}
              ip={currentRole?.ip}
              signal={currentRole?.ip}
              useStore={false}
              $cornerstyle={{
                top: dp2px(-4),
                left: 0,
                right: 0,
                zIndex: $Z_INDEXES.z10
              }}
              $blurStyle={{
                paddingHorizontal: 6
              }}
              buttonContainer={
                <TouchableOpacity
                  onPress={onPressReGenerate}
                  style={[fullStyle, StyleSheet.centerStyle]}
                >
                  <Image
                    source={NEW_REGEN_BTN}
                    style={{
                      ...imageFullStyle,
                      resizeMode: 'cover'
                    }}
                  />
                </TouchableOpacity>
              }
            />
          </View>
        </View>
      );
    };
    return (
      <View
        style={{
          ...centerStyle,
          width: '100%',
          height: '100%'
        }}
      >
        <SharedElement
          id={'make-photo-' + item.photoId}
          style={{
            ...centerStyle
          }}
        >
          {renderCard()}
        </SharedElement>
      </View>
    );
  };

  const changeIndexFromPreview = useMemoizedFn((index: number) => {
    carouselRef?.current?.scrollTo({ index, animated: false });
  });

  const onPressComment = useOneRunning(
    useMemoizedFn(async () => {
      const changeRecord = useKitchenStore.getState().calcIsChange();

      reportClick('button', {
        button_type: 3,
        ...changeRecord
      });

      if (!refCardId) {
        showToast('暂时无法转发至评论区～');
      }

      navigation.dispatch(state => {
        const routesLength = state.routes.length;
        const routes = state.routes.slice(0, routesLength - 2);
        return CommonActions.reset({
          ...state,
          routes,
          index: routesLength - 3
        });
      });

      setTimeout(() => {
        useCommentStore.getState().updateCache(refCardId, {
          kitchenResult: generateResult
        });
        CommentEventBus.emit(CommentEvent.TRIGGER_EDIT_COMMENT, {
          pageKey: prevPageKey,
          type: InputType.TEXT,
          generateResult
        });
      }, 300);
    })
  );

  useEffect(() => {
    if (!showLoadingView) {
      const changeRecord = useKitchenStore.getState().calcIsChange();
      reportExpo('expo', changeRecord, true);
    }
  }, [showLoadingView]);

  useEffect(() => {
    if (isGeneratingError) {
      const { generateResult: result, errorInfo } = useKitchenStore.getState();
      if (
        errorInfo instanceof ErrorRes &&
        errorInfo.reason?.includes(
          PointsCode.POINTS_ERR_INSUFFICIENT_POINTS + ''
        )
      ) {
        showNoBatteryTip();
      } else {
        showToast(
          result.length === 0 || isRetry
            ? '生成失败，请重试'
            : '哎呀，遇到点问题'
        );
      }

      if (result.length) {
        useKitchenStore.getState().updateState({
          isGeneratingError: false
        });
      } else {
        router.back();
      }
    }
  }, [isGeneratingError]);

  useEffect(() => {
    if (loading) {
      setProcess(0);
      const interval = setInterval(() => {
        setProcess(process => {
          if (process < 0.8) {
            return process + 0.005;
          } else if (process < 0.9) {
            return process + 0.003;
          } else {
            return Math.min(0.99, process + 0.001);
          }
        });
      }, 100);
      return () => {
        clearInterval(interval);
      };
    } else {
      setProcess(1);
    }
  }, [loading]);

  useEffect(() => {
    if (generateResult.length === 0 && calcPhotoSizeRef.current) {
      // 清空
      calcPhotoSizeRef.current = '';
      setResultSize({});
    }

    if (generateResult.length && !calcPhotoSizeRef.current) {
      const valid = generateResult.filter(item => item.url);
      if (valid[0]?.url) {
        Image.getSize(valid[0].url, (w, h) => {
          if (h / w < 1.4) {
            const imageWidth = dp2px(327);
            const imageHeight = (imageWidth / w) * h;

            setResultSize({ width: imageWidth, height: imageHeight });
            calcPhotoSizeRef.current = valid[0].url as string;
          } else {
            const imageHeight = dp2px(450);
            const imageWidth = (imageHeight / h) * w;

            setResultSize({ width: imageWidth, height: imageHeight });
            calcPhotoSizeRef.current = valid[0].url as string;
          }
        });
      }
    }
  }, [generateResult]);

  return (
    <Screen
      theme="dark"
      wholePageStyle={{
        backgroundColor: darkTheme.background.page
      }}
      backgroundView={<Image source={loadingBg} style={$bg} />}
      headerLeft={() => (loading ? null : <Credits />)}
      headerRight={() =>
        loading ? null : <SaveToAlbum onSaveImage={onSaveToAlbum} />
      }
      onBack={onPressBack}
      safeAreaEdges={['top']}
    >
      {showLoadingView ? (
        <View style={$loadingPage}>
          <View
            style={{
              width: dp2px(275),
              height: dp2px(367),
              borderRadius: 18,
              overflow: 'hidden'
            }}
          >
            {displayPhoto?.url ? (
              <Image
                source={displayPhoto?.url}
                tosSize="size3"
                style={{
                  width: '100%',
                  height: '100%'
                }}
              />
            ) : null}

            <View
              style={[
                $progressTextContainer,
                !displayPhoto?.url ? $progressTextContainerNoRef : undefined
              ]}
            >
              <Text style={$progressText}>{Math.floor(process * 100)}%</Text>
            </View>
            <ProgressBar
              style={{
                position: 'absolute',
                top: 0,
                left: 0
              }}
              width={dp2px(275)}
              height={dp2px(367)}
              radius={18}
              gap={0}
              filledColor={darkTheme.text.primary}
              unfilledColor="transparent"
              strokeWidth={4}
              progress={process}
            />
          </View>
          <Text style={$loadingTitle}>新配方正在生成...</Text>
          <Text style={$loadingDesc}>
            请不要中途退出App或当前页，否则可能造成图片丢失
          </Text>
        </View>
      ) : generateResult.length ? (
        <View
          style={{
            position: 'relative',
            flex: 1
          }}
        >
          <View
            style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Carousel
              ref={carouselRef}
              data={generateResult}
              enabled={true}
              renderItem={renderItem}
              defaultIndex={focusIndex || 0}
              onProgressChange={(
                _offsetProgress: number,
                absoluteProgress: number
              ) => {
                const currentIndex =
                  carouselRef.current?.getCurrentIndex() || 0;

                if (absoluteProgress > 0.5 || currentIndex === 0) {
                  useKitchenStore
                    .getState()
                    .updateFocus(generateResult[currentIndex]);
                }
              }}
              width={getScreenSize('width')}
              height={Math.max(resultSize.height || 0, dp2px(435))}
              loop={false}
              mode="parallax"
              modeConfig={{
                parallaxScrollingScale: 1,
                parallaxScrollingOffset: 5,
                parallaxAdjacentItemScale: 0.95
              }}
            />

            <View
              style={{
                height: PHOTO_ITEM_HEIGHT + 16,
                width: '100%'
              }}
            >
              <PhotoPreview
                photos={generateResult}
                photoCount={isGeneratingDone ? generateResult.length : 3}
                selectedIndex={focusIndex}
                setIndex={changeIndexFromPreview}
              />
            </View>
          </View>
          <View
            style={{
              marginBottom: safeBottom,
              width: '100%',
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              gap: dp2px(9)
            }}
          >
            {/* {refCardId ? (
              <Button
                style={{
                  width: dp2px(167),
                  borderRadius: 100,
                  backgroundColor: darkTheme.background.shareModal
                }}
                textStyle={{
                  color: darkTheme.text.primary,
                  fontSize: 14,
                  fontWeight: '600'
                }}
                onPress={onPressComment}
              >
                评论区交作业
              </Button>
            ) : null} */}
            <PrimaryButton
              textStyle={{
                color: darkTheme.text.primary,
                fontSize: 14,
                fontWeight: '600'
              }}
              width={dp2px(275)}
              onPress={onPressPublish}
              usingGradient={false}
            >
              去发布
            </PrimaryButton>
          </View>
        </View>
      ) : null}
    </Screen>
  );
}

export default KitchenResult;

const $regenWrap: ViewStyle = {
  width: dp2px(56),
  height: dp2px(56),
  right: 0,
  bottom: 0,
  position: 'absolute'
};

const $loadingPage: ViewStyle = {
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: '100%',
  paddingBottom: 30
};

const $loadingTitle: TextStyle = {
  fontWeight: '500',
  fontSize: 16,
  lineHeight: 18,
  color: darkTheme.text.primary,
  marginTop: 40
};

const $loadingDesc: TextStyle = {
  fontWeight: '400',
  fontSize: 12,
  lineHeight: 16,
  color: darkTheme.text.placeholder,
  marginTop: 6,
  marginBottom: 40
};

const $progressTextContainer: ViewStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'rgba(0, 0, 0, 0.6)'
};

const $progressTextContainerNoRef: ViewStyle = {
  backgroundColor: 'rgba(255, 255, 255, 0.06)'
};

const $progressText: TextStyle = {
  fontWeight: '400',
  fontSize: 24,
  color: darkTheme.text.primary,
  fontFamily: typography.fonts.baba.heavy
};

const $bg: ImageStyle = {
  width: '100%',
  height: '100%',
  resizeMode: 'cover',
  position: 'absolute',
  top: 0,
  left: 0
};
