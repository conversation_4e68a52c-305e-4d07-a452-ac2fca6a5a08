import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import React from 'react';
import { ColorValue, Text, TextStyle, View, ViewStyle } from 'react-native';
import ImageColors from 'react-native-image-colors';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { KitchenSelection } from '@/src/bizComponents/kitchenScreen/KitchenSelection';
import { RefPhotoSection } from '@/src/bizComponents/kitchenScreen/RefPhotoSection';
import { SelectionList } from '@/src/bizComponents/kitchenScreen/SelectionList';
import { IconBack, Screen } from '@/src/components';
import { DynamicTakephotoIcon } from '@/src/components/icons/DynamicTakephotoIcon';
import { PrimaryButton } from '@/src/components/primaryButton';
import CreditWrapper from '@/src/components/v2/credit-wrapper';
import { useSafeBottomArea } from '@/src/hooks';
import { useKitchenStore } from '@/src/store/kitchen';
import { darkTheme, hex } from '@/src/theme';
import { GameType, InvokeType, RoleInfo } from '@/src/types';
import { dp2px } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { clearTimeoutWorklet, setTimeoutWorklet } from '@/src/utils/worklet';
import { useParams } from '../../src/hooks/useParams';
import { useShallow } from 'zustand/react/shallow';

const KitchenPhotoPreviewHeight = dp2px(224);
const KitchenDefaultSpace = dp2px(48);

function Kitchen() {
  const { select, refPhotos, currentRole } = useKitchenStore(
    useShallow(state => {
      const currentSelectRole: RoleInfo | undefined =
        state.selectRoles?.[state.selectPhoto];

      return {
        select: state.select,
        refPhotos: state.refPhotos,
        currentRole: currentSelectRole
      };
    })
  );

  const { source } = useParams<{ source?: string }>();

  const showPhotoSection = Boolean(refPhotos.length);

  const [expand, setExpand] = useState(false);
  const expandTransformY = useSharedValue(0);
  const timerSharedValue = useSharedValue(0);

  const [panelHeight, setPanelHeight] = useState(0);
  const [bgColor, setBgColor] = useState<ColorValue[] | undefined>();

  const safeBottom = useSafeBottomArea(30);

  const $panelStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: expandTransformY.value
        }
      ]
    };
  }, []);

  const onPressGenerate = useMemoizedFn(async () => {
    const changeRecord = useKitchenStore.getState().calcIsChange();
    reportClick('generate_button', {
      module: 'element_table',
      ...changeRecord
    });

    useKitchenStore.getState().generatePhoto();
    router.navigate({
      pathname: '/kitchen/result',
      params: {
        source: source || ''
      }
    });
  });

  const onPressExpand = useMemoizedFn(next => {
    if (expand !== next) {
      if (next) {
        setExpand(next);
      } else {
        // 等动画结束后改变状态
        setTimeoutWorklet(
          timerSharedValue,
          () => {
            setExpand(next);
          },
          300
        );
      }
      expandTransformY.value = withTiming(
        next ? -1 * KitchenPhotoPreviewHeight : 0
      );
    }
  });

  const getCurrentColor = useMemoizedFn((imgUrl: string) => {
    if (!imgUrl) return;
    ImageColors.getColors(imgUrl, {
      cache: true,
      key: imgUrl
    })
      .then(res => {
        // @ts-ignore
        const { platform, dominant, primary } = res || {};
        if (platform === 'android' && dominant) {
          setBgColor([dominant, hex(dominant, 0)]);
        } else if (primary) {
          setBgColor([primary, hex(primary, 0)]);
        }
      })
      .catch(err => {
        //
      });
  });

  useEffect(() => {
    useKitchenStore.getState().getAllFeatures();

    reportExpo('expo', {}, true);

    return () => {
      clearTimeoutWorklet(timerSharedValue);
    };
  }, []);

  useEffect(() => {
    if (select) {
      useKitchenStore.getState().getAllElementsFeatures();
    }
  }, [select]);

  useEffect(() => {
    if (refPhotos?.[0]?.url) {
      getCurrentColor(refPhotos[0].url);
    }
  }, [refPhotos]);

  return (
    <Screen
      theme="dark"
      wholePageStyle={{
        backgroundColor: darkTheme.background.page
      }}
      headerShown={false}
      onLayout={e => {
        setPanelHeight(e.nativeEvent.layout.height);
      }}
      safeAreaEdges={['top']}
      backgroundView={
        bgColor?.length ? (
          <LinearGradient
            colors={bgColor as string[]}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={$bgMask}
          />
        ) : null
      }
    >
      <IconBack color={'white'} containerStyle={$back} />
      {showPhotoSection ? (
        <RefPhotoSection />
      ) : (
        <View style={{ height: KitchenDefaultSpace }}></View>
      )}

      <Animated.View
        style={[
          $panelStyle,
          {
            zIndex: 10
          },
          !panelHeight
            ? { flex: 1 }
            : {
                height:
                  panelHeight -
                  (expand
                    ? 0
                    : showPhotoSection
                      ? KitchenPhotoPreviewHeight
                      : KitchenDefaultSpace) -
                  dp2px(50) -
                  safeBottom
              }
        ]}
      >
        <KitchenSelection
          expand={expand}
          updateExpand={onPressExpand}
          allowExpand={showPhotoSection}
        />
        <View
          style={[
            { flex: 1 },
            {
              overflow: 'hidden',
              backgroundColor: darkTheme.background.page
            }
          ]}
        >
          <SelectionList />
        </View>
      </Animated.View>
      <View
        style={[
          $bottomBar,
          {
            paddingBottom: safeBottom,
            zIndex: 11
          }
        ]}
      >
        <LinearGradient
          colors={['rgba(22, 22, 26, 0)', 'rgba(22, 22, 26, 1)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={$mask}
        />
        <CreditWrapper
          invokeType={InvokeType.INVOKE_FINETUNING_GEN}
          gameType={GameType.PHOTO_FINETUNING}
          ip={currentRole?.ip}
          signal={currentRole?.ip}
          useStore={false}
          buttonContainer={
            <PrimaryButton usingGradient={false} onPress={onPressGenerate}>
              <View style={$buttonWrapper}>
                <DynamicTakephotoIcon useAnimation={false} size={24} />
                <Text style={$buttonText}>立即生成</Text>
              </View>
            </PrimaryButton>
          }
        />
      </View>
    </Screen>
  );
}

export default Kitchen;

const $buttonWrapper: ViewStyle = {
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'center',
  alignItems: 'center'
};

const $buttonText: TextStyle = {
  fontWeight: '600',
  fontSize: 14,
  color: darkTheme.text.primary,
  marginLeft: 6
};

const $mask: ViewStyle = {
  height: 30,
  width: '100%',
  position: 'absolute',
  top: -30
};

const $bgMask: ViewStyle = {
  height: dp2px(175),
  width: '100%',
  position: 'absolute',
  top: 0,
  left: 0,
  opacity: 0.12
};

const $bottomBar: ViewStyle = {
  width: '100%',
  alignItems: 'center',
  pointerEvents: 'box-none',
  backgroundColor: darkTheme.background.page,
  position: 'absolute',
  bottom: 0
};

const $back: ViewStyle = {
  position: 'absolute',
  left: 6,
  top: 5,
  zIndex: 9
};
