import { router } from 'expo-router';
import { memo, useEffect, useState } from 'react';
import { getReEditInfo } from '@/src/api/livephoto';
import { otakudanceClient } from '@/src/api/otakudance';
import Publish, { Edit } from '@/src/bizComponents/livePhotoPublish';
import { TrackType } from '@/src/bizComponents/liveScreen/constants';
import { showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { BackGroundView } from '@/src/components/image/BackgroundImage';
import { useLiveStore } from '@/src/store/live';
import { useNormalGameStore } from '@/src/store/normalGame';
import { GameType } from '@/src/types';
import { reportExpo } from '@/src/utils/report';
import { useParams } from '../../src/hooks/useParams';
import { JellycatExtInfo } from '@/proto-registry/src/web/raccoon/common/jellycat_pb';
import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb';
import { Video } from '@/proto-registry/src/web/raccoon/common/media_pb';
import { OtakudanceExtInfo } from '@/proto-registry/src/web/raccoon/common/otakudance_pb';
import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
import { MagicFlowExtInfo } from '@/proto-registry/src/web/raccoon/magicflow/magicflow_common_pb';

const CODE_NOT_FOUND = 10002;
const BG_IMG = require('@Assets/image/video_publish_background.png');

const LivePhotoPublish = memo(() => {
  const { liveCardMap } = useLiveStore(s => ({
    liveCardMap: s.liveCardMap
  }));
  const {
    id = '',
    re_edit,
    cover,
    game_type
  } = useParams<Record<string, string>>();
  const [data, setData] = useState<{
    height: number;
    width: number;
    videoUrl: string;
    extData:
      | OtakudanceExtInfo
      | LivePhotoExtInfo
      | JellycatExtInfo
      | ReimagineExtInfo
      | MagicFlowExtInfo
      | undefined;
  }>();

  const [showVideo, setVideoShow] = useState(false);
  const [videoCover, setCover] = useState<string>();
  const [editInfo, setEditInfo] = useState<Edit>();
  const gameType =
    liveCardMap?.[id]?.game ||
    (Number(game_type) as GameType | undefined) ||
    GameType.LIVE_PHOTO;

  useEffect(() => {
    if (re_edit === '1') {
      if (gameType === GameType.LIVE_PHOTO) {
        getReEditInfo({
          cardId: id
        })
          .then(res => {
            setData({
              extData: res.extInfo,
              videoUrl: res.extInfo?.videoUrl || '',
              width: res.extInfo?.videoWidth || 0,
              height: res.extInfo?.videoHeight || 0
            });
            setEditInfo({
              title: res.title ?? '',
              content: res.content ?? '',
              bgm: getBgm(res.bgmId),
              topics: res.topics,
              saveToLocal: false
            });
            setCover([res.coverUrl]);
          })
          .catch(e => {
            handleNotFound(e);
          });
      } else if (gameType === GameType.OTAKUDANCE) {
        otakudanceClient
          .getVideoWork({
            cardId: id
          })
          .then(res => {
            setData({
              extData: res.danceInfo,
              videoUrl: res.danceInfo?.video?.url || '',
              width: (res.danceInfo?.video?.meta?.value as Video)?.width || 0,
              height: (res.danceInfo?.video?.meta?.value as Video)?.height || 0
            });
            setEditInfo({
              title: res.title ?? '',
              content: res.content ?? '',
              topics: res.topics,
              saveToLocal: false
            });
            setCover([res.cover?.url ?? '']);
          })
          .catch(e => {
            handleNotFound(e);
          });
      }
    } else {
      const gameType = liveCardMap?.[id]?.game ?? GameType.UNKNOWN;
      const extData = liveCardMap?.[id]?.extInfo?.value?.value;
      const isNormalGame = useNormalGameStore
        .getState()
        .enableGameTypes.includes(gameType);
      setVideoShow(gameType !== GameType.JELLYCAT && !isNormalGame);
      setData({
        extData: extData,
        videoUrl:
          (extData as OtakudanceExtInfo)?.video?.url ||
          (extData as LivePhotoExtInfo)?.videoUrl,
        width:
          ((extData as OtakudanceExtInfo)?.video?.meta?.value as Video)
            ?.width ||
          (extData as LivePhotoExtInfo)?.videoWidth ||
          (extData as JellycatExtInfo)?.compoundImgWidth ||
          0,
        height:
          ((extData as OtakudanceExtInfo)?.video?.meta?.value as Video)
            ?.height ||
          (extData as LivePhotoExtInfo)?.videoHeight ||
          (extData as JellycatExtInfo)?.compoundImgHeight ||
          0
      });
      setCover(liveCardMap?.[id]?.coverImgUrl ?? '');
    }
  }, [id, liveCardMap, gameType]);

  useEffect(() => {
    if (id) {
      reportExpo('page_preview', {
        module: 'publish',
        type: TrackType[gameType!] ?? '',
        game_type: gameType,
        content_type: showVideo ? 1 : 0,
        publish_page_type: 1
      });
    }
  }, [id]);
  if (!data) return null;
  return (
    <PagePerformance pathname="live-photo-publish/index">
      <BackGroundView image={BG_IMG}>
        <Publish
          showRandomBgm={re_edit !== '1'}
          {...data}
          cover={videoCover || cover}
          editInfo={editInfo}
          gameType={`${gameType}` as `${GameType}`}
          showVideo={showVideo}
        />
      </BackGroundView>
    </PagePerformance>
  );
  function handleNotFound(error: { code: number }) {
    if (error.code === CODE_NOT_FOUND) {
      setTimeout(() => {
        showToast('没找到该作品');
        router.back();
      }, 200);
    }
  }
  function getBgm(bgm: string) {
    if (!bgm) {
      return undefined;
    }
    return useLiveStore.getState().bgms.find(item => {
      return item.bgmId === bgm;
    });
  }
});

LivePhotoPublish.displayName = 'LivePhotoPublish';

export default LivePhotoPublish;
