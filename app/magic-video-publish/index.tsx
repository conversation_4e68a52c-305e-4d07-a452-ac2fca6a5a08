import { router } from 'expo-router';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { getPublishReimagine } from '@/src/api/magicvideo';
import MagicVideoPublishPod, {
  Edit
} from '@/src/bizComponents/magic-video/publish';
import { showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { BackGroundView } from '@/src/components/image/BackgroundImage';
import { useLiveStore } from '@/src/store/live';
import { useTopicStore } from '@/src/store/topic';
import { useMagicVideoEditStore } from '@/src/store/video-magic';
import { useMagicVideoStore } from '@/src/store/video-magic/feed';
import { TopicInfo } from '@/src/types';
import { uniqueArray } from '@/src/utils/opt/uniqueArray';
import { reportExpo } from '@/src/utils/report';
import { useParams } from '../../src/hooks/useParams';
import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
import { useShallow } from 'zustand/react/shallow';

const CODE_NOT_FOUND = 10002;
const BG_IMG = require('@Assets/image/video_publish_background.png');

const MagicVideoPublish = memo(() => {
  const {
    id = '',
    re_edit,
    video_cover: defaultCover,
    from_message,
    from_history,
    templte_type
  } = useParams<{
    id: string;
    re_edit?: string;
    from_message?: string;
    video_cover?: string;
    from_history?: string;
    templte_type?: string;
  }>();

  const { liveCardMap } = useLiveStore(
    useShallow(state => ({
      liveCardMap: state.liveCardMap
    }))
  );

  const { historyExtInfo } = useMagicVideoEditStore(
    useShallow(state => ({
      historyExtInfo: state.historyExtInfo
    }))
  );

  const [data, setData] = useState<ReimagineExtInfo>();
  const [videoCover, setVideoCover] = useState<string>();
  const [editInfo, setEditInfo] = useState<Edit>();

  const reGotParams = () => {
    getPublishReimagine({
      cardId: id
    })
      .then(res => {
        console.log(res, '=== get publish res');
        setData(res.extInfo);
        setEditInfo({
          title: res.title ?? '',
          content: res.content ?? '',
          bgm: getBgm(res.bgmId),
          topics: res.topics,
          saveToLocal: true
        });
      })
      .catch(e => {
        console.log(e, '=== get publish info error');
        handleNotFound(e);
      });
  };

  const [topics, setCurrentTopics] = useState<TopicInfo[]>([]);

  useEffect(() => {
    setEditInfo(info => {
      return {
        ...info,
        topics
      } as Edit;
    });
  }, [topics]);

  useEffect(() => {
    reportExpo('page_preview', {
      module: 'publish'
    });
    if (re_edit === '1') {
      reGotParams();
    } else if (from_message === '1') {
      setData(liveCardMap?.[id]?.extInfo?.value?.value as ReimagineExtInfo);
      setVideoCover(liveCardMap?.[id]?.coverImgUrl);
      // handleAutoTopics();
    } else if (from_history === '1') {
      setData(historyExtInfo as ReimagineExtInfo);
      setVideoCover(liveCardMap?.[id]?.coverImgUrl);
      // handleAutoTopics();
    } else {
      if (!liveCardMap?.[id]?.extInfo) {
        reGotParams();
      } else {
        setData(liveCardMap?.[id]?.extInfo?.value?.value as ReimagineExtInfo);
        // handleAutoTopics();
      }
    }
  }, [id, historyExtInfo]);

  useEffect(() => {
    useMagicVideoStore.getState().syncBgms();
  }, []);

  if (!data) return null;

  return (
    <PagePerformance pathname="magic-video-publish/index">
      <BackGroundView image={BG_IMG}>
        <MagicVideoPublishPod
          showRandomBgm={re_edit !== '1'}
          videoData={data}
          cover={videoCover || defaultCover}
          editInfo={editInfo}
          defaultBgm={data?.bgmInfo}
          isFromScript={Number(templte_type) === 1}
        />
      </BackGroundView>
    </PagePerformance>
  );
  function handleNotFound(error: { code: number }) {
    if (error.code === CODE_NOT_FOUND) {
      setTimeout(() => {
        showToast('没找到该作品');
        router.back();
      }, 200);
    }
  }
  function getBgm(bgm: string) {
    if (!bgm) {
      return undefined;
    }
    return useMagicVideoStore.getState().bgms.find(item => {
      return item.bgmId === bgm;
    });
  }
});

export default MagicVideoPublish;
