import { router } from 'expo-router';
import React, { useEffect } from 'react';
import { Image, View } from 'react-native';
import { TouchableHighlight } from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import { uploadAvatarImg } from '@/src/api';
import { hideLoading, showLoading } from '@/src/components';
import { Icon, Screen, showToast } from '@/src/components';
import { AvatarPendantRender } from '@/src/components/avatar/avatarPendant';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useDailyUpdateLimit } from '@/src/hooks/useDailyUpdateLimit';
import { useAuthStore } from '@/src/store/authInfo';
import { useEditPendantStore } from '@/src/store/edit-pendant';
import { useStorageStore } from '@/src/store/storage';
import { colors, rowStyle } from '@/src/theme';
import { reportClick, reportExpo } from '@/src/utils/report';
import { savePicture } from '@/src/utils/savePicture';
import { Text } from '@Components/text';
import { StyleSheet } from '@Utils/StyleSheet';
import { ReportError, errorReport } from '@Utils/error-log';
import { pickUpImage } from '@Utils/image';
import { useParams } from '../../src/hooks/useParams';
import { useShallow } from 'zustand/react/shallow';

const MOCK_IMG = require('@Assets/mock/img1.jpg');

export default function AvatarEdit() {
  const { user, updateUser } = useAuthStore(
    useShallow(state => ({
      user: state.userInfo,
      updateUser: state.updateUser
    }))
  );

  const { hasNewPendant, fetchUpNewStatus } = useEditPendantStore(
    useShallow(state => ({
      hasNewPendant: state.upNewStatus,
      fetchUpNewStatus: state.fetchUpNewStatus
    }))
  );

  const { checkAvatarUpdateLimit } = useDailyUpdateLimit(3, 3);
  const { isMine: isMineStr, avatar } = useParams<{
    isMine: string;
    avatar: string;
  }>();
  const isMine = isMineStr ? parseInt(isMineStr as string) : 1;

  useEffect(() => {
    fetchUpNewStatus();
    reportExpo('avatar_widget', { module: 'avatar' });
  }, []);

  const editPandentText = user?.pendant ? '更新头像挂件' : '设置头像挂件';

  // 保存图片
  const handleSaveImage = async () => {
    showLoading('头像保存中...');
    const saveUrl =
      (avatar as string) ||
      user?.avatar ||
      Image.resolveAssetSource(MOCK_IMG)?.uri;
    try {
      await savePicture(saveUrl);
      showToast('保存成功！');
    } catch (e) {
      errorReport('[save image err]', ReportError.AVATAR_EDIT, e);
      showToast('保存失败！');
    } finally {
      hideLoading();
    }
  };

  // 更新用户信息
  const updateUserInfo = async (image_id: string) => {
    try {
      await updateUser({ avatarImageId: image_id });
      showToast('更新成功~');
      router.back();
    } catch (e) {
      errorReport('[update userinfo error]', ReportError.AVATAR_EDIT, e);
      showToast('更新失败！');
    }
  };

  // 上传头像并更新用户信息
  const uploadAvatarAndUpdateUser = async (uri: string) => {
    try {
      showLoading('头像上传中...');
      const res = await uploadAvatarImg(uri);

      if (res) {
        await updateUserInfo(res?.image_id);
      }
    } catch (e) {
      showToast('上传失败！');
      errorReport('[upload avatar err]', ReportError.AVATAR_EDIT, e);
    } finally {
      hideLoading();
    }
  };

  // 选择头像图片
  const handlePickImage = () => {
    // === 检查当日是否还能更换头像 ===
    if (checkAvatarUpdateLimit()) {
      return;
    }

    pickUpImage({
      type: 'upload',
      options: {
        aspect: [1, 1],
        allowsEditing: true
      },
      onSuccess: assets => {
        const uri = assets[0].uri;
        uploadAvatarAndUpdateUser(uri);
      }
    });

    useStorageStore.getState().__setStorage({
      askedForMediaLibrary: true
    });
  };

  // 拍照
  const handleTakePhoto = () => {
    // === 检查当日是否还能更换头像 ===
    if (checkAvatarUpdateLimit()) {
      return;
    }

    pickUpImage({
      type: 'camera',
      options: {
        aspect: [1, 1],
        allowsEditing: true
      },
      onSuccess: assets => {
        const uri = assets[0].uri;
        uploadAvatarAndUpdateUser(uri);
      }
    });

    useStorageStore.getState().__setStorage({
      askedForCamera: true
    });
  };

  const handleEditPendant = () => {
    reportClick('avatar_widget', { module: 'avatar' });
    router.navigate('/avatar-edit/edit-pendant');
  };

  const pendentIconRender = () =>
    user?.pendant ? (
      <View>
        <Image
          source={user?.avatar ? { uri: user?.avatar } : MOCK_IMG}
          resizeMode="cover"
          style={{ width: 24, height: 24, borderRadius: 500 }}
        />
        <AvatarPendantRender url={user.pendant.pendantUrl} avatarSize={24} />
      </View>
    ) : (
      <Icon icon="pendant" />
    );

  return (
    <PagePerformance pathname="avatar-edit/index">
      <Screen
        title="头像预览"
        screenStyle={{
          backgroundColor: 'black',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          width: '100%'
        }}
        theme="dark"
      >
        <View
          style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
        >
          <View style={st.$avatar}>
            <Animated.Image
              source={user?.avatar ? { uri: avatar || user?.avatar } : MOCK_IMG}
              resizeMode="cover"
              style={{ width: '100%', height: '100%' }}
            />
          </View>
        </View>

        <View style={st.$bottomArea}>
          <TouchableHighlight onPress={handleSaveImage} activeOpacity={0.6}>
            <View style={st.$button}>
              <Text style={st.$buttonText}>保存头像</Text>
              <Icon icon="download" />
            </View>
          </TouchableHighlight>
          {isMine ? (
            <>
              <TouchableHighlight onPress={handlePickImage} activeOpacity={0.6}>
                <View style={st.$button}>
                  <Text style={st.$buttonText}>从相册中选择</Text>
                  <Icon icon="image_pick" />
                </View>
              </TouchableHighlight>
              <TouchableHighlight onPress={handleTakePhoto} activeOpacity={0.6}>
                <View style={[st.$button]}>
                  <Text style={st.$buttonText}>拍摄头像</Text>
                  <Icon icon="camera" />
                </View>
              </TouchableHighlight>
              <TouchableHighlight
                onPress={handleEditPendant}
                activeOpacity={0.6}
              >
                <View style={[st.$button, { borderWidth: 0 }]}>
                  <View style={[rowStyle, { gap: 6 }]}>
                    <Text style={st.$buttonText}>{editPandentText}</Text>
                    {hasNewPendant ? <Icon size={28} icon="up_new" /> : null}
                  </View>
                  {pendentIconRender()}
                </View>
              </TouchableHighlight>
            </>
          ) : null}
        </View>
      </Screen>
    </PagePerformance>
  );
}

const st = StyleSheet.create({
  $avatar: {
    ...StyleSheet.circleStyle,
    width: 315,
    height: 315,
    overflow: 'hidden'
  },

  $bottomArea: {
    display: 'flex',
    flexDirection: 'column',
    gap: 0,
    marginHorizontal: 30,
    borderRadius: 8,
    overflow: 'hidden'
  },

  $button: {
    backgroundColor: 'rgba(255, 255, 255, 0.12)',
    width: 315,
    height: 58,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    borderBottomColor: StyleSheet.hex(StyleSheet.currentColors.white, 0.08),
    borderWidth: 0.5
  },
  $buttonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600'
  }
});
