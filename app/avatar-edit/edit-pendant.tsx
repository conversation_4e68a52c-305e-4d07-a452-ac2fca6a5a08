import { useMemoizedFn, useThrottleFn } from 'ahooks';
import { router } from 'expo-router';
import { memo, useEffect, useMemo, useState } from 'react';
import {
  FlatList,
  Pressable,
  ScrollView,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import { Icon, Image, Screen, Text, showToast } from '@/src/components';
import { AvatarPendantRender } from '@/src/components/avatar/avatarPendant';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { PrimaryButton } from '@/src/components/primaryButton';
import { useScreenSize } from '@/src/hooks';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useOneRunning } from '@/src/hooks/useOneRunning';
import { useAuthStore } from '@/src/store/authInfo';
import { useEditPendantStore } from '@/src/store/edit-pendant';
import { centerStyle, flex1Style } from '@/src/theme';
import { PendantInfo } from '@/src/types';
import { dp2px } from '@/src/utils/dp2px';
import { reportClick, reportExpo } from '@/src/utils/report';
import {
  PendantType,
  RichPendantInfo
} from '@/proto-registry/src/web/raccoon/usersphereinfo/usersphereinfo_pb';
import type { PartialMessage } from '@bufbuild/protobuf';
import { useShallow } from 'zustand/react/shallow';

const EDIT_ITEM_BG = require('@Assets/user/edit-pendant-avatar-bg.png');

export default function EditPendant() {
  const { user } = useAuthStore(
    useShallow(state => ({
      user: state.userInfo
    }))
  );

  const {
    pendantList,
    updateMineWearPendant,
    refreshVisitStatus,
    fetchPendantList,
    deleteUserWearPendant
  } = useEditPendantStore(
    useShallow(state => ({
      pendantList: state.pendantList,
      updateMineWearPendant: state.updateMineWearPendant,
      refreshVisitStatus: state.refreshVisitStatus,
      fetchPendantList: state.fetchPendantList,
      deleteUserWearPendant: state.deleteUserWearPendant
    }))
  );

  const { run: throttledFetchList } = useThrottleFn(fetchPendantList, {
    wait: 3000,
    leading: true,
    trailing: false
  });

  const { width: screenWidth } = useScreenSize('screen');

  const { numColumns, itemGap } = useMemo(() => {
    const width = screenWidth - 32; // 去除页面padding
    const numColumns = width > dp2px(109) * 3 ? 3 : 2;
    return {
      numColumns,
      itemGap: (width - dp2px(109) * numColumns) / (numColumns - 1)
    };
  }, []);

  useEffect(() => {
    throttledFetchList();
    refreshVisitStatus();
    setTimeout(() => {
      reportExpo('widget', { module: 'avatarwidget' });
    }, 10);
  }, []);

  const [currentPendant, setCurrentPendant] = useState<
    PartialMessage<RichPendantInfo> | undefined
  >(user?.pendant);

  useEffect(() => {
    setCurrentPendant(user?.pendant);
  }, [user?.pendant]);

  const onPress = useMemoizedFn((data: RichPendantInfo) => {
    if (!data) return;
    reportClick('widget', {
      module: 'avatarwidget',
      widgetid: data.pendantId ?? '',
      widget_state: data.isAvaiable ? 1 : 2,
      widget_type:
        data.validityPeriodDesc === '永久' ? 1 : data.isExpire ? 3 : 2
    });
    if (data.isExpire) {
      showToast('挂件已过期');
      setCurrentPendant(undefined);
      return;
    }
    setCurrentPendant(data);
  });
  const onPrimaryPress = useOneRunning(
    useMemoizedFn(async () => {
      if (
        currentPendant &&
        currentPendant.isAvaiable &&
        currentPendant.pendantId
      ) {
        reportClick('widget_button', {
          module: 'avatarwidget',
          widget_button:
            currentPendant?.pendantId === user?.pendant?.pendantId ? 2 : 1
        });
        if (currentPendant?.pendantId === user?.pendant?.pendantId) {
          // 卸下接口
          setCurrentPendant(undefined);

          await deleteUserWearPendant(currentPendant?.pendantId);
        } else {
          await updateMineWearPendant(currentPendant?.pendantId);
        }
      } else {
        reportClick('widget_button', {
          module: 'avatarwidget',
          widget_button: 3
        });
        switch (currentPendant?.pendantType) {
          case PendantType.NEW_PUBLISH_CARD: {
            router.navigate('/publish');
            return;
          }
          default:
            return;
        }
      }
    })
  );

  // // to be deleted
  // const mockData = new Array(10).fill(null).map((_, index) => ({
  //   index,
  //   pendantName: '炖图新秀',
  //   validityPeriodDesc: '永久',
  //   isExpire: index > 5,
  //   pendantCondition: '添加脑洞少女发哦当年佛',
  //   pendantUrl:
  //     'https://resource.lipuhome.com/resource/img/test/20250119/aa285a5098cf78eac3d29e6313a8ee80.png'
  // }));

  const sortedPendantList = useMemo(() => {
    if (!pendantList) return [];
    const filterred = pendantList.filter(Boolean);
    const batch = filterred.filter(i => i.isAvaiable && !i.isExpire);
    const batch2 = filterred.filter(i => !i.isAvaiable && !i.isExpire);
    const batch3 = filterred.filter(i => i.isExpire);
    return [...batch, ...batch2, ...batch3];
  }, [pendantList]);

  return (
    <PagePerformance pathname="avatar-edit/edit-pendant">
      <Screen
        title="头像挂件"
        backgroundView={
          <Image
            source={
              'https://resource.lipuhome.com/resource/img/prod/20250119/55084269e6d711092a2e21633ed0801e.png'
            }
            style={{
              width: '100%',
              height: dp2px(375),
              position: 'absolute',
              top: 0,
              left: 0
            }}
          />
        }
        wholePageStyle={{
          backgroundColor: '#F9F9F9'
        }}
      >
        <View style={[centerStyle, { paddingHorizontal: 16, flex: 1 }]}>
          <View style={[centerStyle]}>
            <View style={{ marginVertical: 10 }}>
              <Image
                source={user?.avatar}
                style={{
                  width: 90,
                  height: 90,
                  borderRadius: 500,
                  zIndex: 0,
                  borderWidth: 2,
                  borderColor: 'rgba(255, 255, 255, 0.6)'
                }}
              />
              {currentPendant?.pendantUrl ? (
                <AvatarPendantRender
                  url={currentPendant?.pendantUrl}
                  avatarSize={88}
                />
              ) : null}
            </View>
            <Text
              style={[
                $nameStyle,
                {
                  fontSize: 16,
                  fontWeight: '600',
                  maxWidth: 260,
                  minHeight: 40,
                  textAlign: 'center'
                }
              ]}
            >
              {currentPendant
                ? currentPendant?.isAvaiable
                  ? currentPendant.pendantName
                  : currentPendant.pendantCondition
                : '暂未佩戴头像挂件'}
            </Text>
          </View>
          <View style={flex1Style}>
            <FlatList
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              data={sortedPendantList}
              numColumns={numColumns}
              scrollEventThrottle={200}
              onScroll={throttledFetchList}
              horizontal={false}
              keyExtractor={i => i && i.pendantId}
              columnWrapperStyle={{
                justifyContent: 'flex-start',
                paddingVertical: 8,
                gap: itemGap
              }}
              style={{ minWidth: dp2px(343) }}
              renderItem={({ item }) => (
                <ItemCard
                  data={item}
                  onPress={onPress}
                  isSelected={
                    item && currentPendant?.pendantId === item.pendantId
                  }
                  isWear={item && user?.pendant?.pendantId === item.pendantId}
                />
              )}
            />
          </View>
          {currentPendant ? (
            <PrimaryButton onPress={onPrimaryPress}>
              {currentPendant.isAvaiable
                ? currentPendant.pendantId === user?.pendant?.pendantId
                  ? '卸下'
                  : '佩戴'
                : currentPendant.buttonContent}
            </PrimaryButton>
          ) : null}
        </View>
      </Screen>
    </PagePerformance>
  );
}

interface ItemCardProps {
  data: RichPendantInfo;
  isWear: boolean;
  isSelected: boolean;
  onPress: (data: RichPendantInfo) => void;
}

const ItemCard = memo(
  ({ data, isSelected, isWear, onPress }: ItemCardProps) => (
    <Pressable
      style={[centerStyle, $itemContainer, isSelected ? $selected : null]}
      onPress={() => onPress(data)}
    >
      <View style={[{ opacity: data.isExpire ? 0.2 : 1 }]}>
        <Image
          source={EDIT_ITEM_BG}
          style={{ width: 66, height: 66, borderRadius: 500, zIndex: 0 }}
        />
        <AvatarPendantRender url={data.pendantUrl} avatarSize={66} />
      </View>
      <Text numberOfLines={1} ellipsizeMode={'tail'} style={$nameStyle}>
        {data.pendantName}
      </Text>
      <Text style={$periodStyle}>
        {data.isExpire ? '已过期' : data.validityPeriodDesc}
      </Text>
      {isWear ? (
        <View style={[centerStyle, $wearing]}>
          <Text style={$wearingTextStyle}>{'已佩戴'}</Text>
        </View>
      ) : null}
      {data.isAvaiable ? null : (
        <Icon containerStyle={$lock} size={14} icon="locked"></Icon>
      )}
    </Pressable>
  )
);

const $itemContainer: ViewStyle = {
  backgroundColor: '#fff',
  width: dp2px(109),
  height: dp2px(152),
  borderRadius: 12
};

const $selected: ViewStyle = {
  borderWidth: 1,
  borderColor: 'rgba(255, 106, 59, 1)'
};

const $lock: ViewStyle = {
  position: 'absolute',
  top: 8,
  right: 8
};

const $wearing: ViewStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  backgroundColor: 'rgba(255, 106, 59, 0.12)',
  borderTopLeftRadius: 12,
  borderBottomRightRadius: 8,
  width: 40,
  height: 20
};

const $wearingTextStyle: TextStyle = {
  color: 'rgba(255, 106, 59, 1)',
  fontSize: 9,
  fontWeight: '500'
};

const $nameStyle: TextStyle = {
  color: 'rgba(0, 0, 0, 0.87)',
  fontSize: 13,
  marginTop: 16,
  maxWidth: 85
};

const $periodStyle: TextStyle = {
  color: 'rgba(0, 0, 0, 0.4)',
  fontSize: 11,
  marginTop: 6
};
