import { router } from 'expo-router';
import { LegacyRef, useEffect, useMemo, useRef, useState } from 'react';
import { Alert, Platform } from 'react-native';
import { ABTest, ABTestEvent } from '@/src/api/abTest';
import { Webview, showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { registerNotification } from '@/src/components/v2/notification/register';
import { checkNotificationSuccess } from '@/src/components/v2/notification/util';
import { LOGIN_SCENE, SITE_URL } from '@/src/constants';
// import { LOGIN_SCENE } from '@/src/constants';
import { useAuthState } from '@/src/hooks';
import { useAuthStore } from '@/src/store/authInfo';
import { CommonColor } from '@/src/theme/colors/common';
import TBridge, { injectMethods } from '@/src/utils/bridge';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { reportExpo } from '@/src/utils/report';
import { savePicture } from '@/src/utils/savePicture';
import { Screen } from '@Components/screen';
import { useShallow } from 'zustand/react/shallow';

export default function WishCard() {
  // const SITE_URL = 'http://10.141.35.96:3000'; // for test
  const WISHCARD_URL = SITE_URL + '/wishcard';
  console.log(WISHCARD_URL, 'WISHCARD_URL===');

  const webviewRef = useRef(null);

  const bridge = new TBridge(webviewRef);

  /**
   * 发送 to web
   */
  const sendMessageToWeb = async () => {
    const token = useAuthStore.getState().token;
    bridge.sendMessageToWeb({
      type: 'INIT',
      payload: {
        userToken: token,
        hasNoti: await checkNotificationSuccess(),
        hasLogin: await getUserAuthState(),
        isNewUser: ABTest.variantMap?.[ABTestEvent.WISHCARD]
      }
    });
  };

  const { getUserAuthState, loginIntercept } = useAuthState();
  const { uid } = useAuthStore(
    useShallow(state => ({
      uid: state.uid
    }))
  );

  async function wishCardCallback() {
    console.log('login callback ===');
    const { token } = useAuthStore.getState();
    bridge.sendMessageToWeb({
      type: 'REFRESH_TOKEN',
      payload: {
        userToken: token,
        hasLogin: await getUserAuthState(),
        isNewUser: ABTest.variantMap?.[ABTestEvent.WISHCARD]
      }
    });
  }

  useEffect(() => {
    if (uid) {
      // 登录 callback
      wishCardCallback();
    }
  }, [uid]);

  useEffect(() => {
    bridge.setOnMessageFromWeb((msg: any) => {
      console.log('Received message from web:', msg);
      if (msg.type === 'ACTION_LOGIN') {
        // 执行登录
        loginIntercept(() => {}, { scene: LOGIN_SCENE.MAKE_WISH });
      }
      if (msg.type === 'ACTION_OPENNOTI') {
        // 执行跳转通知
        registerNotification(async () => {
          bridge.sendMessageToWeb({
            type: 'UPDATE',
            payload: {
              hasNoti: await checkNotificationSuccess()
            }
          });
        });
      }
    });
  }, []);

  /** 加载完成发送初始信息 */
  const loadEnd = () => {
    sendMessageToWeb();
  };

  const [commonArgs, setCommonArgs] = useState({});

  useEffect(() => {
    const getArgs = async () => {
      const token = useAuthStore.getState().token;
      setCommonArgs({
        userToken: token,
        hasNoti: await checkNotificationSuccess(),
        hasLogin: await getUserAuthState(),
        isNewUser: ABTest.variantMap?.[ABTestEvent.WISHCARD]
      });
    };
    getArgs();
  }, []);

  return (
    <PagePerformance pathname="wishcard/index">
      <Screen
        preset="auto"
        safeAreaEdges={['top']}
        screenStyle={{
          backgroundColor: CommonColor.white
        }}
        contentContainerStyle={{
          flex: 1
        }}
        style={{
          ...Platform.select({
            ios: {
              marginBottom: 6
            },
            android: {
              marginTop: 6
            }
          })
        }}
        title="许愿卡"
      >
        <Webview
          ref={webviewRef}
          url={WISHCARD_URL}
          onMessage={event => {
            bridge.receiveMessageFromWeb(event);

            // for android
            bridge.setOnMessageFromWeb((msg: any) => {
              console.log('Received message from web:', msg);
              if (msg.type === 'ACTION_LOGIN') {
                // 执行登录
                loginIntercept(() => {}, { scene: LOGIN_SCENE.MAKE_WISH });
              }
              if (msg.type === 'ACTION_SAVE_PIC') {
                // 执行保存
                savePicture(msg.url)
                  .then(res => {
                    showToast('保存成功');
                  })
                  .catch(e => {
                    errorReport('[save image err]', ReportError.WISHCARD, e);
                    showToast('保存失败');
                  });
              }
              if (msg.type === 'ACTION_SAVE_PIC') {
                // 执行保存
                savePicture(msg.url)
                  .then(res => {
                    showToast('保存成功');
                  })
                  .catch(e => {
                    errorReport('[save image err]', ReportError.WISHCARD, e);
                    showToast('保存失败');
                  });
              }
              if (msg.type === 'ACTION_OPENNOTI') {
                // 执行跳转通知
                registerNotification(async () => {
                  bridge.sendMessageToWeb({
                    type: 'UPDATE',
                    payload: {
                      hasNoti: await checkNotificationSuccess()
                    }
                  });
                });
              }
              if (msg.type === 'ACTION_CREATE_ROLE') {
                router.navigate({
                  pathname: '/role-create'
                });
              }
              if (msg.type === 'ACTION_REPORT_EVENT') {
                reportExpo(msg.element, msg.params, msg.reportType);
              }
            });
          }}
          injectedJavaScript={injectMethods}
          injectedJavaScriptObject={{
            type: 'INIT',
            payload: commonArgs
          }}
          onLoadEnd={loadEnd}
          javaScriptEnabled={true}
          domStorageEnabled={true}
        ></Webview>
      </Screen>
    </PagePerformance>
  );
}
