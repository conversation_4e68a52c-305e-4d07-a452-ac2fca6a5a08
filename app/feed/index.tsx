import { useMemoizedFn } from 'ahooks';
import dayjs from 'dayjs';
import { Redirect, router, useNavigation } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import React from 'react';
import { Pressable, TouchableOpacity, View, ViewStyle } from 'react-native';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import {
  ABTest,
  ABTestEvent,
  FirstTriggerType,
  showNoviceGuideByServer
} from '@/src/api/abTest';
import { Socket } from '@/src/api/websocket';
import ToastInner from '@/src/bizComponents/credit/toast';
import { FeedScreen } from '@/src/bizComponents/feedScreen';
import { PureHomePage } from '@/src/bizComponents/feedScreen/PureHomePage';
import { FeedGuideBubble } from '@/src/bizComponents/feedScreen/guideBubble';
import {
  FeedBubbleEvent,
  FeedBubbleEventBus
} from '@/src/bizComponents/feedScreen/guideBubble/event';
import { NewTopicBubble } from '@/src/bizComponents/feedScreen/newTopicBubble';
import { showPublish } from '@/src/bizComponents/globalPublish';
import {
  CustomMessageTabItem,
  CustomProfileTabItem,
  TabItem
} from '@/src/bizComponents/homeBottomTab/BottomTabItem';
import { MessageScreen } from '@/src/bizComponents/messageScreen';
import { useControlSplash } from '@/src/bizComponents/splashScreen/useControlSplash';
import TopicScreen from '@/src/bizComponents/topicScreen';
import Trending from '@/src/bizComponents/trendingScreen';
import { UserScreen } from '@/src/bizComponents/userScreen';
import { UserPageTab } from '@/src/bizComponents/userScreen/constants';
import { showToast } from '@/src/components';
import { useGuideToMakePhotoStatus } from '@/src/components/guide/guide-content/make-photo-guide';
import { showAfterPublishPopups } from '@/src/components/popup/activityModal';
import { PublishEntry } from '@/src/components/publishEntry';
import { SkinnedImage } from '@/src/components/skin/SkinnedImage';
import { CONFIG_KYES } from '@/src/components/skin/getSkinConfig';
import { XiaoliToast } from '@/src/components/toast/XiaoliToast';
import { BOTTOM_TAB_HEIGHT } from '@/src/constants';
import { useSafeBottomArea } from '@/src/hooks';
import { Go2HomeScene } from '@/src/hooks/useChangeRoute';
import { useParams } from '@/src/hooks/useParams';
import { useTeenModeGuard } from '@/src/hooks/useTeenModeGuard';
import { useAppStore } from '@/src/store/app';
import { useAuthStore } from '@/src/store/authInfo';
import { useBehaviorStore } from '@/src/store/behavior';
import { SwitchName, useControlStore } from '@/src/store/control';
import { useHomePageTabStore } from '@/src/store/homePageTab';
import { useStorageStore } from '@/src/store/storage';
import { VideoFlowCommentContext } from '@/src/store/video-flow-comment';
import { useWelfareStore } from '@/src/store/welfare';
import { colorsUI } from '@/src/theme';
import { TabItemType } from '@/src/types';
import { dp2px, isIos } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { CommonEventBus } from '@/src/utils/event';
import {
  reportClick,
  reportExpo,
  setCurrentNavigatorTab,
  setPageName
} from '@/src/utils/report';
import { clearTimeoutWorklet, setTimeoutWorklet } from '@/src/utils/worklet';
import { usePerformanceStore } from '../../src/store/performance';
import { PortalHost } from '@gorhom/portal';
import { BlurView } from '@react-native-community/blur';
import {
  BottomTabBarProps,
  createBottomTabNavigator
} from '@react-navigation/bottom-tabs';
import { RouteProp, useIsFocused, useRoute } from '@react-navigation/native';
import { useNavigation as useRNNavigation } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';

const Tab = createBottomTabNavigator();
const HOME_PORTAL_NAME = 'HomePortalHost';

type FeedRouteParams = {
  tab?: TabItemType;
  tabUpdateTimestamp?: string;
  scene?: Go2HomeScene;
  appendId?: string;
  appendImageUrl?: string;
  appendGameType?: string;
  isAsyncAppend?: string;
  needSaveVideo?: string;
  feedPageTab?: string;
  profilePageTab?: string;
  feedRefresh?: string;
  profileRefresh?: string;
  feedUpdateTimestamp?: string;
  profileUpdateTimestamp?: string;
  flowAppendId?: string;
  flowAppendImageUrl?: string;
  flowAppendGameType?: string;
  flowIsAsyncAppend?: string;
  flowNeedSaveVideo?: string;
  flowPageTab?: string;
  flowUpdateTimestamp?: string;
};

let initialCount = 0;

export function NewFeed() {
  const params = useParams<FeedRouteParams>();

  const enabledHomeFeature = useControlStore(
    useShallow(
      state => !state.checkIsOpen(SwitchName.DISABLE_ALL_HOME_PAGE_FEATURE)
    )
  );

  const { isTeenMode } = useTeenModeGuard();
  const { immersiveTab } = useBehaviorStore(
    useShallow(state => ({ immersiveTab: state.immersiveTab }))
  );

  useEffect(() => {
    if (
      params.profileUpdateTimestamp &&
      params.profilePageTab === UserPageTab.SECRET &&
      params.profileRefresh === '1' &&
      params.scene === Go2HomeScene.VIDEO_GENERATING
    ) {
      FeedBubbleEventBus.emit(FeedBubbleEvent.SHOW_BUBBLE);
    }
  }, [params.profileUpdateTimestamp]);

  useEffect(() => {
    if (params.scene === Go2HomeScene.PUBLISH) {
      showAfterPublishPopups();
    }
  }, [params.scene, params.tabUpdateTimestamp]);

  return (
    <>
      <Tab.Navigator
        initialRouteName={params.tab || TabItemType.HOME}
        tabBar={props => (
          <MyTabBar
            initialRouteName={params.tab || TabItemType.HOME}
            {...props}
          />
        )}
      >
        <Tab.Screen options={{ headerShown: false }} name={TabItemType.HOME}>
          {props =>
            enabledHomeFeature && !isTeenMode ? (
              <FeedScreen
                appendId={params.appendId}
                appendImageUrl={params.appendImageUrl}
                appendGameType={params.appendGameType}
                isAsyncAppend={params.isAsyncAppend}
                needSaveVideo={params.needSaveVideo}
                pageTab={params.feedPageTab}
                refresh={params.feedRefresh}
                timestamp={params.feedUpdateTimestamp}
                {...props}
              />
            ) : (
              <PureHomePage
                pageTab={params.feedPageTab}
                refresh={params.feedRefresh}
                timestamp={params.feedUpdateTimestamp}
                {...props}
              />
            )
          }
        </Tab.Screen>
        <Tab.Screen options={{ headerShown: false }} name={TabItemType.TOPIC}>
          {() =>
            immersiveTab ? (
              <VideoFlowCommentContext>
                <Trending
                  appendId={params.flowAppendId}
                  appendImageUrl={params.flowAppendImageUrl}
                  appendGameType={params.flowAppendGameType}
                  isAsyncAppend={params.flowIsAsyncAppend}
                  needSaveVideo={params.flowNeedSaveVideo}
                  pageTab={params.flowPageTab}
                  timestamp={params.flowUpdateTimestamp}
                />
              </VideoFlowCommentContext>
            ) : (
              <TopicScreen />
              // <TopicScreen />
            )
          }
        </Tab.Screen>
        <Tab.Screen options={{ headerShown: false }} name={TabItemType.MESSSGE}>
          {() => <MessageScreen isRootPage={true} />}
        </Tab.Screen>
        <Tab.Screen options={{ headerShown: false }} name={TabItemType.PROFILE}>
          {() => (
            <UserScreen
              isRootPage={true}
              pageTab={params.profilePageTab}
              refresh={params.profileRefresh}
              timestamp={params.profileUpdateTimestamp}
            />
          )}
        </Tab.Screen>
      </Tab.Navigator>
      <PortalHost name={HOME_PORTAL_NAME} />
    </>
  );
}

export default NewFeed;

interface MyTabBarPros extends BottomTabBarProps {
  initialRouteName?: TabItemType;
}

function MyTabBar({
  state,
  descriptors,
  navigation,
  initialRouteName
}: MyTabBarPros) {
  const bottom = useSafeBottomArea(20);
  const params = useParams<{
    expand?: string;
    tab?: TabItemType;
    tabUpdateTimestamp?: string;
  }>();

  const { isTabVisible, currentTab, setCurrentTab, resetPageNameConfig } =
    useHomePageTabStore(
      useShallow(state => ({
        isTabVisible: state.isVisible,
        currentTab: state.currentTab,
        setCurrentTab: state.setCurrentTab,
        resetPageNameConfig: state.resetPageNameConfig
      }))
    );
  const lastPressTab = useRef<TabItemType | undefined>(initialRouteName);
  const initialCountRef = useRef<number | undefined>();
  const isFocused = useIsFocused();

  const bottomHeight = BOTTOM_TAB_HEIGHT + bottom;

  // 创作引导，优先级最高
  const [showGuide, setShowGuide] = useState(false);
  const { isTeenMode, teenModeGuard } = useTeenModeGuard();

  // 折叠状态
  const $unfold = useSharedValue(1);

  useEffect(() => {
    $unfold.value = withTiming(isTabVisible ? 1 : 0, {
      duration: 300,
      easing: Easing.inOut(Easing.quad)
    });
  }, [isTabVisible]);

  const $foldStyle = useAnimatedStyle(() => ({
    bottom: ($unfold.value - 1) * bottomHeight,
    opacity: $unfold.value
  }));

  useEffect(() => {
    Socket.events.on(
      ABTestEvent.ABTEST_FETCHED,
      () => {
        showNoviceGuideByServer({
          triggerType: FirstTriggerType.DRAWING_FEED_SLIDE,
          sceneId: 'newUserLanding',
          variant: 'creationGuide',
          controlGroupEffected: true,
          // 未命中引导创作实验
          failCallback: () => {
            setShowGuide(true);
          }
        });
      },
      true
    );
  }, []);

  const onPress = useMemoizedFn((route: any, index: number) => {
    const t = Date.now();
    if (route.name !== TabItemType.HOME && !teenModeGuard()) {
      return;
    }
    // 收集需要执行的函数
    const pendingFunctions: (() => void)[] = [];

    const runAfter = (fn: () => void) => {
      pendingFunctions.push(fn);
    };

    runAfter(() => {
      const { immersiveTab } = useBehaviorStore.getState();
      reportClick('down_tab', {
        tabname:
          route.name === TabItemType.TOPIC && immersiveTab ? 'hot' : route.name,
        module: 'feed'
      });
    });
    const isFocused = state.index === index;
    lastPressTab.current = route.name;

    if (!isFocused) {
      // 提前更新pathname，防止弹窗不出现或者埋点上报不准确。
      setCurrentNavigatorTab(route.name);
      // setPageName('feed');
      navigation.navigate(route.name, route.params);
      runAfter(() => {
        setCurrentTab(route.name);
        CommonEventBus.emit('tabBarPressed', {
          tab: route.name
        });
      });
    }
    if (isFocused) {
      CommonEventBus.emit('tabBarPressedWhenFocus', {
        tab: route.name
      });
    }

    // 在 RAF 中一起运行收集的函数
    if (pendingFunctions.length > 0) {
      requestAnimationFrame(() => {
        pendingFunctions.forEach(fn => fn());
        const end = Date.now();
        usePerformanceStore.getState().report('tabBarPressed', {
          duration: end - t
        });
      });
    }
  });

  useEffect(() => {
    const { immersiveTab } = useBehaviorStore.getState();
    if (isTabVisible) {
      reportExpo('down_tab', {
        module: 'feed',
        tabname:
          currentTab === TabItemType.TOPIC && immersiveTab
            ? 'hot'
            : TabItemType.TRENDING
      });
    }
  }, [isTabVisible]);

  // 通过 store 状态触发切换 tab
  useEffect(() => {
    if (isFocused && lastPressTab.current !== currentTab) {
      navigation.navigate(currentTab);
      lastPressTab.current = currentTab;
      CommonEventBus.emit('tabBarPressed', {
        tab: currentTab
      });
    }
  }, [currentTab, isFocused]);

  // 通过 url 参数触发切换 tab
  useEffect(() => {
    const storeTab = useHomePageTabStore.getState().currentTab;
    if (params.tab && params.tab !== lastPressTab.current) {
      navigation.navigate(params.tab);
      lastPressTab.current = params.tab;
      setCurrentTab(params.tab);
      CommonEventBus.emit('tabBarPressed', params);
    } else if (params.tab && params.tab !== storeTab) {
      setCurrentTab(params.tab);
    }
  }, [params.tabUpdateTimestamp]);

  const { uid } = useAuthStore(
    useShallow(state => ({
      uid: state.uid
    }))
  );

  const { signBoardInfo } = useWelfareStore(
    useShallow(state => ({
      signBoardInfo: state.signBoardInfo
    }))
  );

  const { isSplashPlayEnd } = useAppStore(
    useShallow(state => ({
      isSplashPlayEnd: state.isSplashPlayEnd
    }))
  );

  const { checkIsOpen } = useControlStore(
    useShallow(state => ({
      checkIsOpen: state.checkIsOpen
    }))
  );

  useEffect(() => {
    // 首次登录 + tab === 'feed' & today not checked 出现
    const todayFilterIndex =
      (signBoardInfo.task?.processes || [])?.findLastIndex(item => {
        return Number(item.doneAt) > 0;
      }) || 0;
    const todayCheckedTime =
      signBoardInfo?.task?.processes[todayFilterIndex]?.doneAt || 0;
    const hasTodayChecked =
      !!todayCheckedTime &&
      Number(todayCheckedTime) >= dayjs(Date.now()).startOf('day').valueOf();

    if (
      currentTab === 'feed' &&
      uid &&
      !hasTodayChecked &&
      isSplashPlayEnd &&
      signBoardInfo?.task &&
      !checkIsOpen(SwitchName.ENABLE_BATTERY_TASK)
    ) {
      useWelfareStore.getState().showSignModal();
    }
  }, [currentTab, uid, signBoardInfo, isSplashPlayEnd]);

  useEffect(() => {
    // 页面初始化时重置，避免app在后台放置一段时间后唤起时页面组件再次初始化
    const lastTab = resetPageNameConfig();
    if (lastTab !== lastPressTab.current) {
      navigation.navigate(lastTab);
      lastPressTab.current = params.tab;
    }

    initialCountRef.current = initialCount;
    initialCount++;

    if (initialCount > 0) {
      errorReport('feed_abnormal_init', ReportError.FEED, undefined, {
        initialCount
      });
    }
  }, []);

  return (
    <View
      style={{
        flexDirection: 'row'
      }}
    >
      <Animated.View style={[$bottomTabContainer, $foldStyle]}>
        <View
          style={[
            $bottomTabWrapper,
            {
              height: bottomHeight,
              paddingBottom: bottom
            }
          ]}
        >
          <SkinnedImage type={CONFIG_KYES.tabBarBgImage} />

          <FeedGuideBubble />
          <View>
            <TabItem
              preset={TabItemType.HOME}
              active={state.index === 0}
              onClick={() => onPress(state.routes[0], 0)}
            />
          </View>

          <View>
            {/* 若是青少年模式就不渲染 NewTopicBubble */}
            {!isTeenMode && (
              <NewTopicBubble
                tabActive={state.index === 1}
                showGuide={showGuide}
              />
            )}
            <TabItem
              preset={
                useBehaviorStore.getState().immersiveTab
                  ? TabItemType.TRENDING
                  : TabItemType.TOPIC
              }
              active={state.index === 1}
              onClick={() => onPress(state.routes[1], 1)}
            />
          </View>
          <View>
            <PublishEntry
              showGuide={showGuide}
              expandFlag={params?.expand}
              // position={[0, dp2px(-10), dp2px(-20), 0]}
              // style={{ width: dp2px(80), height: dp2px(98) }}
              portalProps={{ hostName: HOME_PORTAL_NAME }}
              afterClick={() => {
                reportClick('down_tab', {
                  tabname: 'publish',
                  module: 'feed'
                });
              }}
            />
          </View>
          <CustomMessageTabItem
            active={state.index === 2}
            onClick={() => onPress(state.routes[2], 2)}
          />
          <CustomProfileTabItem
            active={state.index === 3}
            onClick={() => onPress(state.routes[3], 3)}
          />
        </View>
      </Animated.View>
    </View>
  );
}

const $bottomTabContainer: ViewStyle = {
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  zIndex: 9
};

const $bottomTabWrapper: ViewStyle = {
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'flex-end',
  paddingTop: dp2px(8),
  justifyContent: 'space-between',
  paddingHorizontal: dp2px(25)
};

const $bgContainer: ViewStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: -1,
  borderTopLeftRadius: dp2px(25),
  borderTopRightRadius: dp2px(25),
  overflow: 'hidden'
};

const $blurContainer: ViewStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(255, 255, 255, 0.95)'
};

const $opacityContainer: ViewStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(255, 255, 255, 0.98)'
};
