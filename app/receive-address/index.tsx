import { useDebounceFn } from 'ahooks';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  TextInput,
  View
} from 'react-native';
import { lotteryFissionClient } from '@/src/api/lotteryFission';
import { fillInRecipientInfo, rewardClient } from '@/src/api/reward';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import { fillShippingAddress } from '@/src/api/welfare';
import { ERewardHistoryPage } from '@/src/bizComponents/historyRecord/const';
import { showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { FortuneEvent, FortuneEventBus } from '@/src/components/fortune/event';
import { showConfirm } from '@/src/components/popup/confirmModalGlobal/Confirm';
import Button, { EButtonType } from '@/src/components/v2/button';
import { StyleSheet } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { reportClick, reportExpo } from '@/src/utils/report';
import { Icon } from '@Components/icons';
import { Screen } from '@Components/screen';
import { Text } from '@Components/text';
import { useParams } from '../../src/hooks/useParams';
import { ShippingInfo } from '@/proto-registry/src/web/raccoon/bonus/bonus_pb';
import { RecipientInfo } from '@/proto-registry/src/web/raccoon/reward/common_pb';
import { FillInRecipientInfoReq } from '@/proto-registry/src/web/raccoon/reward/reward_pb';

export default function ReceiveAddress() {
  const [recipientName, setRecipientName] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');
  const { raffleRecordId = '', rewardId = '', page } = useParams();

  const phoneReg = /^1\d{10}$/;

  const isFilled =
    recipientName.trim() !== '' &&
    phoneReg.test(phone.trim()) && // 正则校验
    address.trim() !== '';

  useEffect(() => {
    reportExpo('draw_page_address', { module: 'feed' });
  }, []);

  const originAddressSubmit = async () => {
    try {
      await rewardClient.fillInRecipientInfo({
        recipientInfo: {
          recipientName,
          phone,
          address
        },
        raffleRecordId: raffleRecordId as string
      });
      showToast('填写成功，坐等发货哩~');
      router.back();
      FortuneEventBus.emit(FortuneEvent.SAVE_ADDRESS_SUCCESS, {
        raffleRecordId
      });
    } catch (error) {
      showToast((error as ErrorRes).reason || '填写失败，请重新填写~');
    }
  };

  const fissionAddressSubmit = async () => {
    try {
      await lotteryFissionClient.submitRecipientInfo({
        rewardId: (rewardId as string) || '',
        recipientInfo: {
          recipientName: recipientName,
          phoneNum: phone,
          address: address
        }
      });
      showToast('填写成功，坐等发货哩～');
      router.navigate({
        pathname: '/lottery-fission',
        params: {
          addressDone: 1
        }
      });
    } catch (error) {
      console.log(error, 'lottery_fission save address error');
      showToast((error as ErrorRes).reason || '填写失败，请重新填写~');
      errorReport('lottery_fission save address error', ReportError.REQUEST);
    }
  };

  const welfareAddressSubmit = async () => {
    const res = await fillShippingAddress({
      recordId: (rewardId as string) || '',
      shippingInfo: {
        recipientName: recipientName,
        phone: phone,
        address: address
      } as ShippingInfo
    });
    if (res?.record?.shippingInfo?.address) {
      showToast('填写成功，坐等发货哩～');
      router.back();
    } else {
      showToast('填写失败，请重新填写~');
    }
  };

  const $submitFn = useDebounceFn(async () => {
    if (rewardId) {
      if (page === ERewardHistoryPage.INVITATION) {
        fissionAddressSubmit();
      }
      if (page === ERewardHistoryPage.WELFARE) {
        welfareAddressSubmit();
      }
    } else {
      originAddressSubmit();
    }
  });

  // 点击确认地址
  const onConfirmAddress = () => {
    reportClick('draw_page_address', {
      module: 'feed',
      type: 3
    });
    if (!isFilled) {
      showToast('您还没有填写完信息哩～');
      return;
    }
    // 如果都填写完整了，则执行后续逻辑
    showConfirm({
      title: '确定填写该地址吗？',
      content: '填写地址后，将无法修改哦',
      onConfirm: ({ close }) => {
        $submitFn.run();
        close();
      },
      confirmText: '确认'
    });
  };

  return (
    <PagePerformance pathname="receive-address/index">
      <Screen
        theme="dark"
        title="收货地址"
        preset="auto"
        safeAreaEdges={['top', 'bottom']}
        style={styles.screen}
        backgroundView={
          <View style={[styles.background, StyleSheet.absoluteFill]} />
        }
        onBack={() => {
          router.back();
          reportClick('draw_page_address', {
            module: 'feed',
            type: 4
          });
        }}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          {/* 收件人容器 */}
          <View style={styles.singleLineContainer}>
            <Text style={styles.labelText}>
              收件人
              <Text style={styles.requiredStar}>*</Text>
            </Text>

            <View style={{ width: 12 }} />

            <TextInput
              placeholder="请输入收件人姓名"
              placeholderTextColor="#FFFFFF4D"
              value={recipientName}
              onChangeText={setRecipientName}
              style={styles.inputField}
              onFocus={() => {
                reportClick('draw_page_address', {
                  module: 'feed',
                  type: 0
                });
              }}
            />

            <Pressable
              onPress={() => setRecipientName('')}
              style={styles.clearButtonAbsolute}
            >
              <Icon icon="close_dark_fill" size={20} />
            </Pressable>
          </View>

          <View style={styles.spacing16} />

          {/* 手机号容器 */}
          <View style={styles.singleLineContainer}>
            <Text style={styles.labelText}>
              手机号
              <Text style={styles.requiredStar}>*</Text>
            </Text>

            <View style={{ width: 12 }} />

            <TextInput
              placeholder="请输入正确的手机号"
              placeholderTextColor="#FFFFFF4D"
              keyboardType="number-pad"
              maxLength={11}
              value={phone}
              onChangeText={setPhone}
              style={styles.inputField}
              onFocus={() => {
                reportClick('draw_page_address', {
                  module: 'feed',
                  type: 1
                });
              }}
            />

            <Pressable
              onPress={() => setPhone('')}
              style={styles.clearButtonAbsolute}
            >
              <Icon icon="close_dark_fill" size={20} />
            </Pressable>
          </View>

          <View style={styles.spacing16} />

          {/* 地址（大区域）容器：多行输入 */}
          <View style={styles.addressContainer}>
            <View style={styles.addressContent}>
              <Text style={styles.labelText}>
                地址
                <Text style={styles.requiredStar}>*</Text>
              </Text>
              <View style={{ width: 26 }} />
              <TextInput
                placeholder="请输入收件人地址"
                placeholderTextColor="#FFFFFF4D"
                multiline
                textAlignVertical="top"
                value={address}
                onChangeText={setAddress}
                style={styles.addressInputField}
                onFocus={() => {
                  reportClick('draw_page_address', {
                    module: 'feed',
                    type: 2
                  });
                }}
              />
            </View>
            <Pressable
              onPress={() => setAddress('')}
              style={styles.addressClearButton}
            >
              <Icon icon="close_dark_fill" size={20} />
            </Pressable>
          </View>

          <View style={styles.spacing16} />
        </ScrollView>

        {/* 底部固定按钮 */}
        <View style={styles.bottomFixed}>
          <Button
            type={EButtonType.LINEAR}
            style={[styles.bottomButton, { opacity: isFilled ? 1 : 0.5 }]}
            $customBtnTextStyle={styles.bottomButtonText}
            onPress={onConfirmAddress}
          >
            确认地址
          </Button>
        </View>
      </Screen>
    </PagePerformance>
  );
}

const styles = StyleSheet.create({
  keyboardAvoiding: {
    flex: 1
  },
  screen: {
    flex: 1
  },
  background: {
    backgroundColor: 'rgba(22, 22, 26, 1)'
  },
  scrollContainer: {
    padding: 16,
    alignItems: 'center'
  },
  spacing16: {
    height: 16
  },
  singleLineContainer: {
    width: 343,
    height: 50,
    backgroundColor: 'rgba(255,255,255,0.04)',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    paddingLeft: 16,
    paddingRight: 16
  },
  labelText: {
    fontFamily: 'PingFang SC',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 22,
    letterSpacing: 0,
    color: '#FFFFFFE5'
  },
  requiredStar: {
    color: 'red'
  },
  inputField: {
    flex: 1,
    marginRight: 36,
    fontFamily: 'PingFang SC',
    fontWeight: '500',
    fontSize: 14,
    letterSpacing: 0,
    color: '#FFFFFFE5',
    textAlignVertical: 'center',
    // (容器高度 50 - 行高 22) / 2 = 14
    paddingTop: (50 - 22) / 2,
    paddingBottom: (50 - 22) / 2
  },

  clearButtonAbsolute: {
    position: 'absolute',
    right: 16,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center'
  },
  addressContainer: {
    width: 343,
    height: 138,
    backgroundColor: 'rgba(255,255,255,0.04)',
    borderRadius: 8,
    paddingVertical: 14,
    paddingHorizontal: 16,
    position: 'relative'
  },
  addressContent: {
    width: 311,
    height: 88,
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  addressInputField: {
    flex: 1,
    fontFamily: 'PingFang SC',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 22,
    letterSpacing: 0,
    color: '#FFFFFFE5',
    textAlignVertical: 'top',
    paddingTop: 0,
    paddingBottom: 0
  },
  addressClearButton: {
    position: 'absolute',
    right: 16,
    bottom: 14,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center'
  },
  bottomFixed: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    alignItems: 'center'
  },
  bottomButton: {
    width: 335,
    height: 44,
    borderRadius: 47,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 0
  },
  bottomButtonText: {
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 14,
    lineHeight: 19.6,
    letterSpacing: 0,
    textAlign: 'center',
    color: '#FFFFFF'
  }
});
