import { useMemoizedFn } from 'ahooks';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import {
  getDecisionInstanceRankingList,
  getLastPeriodDecisionInstanceResultInfo
} from '@/src/api/decision';
import { decisionImages } from '@/src/bizComponents/decision/constants';
import RankList from '@/src/bizComponents/decision/rank';
import { useDecisionRankGo } from '@/src/bizComponents/decision/rank/decisionRankGo.hook';
import { Image, Screen } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { ReportError, errorReport } from '@Utils/error-log';
import { useParams } from '../../src/hooks/useParams';
import { Player } from '@/proto-registry/src/web/raccoon/instance/instance_pb';

export default function DecisionRankList() {
  const { id: instanceId, name } = useParams();

  const title = name ? `${name} · 排行榜` : '排行榜';

  const [loading, setLoading] = useState(true);
  const [rankingData, setRankingData] = useState<{
    tops: Player[];
    own?: Player;
  } | null>(null);

  // 存储上期信息和是否已领取奖励
  const [lastPeriodInfo, setLastPeriodInfo] = useState<{
    own?: {
      profile?: {
        avatar?: string;
        name?: string;
      };
      rank: number;
      score: number;
      reward: number;
    };
    claimed?: boolean;
  } | null>(null);

  const fetchData = useMemoizedFn(async () => {
    if (!instanceId) return;
    try {
      // 并行请求当前周期排名数据和上期信息数据
      const [currentRankingRes, lastPeriodRes] = await Promise.all([
        getDecisionInstanceRankingList({ instanceId: String(instanceId) }),
        getLastPeriodDecisionInstanceResultInfo({
          instanceId: String(instanceId)
        })
      ]);

      setRankingData({
        tops: currentRankingRes.tops,
        own: currentRankingRes.own
      });

      setLastPeriodInfo({
        own: lastPeriodRes.own,
        claimed: lastPeriodRes.claimed
      });
    } catch (error) {
      errorReport('获取数据失败', ReportError.DECISION, error, { instanceId });
    } finally {
      setLoading(false);
    }
  });

  useEffect(() => {
    fetchData();
  }, [instanceId, fetchData]);

  // if (loading) {
  //   return (
  //     <Screen
  //       theme="dark"
  //       screenStyle={styles.screenDark}
  //       backgroundView={<View style={styles.loadingBg} />}
  //       safeAreaEdges={['top']}
  //       title={title}
  //     >
  //       <ActivityIndicator color="#fff" />
  //     </Screen>
  //   );
  // }

  const backgroundView = (
    <Image source={decisionImages.RANK_BG} style={styles.bgImage} />
  );

  return (
    <PagePerformance pathname="decision-rank/[id]">
      <Screen
        theme="dark"
        screenStyle={styles.screenTransparent}
        safeAreaEdges={['top']}
        title={title}
        headerStyle={styles.headerTransparent}
        backgroundView={backgroundView}
      >
        <RankList
          style={styles.rankList}
          tops={rankingData?.tops ?? []}
          own={rankingData?.own}
          instanceId={String(instanceId)}
          lastPeriodOwn={lastPeriodInfo?.own}
          lastPeriodClaimed={lastPeriodInfo?.claimed}
        />
      </Screen>
    </PagePerformance>
  );
}

const styles = StyleSheet.create({
  screenDark: {
    backgroundColor: '#000'
  },
  screenTransparent: {
    backgroundColor: 'transparent'
  },
  headerTransparent: {
    backgroundColor: 'transparent'
  },
  bgImage: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: -1
  },
  loadingBg: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center'
  },
  rankList: {
    flex: 1
  }
});
