import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  Keyboard,
  TextInput,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import BirthDayModal, {
  getYearsAgoDate
} from '@/src/bizComponents/profile/BirthDayModal';
import GenderModal, {
  GENDER_TEXT_MAP,
  GenderId
} from '@/src/bizComponents/profile/GenderModal';
import { Icon, Screen, Text, showToast } from '@/src/components';
import { Avatar } from '@/src/components/avatar';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import List from '@/src/components/list';
import { useDailyUpdateLimit } from '@/src/hooks/useDailyUpdateLimit';
import { useAuthStore } from '@/src/store/authInfo';
import { typography } from '@/src/theme';
import { CommonColor } from '@/src/theme/colors/common';
import { getThemeColor } from '@/src/theme/colors/common';
import { Theme } from '@/src/theme/colors/type';
import { $USE_FONT, $Z_INDEXES } from '@/src/theme/variable';
import { StyleSheet } from '@Utils/StyleSheet';
import { dp2px } from '@Utils/dp2px';
import { ReportError, errorReport } from '@Utils/error-log';
import { reportClick } from '@Utils/report';
import { useShallow } from 'zustand/react/shallow';

const ListItem = List.Item;

const getDateString = (date: Date | string | undefined) => {
  if (!date) return <Text style={st.placeholderStyle}>请选择你的生日</Text>;
  const dateString =
    date instanceof Date ? date.toISOString().split('T')[0] : date;
  return <Text style={st.textStyle}>{dateString}</Text>;
};

const getGenderString = (gender: GenderId) => {
  if (!gender) return <Text style={st.placeholderStyle}>请选择你的性别</Text>;
  return <Text style={st.textStyle}>{GENDER_TEXT_MAP[gender]?.label}</Text>;
};

export default function Profile() {
  const [localName, setLocalName] = useState('');
  const [localAvatar, setLocalAvatar] = useState('');
  const [localGender, setLocalGender] = useState<GenderId>(GenderId.NOTSET);
  const [localBirthday, setLocalBirthday] = useState<string | Date | undefined>(
    undefined
  );

  const [isGenderModalVisible, setIsGenderModalVisible] = useState(false);
  const [isBirthDayModalVisible, setIsBirthDayModalVisible] = useState(false);

  const mountedRef = useRef(false);

  const { checkNameUpdateLimit } = useDailyUpdateLimit(3, 3);

  const { name, avatar, gender, birthday } = useAuthStore(
    useShallow(state => ({
      name: state.userInfo?.name,
      avatar: state.userInfo?.avatar,
      gender: state.userInfo?.gender,
      birthday: state.userInfo?.birthday
    }))
  );

  useEffect(() => {
    if (!mountedRef.current) {
      setLocalName(name || '');
      setLocalAvatar(avatar || '');
      setLocalGender((gender || GenderId.NOTSET) as GenderId);
      setLocalBirthday(birthday);
      mountedRef.current = true;
    }
  }, [name, avatar, gender, birthday]);

  useEffect(() => {
    // 兼容修改头像后，其他未保存数据丢失的场景
    if (mountedRef.current) {
      setLocalAvatar(avatar || '');
    }
  }, [avatar]);

  // const themeConfig = getThemeColor(Theme.LIGHT);

  const updateUser = useAuthStore(useShallow(state => state.updateUser));

  const updateProfile = useMemoizedFn(
    (payload: Parameters<typeof updateUser>[0], successCb?: () => void) => {
      useAuthStore
        .getState()
        .updateUser(payload)
        .then(() => {
          showToast('更新成功~');
          successCb?.();
        })
        .catch(e => {
          console.error('update user error', e);
          if (e.code === 21101) {
            showToast('该昵称已被私藏！');
          } else {
            showToast('更新失败！');
          }
          errorReport('[update userinfo error]', ReportError.AVATAR_EDIT, e);
        });
    }
  );

  // 当用户点击输入框时，检查是否已超过改名次数上限
  const handleFocusNameInput = () => {
    console.log('Focus name input!');
    if (checkNameUpdateLimit()) {
      Keyboard.dismiss(); // 不允许编辑，失焦
    } else {
      reportClick('edit_profile_name', { module: 'setting' });
    }
  };

  const isDirty = useMemo(() => {
    const originName = name || '';
    const originAvatar = avatar || '';
    const originGender = gender ?? GenderId.NOTSET;
    const originBirthday = birthday?.toString() || '';
    const currentBirthday = localBirthday?.toString() || '';

    return (
      localName !== originName ||
      localAvatar !== originAvatar ||
      localGender !== originGender ||
      currentBirthday !== originBirthday
    );
  }, [
    name,
    avatar,
    gender,
    birthday,
    localName,
    localAvatar,
    localGender,
    localBirthday
  ]);

  const disabled = useMemo(() => {
    const invalidName = !localName.length || localName.length > 20;
    return invalidName || !isDirty;
  }, [localName, isDirty]);

  return (
    <PagePerformance pathname="profile/edit">
      <Screen
        title="编辑资料"
        headerStyle={{ margin: 0, padding: 0 }}
        headerRight={() => (
          <SaveButton
            disabled={disabled}
            name={localName}
            birthday={localBirthday}
            gender={localGender}
            onUpdate={updateProfile}
          />
        )}
        screenStyle={{
          backgroundColor: CommonColor.white
        }}
      >
        <View style={{ padding: dp2px(16), alignItems: 'center' }}>
          <Avatar
            size={100}
            source={localAvatar}
            cover={{
              url: 'https://resource.lipuhome.com/resource/img/prod/20250102/9e4051088c5139c068a00c704c86b356.png',
              size: 36
            }}
            onPress={() => {
              reportClick('edit_profile_avatar', { module: 'setting' });
              router.navigate('/avatar-edit');
            }}
          />
        </View>

        <List style={{ margin: 16 }}>
          <ListItem
            title="昵称"
            arrowIcon={false}
            titleStyle={st.titleStyle}
            extra={
              <Text style={[st.nameLength]}>{`${localName.length}/20`}</Text>
            }
          >
            <TextInput
              maxLength={20}
              allowFontScaling={false}
              style={[st.inputStyle]}
              value={localName}
              onChangeText={setLocalName}
              // === 当点击输入框时，做限制判断 ===
              onFocus={handleFocusNameInput}
            />
          </ListItem>

          <ListItem
            title="性别"
            titleStyle={st.titleStyle}
            clickable
            onClick={() => {
              Keyboard.dismiss();
              setIsGenderModalVisible(true);
              reportClick('edit_profile_gender', { module: 'setting' });
            }}
          >
            {getGenderString(localGender)}
          </ListItem>

          <ListItem
            title="生日"
            titleStyle={st.titleStyle}
            clickable
            showDivider={false}
            onClick={() => {
              Keyboard.dismiss();
              setIsBirthDayModalVisible(true);
              reportClick('edit_profile_birth', { module: 'setting' });
            }}
          >
            {getDateString(localBirthday)}
          </ListItem>
        </List>
      </Screen>

      <GenderModal
        visible={isGenderModalVisible}
        onClose={() => setIsGenderModalVisible(false)}
        selected={localGender}
        onSelect={gender => {
          setLocalGender(gender);
        }}
      />

      <BirthDayModal
        visible={isBirthDayModalVisible}
        onClose={() => setIsBirthDayModalVisible(false)}
        selected={localBirthday || getYearsAgoDate(18)}
        onSelect={birthday => {
          setLocalBirthday(birthday);
        }}
      />
    </PagePerformance>
  );
}

interface SaveButtonProps {
  disabled: boolean;
  name: string;
  birthday: Date | string | undefined;
  gender: GenderId;
  onUpdate: (payload: any, successCb?: () => void) => void;
}
const $saveButtonStyle: TextStyle = {
  color: StyleSheet.currentColors.brand1,
  fontSize: 16,
  fontWeight: '500'
};

function SaveButton(props: SaveButtonProps) {
  const { name, birthday, gender, onUpdate, disabled } = props;
  const nameRef = useRef(name);

  useEffect(() => {
    nameRef.current = name;
  }, [name]);

  if (disabled) {
    return <Text style={[$saveButtonStyle, { opacity: 0.5 }]}>保存</Text>;
  }

  const getUpdatePayload = (params: Record<string, any>) => {
    const payload: Record<string, any> = {};
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined) {
        payload[key] = value;
      }
    }
    return payload;
  };

  return (
    <TouchableOpacity
      onPress={() => {
        reportClick('edit_profile_save', { module: 'setting' });
        const payload = getUpdatePayload({
          name: nameRef.current,
          birthday,
          gender
        });
        onUpdate(payload, () => {
          router.back();
        });
      }}
    >
      <Text style={$saveButtonStyle}>保存</Text>
    </TouchableOpacity>
  );
}

const st = StyleSheet.create({
  inputStyle: {
    width: 180,
    fontSize: 14,
    fontWeight: '500',
    color: StyleSheet.currentColors.titleGray
  },
  placeholderStyle: {
    color: 'rgba(0, 0, 0, 0.26)'
  },
  titleStyle: {
    width: 76
  },
  textStyle: $USE_FONT(
    StyleSheet.currentColors.titleGray,
    typography.fonts.pingfangSC.normal,
    14,
    'normal',
    '500',
    19.6
  ),
  nameLength: $USE_FONT(
    'rgba(0, 0, 0, 0.26)',
    typography.fonts.pingfangSC.normal,
    12,
    'normal',
    '400',
    16
  )
});
