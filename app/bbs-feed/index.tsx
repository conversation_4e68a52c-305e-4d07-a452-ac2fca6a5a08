import { useFocusEffect } from 'expo-router';
import React from 'react';
import { ImageStyle } from 'react-native';
import { DiscussFeed } from '@/src/bizComponents/feedScreen/DiscussFeed';
import { Image, Screen } from '@/src/components';
import { darkTheme } from '@/src/theme/colors';
import { reportExpo } from '@/src/utils/report';
import { BbsTabKey } from '../../src/bizComponents/feedScreen/type';
import { useParams } from '../../src/hooks/useParams';

// 讨论图标
const TAB_DISCUSS_ACTIVE = require('@Assets/image/feed/tab-discuss-active.png');

export default function BBSFeed() {
  // 曝光上报
  useFocusEffect(() => {
    reportExpo('bbs_tab', {
      module: 'feed'
    });
  });

  const { queryTab } = useParams<{
    queryTab?: BbsTabKey;
  }>();

  const renderHeaderTitle = () => (
    <Image
      native
      source={TAB_DISCUSS_ACTIVE}
      style={$titleIcon}
      tintColor="#ffffff"
    />
  );

  return (
    <Screen
      preset="fixed"
      headerShown={true}
      headerStyle={{
        height: 54
      }}
      headerTitle={renderHeaderTitle}
      backButton={true}
      theme="dark"
      StatusBarProps={{ style: 'light' }}
      screenStyle={{ backgroundColor: darkTheme.background.page }}
    >
      {/* 讨论内容区域 */}
      <DiscussFeed
        active={true}
        pending={false}
        standAlone={true}
        queryTab={queryTab}
      />
    </Screen>
  );
}

// 样式定义
const $titleIcon: ImageStyle = {
  height: 24,
  width: 35
};
