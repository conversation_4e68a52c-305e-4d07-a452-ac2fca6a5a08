import React, { useEffect, useMemo } from 'react';
import RoleSheet from '@/src/bizComponents/role/home';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { ROLE_PORTAL } from '@/src/constants';
import { useRoleHomeStore } from '@/src/store/roleHome';
import { RoleInfo } from '@/src/types';
import { reportExpo } from '@/src/utils/report';
import { useParams } from '../../src/hooks/useParams';
import { PortalHost } from '@gorhom/portal';

export default function RoleScreen() {
  const {
    role = '',
    roleId = '',
    source
  } = useParams<{
    role: string;
    roleId: string;
    source?: string;
  }>();

  const roleInfo: RoleInfo = useMemo(() => {
    const info = useRoleHomeStore.getState().getCurrentRoleInfo(roleId);
    return info as RoleInfo;
  }, [roleId]);
  console.log('=======RoleScreenRoleScreenRoleScreen=========', roleInfo);
  useEffect(() => {
    reportExpo('expo', {
      module: 'character_info_show',
      role_id: roleId,
      source
    });
  }, [roleId, source]);
  return (
    <PagePerformance pathname="role/index">
      <RoleSheet roleInfo={roleInfo} />
      <PortalHost name={ROLE_PORTAL} />
    </PagePerformance>
  );
}
