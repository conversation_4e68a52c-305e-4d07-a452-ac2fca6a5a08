import { useFocusEffect } from 'expo-router';
import React from 'react';
import { Image, Linking, Platform, View } from 'react-native';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { darkTheme } from '@/src/theme/tokens/colors/variants/dark';
import { Screen } from '@Components/screen';
import { SettingGroup, SettingItem } from '@Components/setting';
import { showToast } from '@Components/toast';
import { reportExpo } from '@Utils/report';

const QQIcon = require('@Assets/icon/qq.png');
const DouyinIcon = require('@Assets/icon/douyin.png');
const RedbookIcon = require('@Assets/icon/redbook.png');
const BilibiliIcon = require('@Assets/icon/bilibili.png');

export default function SocialMedia() {
  useFocusEffect(
    React.useCallback(() => {
      reportExpo('social_page', { module: 'setting' });
    }, [])
  );

  const handlePress = (type: string) => {
    let appUrl = '';
    let webUrl = '';
    let socialButtonId = 0;

    switch (type) {
      case 'xiaohongshu':
        appUrl = 'xhsdiscover://user/64bd4e0500000000140379ba';
        webUrl =
          'https://www.xiaohongshu.com/user/profile/64bd4e0500000000140379ba';
        socialButtonId = 1;
        break;

      case 'bilibili':
        appUrl = 'bilibili://space/3546659919235612';
        webUrl = 'https://space.bilibili.com/3546659919235612';
        socialButtonId = 2;
        break;

      case 'douyin':
        if (Platform.OS === 'android') {
          appUrl =
            'snssdk1128://user/profile?refer=web&needlaunchlog=1&scene_from=share_reflow&ori_scene_from=default&host=m.douyin.com&group_id=MS4wLjABAAAA65Ja53RATLkY-PICKo7LQcrixRP-waPZrw31hz3ennEqCpYI0vYJuczlOHRxG9SU&web_id=7443702971143718452&browser_name=safari&referrer_host=m.douyin.com&is_edenx=1&__forbid_pasteboard__=1&gd_label=click_schema_ug_filter_v1_click_schema_lhft_48148339a&launch_h5_method=click_wap_profile_bottom&sec_uid=MS4wLjABAAAA65Ja53RATLkY-PICKo7LQcrixRP-waPZrw31hz3ennEqCpYI0vYJuczlOHRxG9SU&type=need_follow&is_active_tap=1&sec_uid_path=MS4wLjABAAAA65Ja53RATLkY-PICKo7LQcrixRP-waPZrw31hz3ennEqCpYI0vYJuczlOHRxG9SU';
          webUrl = 'https://v.douyin.com/iANPbh3S';
        } else {
          appUrl =
            'snssdk1128://user/profile/MS4wLjABAAAA65Ja53RATLkY-PICKo7LQcrixRP-waPZrw31hz3ennEqCpYI0vYJuczlOHRxG9SU?refer=web&needlaunchlog=1&scene_from=share_reflow&ori_scene_from=default&host=m.douyin.com&group_id=MS4wLjABAAAA65Ja53RATLkY-PICKo7LQcrixRP-waPZrw31hz3ennEqCpYI0vYJuczlOHRxG9SU&web_id=7443702971143718452&browser_name=safari&referrer_host=&is_edenx=1&__forbid_pasteboard__=1&sec_uid=MS4wLjABAAAA65Ja53RATLkY-PICKo7LQcrixRP-waPZrw31hz3ennEqCpYI0vYJuczlOHRxG9SU&type=need_follow&launch_h5_method=click_wap_profile_bottom&is_active_tap=1&gd_label=click_schema_ug_filter_v1_click_schema_lhft_46984860a';
          webUrl = 'https://v.douyin.com/iANPbh3S';
        }
        socialButtonId = 3;
        break;

      case 'qqgroup':
        appUrl =
          'mqqapi://card/show_pslcard?src_type=internal&version=1&uin=103676026&card_type=group&source=qrcode&jump_from=&auth=&authSig=G/wDDKQ06hYrvOHRTHwPlWFma88FOHH5jdWWcjP5xAnnK/T9in5h0ekv74jreq/Y&source_id=2_40001';
        webUrl = 'https://qm.qq.com/q/LCnhteJH0Q';
        socialButtonId = 4;
        break;

      default:
        showToast('功能待开发');
        return;
    }

    // 上报埋点
    reportExpo('social_page_button', {
      social_button: socialButtonId,
      module: 'setting'
    });

    // 打开应用链接或网页
    Linking.openURL(appUrl).catch(() => {
      Linking.openURL(webUrl).catch(err => {
        console.error('无法打开 URL:', err);
        showToast('无法打开链接');
      });
    });
  };

  return (
    <PagePerformance pathname="setting/social-media">
      <Screen
        title="社交媒体"
        screenStyle={{ backgroundColor: darkTheme.background.page }}
        headerStyle={{ justifyContent: 'center', alignItems: 'center' }}
        theme="dark"
      >
        <View style={{ flex: 1, marginTop: 16 }}>
          <SettingGroup>
            <SettingItem
              title="关注狸谱小红书账号"
              leftIcon={RedbookIcon}
              onPress={() => handlePress('xiaohongshu')}
              iconColor
            />
            <SettingItem
              title="关注狸谱Bilibili账号"
              leftIcon={BilibiliIcon}
              onPress={() => handlePress('bilibili')}
              iconColor
            />
            <SettingItem
              title="关注狸谱抖音账号"
              leftIcon={DouyinIcon}
              onPress={() => handlePress('douyin')}
              iconColor
            />
            <SettingItem
              title="进入狸谱QQ群"
              leftIcon={QQIcon}
              onPress={() => handlePress('qqgroup')}
              iconColor
            />
          </SettingGroup>
        </View>
      </Screen>
    </PagePerformance>
  );
}
