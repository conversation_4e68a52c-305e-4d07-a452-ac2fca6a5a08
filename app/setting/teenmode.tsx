import React from 'react';
import { TeenModeView } from '@/src/bizComponents/teenModeScreen';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { Screen } from '@Components/screen';
import { darkTheme } from '@/src/theme/tokens/colors/variants/dark';

export default function TeenModeScreen() {
  return (
    <PagePerformance pathname="setting/teenmode">
      <Screen
        title="青少年模式设置"
        screenStyle={{ backgroundColor: darkTheme.background.page }}
        headerStyle={{
          justifyContent: 'flex-start'
        }}
        theme="dark"
      >
        <TeenModeView />
      </Screen>
    </PagePerformance>
  );
}
