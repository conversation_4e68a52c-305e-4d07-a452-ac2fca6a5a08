import React from 'react';
import { TeenModePassword } from '@/src/bizComponents/teenModeScreen/teenModePassword';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { Screen } from '@Components/screen';
import { darkTheme } from '@/src/theme/tokens/colors/variants/dark';

export default function TeenModePasswordScreen() {
  return (
    <PagePerformance pathname="setting/password">
      <Screen
        title="青少年模式"
        screenStyle={{ backgroundColor: darkTheme.background.page }}
        headerStyle={{ justifyContent: 'flex-start' }}
        theme="dark"
      >
        <TeenModePassword />
      </Screen>
    </PagePerformance>
  );
}
