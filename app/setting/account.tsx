import { useNavigation } from 'expo-router';
import { useState } from 'react';
import { Pressable, View, ViewStyle } from 'react-native';
import { StreamLogout } from '@/src/api/auth';
import { hideLoading, showLoading, showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { showGuide, useGuide } from '@/src/components/guide';
import { GUIDE_TYPE_ENUM } from '@/src/components/guide/_constants';
import FinishGuide from '@/src/components/guide/guide-content/finish-guide';
import { useAuthStore } from '@/src/store/authInfo';
import { useGuideStore } from '@/src/store/guide';
import { useSrefStore } from '@/src/store/sref';
import { Theme } from '@/src/theme/colors/type';
import { darkTheme } from '@/src/theme/tokens/colors/variants/dark';
import { TabItemType, UserType } from '@/src/types';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { showConfirm } from '@Components/popup/confirmModalGlobal/Confirm';
import { Screen } from '@Components/screen';
// import { useNavigation } from 'expo-router';
// import { UserMode } from "@step.ai/proto-gen/proto/user/v1/user_pb";
import { SettingGroup, SettingItem } from '@Components/setting';
import { Text } from '@Components/text';
import { StyleSheet } from '@Utils/StyleSheet';
import { CommonActions } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';

const $settingGroup: ViewStyle = {
  borderRadius: 16,
  overflow: 'hidden',
  margin: 16,
  marginTop: 0,
  backgroundColor: darkTheme.background.card
};

const Account = () => {
  const [signOutConfirVisible, setSignOutConfirmVisible] = useState(false);
  const { user, signOut } = useAuthStore(
    useShallow(state => ({
      user: state.userInfo,
      signOut: state.signOut
    }))
  );
  const navigation = useNavigation();

  const { handleClearGuideStatus, changeIsGuideNeed, handleChangeGuideType } =
    useGuide({});

  // const onPressDeleteAccount = () => {
  //   setSignOutConfirmVisible(true);
  // };
  return (
    <PagePerformance pathname="setting/account">
      <Screen
        title="账号与安全"
        screenStyle={{ backgroundColor: darkTheme.background.page }}
        theme="dark"
      >
        <View style={{ flex: 1, marginTop: 16 }}>
          <SettingGroup>
            <SettingItem
              title="手机号"
              leftIcon={''}
              rightContent={
                <Text
                  key="phoneicon"
                  style={{
                    color: darkTheme.text.tertiary,
                    fontSize: 14,
                    fontWeight: '500'
                  }}
                >
                  {user?.mobile}
                  {/* {userName} */}
                </Text>
              }
              rightIcon=""
            ></SettingItem>
          </SettingGroup>
          <SettingGroup>
            <SettingItem
              title="账号注销"
              leftIcon={''}
              onPress={onPressDeleteAccount}
            ></SettingItem>
          </SettingGroup>
          {/* <Pressable
          onPress={() => {
            showConfirm({
              title: '确认删除账号?',
              content:
                '删除账号将解除手机号绑定并删除您的数据，删除后不可恢复！请再次确认',
              confirmText: '确认删除',
              cancelText: '取消',
              onConfirm: ({ close }) => {
                showLoading();
                StreamLogout();

                signOut()
                  .then(() => {
                    handleClearGuideStatus();
                    changeIsGuideNeed(true);
                    // router.replace('/feed');
                    navigation.dispatch(() => {
                      // setTab(TabItemType.HOME);
                      return CommonActions.reset({
                        index: 0,
                        routes: [{ name: 'feed/index' }]
                      });
                    });
                    hideLoading();
                    close();
                  })
                  .catch(e => {
                    console.log(e);
                    showToast('删除失败');
                    hideLoading();
                  });
              }
            });
          }}
        >
          <Text>清空引导状态</Text>
        </Pressable> */}
          {/* <Pressable
          style={{ borderWidth: 1, alignItems: 'center' }}
          onPress={() => {
            showGuide({
              renderGuideContent: _ => (
                <FinishGuide
                  onStart={() => {
                    // hideGuide();
                  }}
                />
              )
            });
          }}
        >
          <Text>透明视频</Text>
        </Pressable> */}
        </View>
        {/* <Button
        textStyle={{ color: colorsUI.Text.danger.default }}
        style={{ marginHorizontal: 24 }}
        onPress={onPressDeleteAccount}
      >
        删除账号
      </Button> */}
      </Screen>
    </PagePerformance>
  );

  function onPressDeleteAccount() {
    showConfirm({
      title: '确认删除账号?',
      content:
        '删除账号将解除手机号绑定并删除您的数据，删除后不可恢复！请再次确认',
      confirmText: '确认删除',
      cancelText: '取消',
      theme: Theme.DARK,
      onConfirm: ({ close }) => {
        if (user?.type === UserType.Official) {
          showToast('别玩火 bro !');
          close();
          return;
        }

        showLoading();
        StreamLogout();

        signOut()
          .then(() => {
            handleChangeGuideType(GUIDE_TYPE_ENUM.LOGGED_MAKE_PHOTO);
            useGuideStore.setState({ isGuideNeed: false });
            useSrefStore.getState().resetMySrefPhotos();
          })
          .catch(e => {
            errorReport('sign_out_error', ReportError.SETTING, e);
            console.log('signOut err------>', e);

            // 判断错误信息是否包含账号不存在的提示
            if (
              e instanceof Error &&
              e.message &&
              e.message.includes('account is not existing')
            ) {
              // 账号不存在时显示特定提示
              showToast('账号不存在！');
              // authInfo.ts 中的 signOut 已经调用了 logout()，这里只需处理界面相关的状态
              handleChangeGuideType(GUIDE_TYPE_ENUM.LOGGED_MAKE_PHOTO);
              useGuideStore.setState({ isGuideNeed: false });
              useSrefStore.getState().resetMySrefPhotos();
            } else {
              showToast('删除失败');
            }
            errorReport('sign_out_error', ReportError.SETTING, e);
          })
          .finally(() => {
            // router.replace('/feed');
            navigation.dispatch(() => {
              return CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: 'feed/index',
                    params: {
                      tab: TabItemType.HOME,
                      tabUpdateTimestamp: Date.now()
                    }
                  }
                ]
              });
            });
            hideLoading();
            close();
          });
      }
    });
  }
};
export default Account;
