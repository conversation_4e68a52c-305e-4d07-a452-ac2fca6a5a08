// import { useAppUpdateInfo } from '@/src/hooks/useCheckUpdate';
import { View } from 'react-native';
// import { UPDATE_IMAGE } from "@/src/constants";
import { Button } from '@/src/components/button';
import { Icon } from '@/src/components/icons';
import { Image } from '@/src/components/image';
import { Text } from '@/src/components/text';
import { colorsUI, spacing, typography } from '@/src/theme';
import { darkTheme } from '@/src/theme/tokens/colors/variants/dark';
import { Screen } from '@Components/screen';
import { useParams } from '../../src/hooks/useParams';

export default function AboutScreen() {
  // const { goToUpdate, releaseNotes, versionNumText } = useAppUpdateInfo();
  const { force } = useParams();
  return (
    <Screen
      title="版本更新"
      safeAreaEdges={['top', 'bottom']}
      contentContainerStyle={{
        paddingHorizontal: 32
      }}
      backButton={!force}
      screenStyle={{ backgroundColor: darkTheme.background.page }}
      theme="dark"
    >
      <View
        style={{
          flex: 1,
          marginTop: 10,
          justifyContent: 'space-between'
        }}
      >
        <View>
          {/* <Image
            source={UPDATE_IMAGE}
            style={{
              alignSelf: 'center',
              width: 240,
              height: 200,
            }}
          /> */}
          <View>
            <Text
              preset="bold"
              size="xl"
              style={{
                marginBottom: spacing.sm,
                color: darkTheme.text.primary
              }}
            >
              发现新版本!
            </Text>
            <View
              style={{
                flexDirection: 'row'
              }}
            >
              <View
                style={{
                  borderRadius: 2,
                  backgroundColor: colorsUI.Text.brand.brand,
                  padding: 4,
                  paddingHorizontal: 6,
                  paddingBottom: 2,
                  marginBottom: 20
                }}
              >
                <Text
                  style={{
                    fontFamily: typography.fonts.campton,
                    fontWeight: '500',
                    lineHeight: 16,
                    color: 'white'
                  }}
                >
                  {/* {versionNumText} */}
                </Text>
              </View>
            </View>
            {/* {releaseNotes.map((item, idx) => {
              return (
                <View
                  key={idx}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    marginBottom: 4,
                  }}
                >
                  <Icon
                    style={{ marginRight: spacing.xs, marginTop: 8 }}
                    size={10}
                    icon="dot"
                  />
                  <Text style={{ fontSize: 15, lineHeight: 26 }}>{item}</Text>
                </View>
              );
            })} */}
          </View>
        </View>
        <Button
          style={{
            width: '100%'
          }}
          preset="primary"
          // onPress={goToUpdate}
        >
          立即更新
        </Button>
      </View>
    </Screen>
  );
}
