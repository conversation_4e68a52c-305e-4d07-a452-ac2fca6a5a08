import * as Clipboard from 'expo-clipboard';
import { router, useGlobalSearchParams, useNavigation } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import {
  Platform,
  Pressable,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { runOnJS } from 'react-native-reanimated';
import { Socket } from '@/src/api/websocket';
import { hideLoading, showLoading } from '@/src/components';
import { DevicePerformancePanel } from '@/src/components/DevicePerformancePanel';
import { Button } from '@/src/components/button';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { DevSheet } from '@/src/components/dev/DevSheet';
import { useGuide } from '@/src/components/guide';
import { GUIDE_TYPE_ENUM } from '@/src/components/guide/_constants';
import { useAuthStore } from '@/src/store/authInfo';
import { useGuideStore } from '@/src/store/guide';
import { usePublishStore } from '@/src/store/publish';
import { useSrefStore } from '@/src/store/sref';
import { useStorageStore } from '@/src/store/storage';
import { colorsUI } from '@/src/theme';
import { Theme } from '@/src/theme/colors/type';
import { TabItemType } from '@/src/types';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { showConfirm } from '@Components/popup/confirmModalGlobal/Confirm';
import { Screen } from '@Components/screen';
import { SettingGroup, SettingItem } from '@Components/setting';
import { Text } from '@Components/text';
import { showToast } from '@Components/toast';
import { StyleSheet } from '@Utils/StyleSheet';
import { getChannel } from '@Utils/getChannel';
import { reportClick } from '@Utils/report';
// import {
//   AnimationDir,
//   MaskImages
// } from '../parallel-world/_components/mask-images';
import { CommonActions } from '@react-navigation/native';
import {
  getSettingsPagePublicInfo,
  shouldEnableDevTools
} from '@step.ai/app-info-module';
import { bundleVersion } from '@step.ai/publish-module/build/module/StepAIPublishModule';
// import { UserMode } from "@step.ai/proto-gen/proto/user/v1/user_pb";
import { useShallow } from 'zustand/react/shallow';

const SocialMediaIcon = require('@/assets/icon/social-media.png');

// import {
//   shouldEnableDevTools,
//   getSettingsPagePublicInfo,
// } from '@step.ai/app-info-module';

export default function AboutScreen() {
  const { user, signOff } = useAuthStore(
    useShallow(state => ({
      user: state.userInfo,
      // isLogin: state.user?.mode === UserMode.SIGNIN,
      signOff: state.signOff
    }))
  );
  const navigation = useNavigation();

  // const userName = useMemo(() => {
  //   return isLogin ? user?.binding?.mobile?.number ?? "" : "未登录";
  // }, [isLogin, user]);
  const [showDev, setShowDev] = useState(false);
  const [showInfo, setShowInfo] = useState(false);

  const { handleChangeGuideType } = useGuide({});

  const showDevMenu = () => {
    if (!shouldEnableDevTools()) return;
    setShowDev(true);
    useStorageStore.getState().__setStorage({ debugMode: true });
    showToast('debug模式已打开~');
  };
  const tripleInfoTap = Gesture.Tap()
    .numberOfTaps(3)
    .onEnd(() => {
      runOnJS(setShowInfo)(true);
    });
  const devMenuTap = Gesture.Tap()
    .numberOfTaps(8)
    .onEnd(() => {
      runOnJS(showDevMenu)();
    });
  const composed = Gesture.Exclusive(devMenuTap, tripleInfoTap);

  const onPressAccount = () => {
    router.navigate('/setting/account');
  };
  const onPressAbout = async () => {
    router.navigate('/setting/about');
  };
  // const onPressLogout = () => {
  //   setSignOffConfirmVisible(true);
  // };
  const onPressFollow = () => {
    reportClick('social', { module: 'setting' });
    router.navigate('/setting/social-media');
  };
  const onPressFeedback = () => {
    // showToast('反馈待给出飞书问卷');
    router.navigate('/setting/feedback');
  };
  function onPressTeenMode() {
    reportClick('youngman', { module: 'setting' });
    router.navigate('/setting/teenmode');
  }

  return (
    <PagePerformance pathname="setting/index">
      <Screen
        theme="dark"
        StatusBarProps={{ style: 'light' }}
        screenStyle={{ backgroundColor: StyleSheet.darkTheme.background.page }}
        headerStyle={{ justifyContent: 'flex-start' }}
      >
        <View style={{ marginTop: 26, flex: 1, marginBottom: 34 }}>
          <Text
            style={{
              color: StyleSheet.darkTheme.text.primary,
              fontSize: 30,
              fontWeight: '700',
              lineHeight: 35,
              marginLeft: 16
            }}
          >
            设置
          </Text>

          <SettingGroup
            style={{
              marginTop: 30,
              backgroundColor: StyleSheet.darkTheme.background.card
            }}
          >
            <SettingItem
              key="safe"
              title="账号与安全"
              rightContent={
                <Text
                  key="safeicon"
                  style={{
                    marginRight: 12,
                    color: StyleSheet.darkTheme.text.secondary
                  }}
                >
                  {/* {userName} */}
                </Text>
              }
              leftIcon="security"
              onPress={onPressAccount}
            />
            <SettingItem
              key="feedback"
              title="投诉与反馈"
              leftIcon="complain"
              onPress={onPressFeedback}
            />
            <SettingItem
              key="socialmedia"
              title="社交媒体"
              leftIcon={SocialMediaIcon}
              onPress={onPressFollow}
            />

            <SettingItem
              key="about"
              title="关于小狸"
              leftIcon="about"
              onPress={onPressAbout}
            >
              <Text
                style={{
                  color: StyleSheet.darkTheme.text.disabled,
                  fontWeight: '500',
                  fontSize: 12
                }}
              >
                {bundleVersion}
              </Text>
            </SettingItem>
            <SettingItem
              key="teenmode"
              title="青少年模式"
              leftIcon="teenmode"
              onPress={onPressTeenMode}
            />
          </SettingGroup>
          <SettingGroup
            style={{ backgroundColor: StyleSheet.darkTheme.background.card }}
          >
            <SettingItem
              key="signoff"
              title="退出登录"
              leftIcon="signoff"
              onPress={onPressLogout}
            />
          </SettingGroup>
        </View>
        {showInfo && (
          <>
            <TouchableOpacity
              style={{
                alignItems: 'center',
                marginBottom: 20,
                justifyContent: 'center'
              }}
              onPress={async () => {
                await Clipboard.setStringAsync(
                  getSettingsPagePublicInfo() +
                    ` UID:${user?.uid} +  ${getChannel()} cid: ${
                      useStorageStore.getState().cid
                    } bundleVersion: ${bundleVersion}`
                );
                showToast('已复制信息至剪贴板');
                setShowInfo(false);
              }}
            >
              <Text
                style={{
                  textAlign: 'center',
                  color: StyleSheet.darkTheme.text.tertiary,
                  fontSize: 12
                }}
              >
                {getSettingsPagePublicInfo()}
              </Text>
              <Text
                style={{
                  textAlign: 'center',
                  color: StyleSheet.darkTheme.text.tertiary,
                  fontSize: 12
                }}
              >
                UID:{user?.uid}
              </Text>
            </TouchableOpacity>

            {shouldEnableDevTools() && (
              <DevicePerformancePanel showByDefault={false} />
            )}
          </>
        )}
        <GestureDetector gesture={composed}>
          <View
            style={{
              marginBottom: 20,
              alignItems: 'center'
            }}
          >
            <Text
              style={{
                fontSize: 12,
                color: StyleSheet.darkTheme.text.tertiary
              }}
            >
              {bundleVersion}
            </Text>
          </View>
        </GestureDetector>

        <DevSheet
          visible={showDev}
          onClose={() => {
            setShowDev(false);
          }}
        />
      </Screen>
    </PagePerformance>
  );

  function onPressLogout() {
    showConfirm({
      title: '退出登录',
      content: '退出登录后不会丢失任何数据，你仍可以登录此账号使用狸谱',
      confirmText: '退出登录',
      cancelText: '取消',
      theme: Theme.DARK,
      onConfirm: async ({ close }) => {
        showLoading();
        // try {
        //   console.log(11111111111, StreamClient.logout);
        // StreamLogout();
        // } catch (e) {
        //   console.log('error----', e);
        // }
        signOff()
          .then(() => {
            handleChangeGuideType(GUIDE_TYPE_ENUM.LOGGED_MAKE_PHOTO);
            useGuideStore.setState({ isGuideNeed: false });

            useSrefStore.getState().resetMySrefPhotos();

            // setSignOffConfirmVisible(false);
            // router.replace('/feed');
            navigation.dispatch(() => {
              return CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: 'feed/index',
                    params: {
                      tab: TabItemType.HOME,
                      tabUpdateTimestamp: Date.now()
                    }
                  }
                ]
              });
            });
            close();
            hideLoading();
            showToast('退出成功');
            // navigation.reset({
            //   index: 0,
            //   routes: [{ name: '/feed/' }]
            // });
          })
          .catch(e => {
            showToast('退出登录失败');
            hideLoading();
            errorReport('sign_off_error', ReportError.SETTING, e);
          });
      }
    });
  }
}
