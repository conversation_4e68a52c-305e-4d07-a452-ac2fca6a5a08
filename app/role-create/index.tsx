import { router, useFocusEffect } from 'expo-router';
import { debounce } from 'lodash-es';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Dimensions, Keyboard, Pressable, Text, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CreateForm from '@/src/bizComponents/role-create/create-form';
import { RoleImage } from '@/src/bizComponents/role-create/create-form/types';
import { UGCRoleSelectModal } from '@/src/bizComponents/role-create/generateRole/intex';
import { Checkbox } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import CreditCas, {
  CREDIT_LIMIT,
  CREDIT_TYPE,
  PLUS_BORDER_THEME3,
  PLUS_THEME3
} from '@/src/components/credit-cas';
import { PrimaryButton } from '@/src/components/primaryButton';
import CreditWrapper from '@/src/components/v2/credit-wrapper';
import { ROLE_CREATE_PORTAL } from '@/src/constants';
import { usePersistFn } from '@/src/hooks';
import { selectState } from '@/src/store/_utils';
import { useCreditStore } from '@/src/store/credit';
import {
  UGCPhotoInfo,
  UploadImageFE,
  useCreateRoleStore
} from '@/src/store/role/role-create';
import { rowStyle } from '@/src/theme';
import { GameType, InvokeType, PartialMessage } from '@/src/types';
import { dp2px } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { Screen } from '@Components/screen';
import { useParams } from '../../src/hooks/useParams';
import { PortalHost } from '@gorhom/portal';
import { UploadImage } from '@step.ai/proto-gen/raccoon/upload/common_pb';
import { useShallow } from 'zustand/react/shallow';

const windowSize = Dimensions.get('window');

export default function RoleCreate() {
  const {
    createFormData,
    changeRoleImgInfo,
    roleImgInfo,
    resetRolePhotos,
    changeCreateFormData,
    isModify,
    reset: resetCreateRoleStore
  } = useCreateRoleStore(
    useShallow(state =>
      selectState(state, [
        'createFormData',
        'changeCreateFormData',
        'roleImgInfo',
        'changeRoleImgInfo',
        'genRolePhotos',
        'isModify',
        'reset',
        'resetRolePhotos',
        'createOrModifyRole'
      ])
    )
  );
  const { from_card_id, from }: { from_card_id?: string; from?: string } =
    useParams();
  const { totalCredits } = useCreditStore(
    useShallow(state => ({
      totalCredits: state.totalCredits,
      syncCredits: state.syncCredits
    }))
  );
  const updateImage = usePersistFn((roleImgInfo: RoleImage) => {
    changeCreateFormData({
      imageFromUpload: true
    });
    changeRoleImgInfo(roleImgInfo);
  });
  const shouldClearState = useRef(true);
  const [useLocalImage, setLocalImage] = useState(false);
  const [showRoleModal, setRoleModalShow] = useState(false);
  useFocusEffect(
    useCallback(() => {
      shouldClearState.current = true;
      return () => {
        if (shouldClearState.current) {
          resetCreateRoleStore();

          resetRolePhotos();
        }
      };
    }, [])
  );
  useEffect(() => {
    if (from) {
      reportExpo('page_preview', {
        module: 'character_create',
        type: from
      });
    }
  }, [from]);
  const submitDisabled = !(
    createFormData.roleSet &&
    typeof createFormData.gender === 'number' &&
    createFormData.name
  );

  const onSubmit = debounce(
    async (photo?: UGCPhotoInfo) => {
      if (!photo && submitDisabled) {
        return;
      }
      shouldClearState.current = false;
      if (photo) {
        setLocalImage(true);
        changeRoleImgInfo({
          id: photo.imageId,
          url: photo.imageUrl,
          picId: photo.picId
        } as UploadImageFE);
      }
      router.navigate({
        pathname: '/role-create/edit',
        params: {
          from_create: '1'
        }
      });
    },
    200,
    {
      leading: true,
      trailing: false
    }
  );
  const safeInsets = useSafeAreaInsets();

  return (
    <PagePerformance pathname="role-create/index">
      <View style={{ height: '100%' }}>
        <Screen
          theme="dark"
          title={isModify ? '编辑角色' : '创建角色'}
          // headerRight={() => showKeyboard && publishButton}
          contentContainerStyle={{
            height: 'auto',
            // minHeight: '100%'
            flex: 1
            // paddingBottom: 110
          }}
          needDismissKeboard
          onBack={() => {
            router.back();
          }}
          safeAreaEdges={['top']}
          screenStyle={{
            backgroundColor: 'rgba(18, 18, 20, 1)',
            justifyContent: 'center'
          }}
          headerLeft={() => {
            const moreThanLimit = totalCredits >= CREDIT_LIMIT;
            const theme = moreThanLimit
              ? CREDIT_TYPE.V2PLUS
              : CREDIT_TYPE.V2Minus;
            return (
              <View style={{ position: 'absolute', left: 32 }}>
                <CreditCas
                  theme={theme}
                  text={`${totalCredits}`}
                  borderColors={PLUS_BORDER_THEME3}
                  insetsColors={PLUS_THEME3}
                  $customStyle={{
                    height: 'auto',
                    marginVertical: 6
                  }}
                  $customInnerStyle={{
                    paddingLeft: 6,
                    paddingRight: 10
                  }}
                  $customTextStyle={{
                    color: '#FFFFFFE5',
                    fontSize: 12,
                    lineHeight: 22,
                    height: 22
                  }}
                  size={12}
                  hasPad
                />
              </View>
            );
          }}
        >
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              paddingHorizontal: 20,
              paddingTop: 10
            }}
          >
            <CreateForm
              value={createFormData}
              imgInfo={roleImgInfo}
              cardId={from_card_id}
              onImgUpdate={updateImage}
              onChange={changeCreateFormData}
            />
          </View>
        </Screen>
        <View
          style={{
            position: 'absolute',
            paddingTop: 10,
            alignItems: 'center',
            gap: 10,
            height: 180,
            backgroundColor: 'rgba(18, 18, 20, 1)',
            width: '100%',
            top: windowSize.height - safeInsets.bottom - 100,
            justifyContent: 'flex-end'
          }}
        />
        <Pressable
          onPress={() => {
            Keyboard.dismiss();
          }}
          style={{
            position: 'absolute',
            paddingTop: 10,
            alignItems: 'center',
            gap: 10,
            height: 80,

            width: '100%',
            top: windowSize.height - safeInsets.bottom - 80,
            justifyContent: 'flex-end'
          }}
        >
          {from_card_id === undefined && (!roleImgInfo || !useLocalImage) ? (
            <CreditWrapper
              invokeType={InvokeType.INVOKE_DRAWING_UGC_IMAGE_GEN}
              gameType={GameType.DRAWING}
              $blurStyle={{ left: -18 }}
              buttonContainer={
                <PrimaryButton
                  width={windowSize.width - dp2px(60)}
                  customBgStyle={{
                    opacity: submitDisabled ? 0.3 : 1
                  }}
                  onPress={async () => {
                    if (submitDisabled) return;
                    reportClick('paint_button', { module: 'character_create' });
                    setRoleModalShow(true);
                  }}
                >
                  生成形象
                </PrimaryButton>
              }
            />
          ) : (
            <PrimaryButton
              customBgStyle={{ opacity: submitDisabled ? 0.3 : 1 }}
              onPress={() => onSubmit()}
              width={windowSize.width - 60}
            >
              {isModify ? '确认编辑' : '开始创建'}
            </PrimaryButton>
          )}
          {from_card_id === undefined && roleImgInfo && (
            <Pressable
              style={rowStyle}
              onPress={() => {
                reportClick('ref_confirm_button', {
                  module: 'character_create'
                });
                setLocalImage(val => !val);
              }}
            >
              <Checkbox
                checked={useLocalImage}
                size={14}
                style={{ position: 'absolute', left: -20 }}
              />
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: '400',
                  color: 'rgba(255, 255, 255, 0.4)'
                }}
              >
                直接用参考图作为形象
              </Text>
            </Pressable>
          )}
        </Pressable>
      </View>
      <UGCRoleSelectModal
        visible={showRoleModal}
        onChoose={onSubmit}
        onClose={() => {
          setRoleModalShow(false);
        }}
      />
      <PortalHost name={ROLE_CREATE_PORTAL} />
    </PagePerformance>
  );
}
