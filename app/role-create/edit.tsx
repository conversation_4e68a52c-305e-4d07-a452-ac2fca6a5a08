import { router, useFocusEffect } from 'expo-router';
import { useEffect } from 'react';
import { EditRole } from '@/src/bizComponents/role-create/EditRole';
import Background from '@/src/bizComponents/role-create/EditRole/Background';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { selectState } from '@/src/store/_utils';
import { useCreateRoleStore } from '@/src/store/role/role-create';
import { useRoleHomeStore } from '@/src/store/roleHome';
import { StyleSheet } from '@/src/utils';
import { reportExpo } from '@/src/utils/report';
import { Screen } from '@Components/screen';
import { useParams } from '../../src/hooks/useParams';
import { useShallow } from 'zustand/react/shallow';

export default function RoleEdit() {
  const { from_role } = useParams();
  const { roleImgInfo, isModify } = useCreateRoleStore(
    useShallow(state => selectState(state, ['roleImgInfo', 'isModify']))
  );
  useEffect(() => {
    reportExpo('character_profile', {
      module: 'character_info_edit',
      photo_id: roleImgInfo?.picId ?? roleImgInfo?.id,
      source: isModify ? 1 : 0
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModify]);
  return (
    <PagePerformance pathname="role-create/edit">
      <Screen
        theme="dark"
        needDismissKeboard
        contentContainerStyle={styles.absoluteContainer}
        backgroundView={<Background img={roleImgInfo?.url} />}
        onBack={() => {
          if (from_role) {
            useRoleHomeStore.getState().setState({
              showShareVis: true
            });
          }
          router.back();
        }}
        safeAreaEdges={['top']}
        screenStyle={styles.absoluteContainer}
      >
        <EditRole />
      </Screen>
    </PagePerformance>
  );
}
const styles = StyleSheet.create({
  absoluteContainer: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0
  },
  container: { width: '100%', height: '100%' }
});
