import { identity } from 'lodash-es';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import { showToast } from '@/src/components';
import { CreateFormDataType } from '@/src/store/role/role-create';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { CreateUGCRoleReq } from '@/proto-registry/src/web/raccoon/crole/crole_pb';
import {
  CRoleCode,
  PointsCode
} from '@/proto-registry/src/web/raccoon/errorcode/errorcode_pb';
import type { PartialMessage } from '@bufbuild/protobuf';

const errMsg = {
  image: '头像上传失败，违法、带水印、网址的图片都有可能导致失败～',
  nickname: '请修改角色昵称～',
  prompt: '请修改角色生成魔法词～',
  brief: '请修改角色简介～',
  interest: '请修改兴趣～',
  character: '请修改性格～',
  habit: '请修改习惯～',
  identity: '请修改身份～'
};

export const transFormDataToReq: (
  i: CreateFormDataType
) => PartialMessage<CreateUGCRoleReq> = (obj: Omit<CreateFormDataType, ''>) => {
  return {
    ...obj,
    brandId: obj.roleSet?.originBrandId ?? obj.roleSet?.id ?? 200
  };
};

export const handleError = (
  err: unknown,
  isModify: boolean,
  showCreditToast?: () => void
) => {
  if (err instanceof ErrorRes) {
    switch (err.code) {
      case PointsCode.POINTS_ERR_INSUFFICIENT_POINTS: {
        errorReport('[createRole error boundary]', ReportError.CERDIT, err);
        showCreditToast?.();
        break;
      }
      case CRoleCode.CROLE_ERR_RISK_IMAGE: {
        showToast(errMsg.image);
        break;
      }
      case CRoleCode.CROLE_ERR_RISK_DESC: {
        showToast(errMsg.brief);
        break;
      }
      case CRoleCode.CROLE_ERR_RISK_NAME: {
        showToast(errMsg.nickname);
        break;
      }
      case CRoleCode.CROLE_ERR_RISK_PROMPT: {
        showToast(errMsg.prompt);
        break;
      }
      case CRoleCode.CROLE_ERR_RISK_INTEREST: {
        showToast(errMsg.interest);
        break;
      }
      case CRoleCode.CROLE_ERR_RISK_CHARACTER: {
        showToast(errMsg.character);
        break;
      }
      case CRoleCode.CROLE_ERR_RISK_IDENTITY: {
        showToast(errMsg.identity);
        break;
      }
      case CRoleCode.CROLE_ERR_RISK_HABIT: {
        showToast(errMsg.habit);
        break;
      }
      case 2: {
        showToast(err.reason);
        break;
      }
      default: {
        showToast(
          isModify ? '编辑失败了，请重新尝试～' : '创建失败了，请重新尝试～'
        );
      }
    }
  } else {
    showToast(
      isModify ? '编辑失败了，请重新尝试～' : '创建失败了，请重新尝试～'
    );
  }
};
