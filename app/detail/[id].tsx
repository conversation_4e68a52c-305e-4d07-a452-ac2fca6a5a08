import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import React, {
  MutableRefObject,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {
  LayoutChangeEvent,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  View,
  ViewStyle
} from 'react-native';
import { IOScrollView, InView } from 'react-native-intersection-observer';
import { useSharedValue } from 'react-native-reanimated';
import { bindInvitation, triggerMakeCopy } from '@/src/api/activity';
import { GoodsDecoration } from '@/src/bizComponents/detailScreen/GoodsDecoration';
import HeaderMask from '@/src/bizComponents/detailScreen/header/headerMask';
import {
  RolesSectionV2,
  RolesSectionV2Actions
} from '@/src/bizComponents/detailScreen/rolesSection/RolesSectionV2';
import { TakePhotoButton } from '@/src/bizComponents/detailScreen/takePhotoButton';
import { TextContent } from '@/src/bizComponents/detailScreen/textContent';
import {
  HomePagePerformanceConfigDefault,
  usePerformanceCollector
} from '@/src/bizComponents/detailScreen/usePerformanceCollector';
import { WINDOW_WIDTH } from '@/src/bizComponents/nestedScrollView';
import ImageSharePanel from '@/src/bizComponents/share/imageSharePanel';
import { UserScreen } from '@/src/bizComponents/userScreen';
import { showToast } from '@/src/components';
import {
  CommentEvent,
  CommentEventBus
} from '@/src/components/comment/eventbus';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import {
  PreviewImageProps,
  hidePreviewImages,
  showPreviewImages
} from '@/src/components/previewImageModal';
import { LOGIN_SCENE, SHARE_ACTIVITY_TYPE } from '@/src/constants';
import { PREVIEW_TAKE_PHOTO_BUTTON_WIDTH } from '@/src/constants';
import { useAuthState } from '@/src/hooks/useAuthState';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useGameFeatureGate } from '@/src/hooks/useGameFeatureGate';
import { useSafeAreaInsetsStyle } from '@/src/hooks/useSafeAreaInsetsStyle';
import { useSetEmojiCreatorInfo } from '@/src/hooks/useSetDetailEmojiCreatorInfo';
import {
  GestureArea,
  SwipeScreen,
  SwipeScreenEventBus
} from '@/src/hooks/useSwipeScreen';
import { DetailInfo, PhotoInfo, useDetailStore } from '@/src/store/detail';
import { centerStyle } from '@/src/theme';
import { Theme } from '@/src/theme/colors/type';
import { GameType, ImageItem, TopicInfo } from '@/src/types';
import { dp2px, getScreenSize } from '@/src/utils';
import { CommonEventBus } from '@/src/utils/event';
import { formatRole } from '@/src/utils/formatRole';
import { formatNumber } from '@/src/utils/opt/transNum';
import {
  JOIN_BTN_MODULE,
  Source,
  addCommonReportParams,
  reportClick
} from '@/src/utils/report';
import { Comment } from '@Components/comment';
import {
  DoubleClickLike,
  DoubleClickLikeActions
} from '@Components/doubleClickLike';
import { HEADER_HEIGHT, Screen } from '@Components/screen';
import { Text } from '@Components/text';
import { StyleSheet } from '@Utils/StyleSheet';
import { useParams } from '../../src/hooks/useParams';
import { PhotoSrc } from '@/proto-registry/src/web/raccoon/common/assets_pb';
import {
  BottomBar,
  BottombarHandle
} from '@BizComponents/detailScreen/bottomBar';
import {
  DetailHeader,
  DetailHeaderDisplayMode
} from '@BizComponents/detailScreen/header';
import { ImageContent } from '@BizComponents/detailScreen/imageContent';
import { getDeduplicatedPhotos } from '@BizComponents/detailScreen/sameSheet';
import { RouteProp, useRoute } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';
import { DetailEventBus } from './eventbus';

const CARD_NOT_FOUNT_ERROR = 10002;
const CARD_PUBLISH_NOT_ACCESSIBLE = 10003;
const ICON_HL = require('@Assets/image/feed/tab_highlight.png');
export type ScrollViewRef = {
  get: () => ScrollView;
};

function getGameTypeFromQuery(s: string) {
  return Number(s) ? Number(s) : undefined;
}

type DetailRouteParams = {
  id: string;
  gameId: string;
  gameType: string;
  inviteType: string;
};

export default function Detail() {
  const {
    id,
    gameId,
    gameType: gameTypeString,
    inviteType
  } = useParams<DetailRouteParams>();
  const cardId = Array.isArray(id) ? id[0] : id;
  const gameType = getGameTypeFromQuery(gameTypeString as string);

  const { enableDetail } = useGameFeatureGate();
  const $containerInsets = useSafeAreaInsetsStyle(['top', 'bottom']);
  const { loginIntercept } = useAuthState();
  const { go2Create } = useChangeRoute();
  const { markPerformanceTimestamp, enableSubmit } = usePerformanceCollector(
    HomePagePerformanceConfigDefault,
    {
      cardId,
      gameType
    }
  );

  // #region state
  const { loading, error, detail, commonInfo } = useDetailStore(
    useShallow(state => {
      const info = state.getDetail(cardId);
      return {
        loading: info?.loading,
        error: info?.error,
        detail: info?.detail,
        commonInfo: info?.commonInfo
      };
    })
  );

  const [autoPlay, setAutoPlay] = useState(true);
  const [sharing, setSharing] = useState(false);
  const [isImageContentVisible, setIsImageContentVisible] = useState(true);

  const { update: updateEmojiInfo } = useSetEmojiCreatorInfo(cardId, {
    detail: detail || undefined
  });
  // #endregion state

  // #region refs
  const hasReplacePath = useRef(false);
  const tabPositionRef = useRef(0);
  const imageHeight = useRef<number>(0); // 记录图片区域的初始位置和尺寸信息
  const imageInitialY = useRef<number>(0);
  const scrollY = useRef<number>(0);

  const bottomRef = useRef<BottombarHandle>(null);
  const scrollViewRef =
    useRef<ScrollViewRef>() as MutableRefObject<ScrollViewRef>;
  const doubleClickLikeRef = useRef<DoubleClickLikeActions>(null);
  const formulaCompRef = useRef<RolesSectionV2Actions>(null);
  // #endregion refs

  // #region style
  const headerDisplayPercent = useSharedValue(0);
  const headerDisplayChangeHeight = useRef<number>(getScreenSize('height'));
  const headerHeight = useRef<number>(0);
  // #endregion style

  // #region computed
  const ownerUid = commonInfo?.profile?.uid;
  const showRolesSection = Boolean(detail?.usingRoles?.length);
  const showGoodsDecoration =
    Boolean(detail?.gameType === GameType.GOODS) && ownerUid;
  const enableTakePhoto =
    detail?.gameType !== GameType.PUBLICATION &&
    detail?.photos &&
    detail.photos.length > 0;

  const flattenPhotos = useMemo(
    () => getDeduplicatedPhotos(detail?.photos ?? []),
    [detail?.photos]
  );

  const displayCommentCount = commonInfo?.stat?.comments || 0;
  const displayCommentCountStr =
    displayCommentCount === 0 ? '' : formatNumber(displayCommentCount);
  // #endregion computed

  // #region handler
  // 处理图片区域布局变化
  const handleImageAreaLayout = useMemoizedFn((layout: GestureArea) => {
    // 记录高度和初始Y位置
    imageHeight.current = layout.height;
    imageInitialY.current = layout.y;
  });

  // 计算当前禁用手势区域
  const getDisableGestureArea = useMemoizedFn((): GestureArea | undefined => {
    if (imageHeight.current === 0) return undefined;

    return {
      x: 0,
      y: imageInitialY.current - scrollY.current,
      width: WINDOW_WIDTH,
      height: imageHeight.current
    };
  });

  const onScroll = useMemoizedFn(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { layoutMeasurement, contentOffset, contentSize } =
        event.nativeEvent;
      const offsetY = contentOffset.y;
      const contentHeight = contentSize.height;
      const scrollHeight = layoutMeasurement.height;

      if (scrollHeight + offsetY > contentHeight - 200) {
        CommentEventBus.emit('scrollComment');
      }

      // 记录当前滚动位置
      scrollY.current = event.nativeEvent.contentOffset.y;

      const target = headerDisplayChangeHeight.current - headerHeight.current;
      const start = 0;
      const end = -1 * HEADER_HEIGHT;

      const processed = Math.max(
        Math.min(start, target - scrollY.current),
        end
      );
      headerDisplayPercent.value = processed / (end - start);
    }
  );

  const onScreenCapture = useMemoizedFn(() => {
    CommentEventBus.emit(CommentEvent.COLSE_COMMENT_INPUT);
  });

  const onUpdateAutoPlay = useMemoizedFn((auto: boolean = false) => {
    if (autoPlay !== auto) {
      setAutoPlay(auto);
    }
  });

  const onTakePhoto = useMemoizedFn((photoMaybeComposed?: PhotoInfo) => {
    const photo =
      photoMaybeComposed?.subPhotos && photoMaybeComposed?.subPhotos.length > 0
        ? photoMaybeComposed.subPhotos[0]
        : photoMaybeComposed;
    const { photoId: id, url } = photo || {};

    if (inviteType === SHARE_ACTIVITY_TYPE) {
      const uid = commonInfo?.profile?.uid;
      triggerMakeCopy({
        cardId: cardId,
        ownerUid: uid
      }).then((res: any) => {
        console.log('====triggerMakeCopy====', res);
      });
    }

    go2Create({
      gameType: photo?.gameType || gameType || GameType.DRAWING,
      gameParams: {
        cardId: cardId || '',
        photoId: id,
        photoUrl: url,
        topics: detail?.topics as TopicInfo[]
      },
      params: {
        source: Source.DRAWING_WITH_PROMPT
      }
    });
  });

  const onPreviewImage = useMemoizedFn((props: PreviewImageProps) => {
    const allowTakeSamePhoto = !detail?.images && detail?.photos.length;
    showPreviewImages({
      ...props,
      renderTopRightSlot: () => null,
      renderBottomLeftSlot: (target, { saveImage }) => (
        <ImageSharePanel
          image={target}
          saveImage={saveImage}
          reportParams={{
            save_page: '0'
          }}
        />
      ),
      renderBottomRightSlot: target =>
        allowTakeSamePhoto ? (
          <TakePhotoButton
            style={{
              top: 2,
              height: 44,
              width: PREVIEW_TAKE_PHOTO_BUTTON_WIDTH
            }}
            gameType={target?.gameType}
            onPress={() => {
              hidePreviewImages();
              onTakePhoto(target);
            }}
          />
        ) : null
    });
  });

  const onContentFlagLayout = useMemoizedFn((e: LayoutChangeEvent) => {
    headerDisplayChangeHeight.current = e.nativeEvent.layout.y;
  });

  const onHeaderLayout = useMemoizedFn((e: LayoutChangeEvent) => {
    headerHeight.current = e.nativeEvent.layout.height;
  });

  const onUserSharing = useMemoizedFn(() => {
    setSharing(true);
  });

  const onCloseSharing = useMemoizedFn(() => {
    setSharing(false);
  });

  const onImageAllLoad = useMemoizedFn(() => {
    enableSubmit();
  });

  // #endregion handler

  // #region effects
  useLayoutEffect(() => {
    markPerformanceTimestamp('detail_init_timestamp');
  }, []);

  useEffect(() => {
    if (!detail?.photos) return;
    if (inviteType === SHARE_ACTIVITY_TYPE && ownerUid) {
      loginIntercept(
        async () => {
          const uid = ownerUid;
          const result = await bindInvitation({ inviter: uid, cardId: cardId });
          const { toastContent } = result || {};
          if (toastContent) {
            showToast(toastContent);
          }
          const photos = detail?.photos;
          if (!photos) return;
          onTakePhoto(photos[0]);
        },
        { scene: LOGIN_SCENE.TAKE_SAME_STYLE }
      );
    }
  }, [inviteType, ownerUid, cardId, detail?.photos]);

  useEffect(() => {
    DetailEventBus.on('scrollToComment', () => {
      reportClick(
        'buttombar_button',
        { contentid: detail?.cardId, module: 'detail' },
        'comment'
      );
      const ref = scrollViewRef?.current?.get();
      ref && ref.scrollTo({ y: tabPositionRef.current });
    });

    return () => {
      DetailEventBus.off('scrollToComment');
    };
  }, []);

  useEffect(() => {
    const fetchDetailInfo = () => {
      const { getDetail, requestDetail } = useDetailStore.getState();
      if (!getDetail(cardId)?.loading) {
        const gid = Array.isArray(gameId) ? gameId[0] : gameId;
        requestDetail({
          cardId,
          gameId: gid,
          gameType
        }); // todo 参数有问题
      }
    };

    fetchDetailInfo();
  }, [cardId, gameType]);

  useEffect(() => {
    // 避免多次触发 router.replace
    if (hasReplacePath.current) {
      return;
    }

    if (
      // 作品不存在
      (error?.code === CARD_NOT_FOUNT_ERROR && !loading) ||
      (error?.code === CARD_PUBLISH_NOT_ACCESSIBLE && !loading) ||
      // 存在错误且没有缓存
      (error && !loading && !detail)
    ) {
      router.replace('/empty-page');
      hasReplacePath.current = true;
    }
  }, [error, loading, detail, commonInfo]);

  useEffect(() => {
    if (detail?.topics) {
      addCommonReportParams('detail', {
        tag_name: detail?.topics.map(i => i.title).join(',')
      });
    }
  }, [detail]);

  useEffect(() => {
    if (gameType && !enableDetail.includes(gameType)) {
      router.replace('/fallback');
    }
  }, [enableDetail, gameType]);
  // #endregion effects

  // 用户资料页面内容
  const renderPreviewContent = useMemoizedFn(() => {
    return ownerUid ? (
      <UserScreen
        pageTab="works"
        key={`user-${ownerUid}`}
        isRootPage={false}
        fromSwipePreview={true}
        id={ownerUid}
        preloadProfile={commonInfo?.profile}
        onBack={() => {
          SwipeScreenEventBus.emit('swipeScreenGoBack');
        }}
      />
    ) : null;
  });

  const renderSwipeScreenContent = useMemoizedFn(() => (
    <>
      <HeaderMask />
      <DetailHeader
        detailId={cardId}
        displayMode={DetailHeaderDisplayMode.SIMPLE}
        style={$detailHeaderStyle}
        onLayout={onHeaderLayout}
        onSharing={onUserSharing}
        onCloseSharing={onCloseSharing}
      />
      <DetailHeader
        detailId={cardId}
        displayMode={DetailHeaderDisplayMode.DISPLAY_ALL}
        headerDisplayPercent={headerDisplayPercent}
        style={$detailHeaderStyle}
        onSharing={onUserSharing}
        onCloseSharing={onCloseSharing}
      />
      <Screen
        ScrollViewComp={IOScrollView}
        scrollViewRef={scrollViewRef}
        theme="dark"
        preset="auto"
        safeAreaEdges={[]}
        contentContainerStyle={{
          height: 'auto',
          paddingBottom: Number($containerInsets.paddingBottom ?? 0) + 84
        }}
        screenStyle={{
          backgroundColor: StyleSheet.darkTheme.background.page
        }}
        headerShown={false}
        ScrollViewProps={{
          onScroll,
          scrollEventThrottle: 100,
          showsVerticalScrollIndicator: false
        }}
      >
        <ImageContent
          data={detail || undefined}
          onLike={config => {
            if (config?.doubleLike) {
              doubleClickLikeRef.current?.start(
                config.doubleLike.offsetX,
                config.doubleLike.offsetY
              );
            }

            if (bottomRef.current && bottomRef.current.onLike) {
              bottomRef.current.onLike();
            }
          }}
          onScreenCapture={onScreenCapture}
          autoPlay={autoPlay}
          updateAutoPlay={onUpdateAutoPlay}
          isVisible={!sharing && isImageContentVisible}
          onPreviewImage={onPreviewImage}
          // parentGestureValue={lastImageGestureValue}
          onImageAreaLayout={handleImageAreaLayout}
          markPerformanceTimestamp={markPerformanceTimestamp}
          onImageAllLoad={onImageAllLoad}
        />
        <View onLayout={onContentFlagLayout} />
        {cardId ? (
          <DetailHeader
            detailId={cardId}
            displayMode={DetailHeaderDisplayMode.DISPLAY_AUTHOR_INFO}
          />
        ) : null}
        <TextContent data={detail || undefined} theme={Theme.DARK} />
        {showGoodsDecoration && <GoodsDecoration uid={ownerUid} />}
        {detail && showRolesSection ? (
          <InView
            triggerOnce={true}
            onChange={inView => {
              if (inView) {
                formulaCompRef.current?.onExpo?.();
              }
            }}
          >
            <RolesSectionV2
              ref={formulaCompRef}
              theme={Theme.DARK}
              detailId={cardId}
              onRoleSheetStatusChange={(visible: boolean) => {
                setIsImageContentVisible(!visible);
              }}
              onRoleDelete={updateEmojiInfo}
              topics={detail.topics as TopicInfo[]}
            />
          </InView>
        ) : null}

        <View style={$fixWrapperStyle}>
          <Text
            style={[
              tabStyles.$tabItemTextStyle,
              {
                color: StyleSheet.darkTheme.text.disabled
              }
            ]}
          >{`评论 ${displayCommentCountStr}`}</Text>
        </View>

        {<Comment detailId={cardId} onFocus={updateEmojiInfo} />}
      </Screen>
      <BottomBar
        detailId={cardId}
        ref={bottomRef}
        enableTakePhoto={enableTakePhoto}
        gameType={gameType}
        onTakePhoto={() => {
          reportClick('join_button', {
            module: 'detail',
            contentid: detail?.cardId,
            is_tag: (detail?.topics?.length ?? 0) > 0,
            tag_name: detail?.topics?.map(t => t?.title ?? '').join('') ?? '',
            module_click: JOIN_BTN_MODULE.DETAIL,
            share_type: inviteType === SHARE_ACTIVITY_TYPE ? 2 : 1,
            game_type: detail?.gameType,
            source: Source.DRAWING_WITH_PROMPT
          });
          loginIntercept(
            () => {
              const photos = flattenPhotos;

              if (!photos) {
                showToast('哎呀出错了，可以试下退出重进哦');
              }
              onTakePhoto(photos[0]);
            },
            { scene: LOGIN_SCENE.TAKE_SAME_STYLE }
          );
        }}
      />
      <DoubleClickLike ref={doubleClickLikeRef} />
    </>
  ));

  return (
    <PagePerformance pathname="detail/[id]">
      <SwipeScreen
        theme="dark"
        renderPreview={renderPreviewContent}
        enabled={true}
        // allowAutoPreload={Platform.OS === 'ios'}
        // shouldAutoPreload={!loading && !error}
        shouldAutoPreload={false}
        allowAutoPreload={false}
        // 最后一张图片不用触发 swipe screen 了
        // externalGestureValue={Platform.select({
        //   ios: lastImageGestureValue
        // })}
        disableGestureArea={getDisableGestureArea()}
        onSwipeStatusChanged={() => {
          CommonEventBus.emit('resetUserScreenTimer');
        }}
        beforeBackPressed={async () => {
          // 发送事件给 UserScreen 组件，让它处理挽留弹窗逻辑
          // 这里使用 Promise 和事件通信机制
          if (ownerUid) {
            // 创建一个 Promise，在 UserScreen 组件处理完毕后解析
            return new Promise<void>(resolve => {
              // 发送事件，让 UserScreen 组件处理返回逻辑
              // 需要在 src/bizComponents/userScreen/index.tsx 中实现事件监听
              CommonEventBus.emit('handleUserScreenBack', {
                resolve,
                ownerUid
              });
            });
          }
          return Promise.resolve();
        }}
      >
        {renderSwipeScreenContent()}
      </SwipeScreen>
    </PagePerformance>
  );
}

const tabStyles = StyleSheet.create({
  $tabStyle: {
    width: '80%',
    marginLeft: '10%',
    height: 60,
    alignItems: 'center',
    ...StyleSheet.rowStyle
  },

  $tabItemStyle: {
    flex: 1
  },
  $tabItemTextStyle: {
    textAlign: 'center',
    fontSize: 14,
    lineHeight: 26,
    fontWeight: '600'
  },
  $tabActiveSlashWrapper: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    display: 'flex',
    alignItems: 'center'
  },
  $tabActiveSlash: {
    width: 30,
    height: 2,
    backgroundColor: StyleSheet.hex(StyleSheet.currentColors.black, 0.87),
    borderRadius: 4
  },
  $tabActiveNode: {
    position: 'absolute',
    top: 10,
    left: 60,
    width: 46,
    height: 33
  },
  $tabContent: {
    paddingBottom: 13
  }
});

const $detailHeaderStyle: ViewStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  zIndex: 1
};

const $fixWrapperStyle: ViewStyle = {
  width: '100%',
  marginLeft: 16,
  marginTop: 30,
  alignItems: 'center',
  ...StyleSheet.rowStyle
};
