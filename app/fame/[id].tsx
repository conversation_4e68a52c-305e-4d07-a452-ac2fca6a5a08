import { useGlobalSearchParams } from 'expo-router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { TextStyle, View, ViewStyle } from 'react-native';
import { queryClient } from '@/src/api/query';
import { FameCard } from '@/src/bizComponents/fameScreen/FameCard';
import { GAMETYPE_MAP } from '@/src/bizComponents/fameScreen/constant';
import { Screen } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { addCommonParams } from '@/src/components/share/utils';
import { useUserInfoStore } from '@/src/store/userInfo';
import { CommonColor } from '@/src/theme/colors/common';
import { Theme } from '@/src/theme/colors/type';
import { GameType, Pagination } from '@/src/types';
import { StyleSheet } from '@/src/utils';
import { addCommonReportParams } from '@/src/utils/report';
import { DataList } from '@Components/datalist';
import { Icon, IconTypes } from '@Components/icons';
import { Image } from '@Components/image';
import { InfiniteList } from '@Components/infiniteList';
import {
  SkeletonColumn,
  SkeletonRow,
  SkeletonSpan
} from '@Components/skeletion';
import { Text } from '@Components/text';
import { useParams } from '../../src/hooks/useParams';
import type { PartialMessage } from '@bufbuild/protobuf';
import {
  GetCardCopyRankRsp,
  GetUserCopyRankRsp
} from '@step.ai/proto-gen/raccoon/query/query_pb';
import { useShallow } from 'zustand/react/shallow';

const BG = require('@Assets/image/fame/bg.png');

const $numRowStyle: ViewStyle = {
  ...StyleSheet.rowStyle,
  alignItems: 'center',
  gap: 8,
  marginBottom: 12
};
const $numTextStyle: TextStyle = {
  color: StyleSheet.currentColors.textGray
};

export default function Fame() {
  const params = useParams<{
    id?: string;
  }>();

  const { profile, stat } = useUserInfoStore(
    useShallow(state => {
      const userInfo = params.id ? state.getUserInfo(params.id) : undefined;
      return {
        profile: userInfo?.profile,
        stat: userInfo?.stat
      };
    })
  );
  const [copyCnt, setCopyCnt] = useState(stat?.beingCopieds);

  const nameStr = useMemo(() => {
    const name = profile?.name || '';
    if (name.length <= 9) {
      return name;
    }
    return name.slice(0, 8) + '...';
  }, [profile?.name]);

  const gameTypeCopyCnts = useCallback(() => {
    return stat?.gameTypeBeingCopieds?.map((item, index) => {
      const config =
        item.gameType && GAMETYPE_MAP[item.gameType]
          ? GAMETYPE_MAP[item.gameType]
          : GAMETYPE_MAP[GameType.DRAWING];
      return config ? (
        <View key={index} style={$numRowStyle}>
          <Icon icon={config.icon} />
          <Text style={$numTextStyle}>
            {config.label}
            {item?.beingCopieds?.toString()}次
          </Text>
        </View>
      ) : null;
    });
  }, [stat]);

  return (
    <PagePerformance pathname="fame/[id]">
      <Screen
        theme="light"
        backgroundView={
          <View
            style={[
              StyleSheet.absoluteFill,
              {
                position: 'absolute',
                top: 0,
                width: '100%',
                backgroundColor: StyleSheet.lightTheme.background.page
              }
            ]}
          >
            <Image
              source={BG}
              style={[StyleSheet.absoluteFill, { height: 473 }]}
            />
          </View>
        }
        titleStyle={{ flex: 20 }}
        screenStyle={{
          paddingHorizontal: 0,
          paddingBottom: 0
        }}
        headerTitle={() => (
          <Text
            preset="bold"
            size="md"
            numberOfLines={1}
            ellipsizeMode="clip"
            style={{
              width: '100%',
              textAlign: 'center'
              // color: StyleSheet.darkTheme.text.solid
            }}
          >
            {nameStr}获得的声望值
          </Text>
        )}
      >
        <DataList<GetCardCopyRankRsp, GetUserCopyRankRsp>
          fetchData={fetchData}
          renderItem={({ item }) => <FameCard item={item} />}
          theme={Theme.LIGHT}
          customView={listNode => {
            return (
              <>
                <View
                  style={[
                    StyleSheet.rowStyle,
                    { gap: 4, paddingLeft: 30, paddingBottom: 16 }
                  ]}
                >
                  <Text
                    style={{
                      fontFamily: 'AlibabaPuHuiTiBold',
                      fontWeight: '800',
                      lineHeight: 45,
                      fontSize: 42,
                      color: StyleSheet.currentColors.titleGray
                    }}
                  >
                    {stat?.beingCopieds?.toString()}
                  </Text>
                  <Text
                    style={{
                      fontFamily: 'AlibabaPuHuiTiBold',
                      fontSize: 18,
                      fontWeight: '700'
                    }}
                  >
                    声望
                  </Text>
                </View>
                <View style={{ paddingLeft: 30, paddingBottom: 10 }}>
                  {gameTypeCopyCnts()}
                </View>
                {listNode}
              </>
            );
          }}
          contentContainerStyle={{ paddingHorizontal: 10, paddingVertical: 20 }}
          renderSkeleton={renderSkeleton}
        />
      </Screen>
    </PagePerformance>
  );

  function fetchData(pagination: PartialMessage<Pagination>) {
    return queryClient
      .getUserCopyRank({ uid: params.id, pagination })
      .then(res => {
        return {
          resp: res,
          list: res.cards,
          pagination: res.pagination
        };
      });
  }
}

function renderSkeleton() {
  return (
    <SkeletonColumn
      style={{ paddingHorizontal: 30, paddingVertical: 20 }}
      gap={10}
    >
      <SkeletonSpan height={75} width={135}></SkeletonSpan>
      <SkeletonSpan height={20} width={148}></SkeletonSpan>
      <SkeletonSpan height={20} width={148}></SkeletonSpan>
      <SkeletonColumn repeat={2}>
        <SkeletonSpan
          height={223}
          width={'100%'}
          style={{
            backgroundColor: CommonColor.white,
            overflow: 'hidden',
            padding: 10
          }}
          radius={10}
        >
          <SkeletonRow>
            <SkeletonSpan
              style={{ borderRadius: 6, marginBottom: 10 }}
              height={60}
              width={45}
            ></SkeletonSpan>
            <SkeletonColumn>
              <SkeletonSpan height={20} width={126}></SkeletonSpan>
              <SkeletonSpan height={20} width={100}></SkeletonSpan>
            </SkeletonColumn>
          </SkeletonRow>
          <SkeletonRow gap={10}>
            <SkeletonSpan
              height={128}
              width={96}
              style={{ borderRadius: 6 }}
            ></SkeletonSpan>
            <SkeletonSpan
              height={128}
              width={96}
              style={{ borderRadius: 6 }}
            ></SkeletonSpan>
            <SkeletonSpan
              height={128}
              width={96}
              style={{ borderRadius: 6 }}
            ></SkeletonSpan>
            <SkeletonSpan
              height={128}
              width={96}
              style={{ borderRadius: 6 }}
            ></SkeletonSpan>
          </SkeletonRow>
        </SkeletonSpan>
      </SkeletonColumn>
    </SkeletonColumn>
  );
}
