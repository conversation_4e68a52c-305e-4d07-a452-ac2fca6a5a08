import { useMemoizedFn } from 'ahooks';
import { SplashScreen, useNavigation } from 'expo-router';
import React, { memo, useEffect, useRef, useState } from 'react';
import { AppState, LogBox, TouchableOpacity, View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { useSharedValue } from 'react-native-reanimated';
import { createSharedElementStackNavigator } from 'react-navigation-shared-element';
import { lipuAPIEnv } from '@/src/api/env';
import { PublishGlobal } from '@/src/bizComponents/globalPublish';
import { CustomSplashScreen } from '@/src/bizComponents/splashScreen';
import { useControlSplash } from '@/src/bizComponents/splashScreen/useControlSplash';
import { Devtools } from '@/src/components/Devtools';
import { GuideModalGlobal } from '@/src/components/guide';
import { MakePhotoFixExceptionModal } from '@/src/components/popup/MakePhotoFixExceptionModal';
import { CustomConfirmGlobal } from '@/src/components/popup/confirmModalGlobal/CustomConfirm';
import { MagicVideoModal } from '@/src/components/popup/magicVideoModal';
import { PreviewImageGlobal } from '@/src/components/previewImageModal';
import { MessageGlobal } from '@/src/components/v2/message';
import { SystemMessageGlobal } from '@/src/components/v2/systemMessage';
import { TOAST_PORTAL, TRANSPARENT_VIDEO_PORTAL } from '@/src/constants';
import { useGlobalPageChange } from '@/src/hooks/global/useGlobalPageChange';
import { useLifeCycle } from '@/src/hooks/global/useLifeCycle';
import { useRouterAnime } from '@/src/hooks/useRouterAnime';
import { useTeenModeGuard } from '@/src/hooks/useTeenModeGuard';
import { useWorklet } from '@/src/hooks/useWorklet';
import { useAppStore } from '@/src/store/app';
import { useGoodsShefStore } from '@/src/store/goods_shef';
import { usePerformanceStore } from '@/src/store/performance';
import { Theme } from '@/src/theme/colors/type';
import { isIos } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { ModalLayerTracker } from '@/src/utils/modalLayerTracker';
import {
  reportPageGlobal,
  reportPerformanceTrack,
  setCurrentNavigatorTab,
  setPageName
} from '@/src/utils/report';
import { homePerformanceCollector } from '@/src/utils/report/homePageCollector';
import { Image } from '@Components/image';
import {
  LoadingGlobal,
  SheetProvider,
  ToastGlobal,
  WebviewGlobal,
  showToast
} from '@Components/index';
import { LoginGlobal } from '@Components/login';
import Lottie, { LottieRef } from '@Components/lottieView';
import { ModalGlobal, showModal } from '@Components/popup/ModalGlobal';
import { UpgradeModal } from '@Components/popup/UpgradeModal';
import { ConfirmGlobal } from '@Components/popup/confirmModalGlobal/Confirm';
import { CommonShareModalGlobal, ShareModalGlobal } from '@Components/share';
import { StyleSheet } from '@Utils/StyleSheet';
import { FollowGuideToastGlobal } from '../src/components/popup/detailFollowToast';
import { darkTheme } from '../src/theme/colors';
import HelpingHistory from './activity/helping-history';
import RewardHistory from './activity/reward-history';
import EditPendant from './avatar-edit/edit-pendant';
import CustomLyrics from './boom-room/custom-lyrics';
import GenerateImage from './boom-room/generate-image';
import CrefSearchResult from './cref-search/result';
import DecisionRank from './decision-rank/[id]';
import DecisionEntry from './decision/entry';
import Detail from './detail/[id]';
import DreamDescScreen from './dream-mirror-polaroid/dream-desc';
import DreamSelectScreen from './dream-mirror-polaroid/dream-select';
import EmojiCreate from './emoji/create';
import EmojiPreview from './emoji/preview';
import EmojiRecreate from './emoji/recreate/[id]';
import Fame from './fame/[id]';
import GoodsEditScreen from './goods/edit';
import GoodsHomeScreen from './goods/home';
import HouseTemplate from './house-dance/house-template';
import IpDetailScreen from './ip/[brandId]';
import KitchenResult from './kitchen/result';
import ImageClipPage from './magic-video-edit/clip';
import FramePickerPage from './magic-video-edit/frame-picker';
import ScriptVideo from './magic-video-edit/script-video';
import MakePhotoRoleSelect from './make-photo/role-select';
import BatteryRecord from './mall/battery-record';
import OrderHistory from './mall/order-history';
import MemeEffect from './meme/effect';
import MemeRoleSelect from './meme/role-select';
import Camera from './message/camera';
import Follow from './message/follow';
import Like from './message/like';
import ParallelWord from './parallel-world/[id]';
import ParallelWorldCenter from './parallel-world/center';
import PhotoElement from './photo-element/[id]';
import PK from './pk/[id]';
import Post from './post/[id]';
import ProfileEdit from './profile/edit';
import SchoolRank from './rank-list/school';
import RoleEdit from './role-create/edit';
import SearchPrefer from './search/prefer';
import SearchResult from './search/result';
import AboutScreen from './setting/about';
import Account from './setting/account';
import FeedBack from './setting/feedback';
import PassWord from './setting/password';
import SocialMedia from './setting/social-media';
import TeenMode from './setting/teenmode';
import VideoFlowScreen from './short-video/[id]';
import TopicDetail from './topic-page/[id]';
import Topic from './topic/[type]/[id]';
import User from './user/[id]';
import WelfareRewardHistory from './welfare/reward-history';
import Tarot from './welfare/tarot';
import { Portal, PortalHost, PortalProvider } from '@gorhom/portal';
import { ParamListBase } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import * as APMModule from '@step.ai/apm-module';
import { shouldEnableDevTools } from '@step.ai/app-info-module';
import AiPlayNext from './ai-play-next';
import AvatarEdit from './avatar-edit';
import BBSFeed from './bbs-feed';
import BoomRoomScreen from './boom-room';
import Bot from './bot';
import Credit from './credit';
import CrefSearch from './cref-search';
import DressUpCenter from './dressup-center';
import EmptyPage from './empty-page';
import UpdatePage from './fallback';
import Feed from './feed';
import FollowFan from './follow-fan';
import FortuneRecord from './fortune-record';
import Gacha from './gacha';
import GoodsScreen from './goods';
import Kitchen from './kitchen';
import livePhotoPublish from './live-photo-publish';
import lotteryFission from './lottery-fission';
import MagicVideoEdit from './magic-video-edit';
import MagicVideoHistory from './magic-video-history';
import MagicVideoPublish from './magic-video-publish';
import MakePhoto from './make-photo';
import Mall from './mall';
import MemePublish from './memePublish';
import Message from './message';
import Name from './name';
import PKNominate from './pk-nominate';
import Playground from './playground';
import SimplePreview from './preview';
import Publish from './publish';
import PublishCheck from './publishCheck';
import RankList from './rank-list';
import ReceiveAddress from './receive-address';
import RoleScreen from './role';
import RoleCreate from './role-create';
import Search from './search';
import Setting from './setting';
import Activity from './share-activity';
import SoulMaker from './soul-maker';
import Sref from './sref';
import Webview from './webview';
import Welfare from './welfare';
import WishCard from './wishcard';

export { ErrorBoundary } from '@/src/components/error/ErrorBoundary';
if (__DEV__) {
  LogBox.ignoreLogs([
    'Failed prop type: Invalid prop `externalScrollView`',
    /\[ExpoImage\].*/,
    'Require cycle:'
  ]);
}

// (() => {
//   const originLog = console.log;
//   console.log = (...args) => {
//     if (String(args[0]).startsWith('###')) {
//       originLog(...args);
//     }
//   };
// })();

// eslint-disable-next-line @typescript-eslint/no-explicit-any
(BigInt.prototype as any).toJSON = function () {
  return this.toString();
};

interface ParamList extends ParamListBase {
  [key: string]: object & { id: string; uid: string; brandId: string };
}

const Stack = createSharedElementStackNavigator<ParamList>();
const NativeStack = createNativeStackNavigator<ParamList>();

SplashScreen.preventAutoHideAsync();
homePerformanceCollector.markPerformanceTimestamp('app_init_timestamp');
// APMModule.beginLaunchSpan('hide_splash');

// 安卓路由
const NativeStackWrapper = () => {
  return (
    <NativeStack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'none',
        contentStyle: {
          backgroundColor: '#16161A'
        }
      }}
      initialRouteName="feed/index"
    >
      <NativeStack.Screen name="feed/index" component={Feed} key="feed" />
      <NativeStack.Screen
        name="post/[id]"
        component={Post}
        getId={({ params }) => params?.id ?? ''}
      />
      <NativeStack.Screen
        name="detail/[id]"
        component={Detail}
        getId={({ params }) => params?.id ?? ''}
      />
      <NativeStack.Screen
        name="parallel-world/[id]"
        getId={({ params }) => params?.id ?? ''}
        component={ParallelWord}
      />
      <NativeStack.Screen name="ai-play-next/index" component={AiPlayNext} />
      <NativeStack.Screen name="setting/index" component={Setting} />
      <NativeStack.Screen name="photo-element/[id]" component={PhotoElement} />
      <NativeStack.Screen name="kitchen/index" component={Kitchen} />
      <NativeStack.Screen name="kitchen/result" component={KitchenResult} />
      <NativeStack.Screen
        name="user/[id]"
        getId={({ params }) => params?.id ?? ''}
        component={User}
      />
      <NativeStack.Screen
        name="make-photo/index"
        options={{
          gestureEnabled: false
        }}
        component={MakePhoto}
      />
      <NativeStack.Screen
        name="make-photo/role-select"
        component={MakePhotoRoleSelect}
      />
      <NativeStack.Screen name="message/index" component={Message} />
      <NativeStack.Screen name="message/like" component={Like} />
      <NativeStack.Screen name="message/camera" component={Camera} />
      <NativeStack.Screen name="message/follow" component={Follow} />
      <NativeStack.Screen name="name/index" component={Name} />
      <NativeStack.Screen name="empty-page/index" component={EmptyPage} />
      <NativeStack.Screen name="setting/account" component={Account} />
      <NativeStack.Screen name="setting/social-media" component={SocialMedia} />
      <NativeStack.Screen name="setting/feedback" component={FeedBack} />
      <NativeStack.Screen name="setting/teenmode" component={TeenMode} />
      <NativeStack.Screen name="setting/password" component={PassWord} />
      <NativeStack.Screen name="setting/about" component={AboutScreen} />
      <NativeStack.Screen name="webview" component={Webview} />
      <NativeStack.Screen
        name="follow-fan/index"
        component={FollowFan}
        getId={res => {
          return res.params?.uid || '';
        }}
      />
      <NativeStack.Screen
        name="ip/[brandId]"
        getId={res => {
          return res.params?.brandId || '';
        }}
        component={IpDetailScreen}
      />
      <NativeStack.Screen name="search/index" component={Search} />
      <NativeStack.Screen
        name="search/result"
        component={SearchResult}
        options={{ animation: 'none' }}
      />
      <NativeStack.Screen
        name="search/prefer"
        component={SearchPrefer}
        options={{ animation: 'fade' }}
      />
      <NativeStack.Screen name="avatar-edit/index" component={AvatarEdit} />
      <NativeStack.Screen
        name="avatar-edit/edit-pendant"
        component={EditPendant}
      />
      <NativeStack.Screen
        name="dressup-center/index"
        component={DressUpCenter}
      />
      {/* <Stack.Screen name="make-photo/index" component={MakePhoto} /> */}
      <NativeStack.Screen
        name="publish/index"
        component={Publish}
        options={{
          gestureEnabled: false
        }}
      />
      <NativeStack.Screen
        name="live-photo-publish/index"
        component={livePhotoPublish}
        options={{
          gestureEnabled: false
        }}
      />
      <NativeStack.Screen name="memePublish/index" component={MemePublish} />
      <NativeStack.Screen name="wishcard/index" component={WishCard} />
      {/* <Stack.Screen name="emoji/[id]" component={Emoji} /> */}
      <NativeStack.Screen
        name="emoji/recreate/[id]"
        component={EmojiRecreate}
      />
      <NativeStack.Screen name="emoji/create" component={EmojiCreate} />
      <NativeStack.Screen name="emoji/preview" component={EmojiPreview} />
      <NativeStack.Screen name="welfare/index" component={Welfare} />
      <NativeStack.Screen name="welfare/tarot/index" component={Tarot} />
      <NativeStack.Screen
        name="welfare/reward-history"
        component={WelfareRewardHistory}
      />
      <NativeStack.Screen name="gacha/index" component={Gacha} />
      <NativeStack.Screen name="credit/index" component={Credit} />
      <NativeStack.Screen name="mall/index" component={Mall} />
      <NativeStack.Screen
        name="mall/battery-record"
        component={BatteryRecord}
      />
      <NativeStack.Screen name="mall/order-history" component={OrderHistory} />
      <NativeStack.Screen
        name="short-video/[id]"
        component={VideoFlowScreen}
        getId={({ params }) => params?.id ?? ''}
      />
      <NativeStack.Screen name="soul-maker/index" component={SoulMaker} />
      <NativeStack.Screen name="playground/index" component={Playground} />
      {/* 角色选择 */}
      <NativeStack.Screen name="meme/role-select" component={MemeRoleSelect} />
      <NativeStack.Screen name="meme/effect" component={MemeEffect} />
      <NativeStack.Screen
        name="parallel-world/center"
        component={ParallelWorldCenter}
      />
      <NativeStack.Screen name="goods/index" component={GoodsScreen} />
      <NativeStack.Screen name="goods/edit" component={GoodsEditScreen} />
      <NativeStack.Screen name="goods/home" component={GoodsHomeScreen} />
      {/* <NativeStack.Screen name="test/index" component={Test} /> */}
      <NativeStack.Screen name="topic/[type]/[id]" component={Topic} />
      <NativeStack.Screen
        name="topic-page/[id]/index"
        component={TopicDetail}
        getId={({ params }) => {
          return params?.id ?? '';
        }}
      />
      <NativeStack.Screen name="share-activity/index" component={Activity} />
      <NativeStack.Screen name="sref/index" component={Sref} />
      <NativeStack.Screen
        name="fame/[id]"
        component={Fame}
        getId={({ params }) => {
          return params?.id ?? '';
        }}
      />
      {/* <NativeStack.Screen name="[...404]" component={UpdatePage} /> */}
      <NativeStack.Screen name="publishCheck" component={PublishCheck} />
      <NativeStack.Screen name="cref-search/index" component={CrefSearch} />
      <NativeStack.Screen
        name="cref-search/result"
        component={CrefSearchResult}
      />
      {/* 角色创建 */}
      <NativeStack.Screen name="role-create/index" component={RoleCreate} />
      {/* 角色魔改 */}
      <NativeStack.Screen
        name="magic-video-edit/index"
        component={MagicVideoEdit}
      />
      <NativeStack.Screen
        name="magic-video-history/index"
        component={MagicVideoHistory}
      />
      <NativeStack.Screen
        name="magic-video-edit/clip"
        component={ImageClipPage}
      />
      <NativeStack.Screen
        name="magic-video-edit/frame-picker"
        component={FramePickerPage}
      />
      <NativeStack.Screen
        name="magic-video-publish/index"
        component={MagicVideoPublish}
      />
      <NativeStack.Screen
        name="magic-video-edit/script-video"
        component={ScriptVideo}
      />
      <NativeStack.Screen name="role-create/edit" component={RoleEdit} />
      <NativeStack.Screen name="role/index" component={RoleScreen} />
      <NativeStack.Screen name="fallback/index" component={UpdatePage} />
      {/* 梦核 */}
      <NativeStack.Screen
        name="dream-mirror-polaroid/dream-desc"
        component={DreamDescScreen}
      />
      <NativeStack.Screen
        name="dream-mirror-polaroid/dream-select"
        component={DreamSelectScreen}
        options={{
          gestureEnabled: false
        }}
      />
      {/* 排行榜 */}
      <NativeStack.Screen name="rank-list/index" component={RankList} />
      <NativeStack.Screen name="rank-list/school" component={SchoolRank} />
      {/* 脑洞小游戏 */}
      <NativeStack.Screen name="decision/entry" component={DecisionEntry} />
      <NativeStack.Screen name="decision-rank/[id]" component={DecisionRank} />
      {/* 宅舞 */}
      <NativeStack.Screen
        name="house-dance/house-template"
        component={HouseTemplate}
      />
      {/* 新春 */}
      <NativeStack.Screen name="boom-room/index" component={BoomRoomScreen} />
      <NativeStack.Screen
        name="boom-room/generate-image"
        component={GenerateImage}
      />
      <NativeStack.Screen
        name="boom-room/custom-lyrics"
        component={CustomLyrics}
      />
      <NativeStack.Screen name="profile/edit" component={ProfileEdit} />
      {/* pk玩法 */}
      <NativeStack.Screen
        name="pk/[id]"
        component={PK}
        getId={({ params }) => params?.id ?? ''}
      />
      <NativeStack.Screen name="pk-nominate/index" component={PKNominate} />
      <NativeStack.Screen name="preview/index" component={SimplePreview} />
      <NativeStack.Screen name="bot/index" component={Bot} />
      <NativeStack.Screen
        name="fortune-record/index"
        component={FortuneRecord}
      />
      <NativeStack.Screen
        name="receive-address/index"
        component={ReceiveAddress}
      />
      {/* 狸电池实物裂变 */}
      <NativeStack.Screen
        name="lottery-fission/index"
        component={lotteryFission}
      />
      <NativeStack.Screen
        name="activity/helping-history"
        component={HelpingHistory}
      />
      <NativeStack.Screen
        name="activity/reward-history"
        component={RewardHistory}
      />
      {/* 论坛 */}
      <NativeStack.Screen name="bbs-feed/index" component={BBSFeed} />
      {/* 兜底路由，保持在最底部 todo @linyueqiang 暂时注释，待测试后打开 */}
      <NativeStack.Screen name="[...404]" component={UpdatePage} />
    </NativeStack.Navigator>
  );
};

// ios路由
const StackWrapper = () => {
  const {
    RA_SearchIndexOption,
    RA_SearchEaseOption,
    RA_SearchPreferOption,
    RA_DetailOption,
    RA_WorldOption,
    RA_SoulOption,
    RA_RoleHomeOption
  } = useRouterAnime();
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false
      }}
      initialRouteName="feed/index"
    >
      <Stack.Screen name="feed/index" component={Feed} key="feed" />
      <Stack.Screen
        name="post/[id]"
        component={Post}
        getId={({ params }) => params?.id ?? ''}
        options={{
          cardStyle: {
            backgroundColor: darkTheme.background.page
          }
        }}
      />
      <Stack.Screen
        name="detail/[id]"
        component={Detail}
        options={RA_DetailOption}
        getId={({ params }) => params?.id ?? ''}
        sharedElements={(route, otherRoute, showing) => {
          const { cardId } = route.params;
          if (
            ['feed/index', 'ip/[brandId]', 'search/result'].includes(
              otherRoute.name
            )
          ) {
            return [
              {
                id: `item.${cardId}.photo`,
                animation: 'move'
              }
            ];
          }
        }}
      />
      <Stack.Screen
        name="parallel-world/[id]"
        getId={({ params }) => params?.id ?? ''}
        component={ParallelWord}
        options={RA_WorldOption}
      />
      <Stack.Screen name="setting/index" component={Setting} />
      <Stack.Screen name="photo-element/[id]" component={PhotoElement} />
      <Stack.Screen name="kitchen/index" component={Kitchen} />
      <Stack.Screen name="kitchen/result" component={KitchenResult} />
      <Stack.Screen
        name="user/[id]"
        getId={({ params }) => params?.id ?? ''}
        component={User}
      />
      <Stack.Screen
        name="make-photo/index"
        options={{
          gestureEnabled: false
        }}
        component={MakePhoto}
        sharedElements={(route, otherRoute, showing) => {
          const { photoId } = otherRoute.params;
          return [
            {
              id: `make-photo-${photoId}`,
              animation: 'move'
            }
          ];
        }}
      />
      <Stack.Screen
        name="make-photo/role-select"
        component={MakePhotoRoleSelect}
      />
      <Stack.Screen name="message/index" component={Message} />
      <Stack.Screen name="message/like" component={Like} />
      <Stack.Screen name="message/camera" component={Camera} />
      <Stack.Screen name="message/follow" component={Follow} />
      <Stack.Screen name="name/index" component={Name} />
      <Stack.Screen name="empty-page/index" component={EmptyPage} />
      <Stack.Screen name="setting/account" component={Account} />
      <Stack.Screen name="setting/social-media" component={SocialMedia} />
      <Stack.Screen name="setting/feedback" component={FeedBack} />
      <Stack.Screen name="setting/teenmode" component={TeenMode} />
      <Stack.Screen name="setting/password" component={PassWord} />
      <Stack.Screen name="setting/about" component={AboutScreen} />
      <Stack.Screen name="webview" component={Webview} />
      <Stack.Screen
        name="follow-fan/index"
        component={FollowFan}
        getId={res => {
          return res.params?.uid || '';
        }}
      />
      <Stack.Screen
        name="ip/[brandId]"
        getId={res => {
          return res.params?.brandId || '';
        }}
        component={IpDetailScreen}
      />
      <Stack.Screen
        name="search/index"
        component={Search}
        options={{
          ...RA_SearchIndexOption
        }}
      />
      <Stack.Screen
        name="search/result"
        component={SearchResult}
        options={RA_SearchEaseOption}
      />
      <Stack.Screen
        name="search/prefer"
        component={SearchPrefer}
        options={RA_SearchPreferOption}
      />
      <Stack.Screen name="avatar-edit/index" component={AvatarEdit} />
      <Stack.Screen name="avatar-edit/edit-pendant" component={EditPendant} />
      <Stack.Screen name="dressup-center/index" component={DressUpCenter} />
      {/* <Stack.Screen name="make-photo/index" component={MakePhoto} /> */}
      <Stack.Screen
        name="publish/index"
        component={Publish}
        options={{
          gestureEnabled: false
        }}
      />
      <Stack.Screen
        name="live-photo-publish/index"
        component={livePhotoPublish}
        options={{
          gestureEnabled: false
        }}
      />
      <Stack.Screen name="memePublish/index" component={MemePublish} />
      <Stack.Screen name="wishcard/index" component={WishCard} />
      {/* <Stack.Screen name="emoji/[id]" component={Emoji} /> */}
      <Stack.Screen name="emoji/recreate/[id]" component={EmojiRecreate} />
      <Stack.Screen name="emoji/create" component={EmojiCreate} />
      <Stack.Screen name="emoji/preview" component={EmojiPreview} />
      <Stack.Screen name="welfare/index" component={Welfare} />
      <Stack.Screen name="welfare/tarot/index" component={Tarot} />
      <Stack.Screen
        name="welfare/reward-history"
        component={WelfareRewardHistory}
      />
      <Stack.Screen name="gacha/index" component={Gacha} />
      <Stack.Screen name="credit/index" component={Credit} />
      <Stack.Screen name="mall/index" component={Mall} />
      <Stack.Screen name="mall/battery-record" component={BatteryRecord} />
      <Stack.Screen name="mall/order-history" component={OrderHistory} />
      <Stack.Screen
        name="short-video/[id]"
        component={VideoFlowScreen}
        options={RA_DetailOption}
        getId={({ params }) => params?.id ?? ''}
      />
      <Stack.Screen
        name="soul-maker/index"
        options={RA_SoulOption}
        component={SoulMaker}
      />
      <Stack.Screen name="playground/index" component={Playground} />
      {/* 角色选择 */}
      <Stack.Screen name="meme/role-select" component={MemeRoleSelect} />
      <Stack.Screen
        name="meme/effect"
        component={MemeEffect}
        sharedElements={(route, otherRoute, showing) => {
          const { photoId } = otherRoute.params;
          return [
            {
              id: `make-photo-${photoId}`,
              animation: 'move'
            }
          ];
        }}
      />
      <Stack.Screen
        name="parallel-world/center"
        component={ParallelWorldCenter}
      />
      <Stack.Screen name="goods/index" component={GoodsScreen} />
      <Stack.Screen name="goods/edit" component={GoodsEditScreen} />
      <Stack.Screen name="goods/home" component={GoodsHomeScreen} />
      {/* <Stack.Screen name="test/index" component={Test} /> */}
      <Stack.Screen name="ai-play-next/index" component={AiPlayNext} />
      <Stack.Screen name="topic/[type]/[id]" component={Topic} />
      <Stack.Screen
        name="topic-page/[id]/index"
        component={TopicDetail}
        getId={({ params }) => {
          return params?.id ?? '';
        }}
      />
      <Stack.Screen name="share-activity/index" component={Activity} />
      <Stack.Screen name="sref/index" component={Sref} />
      <Stack.Screen
        name="fame/[id]"
        component={Fame}
        getId={({ params }) => {
          return params?.id ?? '';
        }}
      />
      {/* <Stack.Screen name="[...404]" component={UpdatePage} /> */}
      <Stack.Screen name="publishCheck" component={PublishCheck} />
      <Stack.Screen name="cref-search/index" component={CrefSearch} />
      <Stack.Screen name="cref-search/result" component={CrefSearchResult} />
      {/* 角色创建 */}
      <Stack.Screen name="role-create/index" component={RoleCreate} />
      {/* 角色魔改 */}
      <Stack.Screen name="magic-video-edit/index" component={MagicVideoEdit} />
      <Stack.Screen
        name="magic-video-history/index"
        component={MagicVideoHistory}
      />
      <Stack.Screen name="magic-video-edit/clip" component={ImageClipPage} />
      <Stack.Screen
        name="magic-video-edit/frame-picker"
        component={FramePickerPage}
      />
      <Stack.Screen
        name="magic-video-publish/index"
        component={MagicVideoPublish}
      />
      <Stack.Screen
        name="magic-video-edit/script-video"
        component={ScriptVideo}
      />
      <Stack.Screen name="role-create/edit" component={RoleEdit} />
      <Stack.Screen name="role/index" component={RoleScreen} />
      <Stack.Screen name="fallback/index" component={UpdatePage} />
      {/* 梦核 */}
      <Stack.Screen
        name="dream-mirror-polaroid/dream-desc"
        component={DreamDescScreen}
      />
      <Stack.Screen
        name="dream-mirror-polaroid/dream-select"
        component={DreamSelectScreen}
        options={{
          gestureEnabled: false
        }}
      />
      {/* 排行榜 */}
      <Stack.Screen name="rank-list/index" component={RankList} />
      <Stack.Screen name="rank-list/school" component={SchoolRank} />
      {/* 脑洞小游戏 */}
      <Stack.Screen name="decision/entry" component={DecisionEntry} />
      <Stack.Screen name="decision-rank/[id]" component={DecisionRank} />
      {/* 宅舞 */}
      <Stack.Screen
        name="house-dance/house-template"
        component={HouseTemplate}
      />
      {/* 新春 */}
      <Stack.Screen name="boom-room/index" component={BoomRoomScreen} />
      <Stack.Screen name="boom-room/generate-image" component={GenerateImage} />
      <Stack.Screen name="boom-room/custom-lyrics" component={CustomLyrics} />
      <Stack.Screen name="profile/edit" component={ProfileEdit} />
      {/* pk玩法 */}
      <Stack.Screen
        name="pk/[id]"
        component={PK}
        getId={({ params }) => params?.id ?? ''}
      />
      <Stack.Screen name="pk-nominate/index" component={PKNominate} />
      <Stack.Screen name="preview/index" component={SimplePreview} />
      <Stack.Screen name="bot/index" component={Bot} />
      <Stack.Screen name="fortune-record/index" component={FortuneRecord} />
      <Stack.Screen name="receive-address/index" component={ReceiveAddress} />
      {/* 狸电池实物裂变 */}
      <Stack.Screen name="lottery-fission/index" component={lotteryFission} />
      <Stack.Screen
        name="activity/helping-history"
        component={HelpingHistory}
      />
      <Stack.Screen name="activity/reward-history" component={RewardHistory} />
      {/* 论坛 */}
      <Stack.Screen
        name="bbs-feed/index"
        component={BBSFeed}
        options={{ animationTypeForReplace: 'pop' }}
      />
      {/* 兜底路由，保持在最底部 todo @linyueqiang 暂时注释，待测试后打开 */}
      <Stack.Screen name="[...404]" component={UpdatePage} />
    </Stack.Navigator>
  );
};

function RootLayoutNav() {
  // It's not only for logging env, but also to make sure the initial env is synced to native
  console.log('Current env', lipuAPIEnv.currentAPIEnvItem());

  const { showSplashScreen, allowRenderLayout } = useControlSplash();

  // 路由和参数变化，执行数据上报等逻辑
  useGlobalPageChange();
  useLifeCycle([]);

  const { isTeenMode } = useTeenModeGuard();

  const navigation = useNavigation();
  useEffect(() => {
    let gotoRouteName = '';
    let st = 0;
    let isBack = 0;
    let stack: string[] = [];

    // 首页 feed
    setPageName('feed');

    // 监听路由变化，执行路由跳转前会下发跳转指令，并执行下面回调
    const unsubscribeRouteEnter = navigation.addListener(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      '__unsafe_action__' as any,
      e => {
        st = Date.now();
        console.log('e?.data?.action----', e?.data?.action);
        if (e?.data?.action && e.data.action.type === 'RESET') {
          stack = e.data.action.payload.routes.map(item => {
            return item?.params?.name || item?.name;
          });

          const lastItem =
            e.data.action.payload.routes[
              e.data.action.payload.routes?.length - 1
            ];
          if (lastItem?.name === 'feed/index') {
            // todo 副作用，暂时
            setCurrentNavigatorTab(lastItem?.params?.name || 'feed');
          }
          console.log('stack----', stack);
          reportPageGlobal(stack[stack.length - 1], 0);
        } else if (e?.data?.action?.payload?.name) {
          st = Date.now();
          gotoRouteName = e.data.action.payload.name;
          stack.push(gotoRouteName);
          reportPageGlobal(gotoRouteName, 0);
          // alert(gotoRouteName);
        }
        // 回退
        if (
          e?.data?.action &&
          (e.data.action.type === 'GO_BACK' || e.data.action.type === 'POP')
        ) {
          isBack = 1;
          usePerformanceStore.getState().setCommonState({ is_back: 1 });
          stack.pop();
          // alert(stack[stack.length - 1]);
          reportPageGlobal(stack[stack.length - 1], 1);
        } else {
          isBack = 0;
          usePerformanceStore.getState().setCommonState({ is_back: 0 });
        }

        console.log('__unsafe_action__', e?.data);
      }
    );
    const unsubscribe = navigation.addListener('state', e => {
      if (gotoRouteName && st) {
        const renderTime =
          usePerformanceStore.getState().pageMetrics[gotoRouteName]
            ?.preStepDuration ?? 0;
        const duration = Math.max(Date.now() - st - renderTime, 0);

        // 设置上报公参
        const lastPage =
          usePerformanceStore.getState().commonState.current_page;
        usePerformanceStore.getState().setCommonState({
          last_page: lastPage,
          current_page: gotoRouteName,
          stack: e.data.state.routes // todo 不准，要细化下tab
            .map(item => {
              return (
                item.state?.routeNames?.[item.state?.index || 0] || item.name
              );
            })
            .join(',')
        });
        usePerformanceStore.getState().report('route', {
          duration,
          target_page: gotoRouteName,
          is_back: isBack
        });
        usePerformanceStore.getState().removePageMetrics(gotoRouteName);
      }
      // 我也不希望+这种奇怪的逻辑，为了用户页面的 bounce动画 做判断
      const routeStates = e.data.state.routes?.map(r => r.state);
      const routeKeys = e.data.state.routes?.map(r => r.key);

      const routerGoodsLock = routeKeys?.some(item => item?.includes('goods'));

      if (routerGoodsLock && routeKeys?.length > 1) {
        useGoodsShefStore.getState().syncLockBounce(true);
        return;
      }
      if (
        routeStates &&
        routeStates[0]?.index &&
        routeStates[0]?.index !== 3 &&
        routeKeys?.length === 1
      ) {
        useGoodsShefStore.getState().syncLockBounce(false);
        return;
      }
    });
    return () => {
      unsubscribe();
      unsubscribeRouteEnter();
    };
  }, [navigation]);

  const showDevtools = shouldEnableDevTools();

  const renderLayout = () => {
    /**：
     * 如果是青少年模式，则只挂载 Feed、TeenMode、PassWord 这三个页面
     */
    if (isTeenMode) {
      return (
        <>
          <SheetProvider>
            <LoginGlobal />
            <UpgradeModal />
            <MakePhotoFixExceptionModal />
            <Stack.Navigator
              screenOptions={{
                headerShown: false
              }}
              initialRouteName="feed/index"
            >
              {/* 只保留以下三个页面 */}
              <Stack.Screen name="feed/index" component={Feed} key="feed" />
              <Stack.Screen name="setting/teenmode" component={TeenMode} />
              <Stack.Screen name="setting/password" component={PassWord} />
            </Stack.Navigator>
            {/* 如果需要 Devtools 且已同意协议才显示 */}
            {!!showDevtools && <Devtools />}
            <ToastGlobal theme={Theme.DARK} />
          </SheetProvider>
          {/* Portal Hosts */}
          <PortalHost name={TOAST_PORTAL} />
          <PortalHost name={TRANSPARENT_VIDEO_PORTAL} />
        </>
      );
    }

    /**
     * 如果不是青少年模式，渲染原先全部路由
     */
    return (
      <>
        <SheetProvider>
          <SystemMessageGlobal />
          <MessageGlobal />
          <LoginGlobal />
          <WebviewGlobal />
          <ConfirmGlobal />
          <CustomConfirmGlobal />
          <ModalGlobal />
          <PreviewImageGlobal />
          <ShareModalGlobal />
          <CommonShareModalGlobal />
          <UpgradeModal />
          <MakePhotoFixExceptionModal />

          {/* <HalloweenModal /> */}
          <MagicVideoModal />
          <GuideModalGlobal />
          <PublishGlobal />
          <FollowGuideToastGlobal />

          {isIos ? <StackWrapper /> : <NativeStackWrapper />}
          {/* <Video /> */}
          {!!showDevtools && <Devtools />}
          <ToastGlobal theme={Theme.DARK} />
        </SheetProvider>
        {/* Toast 专用的 Portal */}
        <PortalHost name={TOAST_PORTAL} />
        {/* 透明视频需要一个单独的portal，消除回流导致的重新播放bug */}
        <PortalHost name={TRANSPARENT_VIDEO_PORTAL} />
      </>
    );
  };

  return (
    <>
      {allowRenderLayout ? renderLayout() : null}
      {showSplashScreen ? <CustomSplashScreen /> : null}
    </>
  );
}

export default function RootLayout() {
  // const [count, setCount] = useState(0);

  return (
    <GestureHandlerRootView>
      <PortalProvider>
        <LoadingGlobal />
        {/* <Portal>
        <View
          style={{
            position: 'absolute',
            top: 100,
            right: 20,
            zIndex: 9999
          }}
        >
          <TouchableOpacity
            style={{
              backgroundColor: '#000',
              padding: 10,
              borderRadius: 5
            }}
            onPress={() => {
              setCount(prev => {
                const newCount = prev + 1;
                showToast(`这是第 ${newCount} 次点击`, 1000, true);
                return newCount;
              });
            }}
          >
            <Text style={{ color: '#fff' }}>点击测试 Toast ({count})</Text>
          </TouchableOpacity>
        </View>
      </Portal> */}
        <RootLayoutNav />
      </PortalProvider>
    </GestureHandlerRootView>
  );
}
