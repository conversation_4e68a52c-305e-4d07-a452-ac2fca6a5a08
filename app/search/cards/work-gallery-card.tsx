import dayjs from 'dayjs';
import { router } from 'expo-router';
import { useEffect } from 'react';
import {
  ImageStyle,
  Pressable,
  Text,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { DetailLike } from '@/src/components/like';
import { LikeStyle } from '@/src/components/like/LikeIcon';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { typography } from '@/src/theme';
import { CommonColor } from '@/src/theme/colors/common';
import { $SEARCH_COLORS, $USE_FONT, $Z_INDEXES } from '@/src/theme/variable';
import { GameType, RichCardInfo } from '@/src/types';
import { dp2px } from '@/src/utils';
import { encodeSP } from '@/src/utils/placeholder';
import { reportClick, reportExpo } from '@/src/utils/report';
import { Image } from '@Components/image';

const CardMaskIcon = require('@Assets/image/feed/card_mask.png');

export default function MiniWorkGalleryCard({
  cardInfo,
  keywords,
  cardIndex
}: {
  cardInfo: RichCardInfo;
  keywords?: string;
  cardIndex?: number;
}) {
  const { go2Detail } = useChangeRoute();
  const reportLike = (isLiked: boolean) => {
    // TODO: report
  };

  const imageLoadOpacity = useSharedValue(1);
  //   const imageLoadOpacity = useSharedValue(0);
  const $imageLoadStyle = useAnimatedStyle(() => ({
    opacity: imageLoadOpacity.value
  }));

  const handleOnLoad = () => {
    imageLoadOpacity.value = withTiming(1, {
      duration: 200
    });
  };

  const enterGallery = () => {
    reportClick('userlist_contentlist', {
      words: keywords,
      contentid: cardIndex
    });

    if (cardInfo.card?.gameType && cardInfo.card?.id) {
      go2Detail(
        cardInfo.card.gameType === GameType.WORLD
          ? {
              gameType: cardInfo.card.gameType,
              gameParams: {
                title: cardInfo.card?.title,
                cardId: cardInfo.card?.id || ''
              }
            }
          : {
              gameType: cardInfo.card.gameType,
              gameParams: {
                cardId: cardInfo.card?.id || ''
              },
              routeParams: {
                gameId: cardInfo.card?.gameId || '',
                displayImageUrl: cardInfo.card?.displayImageUrl || ''
              }
            }
      );
    }
  };

  useEffect(() => {
    reportExpo('userlist_contentlist', {
      words: keywords,
      contentid: cardIndex
    });
  }, []);

  return (
    <Pressable onPress={enterGallery}>
      <View style={[$cardBg]}>
        {/* <Animated.View style={$imageLoadStyle}> */}
        <Image
          onLoad={handleOnLoad}
          style={[
            {
              flex: 1
            }
          ]}
          source={cardInfo.card?.displayImageUrl}
          contentPosition={'center'}
          tosSize="size4"
        />
        <View style={$cardView}>
          <Text
            numberOfLines={1}
            ellipsizeMode={'tail'}
            style={[
              $title,
              {
                marginRight: -2
              }
            ]}
          >
            {cardInfo?.card?.title}
          </Text>
          <View style={$cardSubView}>
            <Text style={$subText}>
              {dayjs(Number(cardInfo?.card?.createAt) * 1000).format('MM-DD')}
            </Text>
            <DetailLike
              cardId={cardInfo?.card?.id!}
              liked={false}
              likeCount={Number(cardInfo.socialStat?.beingLikeds)}
              size={13}
              style={{
                height: 18,
                alignItems: 'center'
              }}
              activeColor={CommonColor.black40}
              inactiveColor={CommonColor.black40}
              inactiveIconColor={'#fff'}
              fontStyle={{
                fontSize: 11,
                marginLeft: 2,
                marginTop: 1,
                fontWeight: '500',
                fontFamily: typography.fonts.pingfangSC.normal,
                color: '#fff'
              }}
              onLikeClicked={reportLike}
            />
          </View>
        </View>
        <Image source={CardMaskIcon} style={$cardMask} />
        {/* </Animated.View> */}
      </View>
    </Pressable>
  );
}

const $cardBg: ViewStyle = {
  width: 134,
  height: 180,
  borderRadius: 8,
  overflow: 'hidden',
  backgroundColor: $SEARCH_COLORS.searchBg,
  position: 'relative'
};

const $cardMask: ImageStyle = {
  width: '100%',
  height: 78,
  position: 'absolute',
  bottom: 0,
  opacity: 0.6,
  zIndex: $Z_INDEXES.z1
};

const $cardView: ImageStyle = {
  width: '100%',
  height: 72,
  position: 'absolute',
  bottom: 0,
  padding: 8,
  flexDirection: 'column',
  justifyContent: 'flex-end',
  zIndex: $Z_INDEXES.z10
};

const $cardSubView: ViewStyle = {
  flexDirection: 'row',
  justifyContent: 'space-between'
};

const $title: TextStyle = $USE_FONT(
  $SEARCH_COLORS.white,
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  '600',
  18
);

const $subText: TextStyle = $USE_FONT(
  $SEARCH_COLORS.white,
  typography.fonts.pingfangSC.normal,
  11,
  'normal',
  '500',
  18
);
