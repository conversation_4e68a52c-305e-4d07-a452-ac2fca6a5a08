import { router } from 'expo-router';
import { useEffect, useMemo, useState } from 'react';
import {
  ImageStyle,
  LayoutChangeEvent,
  Pressable,
  Text,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useWorldStore } from '@/src/store/world';
import { typography } from '@/src/theme';
import { $SEARCH_COLORS, $USE_FONT, $Z_INDEXES } from '@/src/theme/variable';
import { GameType, RichCardInfo } from '@/src/types';
import { StyleSheet, isIos } from '@/src/utils';
import { dp2px } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { Image } from '@Components/image';
import { StrokeText } from '@charmy.tech/react-native-stroke-text';

const PARALLEL_MASK = require('@Assets/image/search/mini-para-bg.png');

const CardMaskIcon = require('@Assets/image/feed/card_mask.png');

export default function MiniWorkParamCard({
  cardInfo,
  keywords,
  cardIndex
}: {
  cardInfo: RichCardInfo;
  keywords?: string;
  cardIndex?: number;
}) {
  const reportLike = (isLiked: boolean) => {
    // TODO: report
  };

  const [cardWidth, setCardWidth] = useState(0);
  const { go2Detail } = useChangeRoute();

  const imageLoadOpacity = useSharedValue(1);
  //   const imageLoadOpacity = useSharedValue(0);
  const $imageLoadStyle = useAnimatedStyle(() => ({
    opacity: imageLoadOpacity.value
  }));

  const handleOnLoad = () => {
    imageLoadOpacity.value = withTiming(1, {
      duration: 200
    });
  };

  const [worldNameWidth, setWorldNameWidth] = useState(-200);
  const $worldNameOpacity = useSharedValue(0);

  const $worldNameOffsetStyle = useAnimatedStyle(() => {
    return {
      marginLeft: (134 - worldNameWidth) / 2, // 斜体居中,
      opacity: $worldNameOpacity.value
    };
  });

  const updateWorldNameWidth = (e: LayoutChangeEvent) => {
    setWorldNameWidth(e.nativeEvent.layout.width);
    $worldNameOpacity.value = withTiming(1, {
      duration: 300
    });
  };

  interface IWorldInfo {
    tag_id: number;
    world_name: string;
    world_num: string;
  }

  const worldInfo: IWorldInfo = useMemo(
    () => JSON.parse(cardInfo?.card?.extInfo || JSON.stringify('')),
    [cardInfo?.card?.extInfo]
  );

  const MAX_SLICE = 5;

  const enterWorld = () => {
    reportClick('userlist_contentlist', {
      words: keywords,
      contentid: cardIndex
    });

    if (cardInfo.card?.id) {
      go2Detail({
        gameType: GameType.WORLD,
        gameParams: {
          title: cardInfo.card?.title,
          cardId: cardInfo.card?.id || ''
        }
      });
    }
  };

  useEffect(() => {
    reportExpo('userlist_contentlist', {
      words: keywords,
      contentid: cardIndex
    });
  }, []);

  return (
    <Pressable onPress={enterWorld}>
      <View style={[$cardBg]}>
        <Image
          onLoad={handleOnLoad}
          style={[
            {
              flex: 1
            }
          ]}
          source={cardInfo?.card?.displayImageUrl}
          contentPosition={'center'}
          tosSize="size4"
        />
        <View style={$cardView}>
          <Animated.View
            style={[$worldNameWrapper, $worldNameOffsetStyle]}
            onLayout={updateWorldNameWidth}
          >
            <StrokeText
              text={`《${worldInfo.world_name?.length > MAX_SLICE ? worldInfo.world_name.slice(0, MAX_SLICE - 1) + '...' : worldInfo.world_name}》`}
              strokeWidth={isIos ? 1 : 2}
              strokeColor={'#000000'}
              fontFamily={typography.fonts.feed}
              numberOfLines={1}
              color={'#ffffff'}
              align="center"
              fontSize={16}
            />
          </Animated.View>
          <View style={$worldArea}>
            <Image
              style={[$worldMask]}
              source={PARALLEL_MASK}
              contentFit="fill"
            />

            <View
              style={[
                {
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                  height: 28,
                  marginVertical: 4
                }
              ]}
            >
              <Text style={$worldNum}>{worldInfo?.world_num}</Text>
              <Text style={$worldAttr}>平行世界</Text>
            </View>
          </View>
          <Text
            numberOfLines={1}
            ellipsizeMode={'tail'}
            style={[$title, $titleFont]}
          >
            {cardInfo?.card?.title}
          </Text>
        </View>
        <Image source={CardMaskIcon} style={$cardMask} />
      </View>
    </Pressable>
  );
}

const $cardBg: ViewStyle = {
  width: 134,
  height: 180,
  borderRadius: 8,
  overflow: 'hidden',
  backgroundColor: $SEARCH_COLORS.searchBg,
  position: 'relative'
};

const $cardMask: ImageStyle = {
  width: '100%',
  height: 78,
  position: 'absolute',
  bottom: 0,
  opacity: 0.6,
  zIndex: $Z_INDEXES.z1
};

const $cardView: ImageStyle = {
  width: '100%',
  height: '100%',
  position: 'absolute',
  bottom: 0,
  flexDirection: 'column',
  justifyContent: 'flex-end',
  zIndex: $Z_INDEXES.z10
};

const $title: TextStyle = {
  paddingHorizontal: 8,
  paddingBottom: 8,
  marginRight: -2
};

const $titleFont: TextStyle = $USE_FONT(
  $SEARCH_COLORS.white,
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  '600',
  18
);

const $worldNameWrapper: ViewStyle = {
  display: 'flex',
  height: 30,
  justifyContent: 'center',
  position: 'absolute',
  bottom: 60
};

const $worldName: TextStyle = {
  color: StyleSheet.colors.white,
  fontSize: 18,
  fontWeight: '400',
  lineHeight: 25,
  textAlign: 'center',
  height: 30,
  width: '100%',
  letterSpacing: -2,
  transform: [
    {
      translateY: 68
    }
  ]
};

const $worldMask: ImageStyle = {
  width: dp2px(118),
  height: dp2px(28),
  position: 'absolute'
};

const $worldArea: ViewStyle = {
  justifyContent: 'center',
  display: 'flex',
  flexDirection: 'row',
  position: 'relative',
  alignItems: 'center',
  paddingHorizontal: 8,
  height: 'auto'
};

const $worldNum: TextStyle = {
  color: '#377691',
  fontSize: 11,
  fontWeight: '400',
  fontFamily: typography.fonts.world
};

const $worldAttr: TextStyle = {
  color: '#000',
  fontSize: 11,
  fontWeight: '400',
  fontFamily: typography.fonts.world
};
