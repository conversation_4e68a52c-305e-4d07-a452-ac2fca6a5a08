import { router } from 'expo-router';
import { useCallback, useEffect } from 'react';
import { Pressable, View } from 'react-native';
import Animated from 'react-native-reanimated';
import ACBadge from '@/src/bizComponents/achievement/badge';
import { SCREEN_WIDTH } from '@/src/bizComponents/nestedScrollView';
import { Avatar } from '@/src/components/avatar';
import { Follow } from '@/src/components/follow';
import { IFollowProps } from '@/src/components/follow/index';
import { Text } from '@/src/components/text';
import { useUserInfoStore } from '@/src/store/userInfo';
import { typography } from '@/src/theme';
import { $USE_FONT } from '@/src/theme/variable';
import { PartialMessage, UserProfile } from '@/src/types';
import { stirngRemoveEnter } from '@/src/utils/opt/replace';
import { reportClick, reportExpo } from '@/src/utils/report';
import { StyleSheet } from '@Utils/StyleSheet';

export default function SearchUserItem(props: {
  followed: boolean;
  beingFollowed: boolean;
  onUnfollow?: (user: IFollowProps) => void;
  onFollow?: (user: IFollowProps) => void;
  isMine: boolean;
  type: string;
  fansNum?: number;
  gotLikeNum?: number;
  profile?: PartialMessage<UserProfile>;
  keywords?: string;
}) {
  const {
    onFollow,
    onUnfollow,
    followed,
    beingFollowed,
    isMine,
    type,
    fansNum,
    gotLikeNum,
    profile,
    keywords
  } = props;

  const { uid, avatar, name } = profile!;

  const jumpToUser = useCallback(() => {
    reportClick('userlist', {
      module: 'search',
      uid,
      words: keywords
    });
    useUserInfoStore.getState().syncUserInfo(uid || '', profile);
    router.navigate({
      pathname: '/user/[id]',
      params: {
        id: String(uid)
      }
    });
  }, [isMine, type, uid]);

  useEffect(() => {
    reportExpo('userlist', {
      module: 'search',
      uid,
      words: keywords
    });
  }, []);

  return (
    <Animated.View style={[userSt.container]}>
      <View style={userSt.left}>
        <Avatar profile={profile} size={54} source={avatar} />
        <Pressable onPress={jumpToUser}>
          <View style={userSt.userInfo}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={userSt.name} numberOfLines={1} ellipsizeMode="tail">
                {stirngRemoveEnter(name)}
              </Text>
              <ACBadge
                badgeIconStyle={{
                  marginLeft: 4
                }}
                icon={profile?.simpleAchievementInfo?.achievementIconUrl}
              />
            </View>
            <View style={userSt.subInfo}>
              <Text style={userSt.subText}>粉丝数 {fansNum ?? ' - '}</Text>
              <Text style={userSt.subText}> · </Text>
              <Text style={userSt.subText}>点赞数 {gotLikeNum ?? ' - '}</Text>
            </View>
          </View>
        </Pressable>
      </View>
      <View style={userSt.right}>
        <Follow
          uid={uid}
          followed={followed}
          beingFollowed={beingFollowed}
          onUnfollow={onUnfollow}
          onFollow={onFollow}
        />
      </View>
    </Animated.View>
  );
}

const userSt = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    height: 74
  },
  left: {
    width: 200,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start'
  },
  userInfo: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginLeft: 12,
    flex: 1,
    paddingVertical: 16
  },
  subInfo: {
    flexDirection: 'row'
  },
  subText: $USE_FONT(
    StyleSheet.darkTheme.text.secondary,
    typography.fonts.pingfangSC.normal,
    13,
    'normal',
    '400',
    undefined
  ),
  name: {
    fontSize: 15,
    lineHeight: 21,
    color: StyleSheet.darkTheme.text.primary,
    fontWeight: '600',
    maxWidth: SCREEN_WIDTH < 375 ? 160 : 200
  },
  right: {
    // width: 80,
    flex: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end'
  }
});
