import { useState } from 'react';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  View,
  ViewStyle
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { RichCardInfo } from '@/src/types';
import MiniWorkGalleryCard from '../cards/work-gallery-card';
import MiniWorkParaCard from '../cards/work-para-card';
import { EWorkCardType } from '../enums';
import { GameType } from '@/proto-registry/src/web/raccoon/common/types_pb';

export default function TopUserList({
  cards,
  keywords
}: {
  cards: RichCardInfo[];
  keywords?: string;
}) {
  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const offsetX = contentOffset.x;
    const contentHeight = contentSize.width;
    const scrollHeight = layoutMeasurement.width;

    if (scrollHeight + offsetX > contentHeight - 100) {
      //   loadUserList();
    }
  };

  return (
    <View>
      <ScrollView
        onScroll={onScroll}
        style={$searchContainer}
        contentContainerStyle={{
          marginBottom: 16
        }}
        horizontal
        scrollEventThrottle={100}
        showsHorizontalScrollIndicator={false}
        nestedScrollEnabled
      >
        {cards.map((card, cardIndex) => {
          let renderCard = <></>;
          switch (card?.card?.gameType) {
            case GameType.DRAWING: {
              renderCard = (
                <MiniWorkGalleryCard
                  cardInfo={card}
                  keywords={keywords}
                  cardIndex={cardIndex}
                ></MiniWorkGalleryCard>
              );
              break;
            }
            case GameType.WORLD: {
              renderCard = (
                <MiniWorkParaCard
                  cardInfo={card}
                  keywords={keywords}
                  cardIndex={cardIndex}
                ></MiniWorkParaCard>
              );
              break;
            }
            default:
              return;
          }
          return (
            <View
              key={cardIndex}
              style={cardIndex === 0 ? [{ marginRight: 4 }] : [$cardGap]}
            >
              {renderCard}
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
}

const $searchContainer: ViewStyle = {
  overflow: 'scroll'
};

const $cardGap: ViewStyle = {
  marginHorizontal: 4
};
