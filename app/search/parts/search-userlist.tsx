import { useNavigation } from 'expo-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  Text,
  View,
  ViewStyle
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue
} from 'react-native-reanimated';
import { FollowClient } from '@/src/api/follow';
import { searchClient } from '@/src/api/search';
import {
  RecSceneName,
  SearchFeatureName
} from '@/src/bizComponents/feedScreen/type';
import NestedScrollView from '@/src/bizComponents/nestedScrollView/components/nestedScrollView';
import { EmptyPlaceHolder, EmptyWrapper } from '@/src/components/Empty';
import { ScrollListRender } from '@/src/components/datalist';
import { useSafeAreaInsetsStyle } from '@/src/hooks';
import { useAuthStore } from '@/src/store/authInfo';
import { $SEARCH_COLORS } from '@/src/theme/variable';
import { Pagination, RichUserProfile, UserSocialStat } from '@/src/types';
import { FriendInfo } from '@/proto-registry/src/web/raccoon/follow/follow_pb';
import {
  SearchResult,
  SearchResultType,
  SearchUserResult
} from '@/proto-registry/src/web/raccoon/query/query_pb';
import { useIsFocused } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';
import SearchUserItem from './search-useritem';
import TopUserList from './top-userlist';

export default function SearchUserList({ keywords }: { keywords: string }) {
  const emptyOpacity = useSharedValue(1);

  const $emptyOpacityStyle = useAnimatedStyle(() => ({
    opacity: emptyOpacity.value
  }));

  const [isEmpty, setIsEmpty] = useState(false);

  const $containerInsets = useSafeAreaInsetsStyle(['bottom']);
  const navigation = useNavigation();
  const isFocused = useIsFocused();

  useEffect(() => {
    resetToken();
    keywords && loadUserList(keywords, true);
  }, [keywords]);

  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const offsetY = contentOffset.y;
    const contentHeight = contentSize.height;
    const scrollHeight = layoutMeasurement.height;

    if (scrollHeight + offsetY > contentHeight - 200) {
      loadUserList(keywords);
    }
  };

  const [userList, setUserList] = useState<SearchUserResult[]>([]);
  const PAGE_SIZE = 20;

  const { uid } = useAuthStore(
    useShallow(state => ({
      uid: state.uid
    }))
  );

  const nextPageToken = useRef<Partial<Pagination>>({
    size: 10,
    cursor: ''
  });

  const resetToken = () => {
    nextPageToken.current = {
      size: 10,
      cursor: ''
    };
  };

  const [hasError, setHasError] = useState(true);

  const loadUserList = async (keyword: string, isInit?: boolean) => {
    // 没有数据了

    if (nextPageToken?.current?.nextCursor === '') {
      return;
    }

    // 先 mock userlist
    try {
      const res = await searchClient.search({
        searchSceneName: RecSceneName.SEARCH,
        features: {
          user_tab: 'true'
          // mock: 'kingxliu_user_test',
          // game_type_tab: '5'
        },
        pagination: nextPageToken.current,
        keyword: keyword
      });
      console.log(
        res.result.length,
        '请求user列表response: ',
        res.pagination,
        '|req param:',
        {
          searchSceneName: RecSceneName.SEARCH,
          features: {
            user_tab: 'true'
            // mock: 'kingxliu_user_test',
            // game_type_tab: '5'
          },
          pagination: nextPageToken.current,
          keyword: keyword
        }
      );

      if (!res.result.length && !userList.length) {
        setIsEmpty(true);
      } else {
        setIsEmpty(false);
      }

      nextPageToken.current = {
        ...nextPageToken.current,
        cursor: res.pagination.nextCursor, // 用于下次请求
        nextCursor: res.pagination.nextCursor
      };

      const users = res.result
        ?.filter((item: SearchResult) => item.type === SearchResultType.User)
        .map((i: SearchResult) => i.item.value);

      if (isInit) {
        setUserList(users);
      } else {
        setUserList([...userList, ...users]);
      }
      setHasError(false);
    } catch (e) {
      setHasError(true);
      console.log('请求user列表错误: ', e);
    } finally {
    }
  };

  const onUpdatefollow = (user: RichUserProfile, isFollowed: boolean) => {
    const uid = user.profile?.uid;
    const newList = [...userList];
    const targetIndex = newList.findIndex(x => x.user?.profile?.uid === uid);
    if (targetIndex !== -1) {
      newList[targetIndex] = {
        ...newList[targetIndex],
        user: {
          ...newList[targetIndex]['user'],
          stat: {
            ...((newList[targetIndex]['user']?.['stat']
              ? newList[targetIndex]['user']?.['stat']
              : {}) as UserSocialStat),
            followed: isFollowed
          }
        }
      };
    }
    setUserList(newList);
  };

  return (
    <Animated.View style={[$userBg, $emptyOpacityStyle]}>
      {hasError ? (
        <EmptyPlaceHolder
          style={{ height: 400 }}
          buttonText="刷新"
          button={Boolean(loadUserList)}
          onButtonPress={() => loadUserList?.(keywords, true)}
        >
          哎呀，小狸走丢了
        </EmptyPlaceHolder>
      ) : !isEmpty ? (
        <ScrollView
          onScroll={onScroll}
          style={$searchContainer}
          contentContainerStyle={{
            height: 'auto',
            paddingBottom: Number($containerInsets.paddingBottom ?? 0)
          }}
          scrollEventThrottle={100}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled
        >
          <View style={$userList}>
            {userList.map((user: SearchUserResult, userIndex) => {
              const searchUser = user.user;
              const isMine = uid === searchUser?.profile?.uid;
              userIndex === 0 &&
                console.log(user.cards.length, 'toplist length');

              // 关注列表，初始必然关注
              // 是否被关注取决于isFriend字段

              return (
                <View>
                  {userIndex === 0 ? (
                    <View style={{ marginBottom: -8, marginTop: -8 }}>
                      <SearchUserItem
                        onUnfollow={() => onUpdatefollow(searchUser!, false)}
                        onFollow={() => onUpdatefollow(searchUser!, true)}
                        followed={searchUser?.stat?.followed!}
                        beingFollowed={searchUser?.stat?.beingFollowed!}
                        key={searchUser?.profile?.uid}
                        isMine={isMine}
                        type={'follow'}
                        fansNum={Number(searchUser?.stat?.fans)}
                        gotLikeNum={Number(searchUser?.stat?.beingLikeds)}
                        profile={searchUser?.profile!}
                        keywords={keywords}
                      />
                      {/* {user?.cards.length >= 3 ? (
                        <TopUserList
                          cards={user?.cards}
                          keywords={keywords}
                        ></TopUserList>
                      ) : null} */}
                    </View>
                  ) : (
                    <SearchUserItem
                      onUnfollow={() => onUpdatefollow(searchUser!, false)}
                      onFollow={() => onUpdatefollow(searchUser!, true)}
                      followed={searchUser?.stat?.followed!}
                      beingFollowed={searchUser?.stat?.beingFollowed!}
                      key={searchUser?.profile?.uid}
                      isMine={isMine}
                      type={'follow'}
                      fansNum={Number(searchUser?.stat?.fans)}
                      gotLikeNum={Number(searchUser?.stat?.beingLikeds)}
                      profile={searchUser?.profile!}
                      keywords={keywords}
                    />
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      ) : (
        <EmptyWrapper
          isEmpty={true}
          emptyText={'没有找到相关用户，换个词试试吧～'}
        ></EmptyWrapper>
      )}
    </Animated.View>
  );
}
const $searchContainer: ViewStyle = {};

const $userBg: ViewStyle = {
  flex: 1,
  paddingHorizontal: 8
};

const $userList: ViewStyle = {
  paddingHorizontal: 8,
  paddingVertical: 10
};
