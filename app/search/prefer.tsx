import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  GestureResponderEvent,
  KeyboardAvoidingView,
  NativeSyntheticEvent,
  ScrollView,
  TextInputFocusEventData,
  TextInputSubmitEditingEventData
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import SearchBar from '@/src/bizComponents/search/search-bar';
import { showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useParams } from '@/src/hooks/useParams';
import { useSearchStore } from '@/src/store/search';
import { $flex } from '@/src/theme/variable';
import { dp2px, isIos } from '@/src/utils';
import { decodeSP, encodeSP } from '@/src/utils/placeholder';
import { reportClick, reportExpo } from '@/src/utils/report';
import { StyleSheet } from '@Utils/StyleSheet';
import News from './parts/news';

export default function SearchPrefer() {
  const [searchText, setSearchText] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [isFirstPress, setIsFirstPress] = useState(true);

  const { result } = useParams();

  useEffect(() => {
    if (typeof result === 'string') {
      setSearchText(decodeSP(result || ''));
      setSearchValue(decodeSP(result || ''));
    }
  }, [result]);

  const onSearch = (
    e:
      | GestureResponderEvent
      | NativeSyntheticEvent<TextInputSubmitEditingEventData>,
    keywords: string
  ) => {
    if (!keywords.trim()) {
      showToast('请输入搜索内容~');
      return;
    }
    setIsFirstPress(true);

    reportClick('remind_inputconfirm', {
      module: 'search',
      words: keywords
    });

    router.navigate({
      pathname: '/search/result',
      params: {
        keywords: encodeSP(keywords)
      }
    });

    clearWord();
  };

  const onFocus = (
    e: NativeSyntheticEvent<TextInputFocusEventData>,
    keywords: string
  ) => {
    reportClick('begin_input', { module: 'search' });
  };

  const clearWord = () => {
    setSearchValue('');
  };

  const onChange = (v: string) => {
    setSearchValue(v);
    if (isFirstPress) {
      reportExpo('remind_input', { module: 'search' });
      setIsFirstPress(false);
    }
  };

  return (
    <PagePerformance pathname="search/prefer">
      <SafeAreaView
        style={[
          $flex,
          { backgroundColor: StyleSheet.darkTheme.background.page }
        ]}
      >
        <ScrollView keyboardShouldPersistTaps={'handled'}>
          <KeyboardAvoidingView behavior={isIos ? 'height' : undefined}>
            <SearchBar
              value={searchValue}
              onSearch={onSearch}
              onChange={onChange}
              onFocus={onFocus}
              placeHolderContent={
                searchText || useSearchStore.getState().lastPlaceText
              }
              autoFocus={true}
              clearWord={clearWord}
              ellipseWidth={dp2px(210)}
            ></SearchBar>
          </KeyboardAvoidingView>
          {!searchValue ? <News hidden={!!searchValue}></News> : <></>}
        </ScrollView>
      </SafeAreaView>
    </PagePerformance>
  );
}
