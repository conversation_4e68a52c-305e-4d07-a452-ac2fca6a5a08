import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Pressable, Text, TextStyle, View } from 'react-native';
import {
  Gesture,
  GestureDetector,
  PanGestureHandler
} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { feedClient } from '@/src/api';
import { searchClient } from '@/src/api/search';
import {
  RecSceneName,
  SearchFeatureName
} from '@/src/bizComponents/feedScreen/type';
import { hideLoading, showLoading, showToast } from '@/src/components';
import { EmptyWrapper } from '@/src/components/Empty';
import { RequestScene } from '@/src/components/infiniteList/typing';
import { WaterFall2 } from '@/src/components/waterfall/WaterFall2';
import {
  ECellCardReportType,
  EWaterFallTabReportType,
  EWaterFallTabType
} from '@/src/components/waterfall/type';
import {
  FetchMethodPayloadType,
  useRequestFeed
} from '@/src/components/waterfall/useRequsetFeed';
import { usePersistFn, useSafeBottomArea, useScreenSize } from '@/src/hooks';
import { useAuthState } from '@/src/hooks/useAuthState';
import { useParams } from '@/src/hooks/useParams';
import { useBotStore } from '@/src/store/bot';
import { typography } from '@/src/theme';
import {
  $SEARCH_COLORS,
  $USE_FONT,
  $flex,
  $flexCenter
} from '@/src/theme/variable';
import { GameType, ListResponse } from '@/src/types';
import { dp2px } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { BotSource, reportClick, reportExpo } from '@/src/utils/report';
import { StyleSheet } from '@Utils/StyleSheet';

interface ISearchWaterFallProps {
  isCurrentTab: boolean;
  feature: GameType;
  keywords: string;
}

const showRoleGuideList = [GameType.DRAWING, GameType.UNKNOWN].map(item =>
  item.toString()
);

export default function SearchWaterFall({
  isCurrentTab,
  feature,
  keywords
}: ISearchWaterFallProps) {
  const { loginIntercept } = useAuthState();
  const scrollStickyTop = useSharedValue(0);
  const emptyOpacity = useSharedValue(1);
  const roleGuideVisible = showRoleGuideList.indexOf(feature + '') > -1;

  const [isSticky, setIsSticky] = useState(false);

  const { appendId, ...searchParams } = useParams();

  const [extendedState, reportParams] = useMemo(() => {
    return [
      {
        reportParams: {
          from: 'search_scene',
          words: keywords
        }
      },
      {
        from: 'search_scene',
        words: keywords
      }
    ];
  }, [keywords]);

  useEffect(() => {
    if (isSticky) {
      scrollStickyTop.value = withTiming(-dp2px(280), {
        duration: 300
      });
      emptyOpacity.value = withTiming(0, {
        duration: 300
      });
    } else {
      scrollStickyTop.value = withTiming(0, {
        duration: 300
      });
      emptyOpacity.value = withTiming(1, {
        duration: 300
      });
    }
  }, [isSticky]);

  const $emptyOpacityStyle = useAnimatedStyle(() => ({
    opacity: emptyOpacity.value
  }));

  const $safePaddingBottom = useSafeBottomArea();

  const onSearchScrollWaterfall = usePersistFn(
    ({ offsetY }: { offsetY: number }) => {
      if (offsetY > 30) {
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    }
  );

  const $scrollStickyTopStyle = useAnimatedStyle(() => ({
    marginTop: scrollStickyTop.value
  }));

  const [firstDataStatus, setFirstDataStatus] = useState(true);

  const fetchSearchFeedMethod = async (
    payload: FetchMethodPayloadType
  ): Promise<ListResponse> => {
    if (payload.scene === RequestScene.INIT) {
      showLoading();
    }

    const isAll = feature + '' === GameType.UNKNOWN + '';
    const reqParams = {
      searchSceneName: RecSceneName.SEARCH,
      features: isAll
        ? undefined
        : {
            [SearchFeatureName.GAME_TYPE + '']: feature + ''
          },
      pagination: payload.pagination,
      keyword: keywords
    };

    try {
      const res = await searchClient.search(reqParams);
      const packRes = {
        cards: res?.result?.map((r: any) => {
          return r.item.value;
        }),
        pagination: res?.pagination
      };

      if (payload.scene === RequestScene.INIT) {
        reportExpo('result', {
          module: 'search',
          status: res?.result?.length > 0 ? '1' : '0',
          type: ECellCardReportType[feature],
          gameType: feature,
          words: keywords
        });

        if (res?.result?.length === 0 && isCurrentTab && roleGuideVisible) {
          reportExpo('guide_create_role', { module: 'search' });
        }

        hideLoading();
        setFirstDataStatus(res?.result?.length > 0);
      }

      return new Promise((resolve, reject) => {
        try {
          resolve(packRes as ListResponse);
        } catch (error) {
          reject(error);
        }
      });
    } catch (error) {
      errorReport('[search error]', ReportError.SEARCH, error);
      hideLoading();
      return new Promise((resolve, reject) => {
        reject(error);
      });
    }
  };

  const {
    sourceData: searchData,
    loading: searchDataLoading,
    error: searchError,
    hasMore: searchHasMore,
    fetchList: fetchSearchList
  } = useRequestFeed({
    defaultFetch: false,
    fetchMethod: fetchSearchFeedMethod,
    onError: scene =>
      scene === RequestScene.REFRESHING
        ? showToast('刷新失败啦，请重试')
        : undefined
  });

  useEffect(() => {
    if (keywords) {
      fetchSearchList(RequestScene.INIT);
    }
  }, [keywords, isCurrentTab]);

  useEffect(() => {
    fetchRecommendList(RequestScene.INIT);
  }, []);

  const fetchRecommendMethod = async (payload: FetchMethodPayloadType) => {
    const res = await feedClient.allCards({
      useHitBack: false,
      recSceneName: RecSceneName.SEARCH_RECOMMEND,
      reserved: searchParams as Record<string, string>,
      pagination: payload.pagination
    });
    return res;
  };

  const {
    sourceData: recommendData,
    loading: recommendDataLoading,
    error: recommendError,
    hasMore: recommendHasMore,
    fetchList: fetchRecommendList
  } = useRequestFeed({
    fetchMethod: fetchRecommendMethod,
    onError: scene =>
      scene === RequestScene.REFRESHING
        ? showToast('刷新失败啦，请重试')
        : undefined
  });

  const onWaterfallScroll = (offset: any) => {};

  return (
    <View style={[$flex]}>
      <WaterFall2
        data={searchData}
        loading={searchDataLoading}
        renderLoading={() => {
          return <View />;
        }}
        onScroll={onWaterfallScroll}
        error={searchError}
        hasMore={searchHasMore}
        onRequest={fetchSearchList}
        footerStyle={{ paddingBottom: $safePaddingBottom }}
        enablePullRefresh={false}
        scrollViewProps={{
          bounces: firstDataStatus,
          style: {
            paddingTop: 8,
            ...(!firstDataStatus && {
              maxHeight: dp2px(225)
            })
          }
        }}
        renderEmpty={() => {
          return (
            <Animated.View style={[{ height: dp2px(225) }, $emptyOpacityStyle]}>
              <EmptyWrapper
                isEmpty={true}
                renderEmptyText={() => {
                  return (
                    <>
                      <Text style={$emptyText}>
                        暂无搜索结果，为您推荐热门内容
                      </Text>
                      <Pressable
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center'
                        }}
                        onPress={() => {
                          loginIntercept(() => {
                            // 数据预加载
                            useBotStore.getState().prefetchMessages();
                            router.navigate({
                              pathname: '/bot',
                              params: {
                                source: BotSource.SEARCH_RESULT
                              }
                            });
                          });
                        }}
                      >
                        <Text style={$emptyText}>也欢迎</Text>
                        <Text style={[$emptyText, botLinkText]}>
                          找小狸聊天
                        </Text>
                        <Text style={$emptyText}>哦!</Text>
                      </Pressable>
                    </>
                  );
                }}
              />
            </Animated.View>
          );
        }}
        renderFooter={() => {
          return (
            <>
              {firstDataStatus && !searchDataLoading ? (
                <View
                  style={[
                    {
                      alignItems: 'center',
                      marginTop: 12,
                      paddingBottom: $safePaddingBottom
                    }
                  ]}
                >
                  <Text style={{ color: StyleSheet.darkTheme.text.tertiary }}>
                    无更多结果啦~
                  </Text>
                </View>
              ) : (
                <></>
              )}
            </>
          );
        }}
        isActive={isCurrentTab}
        extendedState={extendedState}
        reportParams={reportParams}
      />
      {!firstDataStatus && (
        <Animated.View
          key={'SEARCH_RECOMMEND'}
          style={[
            {
              flex: 1
            },
            $scrollStickyTopStyle
          ]}
        >
          <View
            style={{
              paddingHorizontal: 4,
              paddingVertical: 12
            }}
          >
            <Text style={$recommendText}>为您推荐</Text>
          </View>
          <WaterFall2
            onScroll={onSearchScrollWaterfall}
            data={recommendData}
            loading={recommendDataLoading}
            error={recommendError}
            hasMore={recommendHasMore}
            onRequest={fetchRecommendList}
            footerStyle={{ paddingBottom: $safePaddingBottom }}
            enablePullRefresh={false}
            isActive={isCurrentTab}
            extendedState={RECOMMEND_EXTENDEDSTATE}
            reportParams={RECOMMEND_REPORT_PARAMS}
            renderFooter={() => {
              return (
                <>
                  {firstDataStatus && !recommendDataLoading ? (
                    <View
                      style={[
                        {
                          alignItems: 'center',
                          marginTop: 12,
                          paddingBottom: $safePaddingBottom
                        }
                      ]}
                    >
                      <Text
                        style={{ color: StyleSheet.darkTheme.text.tertiary }}
                      >
                        无更多结果啦~
                      </Text>
                    </View>
                  ) : (
                    <></>
                  )}
                </>
              );
            }}
          />
        </Animated.View>
      )}
    </View>
  );
}

const RECOMMEND_EXTENDEDSTATE = {
  reportParams: {
    tab: EWaterFallTabReportType[EWaterFallTabType.SEARCH_RECOMMEND]
  }
};

const RECOMMEND_REPORT_PARAMS = {
  from: 'search_scene',
  recommend: '1'
};

const $recommendText: TextStyle = $USE_FONT(
  StyleSheet.darkTheme.text.primary,
  typography.fonts.pingfangSC.normal,
  15,
  'normal',
  '600',
  21
);

const $emptyText: TextStyle = $USE_FONT(
  StyleSheet.darkTheme.text.tertiary,
  typography.fonts.pingfangSC.normal,
  14,
  'normal',
  19.6
);

const botLinkText: TextStyle = {
  color: 'rgba(255, 106, 59, 0.8)'
};
