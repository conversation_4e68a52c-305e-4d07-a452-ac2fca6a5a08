import { useDebounceFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useMemo, useState } from 'react';
import { InteractionManager, KeyboardAvoidingView, View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';
import SearchBar from '@/src/bizComponents/search/search-bar';
import { showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import TabScene, {
  ETabHeaderArrange,
  IIinitialStateItemType
} from '@/src/components/v2/tabScene';
import { useSearchStore } from '@/src/store/search';
import { darkTheme } from '@/src/theme';
import { $flex } from '@/src/theme/variable';
import { ESearchTabScene } from '@/src/types';
import { StyleSheet, dp2px, isIos } from '@/src/utils';
import { decodeSP, encodeSP } from '@/src/utils/placeholder';
import { reportClick } from '@/src/utils/report';
import SearchUserList from './parts/search-userlist';
import { GameType } from '@/proto-registry/src/web/raccoon/common/types_pb';
import { useRoute } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';
import SearchWaterFall from './search-waterfall';

const UNDER_NODE = require('@Assets/image/search/search-tab-underline.png');

export default function SearchResult() {
  const route = useRoute();

  const [keywords, setKeywords] = useState('');
  const [activeTabIndex, setActiveTabIndex] = useState(0);

  useEffect(() => {
    setKeywords(decodeSP((route.params as any)?.keywords));
  }, [route.params]);

  const { resourseTabs } = useSearchStore(
    useShallow(state => ({
      resourseTabs: state.initTags
    }))
  );

  const tabs = useMemo(
    () =>
      resourseTabs.map(s => {
        return {
          title: s.name,
          key: s.feature,
          tabType: s.tabType
        };
      }),
    [resourseTabs]
  );

  const activeTabKey = useMemo(() => {
    console.log(resourseTabs, 'resourseTabs');
    const reportType =
      resourseTabs[activeTabIndex]?.feature || GameType.UNKNOWN + '';
    reportClick('result_tab', {
      module: 'search',
      type: reportType
    });
    return reportType;
  }, [activeTabIndex]);

  const handleTabChange = useDebounceFn(
    (index: number) => {
      if (activeTabIndex !== index) {
        setActiveTabIndex(index);
      }
    },
    { wait: 50 }
  );

  const renderWaterFall = (feature: GameType) => {
    return (
      <SearchWaterFall
        feature={feature}
        keywords={keywords}
        isCurrentTab={activeTabKey === feature + ''}
      />
    );
  };

  const renderUserList = () => {
    return <SearchUserList keywords={keywords} />;
  };

  const renderTabScene = (item: IIinitialStateItemType) => {
    if (item.tabType === ESearchTabScene.USER) {
      return renderUserList();
    }
    return renderWaterFall(item.key as GameType);
  };

  const clearWord = () => {
    setKeywords('');
    router.navigate({
      pathname: '/search/prefer' as RelativePathString,
      params: {
        keywords: ''
      }
    });
  };

  const focusWord = () => {
    router.navigate({
      pathname: '/search/prefer' as RelativePathString,
      params: {
        result: encodeSP(keywords)
      }
    });
  };

  const tabOpacity = useSharedValue(0);

  const $tabOpacity = useAnimatedStyle(() => ({
    opacity: tabOpacity.value
  }));

  useEffect(() => {
    tabOpacity.value = withTiming(1, {
      duration: 600
    });
    if (resourseTabs.length === 0) {
      showToast('哎呀，小狸走丢了');
    }
  }, [resourseTabs]);

  useEffect(() => {
    const control = InteractionManager.runAfterInteractions(async () => {
      useSearchStore.getState().initSearchTags();
    });

    return () => {
      control.cancel();
    };
  }, []);

  return (
    <PagePerformance pathname="search/result">
      <View
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: darkTheme.background.page
        }}
      >
        <Animated.View
          style={[
            $tabOpacity,
            { flex: 1, backgroundColor: darkTheme.background.page }
          ]}
        >
          <SafeAreaView
            style={[
              $flex,
              { backgroundColor: StyleSheet.darkTheme.background.page }
            ]}
            edges={['top']}
          >
            <KeyboardAvoidingView behavior={isIos ? 'height' : undefined}>
              <SearchBar
                value={keywords}
                hiddenRight
                clearWord={clearWord}
                onParentFocus={focusWord}
                canbeEdit={false}
                autoFocus={false}
                ellipseMode={true}
                ellipseWidth={dp2px(275)}
              />
            </KeyboardAvoidingView>
            <TabScene
              overdrag={false}
              activeIndex={activeTabIndex}
              tabNames={tabs}
              tabHeaderArrange={ETabHeaderArrange.LEFT}
              renderScene={renderTabScene}
              onIndexChange={handleTabChange.run}
              gap={30}
              underNodeWidth={24}
              underNodeHeight={2}
              underNode={UNDER_NODE}
              $tabNormalTextStyle={{ color: StyleSheet.darkTheme.text.solid }}
              $tabActiveTextStyle={{ color: StyleSheet.darkTheme.text.solid }}
              $underNodeStyle={{
                width: 24,
                height: 2,
                bottom: 4,
                left: 0
              }}
            />
            <View
              style={{
                position: 'absolute',
                left: 0,
                top: 0,
                bottom: 0,
                width: 8,
                backgroundColor: 'transparent'
              }}
            />
          </SafeAreaView>
        </Animated.View>
      </View>
    </PagePerformance>
  );
}
