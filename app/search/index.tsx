/**
 * TODO: 优化该搜索页性能
 *  */
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  GestureResponderEvent,
  InteractionManager,
  KeyboardAvoidingView,
  NativeSyntheticEvent,
  ScrollView,
  TextInputFocusEventData,
  TextInputSubmitEditingEventData
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { queryClient } from '@/src/api/query';
import { getOffsiteLuckyBatteryModel } from '@/src/api/reward';
import SearchBar from '@/src/bizComponents/search/search-bar';
import { showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { showPasswordModal } from '@/src/components/popup/activityModal/passwordModal';
import { usePersistFn } from '@/src/hooks';
import { useSearchStore } from '@/src/store/search';
import { darkTheme } from '@/src/theme';
import { $flex } from '@/src/theme/variable';
import { StyleSheet, dp2px, isIos } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { encodeSP } from '@/src/utils/placeholder';
import { reportClick, reportExpo } from '@/src/utils/report';
import News from './parts/news';
import { useIsFocused, useRoute } from '@react-navigation/native';

export default function Search() {
  const [searchText, setSearchText] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [isFirstPress, setIsFirstPress] = useState(true);

  const route = useRoute();
  const isFocus = useIsFocused();

  useEffect(() => {
    const control = InteractionManager.runAfterInteractions(() => {
      setSearchText((route.params as Record<string, string>)?.keywords);
      setSearchValue((route.params as Record<string, string>)?.result);
    });
    return () => {
      control.cancel();
    };
  }, [route.params]);

  useEffect(() => {
    if (isFocus) {
      const control = InteractionManager.runAfterInteractions(() => {
        useSearchStore.getState().initHotRanks();
      });
      return () => {
        control.cancel();
      };
    }
  }, [isFocus]);

  useEffect(() => {
    const control = InteractionManager.runAfterInteractions(async () => {
      useSearchStore.getState().initSearchTags();
    });

    return () => {
      control.cancel();
    };
  }, []);

  const onSearch = usePersistFn(
    async (
      e:
        | GestureResponderEvent
        | NativeSyntheticEvent<TextInputSubmitEditingEventData>,
      keywords: string
    ) => {
      if (!keywords.trim()) {
        showToast('请输入搜索内容~');
        return;
      }
      try {
        setIsFirstPress(true);
        const v = keywords;
        clearWord();

        reportClick('remind_inputconfirm', {
          module: 'search',
          words: v
        });
        try {
          // 判断是否命中关键词, 异常不阻塞搜索
          const qualificationsRes = await queryClient.hitKeywordIntercept({
            keyWord: v
          });
          if (qualificationsRes.hit) {
            const res = await getOffsiteLuckyBatteryModel(v);
            showPasswordModal({
              popup: res,
              shareCode: v,
              onClose: function (): void {},
              onPress: function (): void {}
            });
            return;
          }
        } catch (error) {
          errorReport('onSearchReward', ReportError.ERROR, error);
        }
        router.navigate({
          pathname: '/search/result',
          params: {
            keywords: encodeSP(v)
          }
        });
      } catch (error) {
        errorReport('onSearch', ReportError.ERROR, error);
      }
    }
  );

  const onFocus = usePersistFn(
    (e: NativeSyntheticEvent<TextInputFocusEventData>, keywords: string) => {
      reportClick('begin_input', { module: 'search' });
    }
  );

  const clearWord = usePersistFn(() => {
    setSearchValue('');
  });

  const onChange = usePersistFn((v: string) => {
    setSearchValue(v);
    if (isFirstPress) {
      reportExpo('remind_input', { module: 'search' });
      setIsFirstPress(false);
    }
  });

  return (
    <PagePerformance pathname="search/index">
      <SafeAreaView
        style={[$flex, { backgroundColor: darkTheme.background.page }]}
      >
        <ScrollView keyboardShouldPersistTaps={'handled'}>
          <KeyboardAvoidingView behavior={isIos ? 'height' : undefined}>
            <SearchBar
              backColor={StyleSheet.darkColors.white[1000]}
              value={searchValue}
              onSearch={onSearch}
              onChange={onChange}
              onFocus={onFocus}
              placeHolderContent={
                searchText || useSearchStore.getState().lastPlaceText
              }
              autoFocus={true}
              clearWord={clearWord}
              ellipseWidth={dp2px(210)}
            />
          </KeyboardAvoidingView>
          {!searchValue ? <News hidden={!!searchValue} /> : null}
        </ScrollView>
      </SafeAreaView>
    </PagePerformance>
  );
}
