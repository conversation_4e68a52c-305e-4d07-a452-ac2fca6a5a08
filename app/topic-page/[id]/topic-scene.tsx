import { useMemoizedFn, useRequest } from 'ahooks';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Pressable, View } from 'react-native';
import { feedClient } from '@/src/api';
import { queryClient } from '@/src/api/query';
import { queryTopicCards } from '@/src/api/topic';
import {
  FeedRichCardInfo,
  RecSceneName
} from '@/src/bizComponents/feedScreen/type';
import { CellCardScene } from '@/src/bizComponents/feedcard/types';
import { showToast } from '@/src/components';
import { RequestScene } from '@/src/components/infiniteList/typing';
import {
  WaterFall2,
  genDefaultGetLayoutProvider
} from '@/src/components/waterfall/WaterFall2';
import {
  EWaterFallTabReportType,
  EWaterFallTabType,
  WaterFallCardData
} from '@/src/components/waterfall/type';
import {
  FetchMethodPayloadType,
  useRequestFeed
} from '@/src/components/waterfall/useRequsetFeed';
import { BOTTOM_TAB_HEIGHT, LIST_BOTTOM_SAFE_HEIGHT } from '@/src/constants';
import { useSafeBottomArea } from '@/src/hooks';
import { PostDetailSource } from '../../../src/utils/report';
import { TopicDetail } from '@/proto-registry/src/web/raccoon/common/topic_pb';

export enum TOPIC_FETCH_TYPE {
  // 热度排序
  BY_HEAT = 1,
  // 时间排序
  BY_TIME = 2,
  IP_DISCUSS = 3,
  TOPIC_DISCUSS = 4,
  IP_RECOMMEND = 5
}

const topicTabMap = {
  [TOPIC_FETCH_TYPE.IP_DISCUSS]: EWaterFallTabType.IP_DISCUSS,
  [TOPIC_FETCH_TYPE.TOPIC_DISCUSS]: EWaterFallTabType.TOPIC_DISCUSS
};

export default function TopicScene({
  id,
  pendingId,
  type,
  isActive,
  onScroll
}: {
  id: string;
  pendingId?: string;
  type: TOPIC_FETCH_TYPE;
  isActive: boolean;
  onScroll?: () => void;
}) {
  const $safePaddingBottom = useSafeBottomArea();

  const fetchFeedMethod = useMemoizedFn(
    async (payload: FetchMethodPayloadType) => {
      const res = await (type === TOPIC_FETCH_TYPE.IP_DISCUSS ||
      type === TOPIC_FETCH_TYPE.TOPIC_DISCUSS
        ? queryClient.allBBSPosts({
            pagination: payload.pagination,
            useHitBack: payload.useHitBack,
            recSceneName: 'bbsBrandRecTab',
            ...(type === TOPIC_FETCH_TYPE.IP_DISCUSS
              ? {
                  brands: [Number(id)]
                }
              : {
                  topicIds: [id]
                })
          })
        : type === TOPIC_FETCH_TYPE.IP_RECOMMEND
          ? feedClient.allCards({
              brand: Number(id),
              recSceneName: RecSceneName.IP_LANDING,
              pagination: payload.pagination
            })
          : queryTopicCards({
              pagination: payload.pagination,
              topicId: id,
              order: type
            }));
      return res;
    }
  );

  const {
    sourceData,
    loading,
    error,
    hasMore,
    fetchList: fetchCards,
    unshiftData: unshiftRecommendData
  } = useRequestFeed({
    fetchMethod: fetchFeedMethod,
    tag: 'discuss',
    onError: scene =>
      scene === RequestScene.REFRESHING
        ? showToast('刷新失败啦，请重试')
        : undefined,
    defaultFetch: Boolean(id),
    initFetchDeps: [id]
  });

  useEffect(() => {
    if (pendingId) {
      unshiftRecommendData(pendingId);
    }
  }, [pendingId]);

  const extendedState = useMemo(
    () => ({
      isTopicVisible: false,
      reportParams: {
        tab: EWaterFallTabReportType[topicTabMap[type]],
        from: PostDetailSource.TAG
      },
      scene:
        type === TOPIC_FETCH_TYPE.IP_DISCUSS ||
        type === TOPIC_FETCH_TYPE.TOPIC_DISCUSS
          ? CellCardScene.DISCUSS
          : undefined
    }),
    [type]
  );

  return (
    <View key="1" style={[{ flex: 1, minHeight: 10 }]}>
      <WaterFall2
        data={sourceData}
        loading={loading}
        error={error}
        hasMore={hasMore}
        onRequest={fetchCards}
        footerStyle={{
          paddingBottom:
            $safePaddingBottom + BOTTOM_TAB_HEIGHT + LIST_BOTTOM_SAFE_HEIGHT
        }}
        isActive={isActive}
        extendedState={extendedState}
        onScroll={onScroll}
        // getLayoutProvider={customGetLayoutProvider}
        reportParams={{
          module: 'tag'
        }}
        customListProps={{
          canChangeSize: true
        }}
      />
    </View>
  );
}

function customGetLayoutProvider(listData?: WaterFallCardData[]) {
  const calcContentHeight = (card?: WaterFallCardData) => {
    const height = 38 + ((card?.card?.title.length || 0) > 16 ? 44 : 26);
    return height;
  };

  return genDefaultGetLayoutProvider(calcContentHeight)(listData);
}
