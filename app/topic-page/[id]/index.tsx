import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { router, useFocusEffect } from 'expo-router';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import { Pressable, TextStyle, View, ViewStyle } from 'react-native';
import { ShadowedView, shadowStyle } from 'react-native-fast-shadow';
import PagerView from 'react-native-pager-view';
import { SharedValue, runOnUI, useSharedValue } from 'react-native-reanimated';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import { RecommendSecondaryTab } from '@/src/bizComponents/feedScreen/recommendSecondaryTab';
import { showPublish } from '@/src/bizComponents/globalPublish';
import { GlobalPublishButton } from '@/src/bizComponents/globalPublish/globalPublishButton';
import { NestedScrollView } from '@/src/bizComponents/nestedScrollView';
import { TopicDetailBg } from '@/src/bizComponents/topicDetailScreen/TopicDetailBg';
import { TopicDetailHeader } from '@/src/bizComponents/topicDetailScreen/TopicDetailHeader';
import { TopicInfo as TopicDetailInfo } from '@/src/bizComponents/topicDetailScreen/TopicDetailInfo';
import { TOPIC_FROM_PAGE } from '@/src/bizComponents/topicDetailScreen/constants';
import { Button, Image, Screen, hideLoading } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useAuthState, usePersistFn, useSafeBottomArea } from '@/src/hooks';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { selectState } from '@/src/store/_utils';
import { ConfigType, useConfigStore } from '@/src/store/config';
import {
  SPECIAL_TOPIC,
  checkTopicType,
  useTopicStore
} from '@/src/store/topic';
import { useTopicPageStore } from '@/src/store/topic-page';
import { CommonColor } from '@/src/theme/colors/common';
import { $flex } from '@/src/theme/variable';
import { GameType, TopicInfo } from '@/src/types';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { getRandomValue } from '@/src/utils/opt/getRandomValue';
import {
  Source,
  reportClick,
  reportExpo,
  setIpOrTopicTabForPostReport
} from '@/src/utils/report';
import { BbsPageType } from '@/src/utils/report/type';
import { showToast } from '@Components/toast';
import { useParams } from '../../../src/hooks/useParams';
import { TOPIC_PAGE_APM } from '../_constants';
import { TopicDetail } from '@/proto-registry/src/web/raccoon/common/topic_pb';
import { ConnectError } from '@connectrpc/connect';
import { useShallow } from 'zustand/react/shallow';
import TopicScene, { TOPIC_FETCH_TYPE } from './topic-scene';

const routes = [
  {
    key: '0',
    type: TOPIC_FETCH_TYPE.BY_HEAT,
    title: '最热'
  },
  {
    key: '1',
    type: TOPIC_FETCH_TYPE.BY_TIME,
    title: '最新'
  },
  {
    key: '2',
    type: TOPIC_FETCH_TYPE.TOPIC_DISCUSS,
    title: '讨论区'
  }
];

type TabItemInfo = {
  key: string;
  title: string;
  type: TOPIC_FETCH_TYPE;
};

interface ITopicList {
  topicId: string;
  cardId: string;
}
export default function TopicDetailPage() {
  const { id } = useParams();

  const sheetRef = useRef<NestedScrollView>(null);
  const [pendingId, setPendingId] = useState<string | undefined>('');
  const [showGuide, setShowGuide] = useState(true);

  const hasSlideUp = useSharedValue(false);
  const bottom = useSafeBottomArea();

  const { topicDetail, getTopicDetail } = useTopicPageStore(
    useShallow(state => selectState(state, ['topicDetail', 'getTopicDetail']))
  );

  const { topicType } = useTopicType({ topicId: id as string });

  const validTabs = useMemo(() => {
    if (topicType === SPECIAL_TOPIC.PK) {
      return routes.filter(r => r.type !== TOPIC_FETCH_TYPE.TOPIC_DISCUSS);
    } else {
      return routes;
    }
  }, [topicType]);

  const { run: debounceShowGuide } = useDebounceFn(
    () => {
      setShowGuide(true);
    },
    {
      wait: 1000
    }
  );

  const onScroll = useMemoizedFn(() => {
    setShowGuide(false);
    debounceShowGuide();
  });

  const { loginIntercept } = useAuthState();
  const { go2Create } = useChangeRoute();

  const [curTabIdx, setCurTabIdx] = useState<string>('0');

  const pagerViewRef = useRef<PagerView>(null);

  const onClickPublish = useMemoizedFn(() => {
    reportClick('tag_page_button', {
      module: 'tag',
      tag_page_button: TOPIC_PAGE_APM.TAG_PAGE_CLICK.PUBLISH
    });

    loginIntercept(() => {
      takeSamePhoto();
    });
  });

  const { run: debounceClickPublish } = useDebounceFn(onClickPublish, {
    wait: 500
  });

  useDefaultScroll({
    handleScroll: () => {
      setCurTabIdx(routes[2].key);
      pagerViewRef.current?.setPage(Number(routes[2].key));
    }
  });

  useEffect(() => {
    setTimeout(() => {
      reportExpo('tag_page', { tag_name: topicDetail?.title, module: 'tag' });
    }, 100);
  }, []);

  useFocusEffect(
    useCallback(() => {
      if (id) {
        getTopicDetail({ topicId: id as string }).catch((error?: ErrorRes) => {
          if (
            error instanceof ConnectError &&
            error.rawMessage === '需要升级'
          ) {
            router.replace('/fallback');
          }
        });
      }
    }, [id])
  );

  return (
    <PagePerformance pathname="topic-page/[id]/index">
      <Screen
        theme="dark"
        backgroundView={
          <TopicDetailBg
            themeColor={topicDetail?.backgroundColor || 'rgb(232, 91, 99)'}
          />
        }
        safeAreaEdges={['top']}
        headerShown={false}
      >
        <TopicDetailHeader
          topicInfo={topicDetail || undefined}
          hasSlideUp={hasSlideUp}
        />
        <View style={$flex}>
          <NestedScrollView
            ref={sheetRef}
            hasSlideUp={hasSlideUp}
            topPreserveInset={46}
            headerComponent={
              <View>
                <TopicDetailInfo topicInfo={topicDetail || undefined} />
                <RecommendSecondaryTab<TabItemInfo>
                  enableExpand={false}
                  activeIndex={curTabIdx}
                  list={validTabs}
                  onPress={(key, item) => {
                    pagerViewRef.current?.setPage(Number(key));
                    if (item.type === TOPIC_FETCH_TYPE.TOPIC_DISCUSS) {
                      setIpOrTopicTabForPostReport('topic_discuss');
                    }
                    reportClick('tag_page_button', {
                      module: 'tag',
                      tag_page_button:
                        item.type === TOPIC_FETCH_TYPE.BY_HEAT
                          ? TOPIC_PAGE_APM.TAG_PAGE_CLICK.HEAT
                          : TOPIC_PAGE_APM.TAG_PAGE_CLICK.TIME
                    });
                  }}
                />
              </View>
            }
          >
            <PagerView
              ref={pagerViewRef}
              key={id as string}
              style={{ flex: 1 }}
              onPageSelected={e => {
                setCurTabIdx(e.nativeEvent.position.toString());
              }}
              overdrag
            >
              <TopicScene
                id={id as string}
                onScroll={onScroll}
                type={TOPIC_FETCH_TYPE.BY_HEAT}
                isActive={routes[curTabIdx]?.type === TOPIC_FETCH_TYPE.BY_HEAT}
              />
              <TopicScene
                id={id as string}
                onScroll={onScroll}
                type={TOPIC_FETCH_TYPE.BY_TIME}
                isActive={routes[curTabIdx]?.type === TOPIC_FETCH_TYPE.BY_TIME}
              />
              <TopicScene
                id={id as string}
                onScroll={onScroll}
                pendingId={pendingId}
                type={TOPIC_FETCH_TYPE.TOPIC_DISCUSS}
                isActive={
                  routes[curTabIdx]?.type === TOPIC_FETCH_TYPE.TOPIC_DISCUSS
                }
              />
            </PagerView>
          </NestedScrollView>
        </View>

        {curTabIdx === '2' || topicType === SPECIAL_TOPIC.PK ? (
          <GlobalPublishButton
            showGuide={showGuide}
            pageType={BbsPageType.topic}
            publishProps={{
              mode: topicType === SPECIAL_TOPIC.PK ? 'newPk' : 'newPost',
              showTab: curTabIdx === '2',
              asyncTopics: topicDetail ? [topicDetail] : undefined,
              onPublishCallback: ({ cardId }) => {
                setPendingId(cardId);
              }
            }}
          />
        ) : (
          <View
            style={{
              position: 'absolute',
              width: '100%',
              alignItems: 'center',
              bottom: 10 + bottom
            }}
          >
            <Button
              style={$publishButton}
              textStyle={$publishButtonText}
              onPress={debounceClickPublish}
              iconSize={12}
            >
              {'去发布'}
            </Button>
          </View>
        )}
      </Screen>
    </PagePerformance>
  );

  async function getCardId() {
    const topicList = (await useConfigStore
      .getState()
      .getConfig(ConfigType.topic)) as ITopicList[];
    const cardList = topicList.find(item => item.topicId === id);
    if (cardList && cardList.cardId) {
      return getRandomValue(cardList.cardId.split(','));
    }
  }

  async function takeSamePhoto() {
    const gameType = topicDetail?.gameType || GameType.DRAWING;

    const cardId = await getCardId();

    if (!cardId) {
      errorReport('takeSamePhoto cardid empty', ReportError.TOPIC, undefined, {
        id
      });
    }

    try {
      // @ts-ignore
      await go2Create({
        gameType: gameType,
        gameParams: {
          cardId,
          topics: topicDetail ? [topicDetail] : []
        },
        params: {
          source: Source.TOPIC
        }
      });

      hideLoading();
    } catch (e) {
      showToast('操作失败，请重试');
      hideLoading();
      errorReport('takePhotoFromCardId', ReportError.TOPIC, e);
    }
  }
}

const useTopicType = ({ topicId }: { topicId: string }) => {
  const { specialTopicMap } = useTopicStore(
    useShallow(state => selectState(state, ['specialTopicMap']))
  );

  const topicType = checkTopicType(topicId, specialTopicMap);
  return { topicType };
};

const useDefaultScroll = ({ handleScroll }: { handleScroll: () => void }) => {
  const { from } = useParams();

  useEffect(() => {
    if (from === TOPIC_FROM_PAGE.BBS) {
      setTimeout(() => {
        handleScroll();
      }, 100);
    }
  }, [from]);
};

const $publishButton: ViewStyle = {
  width: 144,
  height: 44,
  backgroundColor: CommonColor.brand1,
  borderRadius: 24,
  paddingVertical: 0
};

const $publishButtonText: TextStyle = {
  color: CommonColor.white,
  fontSize: 14,
  lineHeight: 20,
  fontWeight: '600'
};
