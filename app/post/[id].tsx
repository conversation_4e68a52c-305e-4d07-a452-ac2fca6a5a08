import { useMemoizedFn } from 'ahooks';
import { router, useNavigation } from 'expo-router';
import { pickBy } from 'lodash';
import React, { useCallback, useEffect, useMemo } from 'react';
import {
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  TouchableOpacity,
  View
} from 'react-native';
import { useSharedValue } from 'react-native-reanimated';
import { Easing, withTiming } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BBSScreen } from '@/src/bizComponents/bbsScreen';
import { BBSBottomBar } from '@/src/bizComponents/bbsScreen/BBSBottomBar';
import { BBSHeader } from '@/src/bizComponents/bbsScreen/BBSHeader';
import {
  CommentEvent,
  CommentEventBus
} from '@/src/components/comment/eventbus';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useSetEmojiCreatorInfo } from '@/src/hooks/useSetDetailEmojiCreatorInfo';
import { useAuthStore } from '@/src/store/authInfo';
import { useBBSStore } from '@/src/store/bbs';
import { GameType } from '@/src/types';
import { StyleSheet } from '@/src/utils/StyleSheet';
import { getStrDisplayLines } from '@/src/utils/char';
import { dp2px } from '@/src/utils/dp2px';
import {
  addCommonReportParams,
  addPageExpoParams,
  reportExpo
} from '@/src/utils/report';
import { PostDetailSource } from '@/src/utils/report/type';
import { Screen } from '@Components/screen';
import { Text } from '@Components/text';
import { IconDoubleRight } from '../../assets/image/svg';
import { BbsTabKey } from '../../src/bizComponents/feedScreen/type';
import { Image } from '../../src/components';
import { useAndroidBackHandler, useExitReporting } from '../../src/hooks';
import { useParams } from '../../src/hooks/useParams';
import { useFocusEffect } from '@react-navigation/native';
import { BBSPostType } from '@step.ai/proto-gen/raccoon/common/bbs_pb';
import { ScrollEvent } from 'recyclerlistview/dist/reactnative/core/scrollcomponent/BaseScrollView';
import { useShallow } from 'zustand/react/shallow';

const TAB_DISCUSS_ACTIVE = require('@Assets/image/feed/tab-discuss-active.png');

type PostPageParams = {
  id?: string;
  source?: string;
  discuss_tab?: string;
  immersive?: string;
  from_role_interaction?: string;
  from?: PostDetailSource;
  fromGameType?: string;
};

const SAFE_AREA = dp2px(22);
const ANIMATION_DISTANCE = dp2px(34);

export default function PostPage() {
  const headerDisplayPercent = useSharedValue(0);
  const params = useParams<PostPageParams>();
  const isFromRoleInteraction = params.from_role_interaction === 'true';
  const navigation = useNavigation();

  useFocusEffect(
    useCallback(() => {
      navigation.setOptions({ gestureEnabled: !isFromRoleInteraction });
    }, [navigation, isFromRoleInteraction])
  );

  const handleRoleInteractionBack = useMemoizedFn(() => {
    if (isFromRoleInteraction) {
      router.replace({
        pathname: '/bbs-feed',
        params: {
          queryTab: BbsTabKey.oc
        }
      });
    } else {
      router.back();
    }
  });

  useAndroidBackHandler(handleRoleInteractionBack, isFromRoleInteraction);

  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<{
    code: number;
    message?: string;
  }>();
  const [scrollY, setScrollY] = React.useState(0);
  const [isPostLoaded, setIsPostLoaded] = React.useState(false);
  const windowHeight = Dimensions.get('window').height;
  const hasReported = React.useRef(false);

  const { post, socialInfo, ctrlInfo } = useBBSStore(
    useShallow(state => {
      const cache = state.posts.get(params.id || '');
      return {
        post: cache?.post,
        socialInfo: cache?.socialInfo,
        ctrlInfo: cache?.ctrlInfo
      };
    })
  );

  const { getPost, getSocialInfo, getCtrlInfo } = useBBSStore(state => ({
    getPost: state.getPostWithCache,
    getSocialInfo: state.getSocialInfoWithCache,
    getCtrlInfo: state.getCtrlInfoWithCache
  }));

  // 使用 useCallback 缓存函数
  const fetchPost = useCallback(async () => {
    const id = params.id;
    if (!id) return;

    // 如果没有缓存的帖子信息，显示 loading
    if (!post) {
      setLoading(true);
    }

    try {
      // 获取最新数据
      const post = await getPost(id);
      if (post) {
        // 在后台加载 socialInfo 和 ctrlInfo, 如果是转发贴，同时加载转发贴的ctrlInfo
        await Promise.all([
          getSocialInfo(id).catch(console.error),
          getCtrlInfo(id).catch(console.error),
          ...(post.originPost?.cardId
            ? [getCtrlInfo(post.originPost.cardId).catch(console.error)]
            : [])
        ]);
      }
      setError(undefined);
      setIsPostLoaded(true);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error('Failed to fetch post:', error);
      setError({
        code: error.code || 500,
        message: error.message
      });
    } finally {
      setLoading(false);
    }
    // post 更新不触发
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.id, getPost, getSocialInfo, getCtrlInfo]);

  React.useEffect(() => {
    fetchPost();
  }, [fetchPost]);

  // 添加 post_detail 部分上报私参
  const addPostDetailParams = useCallback(() => {
    // post_detail 公共参数
    const commonReportParams = pickBy(
      {
        contentid: post?.cardId,
        userid: useAuthStore.getState().uid,
        authorid: post?.authorUinfo?.uid,
        post_type: String(post?.postType || 0),
        gameType: GameType.BBS,
        from_discuss_tab: params?.discuss_tab,
        from: params?.from,
        fromGameType: params?.fromGameType
      },
      value => value !== undefined && value !== null && value !== ''
    ) as Record<string, string | number>;

    // expo 专用参数
    const expoParams = pickBy(
      {
        tag_name: [
          ...(post?.topics?.map(i => i.title) || []),
          ...(post?.brands?.map(i => i.displayName) || [])
        ]
          .filter(Boolean)
          .join(','),
        is_tag:
          (typeof post?.topics?.length === 'number' &&
            post?.topics?.length > 0) ||
          (typeof post?.brands?.length === 'number' && post?.brands?.length > 0)
            ? 1
            : 0,
        is_poll: post?.postType === BBSPostType.BBS_POST_TYPE_VOTE ? 1 : 0,
        poll_id: post?.vote?.voteId,
        poll_tittle: post?.vote?.title,
        post_origina: post?.originPost?.cardId,
        from_discuss_tab: params?.discuss_tab,
        from: params?.from,
        fromGameType: params?.fromGameType
      },
      value => value !== undefined && value !== null && value !== ''
    ) as Record<string, string | number>;

    addCommonReportParams('post_detail', commonReportParams);
    addPageExpoParams('post_detail', expoParams);
  }, [
    post?.cardId,
    post?.authorUinfo?.uid,
    post?.postType,
    post?.topics,
    post?.brands,
    post?.vote?.voteId,
    post?.vote?.title,
    post?.originPost?.cardId,
    params?.discuss_tab,
    params?.from,
    params?.fromGameType
  ]);

  useSetEmojiCreatorInfo(post?.cardId as string);

  // 监听 uid 变化时添加上报参数
  React.useLayoutEffect(() => {
    addPostDetailParams();
    const unsubscribe = useAuthStore.subscribe(() => {
      if (post?.topics) {
        addPostDetailParams();
      }
    });
    return () => {
      CommentEventBus.emit(CommentEvent.POST_SOURCE_CHANGED, undefined);
      unsubscribe();
    };
  }, [addPostDetailParams, post?.topics]);

  // 使用 useCallback 缓存滚动处理函数
  const onScroll = useCallback(
    (event: ScrollEvent, offsetX: number, offsetY: number) => {
      setScrollY(offsetY);

      if (offsetY < SAFE_AREA) {
        headerDisplayPercent.value = withTiming(0, {
          duration: 300,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1)
        });
        return;
      }

      const animationProgress = (offsetY - SAFE_AREA) / ANIMATION_DISTANCE;
      const targetOpacity = Math.min(Math.max(animationProgress, 0), 1);

      headerDisplayPercent.value = withTiming(targetOpacity, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      });
    },
    [headerDisplayPercent]
  );

  // 计算投票组件到顶部的预估距离
  const estimatedVoteDistance = useMemo(() => {
    if (!post || post.postType !== BBSPostType.BBS_POST_TYPE_VOTE) return 0;

    const hasMediaItems = post.mediaItems && post.mediaItems.length > 0;
    const hasTopicsOrBrands =
      (post.topics && post.topics.length > 0) ||
      (post.brands && post.brands.length > 0);

    let distance = dp2px(54) + dp2px(26);

    if (post.content) {
      const contentWidth = Dimensions.get('window').width - dp2px(24);
      const lines = getStrDisplayLines(post.content, contentWidth, 16, 999);
      distance += lines * 26;
    }

    distance += dp2px(10);

    if (hasMediaItems) {
      const imageRows = Math.ceil((post.mediaItems?.length || 0) / 3);
      distance += imageRows * dp2px(120);
    }

    if (hasTopicsOrBrands) {
      distance += dp2px(32);
    }

    return distance;
  }, [post]);

  // 检查投票组件是否在视口内并上报
  useEffect(() => {
    if (
      !post ||
      post.postType !== BBSPostType.BBS_POST_TYPE_VOTE ||
      !post.vote ||
      hasReported.current
    )
      return;

    const componentTop = estimatedVoteDistance;
    const componentHeight = dp2px(120);
    const componentHalfHeight = componentHeight / 2;
    const componentCenter = componentTop + componentHalfHeight;

    const isHalfVisible =
      (scrollY > 0 && componentCenter <= scrollY + windowHeight - dp2px(95)) ||
      (scrollY === 0 && componentCenter <= windowHeight - dp2px(95));

    if (isHalfVisible) {
      hasReported.current = true;
      reportExpo(
        'poll',
        {
          module: 'post_detail',
          poll_id: post.vote.voteId || '',
          poll_tittle: post.vote.title || ''
        },
        true
      );
    }
  }, [post, estimatedVoteDistance, scrollY, windowHeight]);

  // 检查是否需要自动跳转到评论区
  useEffect(() => {
    if (
      params.source === 'post-update-message' &&
      isPostLoaded &&
      !loading &&
      !error
    ) {
      console.log('[PostPage] 触发滚动到首条评论事件');
      CommentEventBus.emit(CommentEvent.SCROLL_TO_COMMENT);
    }
  }, [params.source, isPostLoaded, loading, error]);

  // 使用 useExitReporting 钩子上报页面退出事件和停留时间
  useExitReporting('post_detail');

  // 使用 useMemo 缓存渲染内容
  const mainContent = useMemo(() => {
    return (
      <View style={styles.$container}>
        <BBSHeader
          post={post}
          socialInfo={socialInfo}
          headerDisplayPercent={headerDisplayPercent}
          onBack={handleRoleInteractionBack}
        />
        <View style={styles.$content}>
          <BBSScreen
            postId={params.id}
            onScroll={onScroll}
            loading={loading}
            error={error}
          />
        </View>
      </View>
    );
  }, [
    post,
    socialInfo,
    headerDisplayPercent,
    params.id,
    onScroll,
    loading,
    error,
    handleRoleInteractionBack
  ]);

  // 用户资料页面内容
  // const userProfileContent = post?.authorUinfo?.uid ? (
  //   <UserScreen
  //     pageTab="works"
  //     key={`user-${post.authorUinfo.uid}`}
  //     isRootPage={true}
  //     id={post.authorUinfo.uid}
  //   />
  // ) : null;

  return (
    <PagePerformance pathname="post/[id]">
      {/* <SwipeScreen
        theme="dark"
        previewContent={userProfileContent}
        // BBS 预览功能暂时关闭
        enabled={false}
        allowAutoPreload={true}
        shouldAutoPreload={isPostLoaded && !loading && !error}
      > */}
      <Screen
        screenStyle={{
          backgroundColor: StyleSheet.darkTheme.background.page
        }}
        theme="dark"
        headerShown={false}
      >
        {mainContent}
      </Screen>
      {post && post.cardId && (
        <BBSBottomBar
          authorUid={post.authorUinfo?.uid || ''}
          postId={post.cardId}
          socialInfo={socialInfo}
          ctrlInfo={ctrlInfo}
          gameType={GameType.BBS}
        />
      )}
      {/* </SwipeScreen> */}
      {params.immersive === 'true' && (
        <TouchableOpacity
          style={styles.$bbsPillButton}
          onPress={() => {
            router.navigate({
              pathname: '/bbs-feed',
              params: {
                from: 'immersive_post_detail'
              }
            });
          }}
          activeOpacity={0.8}
        >
          <Image
            source={TAB_DISCUSS_ACTIVE}
            style={styles.$discussIcon}
            tintColor={StyleSheet.darkTheme.text.primary}
            native
            contentFit="contain"
          />
          <Text style={styles.$pillText}>前往讨论区</Text>
          <IconDoubleRight
            width={dp2px(12)}
            height={dp2px(12)}
            style={{
              marginLeft: 'auto'
            }}
            reverseOpacity
            color={StyleSheet.darkTheme.text.secondary}
          />
        </TouchableOpacity>
      )}
    </PagePerformance>
  );
}

const styles = StyleSheet.create({
  $container: {
    flex: 1,
    position: 'relative'
  },
  $content: {
    flex: 1,
    marginTop: 54
  },
  $bbsPillButton: {
    position: 'absolute',
    bottom: 106,
    left: '50%',
    marginLeft: -70,
    height: 36,
    backgroundColor: StyleSheet.darkTheme.background.toast,
    borderRadius: 60,
    borderWidth: 0.5,
    borderColor: StyleSheet.darkTheme.border.button,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingLeft: 14,
    paddingRight: 14
  },
  $discussIcon: {
    height: 20,
    width: 29,
    marginRight: 6
  },
  $pillText: {
    color: StyleSheet.darkTheme.text.secondary,
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4
  }
});
