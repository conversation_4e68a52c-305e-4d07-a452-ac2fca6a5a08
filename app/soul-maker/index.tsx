import { router } from 'expo-router';
import { useEffect } from 'react';
import { useParams } from '@/src/hooks/useParams';
import { GameType } from '@/src/types';
import { getLastPage, reportExpo } from '@/src/utils/report';

export default function SoulMaker() {
  const { source } = useParams();
  useEffect(() => {
    setTimeout(() => {
      reportExpo('expo', {
        source: source || '0'
      });
    }, 100);
  }, []);

  useEffect(() => {
    console.log('### cangoback: ', router.canGoBack());
    if (!router.canGoBack()) {
      setTimeout(() => {
        router.replace({
          pathname: '/playground',
          params: {
            gameType: GameType.REVIVE
          }
        });
      }, 0);
    } else {
      router.replace({
        pathname: '/playground',
        params: {
          gameType: GameType.REVIVE
        }
      });
    }
  }, []);

  return null;
}
