import { router } from 'expo-router';
import React from 'react';
import { RankListScreen } from '@/src/bizComponents/rankListScreen';
import { PagePerformance } from '@/src/components/common/pagePerformance';

export default function RankListRoute() {
  return (
    <PagePerformance pathname="rank-list/index">
      <RankListScreen
        onPressBack={() => router.back()}
        // 自定义指示器图片，传：
        // rankListTabBg={require('@/assets/xxx.png')}
      />
    </PagePerformance>
  );
}
