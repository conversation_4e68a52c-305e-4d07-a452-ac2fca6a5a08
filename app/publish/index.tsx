/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import React from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  Pressable,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import DraggableFlatList, {
  ScaleDecorator
} from 'react-native-draggable-flatlist';
import { ScrollView } from 'react-native-gesture-handler';
import Animated, { useSharedValue } from 'react-native-reanimated';
import { SharedElement } from 'react-navigation-shared-element';
import { GenPhotoStory } from '@/src/api/makephotov2';
import { PersistPhotos } from '@/src/api/makephotov2';
import { checkSecurity } from '@/src/api/utils';
import { Socket } from '@/src/api/websocket';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import { TakePhotoButton } from '@/src/bizComponents/detailScreen/takePhotoButton';
import ImageSharePanel from '@/src/bizComponents/share/imageSharePanel';
import { TopicSelector } from '@/src/bizComponents/topicSelector';
import { useTopicSelect } from '@/src/bizComponents/topicSelector/topicSelect.hook';
import { hideLoading, showLoading, showToast } from '@/src/components';
import { SaveButtonPure } from '@/src/components/album/SaveButton';
import { AlbumFromType } from '@/src/components/album/const';
import { BounceView } from '@/src/components/animation';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { GuideWrapper } from '@/src/components/guide';
import { GUIDE_TYPE_ENUM } from '@/src/components/guide/_constants';
import { AvatarPendantGuide } from '@/src/components/guide/guide-content/AvatarPendantGuide';
import { MakePhotoEvents } from '@/src/components/makePhoto/constant';
import { formatIds } from '@/src/components/makePhoto/utils';
import {
  hidePreviewImages,
  showPreviewImages
} from '@/src/components/previewImageModal';
import { showActivitySheet } from '@/src/components/shareActivity/ActivitySheet';
import Toggle from '@/src/components/toggle';
import { PREVIEW_TAKE_PHOTO_BUTTON_WIDTH } from '@/src/constants';
import { useKeyboard, usePersistFn } from '@/src/hooks';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { usePublish } from '@/src/hooks/usePublish';
import { useSafeAreaInsetsStyle } from '@/src/hooks/useSafeAreaInsetsStyle';
import { selectState } from '@/src/store/_utils';
import { useBehaviorStore } from '@/src/store/behavior';
import { useGuideStore } from '@/src/store/guide';
import { useMakePhotoEdit } from '@/src/store/makePhotoEdit';
import {
  aspectRatioSizeMap,
  useMakePhotoStoreV2
} from '@/src/store/makePhotoV2';
import { usePerformanceStore } from '@/src/store/performance';
import {
  AlbumType,
  PhotosItem,
  SelectedItem,
  usePublishStore
} from '@/src/store/publish';
import { rowStyle } from '@/src/theme';
import { $flexCenter } from '@/src/theme/variable';
import { GameType } from '@/src/types';
import { dp2px, isIos } from '@/src/utils';
import { clickEffect } from '@/src/utils/clickeffect';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { formatTosUrl } from '@/src/utils/getTosUrl';
import {
  getPageID,
  reportClick,
  reportExpo,
  reportPage
} from '@/src/utils/report';
import { Source } from '@/src/utils/report';
import { AlbumSheet } from '@Components/album';
import { Button } from '@Components/button';
import { Icon } from '@Components/icons';
import { Image } from '@Components/image';
import { showConfirm } from '@Components/popup/confirmModalGlobal/Confirm';
import { Loading } from '@Components/promptLoading';
import { AdviceInput } from '@Components/publish/adviceList';
import { RollButton } from '@Components/publish/rollButton';
import { Screen } from '@Components/screen';
import { Text } from '@Components/text';
import { StyleSheet } from '@Utils/StyleSheet';
import { savePicture } from '@Utils/savePicture';
import { uuid } from '@Utils/uuid';
import { useParams } from '../../src/hooks/useParams';
import {
  Photo,
  PublishPhotoRequest
} from '@/proto-registry/src/web/raccoon/makephoto/makephoto_pb';
import { PartialMessage } from '@bufbuild/protobuf';
import { ImageAspectRatio } from '@step.ai/proto-gen/raccoon/common/imagen_pb';
import { useShallow } from 'zustand/react/shallow';

const STORY_LIMIT = 2000;
const ADD_KEY = 9999;
const $line: ViewStyle = {
  borderTopWidth: 0.5,
  borderColor: StyleSheet.hex(StyleSheet.currentColors.white, 0.08)
};

const $topButton: ViewStyle = {
  position: 'absolute',
  top: -12,
  right: 0
};

const $rollWrap: ViewStyle = {
  ...rowStyle,
  justifyContent: 'space-between',
  paddingHorizontal: 20,
  marginBottom: 10
};

const $iconContainerStyle: ViewStyle = {
  backgroundColor: 'rgba(255, 255, 255, 0.12)',
  height: 30,
  width: 30
};

let PIC_WRITE_FLAG = 0;
function Publish() {
  const [isSave, setSaveState] = useState(false);
  const [showAlbum, setShowAlbum] = useState(false);
  const { photos } = usePublishStore(
    useShallow(state => ({ photos: state.photos }))
  );
  const {
    lishi,
    fromType,
    story: paramsStory,
    title: paramsTitle,
    disableAIStory: paramsDisableAIStory,
    maxImageLimit: paramsMaxImageLimit,
    scene = '',
    extraId,
    log_params,
    exception
  } = useParams<Record<string, string>>();
  const logParams = useMemo(() => {
    if (log_params) {
      return JSON.parse(log_params);
    }
    return undefined;
  }, [log_params]);
  const showKeyboard = useKeyboard();
  const { paddingBottom } = useSafeAreaInsetsStyle(['bottom'], 'padding');
  const titleRef = useRef<TextInput>(null);
  const storyRef = useRef<TextInput>(null);
  const pageScrollRef = useRef<any>(null);

  const titleTextRef = useRef('');
  const storyTextRef = useRef('');
  const submitDisRef = useRef(false);
  const currentStoryIdRef = useRef('');
  const [title, setTitle] = useState('');
  const [story, setStory] = useState('');
  const [showAdviceInput, setShowAdviceInput] = useState(false);
  const [showStoryAdviceInput, setShowStoryAdviceInput] = useState(false);
  const [advicing, setAdvicing] = useState(false);
  const [adviceLoading, setAdviceLoading] = useState(false);
  const [notSafeImages, setNotSafeImages] = useState<string[]>([]);
  const noAutoWrite = useMemo(
    () =>
      Boolean(paramsDisableAIStory) || ['dreamcore', 'goods'].includes(scene),
    [scene, paramsDisableAIStory]
  );

  const maxImageLimitNum = paramsMaxImageLimit
    ? Number(paramsMaxImageLimit)
    : undefined;

  const showRecommend = useSharedValue(false);
  const showStoryTag = useSharedValue(false);
  const { publishPhotos: publishPhotosFn } = usePublish();
  const { go2Create } = useChangeRoute();

  // const $dragListStyle = useAnimatedStyle(() => {
  //   return {
  //     transform: [{
  //       scale: dragListScale.value
  //     }]
  //   };
  // });

  // 数据上报相关
  const startAdviceTimeRef = useRef(0);
  const firstWordTimeRef = useRef(0);
  const picwriteIdRef = useRef('');

  // 新手引导
  const [isElMounted, setIsElMounted] = useState(false);

  // 预览图片
  const [previewImageModalVisible, setPreviewImageModalVisible] =
    useState(false);

  const { publishPhotoGuideTitle, changePublishPhotoGuideTitle } =
    useGuideStore(
      useShallow(state =>
        selectState(state, [
          'publishPhotoGuideTitle',
          'changePublishPhotoGuideTitle'
        ])
      )
    );
  const { newUserPendantGuide } = useBehaviorStore(
    useShallow(state => ({
      newUserPendantGuide: state.newUserPendantGuide
    }))
  );

  useEffect(() => {
    useBehaviorStore.getState().refreshNewUserPendantGuide();
  }, []);

  useEffect(() => {
    if (publishPhotoGuideTitle && isElMounted) {
      setTitle(publishPhotoGuideTitle);
    }
  }, [publishPhotoGuideTitle, isElMounted]);

  useEffect(() => {
    if (paramsStory) {
      setTimeout(() => {
        setStory(paramsStory as string);
      });
    }
  }, [paramsStory]);

  useEffect(() => {
    if (paramsTitle) {
      setTimeout(() => {
        setTitle(paramsTitle as string);
      });
    }
  }, [paramsTitle]);

  useEffect(() => {
    // 延迟一会儿报
    setTimeout(() => {
      reportPage('page_view', {
        type: fromType === 'emoji' ? '2' : '1'
      });
    }, 100);
    setStory('');
    if (lishi) {
      onInvokeAddPhoto();
    }

    return () => {
      changePublishPhotoGuideTitle('');
    };
  }, []);

  useEffect(() => {
    titleTextRef.current = title;
  }, [title]);

  useEffect(() => {
    storyTextRef.current = story;
  }, [story]);

  const mainPhoto = useMemo(() => {
    return photos && photos[0];
  }, [photos]);

  const validTitle = useMemo(() => {
    if (!title.length) return false;
    if (title.length > 30) return false;
    return true;
  }, [title]);

  const validStory = useMemo(() => {
    // if (!story.length) return false;
    if (story.length > STORY_LIMIT) return false;
    return true;
  }, [story]);

  const blurAll = () => {
    storyRef.current?.blur();
    titleRef.current?.blur();
  };

  const submitDis = useMemo(() => {
    return !validTitle || !validStory || !photos;
  }, [validTitle, validStory, photos]);

  const onSubmit = usePersistFn(
    (
      isHideActivity?: boolean,
      extraArgs: PartialMessage<PublishPhotoRequest> = {}
    ) => {
      if (submitDisRef.current) return;
      const publishPhotos = usePublishStore.getState().photos;
      const newPhotoIds = usePublishStore.getState().photos.map(i => {
        const { photoId, imageId, subPhotoIds } = i || {};
        if (photoId) {
          return { photoId };
        } else {
          return {
            composeEmojiImageId: imageId,
            subPhotoIds: subPhotoIds?.map(subPhotoId =>
              !subPhotoId ? '' : subPhotoId
            )
          };
        }
      });
      const cover = usePublishStore.getState().photos[0]?.url;
      const story = storyTextRef.current || ' ';
      const title = titleTextRef.current || story.slice(0, 30);
      if (!newPhotoIds.length) {
        showToast('请选择图片');
        return;
      }
      submitDisRef.current = true;
      showLoading();
      stopAdvice();

      if (PIC_WRITE_FLAG) {
        reportExpo(
          'picwrite_title_edit',
          {
            module: 'publish',
            title_edit_id: picwriteIdRef.current,
            title
          },
          'end'
        );
        PIC_WRITE_FLAG = 0;
      }
      reportClick('submit', {
        module: 'publish',
        story,
        title,
        photo_ids: JSON.stringify(newPhotoIds),
        photo_count: newPhotoIds.length,
        ...logParams
      });
      const reportParams = {
        story,
        title,
        photo_ids: JSON.stringify(newPhotoIds),
        photo_count: newPhotoIds.length,
        sticker_id: formatIds(publishPhotos, 'stickerId'),
        template_id: formatIds(publishPhotos, 'templateId'),
        text_num: publishPhotos.reduce((prev, cur) => {
          const curNum = cur?.textNum || 0;
          return prev + curNum;
        }, 0)
      };
      return publishPhotosFn(
        {
          photoIds: newPhotoIds as any[],
          story,
          title,
          topics: selectedTopics,
          pt: {
            page: getPageID()
          },
          ...extraArgs
        },
        cover ?? '',
        publishPhotos as unknown as Photo[],
        isSave
      )
        .then(res => {
          if (!res.ok && res.blockInfo) {
            if (res.blockInfo?.photos4blocked.length > 0) {
              const blockImages = res?.blockInfo?.photos4blocked;
              setNotSafeImages(
                blockImages.map(bi => bi?.photoId?.photoId || '')
              );
            }
            reportClick('submit', {
              module: 'publish',
              card_id: res?.cardId || '',
              type: 0,
              ...reportParams,
              ...logParams
            });
            return;
          }
          reportExpo('publish_success', {
            module: 'publish',
            sourceid: res.cardId,
            photo_count: newPhotoIds.length,
            game_type: [...new Set(photos?.map(i => i.gameType))].join(','),
            ...logParams
          });
          reportClick('submit', {
            module: 'publish',
            card_id: res?.cardId || '',
            type: 1,
            ...reportParams,
            ...logParams
          });
          useMakePhotoStoreV2.getState().reset();
          usePublishStore.getState().reset();
          useMakePhotoEdit.getState().reset();
          if (res.cardId) {
            setTimeout(() => {
              if (!isHideActivity) {
                showActivitySheet();
              }
            }, 200);
          }
        })
        .catch((e: ErrorRes) => {
          reportClick('submit', {
            module: 'publish',
            card_id: '',
            type: 0,
            ...reportParams
          });
        })
        .finally(() => {
          submitDisRef.current = false;
          hideLoading();
        });
    }
  );

  useEffect(() => {
    submitDisRef.current = submitDis;
  }, [submitDis]);

  useEffect(() => {
    if (exception === 'taskDone') {
      // 炖图成功但未发布的异常退出
      onInvokeAddPhoto();
    }
  }, [exception]);

  const { keyword, originalPhotos } = useMakePhotoStoreV2(
    useShallow(state => selectState(state, ['keyword', 'originalPhotos']))
  );
  const defaultGameType = photos[0]?.gameType;

  const getRecommendParams = useMemoizedFn(() => ({
    photoId: photos
      ?.filter(i => i && i.photoId)
      .map(i => i.photoId) as string[],
    gameType: defaultGameType
  }));

  const {
    isFold,
    toggleTopicSelect,
    handleSelectTopic,
    selectedTopics,
    handleInitTopic
  } = useTopicSelect({
    defaultFold: true,
    getRecommendParams
  });

  const FoldTopic = useMemo(() => {
    if (showKeyboard) return null;

    return (
      <TopicSelector
        gameType={defaultGameType}
        selectedTopics={selectedTopics}
        onTopicSelect={handleSelectTopic}
        isFold={true}
        onTopicInit={handleInitTopic}
        style={{
          marginLeft: -12,
          marginRight: -12
        }}
        foldTopicNonPressable={true}
      />
    );
  }, [selectedTopics, showKeyboard, defaultGameType]);

  const UnfoldTopic = useMemo(() => {
    return (
      <TopicSelector
        gameType={defaultGameType}
        selectedTopics={selectedTopics}
        onTopicSelect={handleSelectTopic}
        onTopicInit={handleInitTopic}
        isFold={false}
        foldTopicNonPressable={true}
      />
    );
  }, [selectedTopics, defaultGameType]);

  const publishButton = useMemo(() => {
    return (
      <BounceView style={$topButton}>
        <Button
          style={{
            backgroundColor: StyleSheet.currentColors.brand1,
            borderRadius: 500,
            paddingHorizontal: 16,
            paddingVertical: 4,
            // alignItems: 'center',
            width: dp2px(70),
            height: dp2px(26)
          }}
          onPress={() => {
            blurAll();
            onSubmit();
          }}
          disabled={submitDis}
        >
          <View
            style={[
              StyleSheet.rowStyle,
              { height: dp2px(18), alignItems: 'center', gap: 3 }
            ]}
          >
            <Icon icon="publish" size={12} />
            <Text style={[st.$publishButtonText, st.$publishButtonTextSm]}>
              发布
            </Text>
          </View>
        </Button>
      </BounceView>
    );
  }, [submitDis, onSubmit]);

  const primaryPublishButton = useMemo(() => {
    if (showKeyboard || previewImageModalVisible) return null;
    return (
      <View
        style={[
          StyleSheet.rowStyle,
          {
            position: 'absolute',
            bottom: 0,
            width: '100%',
            paddingTop: 8,
            paddingBottom: +(paddingBottom || 0) + 8,
            justifyContent: 'center'
          }
        ]}
      >
        <GuideWrapper
          isGuideVisible={newUserPendantGuide && isFold}
          guideType={GUIDE_TYPE_ENUM.ELEMENT}
          delay={600}
          renderGuideContent={m => m && <AvatarPendantGuide measure={m} />}
        >
          <Button
            style={{
              backgroundColor: StyleSheet.currentColors.brand1,
              borderRadius: 500,
              paddingHorizontal: 16,
              paddingVertical: 4,
              // alignItems: 'center',
              width: dp2px(290),
              height: dp2px(50)
            }}
            onPress={() => {
              onSubmit();
            }}
            onLayout={() => {
              setTimeout(() => {
                setIsElMounted(true);
              }, 100);
            }}
            disabled={submitDis}
          >
            <View
              style={[
                StyleSheet.rowStyle,
                { justifyContent: 'center', gap: 5 }
              ]}
            >
              <Icon icon="publish" size={15} />
              <Text style={st.$publishButtonText}>发布</Text>
            </View>
          </Button>
        </GuideWrapper>
      </View>
    );
  }, [
    paddingBottom,
    onSubmit,
    submitDis,
    showKeyboard,
    isFold,
    newUserPendantGuide
  ]);

  const renderClose = () => {
    if (!story) {
      return null;
    }
    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-end',
          paddingLeft: 12,
          paddingRight: 4
        }}
      >
        <TouchableOpacity
          hitSlop={10}
          onPress={() => {
            setStory('');
          }}
        >
          <Icon icon="close_dark_fill" size={20} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderTopic = () => {
    return (
      <Pressable
        style={[st.$tipWrap, $line]}
        onPress={() => {
          blurAll();
          toggleTopicSelect(false);
        }}
      >
        <View style={[StyleSheet.rowStyle, { gap: 4, opacity: 0.6 }]}>
          <Icon
            icon="topic_tag"
            size={11}
            color={StyleSheet.currentColors.white}
          />
          <Text
            style={{
              color: StyleSheet.currentColors.white,
              fontSize: 14,
              fontWeight: '400'
            }}
          >
            添加话题
          </Text>
        </View>
        <View>
          {isFold ? (
            <Icon
              icon="back"
              color={'rgba(256, 256, 256, 0.6)'}
              size={16}
              style={{ transform: [{ rotateY: '180deg' }] }}
            />
          ) : (
            <Text style={{ color: 'rgba(256, 256, 256, 0.6)' }}>收起</Text>
          )}
        </View>
      </Pressable>
    );
  };

  const onHeaderInputFocus = usePersistFn(() => {
    showRecommend.value = true;
    !noAutoWrite && setShowAdviceInput(true);
    toggleTopicSelect(true);
    picwriteIdRef.current = uuid();
    PIC_WRITE_FLAG = 1;
    reportExpo(
      'picwrite_title_edit',
      {
        module: 'publish',
        title_edit_id: picwriteIdRef.current,
        title
      },
      'start'
    );
  });

  const handlePhotoDelete = photo => {
    const newPhotos = photos
      .map(i =>
        i.photoId === photo.photoId ||
        (i.imageId && i.imageId === photo.imageId)
          ? false
          : i
      )
      .filter((i => Boolean(i)) as <T>(i: T | false) => i is T);
    usePublishStore.getState().setPhotos(newPhotos);
  };

  const renderPureItem = useMemoizedFn(({ item, drag, isActive }) => {
    return (
      <Pressable
        onLongPress={() => {
          drag();
          clickEffect();
        }}
        delayLongPress={400}
        disabled={isActive}
        onPress={() => {
          onPreviewImage(item.index, scene === 'goods' ? false : true);
        }}
      >
        <View style={st.$previewItem} key={item.photoId}>
          <Image
            source={formatTosUrl(item?.url || '', {
              size: 'size4'
            })}
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              resizeMode:
                item?.gameType === GameType.EMOJI ? 'contain' : 'cover'
            }}
          />
          <View
            style={[
              $flexCenter,
              st.$validateSafe,
              {
                opacity: notSafeImages?.includes(item?.photoId || '') ? 1 : 0
              }
            ]}
          >
            <Icon icon="makephoto_publish_validate_safe" size={24} />
          </View>
          <TouchableOpacity
            hitSlop={5}
            style={st.$itemDel}
            onPress={() => {
              showConfirm({
                title: '确认删除图片？',
                content: '被删除的图片保留在图集中',
                confirmText: '确认',
                cancelText: '取消',
                onConfirm: ({ close }) => {
                  handlePhotoDelete(item);
                  close();
                },
                simultaneous: true
              });
            }}
          >
            <Icon size={8.3} icon="publish_delete" />
          </TouchableOpacity>
        </View>
      </Pressable>
    );
  });

  const renderItem = ({ item, drag, isActive }) => {
    if (Number(item?.key) === ADD_KEY) {
      return (
        <TouchableOpacity
          style={[st.$previewItem, st.$addImageIcon]}
          onPress={onInvokeAddPhoto}
        >
          <Icon
            size={20}
            icon="makephoto_preview_add"
            style={{
              tintColor: StyleSheet.darkTheme.text.secondary
            }}
          />
        </TouchableOpacity>
      );
    }
    return <>{renderPureItem({ item, drag, isActive })}</>;
  };

  return (
    <PagePerformance pathname="publish/index">
      <KeyboardAvoidingView
        behavior={isIos ? undefined : 'height'}
        style={[StyleSheet.absoluteFill]}
      >
        <Screen
          theme="dark"
          title="发布作品"
          headerRight={() => showKeyboard && publishButton}
          contentContainerStyle={{
            height: 'auto',
            minHeight: '100%',
            display: previewImageModalVisible ? 'none' : 'flex'
          }}
          safeAreaEdges={['top']}
          screenStyle={{
            backgroundColor: StyleSheet.darkTheme.background.page
          }}
          onBack={() => {
            if (originalPhotos?.length) {
              useMakePhotoStoreV2.getState().setState({
                photos: originalPhotos
              });
            }
            router.back();
          }}
        >
          <ScrollView
            keyboardShouldPersistTaps="handled"
            ref={pageScrollRef}
            keyboardDismissMode="on-drag"
            contentContainerStyle={{
              flex: showKeyboard && showStoryAdviceInput ? 0 : 1,
              paddingBottom: 64
            }}
          >
            <Animated.View style={[st.$previewCont]}>
              <DraggableFlatList
                style={st.$previewWrap}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingLeft: 20, paddingVertical: 20 }}
                keyExtractor={(item, index) => String(item?.key || index)}
                onDragEnd={params => {
                  const { data } = params || {};
                  const newPhotoSet = data
                    .filter(item => Number(item.key) !== ADD_KEY)
                    .map((item, index) => ({ ...item, num: index + 1 }));
                  usePublishStore
                    .getState()
                    .setPhotos(newPhotoSet as PhotosItem[]);
                }}
                renderItem={renderItem}
                data={[
                  ...photos.map((i, index) => ({
                    ...i,
                    key: i.photoId,
                    index
                  })),
                  {
                    key: ADD_KEY,
                    disabledDrag: true
                  }
                ]}
              />
            </Animated.View>
            <View style={[st.$formItem, { paddingBottom: 12 }]}>
              <TextInput
                allowFontScaling={false}
                style={[
                  st.$textinput,
                  {
                    paddingRight: 78
                  }
                ]}
                numberOfLines={1}
                placeholder="好的标题会获得更多赞哦"
                placeholderTextColor="rgba(255,255,255,0.5)"
                ref={titleRef}
                maxLength={30}
                value={title}
                onChangeText={t => {
                  // if (t.length > 20) {
                  //   setTitle(t.slice(0, 20));
                  //   return;
                  // }
                  setTitle(t);
                }}
                onFocus={onHeaderInputFocus}
                onBlur={() => {
                  showRecommend.value = false;
                  setShowAdviceInput(false);
                  PIC_WRITE_FLAG = 0;
                  reportExpo(
                    'picwrite_title_edit',
                    {
                      module: 'publish',
                      title_edit_id: picwriteIdRef.current,
                      title
                    },
                    'end'
                  );
                }}
              />

              <Text
                style={[
                  st.$tip,
                  {
                    bottom: Platform.OS === 'ios' ? 8 : 13
                  },
                  st.$text,
                  !validTitle && title.length ? st.$error : null
                ]}
              >
                {!title.length
                  ? '必填'
                  : showAdviceInput
                    ? `字数(${title.length}/30)`
                    : ''}
              </Text>
            </View>

            <View style={[st.$formItem, $line, { paddingTop: 10 }]}>
              <View
                style={{
                  height: 139
                }}
              >
                <TextInput
                  allowFontScaling={false}
                  style={[
                    st.$textinput2,
                    {
                      flex: 1,
                      opacity: adviceLoading ? 0 : 1
                    }
                  ]}
                  multiline
                  placeholder="添加正文"
                  placeholderTextColor="rgba(255,255,255,0.5)"
                  value={story}
                  ref={storyRef}
                  textAlignVertical="top"
                  onChangeText={setStory}
                  onFocus={() => {
                    showStoryTag.value = true;
                    toggleTopicSelect(true);
                    setShowStoryAdviceInput(true);
                  }}
                  onBlur={() => {
                    showStoryTag.value = false;
                    setShowStoryAdviceInput(false);
                  }}
                />
                {adviceLoading && (
                  <Loading
                    itemStyle={st.loadingItemStyle}
                    style={st.loadingStyle}
                  />
                )}
              </View>
              <View
                style={{
                  minHeight: 60,
                  flexDirection: 'column',
                  gap: 12
                }}
              >
                {FoldTopic}
              </View>
              {showKeyboard && showStoryAdviceInput && (
                <View style={{ height: 90 }} />
              )}
            </View>
            {!showKeyboard && (
              <View>
                <Pressable
                  style={[st.$tipWrap]}
                  onPress={() => {
                    toggleTopicSelect(!isFold);
                  }}
                >
                  <View style={[StyleSheet.rowStyle, { gap: 4, opacity: 0.6 }]}>
                    <Icon
                      icon="topic_tag"
                      size={11}
                      color={StyleSheet.currentColors.white}
                    />
                    <Text
                      style={{
                        color: StyleSheet.currentColors.white,
                        fontSize: 14,
                        fontWeight: '400'
                      }}
                    >
                      添加话题
                    </Text>
                  </View>
                  <View>
                    {isFold ? (
                      <Icon
                        icon="back"
                        color={'rgba(256, 256, 256, 0.6)'}
                        size={16}
                        style={{ transform: [{ rotateY: '180deg' }] }}
                      />
                    ) : (
                      <Text style={{ color: 'rgba(256, 256, 256, 0.6)' }}>
                        收起
                      </Text>
                    )}
                  </View>
                </Pressable>
                <View
                  style={{
                    paddingHorizontal: 8,
                    paddingBottom: !isFold || selectedTopics.length > 0 ? 24 : 0
                  }}
                >
                  {!isFold && UnfoldTopic}
                </View>
              </View>
            )}
            {!showKeyboard && (
              <View style={[st.$tipWrap, $line]}>
                <View style={[StyleSheet.rowStyle, { gap: 4, opacity: 0.6 }]}>
                  <Icon icon="makephoto_download" size={11} />
                  <Text
                    style={{
                      color: StyleSheet.currentColors.white,
                      fontSize: 14,
                      fontWeight: '400'
                    }}
                  >
                    保存至相册
                  </Text>
                </View>
                <Toggle
                  onToggle={isSave => {
                    reportClick(MakePhotoEvents.save_image, {
                      isSave,
                      module: 'publish'
                    });
                    setSaveState(isSave);
                  }}
                  isOn={isSave}
                />
              </View>
            )}

            {Platform.OS === 'android' && primaryPublishButton}
            {/* </Pressable> */}
            <AlbumSheet
              maxLen={maxImageLimitNum}
              callWhere={AlbumFromType.PUBLISH}
              showDrawingEntry={true}
              isVisible={showAlbum}
              defaultKey={AlbumType.history}
              onClose={() => {
                setShowAlbum(false);
              }}
            />
          </ScrollView>
          <View
            style={[
              {
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0
              },
              {
                opacity: showAdviceInput && showKeyboard ? 1 : 0,
                pointerEvents: showAdviceInput && showKeyboard ? 'auto' : 'none'
              }
            ]}
          >
            <AdviceInput
              theme="dark"
              refreshParams={mainPhoto}
              fetchList={fetchAdviceList}
              visitID={getPageID()}
              reportEditIdRef={picwriteIdRef}
              visible={showAdviceInput && showKeyboard}
              onSelect={v => {
                setTitle(t => t + v);
              }}
            />
          </View>

          {showKeyboard && showStoryAdviceInput ? (
            <View
              style={[
                {
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0
                }
              ]}
            >
              <Pressable
                style={[$rollWrap]}
                onPress={() => {
                  blurAll();
                }}
              >
                {!noAutoWrite && (
                  <RollButton
                    loading={advicing}
                    text="小狸帮你写故事"
                    onPress={e => {
                      e?.stopPropagation();
                      onAdviceStory();
                    }}
                  />
                )}
                <View style={st.contentClearWrap}>
                  <Text
                    style={[
                      st.$text,
                      !validStory && story.length ? st.$error : null
                    ]}
                  >
                    字数({story.length}/{STORY_LIMIT})
                  </Text>
                  {renderClose()}
                </View>
              </Pressable>
              <View>{renderTopic()}</View>
            </View>
          ) : null}
        </Screen>
      </KeyboardAvoidingView>
      {Platform.OS === 'ios' && primaryPublishButton}
    </PagePerformance>
  );

  function onInvokeAddPhoto() {
    usePublishStore.getState().getAlbumPhotos(true);
    usePublishStore.getState().getHistoryPhotos(true);
    const rearrangedPhotos = photos
      .sort((a, b) => a.num - b.num)
      .map((i, index) => ({ ...i, num: index + 1 }));
    const { photosSet, historyPhotosSet } = rearrangedPhotos.reduce(
      (acc, cur) => {
        if (!cur.photoId) {
          return acc;
        }
        if (cur.albumType === AlbumType.album) {
          acc.photosSet[cur.photoId as string] = cur;
        } else if (cur.albumType === AlbumType.history) {
          acc.historyPhotosSet[cur.photoId as string] = cur;
        }
        return acc;
      },
      {
        photosSet: {},
        historyPhotosSet: {}
      } as {
        photosSet: Record<string, SelectedItem>;
        historyPhotosSet: Record<string, SelectedItem>;
      }
    );

    usePublishStore.getState().changePhotos(photosSet);
    usePublishStore.getState().changeHistoryPhotos(historyPhotosSet);
    reportClick(MakePhotoEvents.add_image_click, { module: 'publish' });
    blurAll();
    setShowAlbum(true);
    usePerformanceStore.getState().recordStart('make_photo_photo_set_render', {
      performance_type: 'render',
      performance_key: AlbumFromType.PUBLISH
    });
  }

  function fetchAdviceList() {
    return usePublishStore.getState().getAdvicePrompts(storyTextRef.current);
  }

  function onAdviceStory(retry?: boolean) {
    if (advicing) {
      stopAdvice();

      reportExpo(
        'picwrite_api',
        {
          module: 'publish',
          trace_id: currentStoryIdRef.current,
          total_time_cost: Date.now() - startAdviceTimeRef.current,
          first_word_time_cost: firstWordTimeRef.current,
          end_type: 'user_stop'
        },
        'get_content'
      );
      reportClick('picwrite_stop_button');
      return;
    }

    const { photoId, subPhotoIds } = usePublishStore.getState().photos[0] || {};
    if (photoId || subPhotoIds) reportClick('picwrite_button');
    if (!photoId && !subPhotoIds?.[0]) {
      showToast('请选择图片~');
      return;
    }
    let newStory = '';
    setAdvicing(true);
    setAdviceLoading(true);
    // pageScrollRef.current?.scrollTo({
    //   y: 110,
    //   animated: true
    // });
    let loaded = false;
    // if (Platform.OS === 'ios') {
    //   Haptics.impactAsync();
    // }
    const newMsgId = uuid();
    currentStoryIdRef.current = newMsgId;
    startAdviceTimeRef.current = Date.now();
    let first_word_time_cost = Date.now();
    GenPhotoStory(
      {
        photoId: photoId || subPhotoIds?.[0],
        title: titleTextRef.current || '',
        imageRegrasp: !retry,
        pt: {
          page: getPageID()
        }
      },
      async ({ delta, finished }, msgId, traceid) => {
        if (!first_word_time_cost) {
          first_word_time_cost = Date.now() - first_word_time_cost;
          firstWordTimeRef.current = first_word_time_cost;
        }
        if (msgId !== currentStoryIdRef.current) return;
        setAdviceLoading(false);
        newStory += delta || '';
        setStory(newStory);
        if (!loaded) {
          loaded = true;
        }
        if (finished) {
          setStory(newStory);
          reportExpo(
            'picwrite_api',
            {
              module: 'publish',
              trace_id: traceid,
              total_time_cost: Date.now() - startAdviceTimeRef.current,
              first_word_time_cost,
              end_type: 'api_end'
            },
            'get_content'
          );

          showToast('小狸的故事写好了，快看看吧~');
          console.log('finished-------', newStory);
          setAdvicing(false);
        }
      },
      e => {
        setAdvicing(false);
        setAdviceLoading(false);
        if ((e as ErrorRes) && checkSecurity(e as ErrorRes) && !retry) {
          // 只重试一次
          setStory('');
          onAdviceStory(true);
        } else {
          showToast('生成小故事失败~');
        }
        errorReport('GenPhotoStory', ReportError.PUBLISH, e);
      },
      newMsgId
    );

    Socket.events.on(
      'disconnect',
      () => {
        stopAdvice();
        showToast('当前网络信号差，请重试~');
      },
      true
    );
    reportClick('edit_button');
    // requestAnimationFrame()
    // string photo_id = 1;
    // string title = 2;
    // bool image_regrasp = 3; //是否重新进行图片理解
  }

  function stopAdvice() {
    setAdvicing(false);
    setAdviceLoading(false);
    currentStoryIdRef.current = '';
  }

  function persistPhotos(photo: PhotosItem): Promise<void> {
    if (!photo?.photoId) {
      showToast('保存失败，请重试');
      return Promise.resolve();
    }

    showLoading();

    reportClick('image_generate_save', {
      module: 'publish',
      save_image_id: photo.photoId
    });

    return PersistPhotos({ photoIds: [photo?.photoId] })
      .then(res => {
        const { ok } = res || {};
        console.log('=======ok====', res);
        showToast('保存成功');
        hideLoading();
      })
      .catch(e => {
        console.log(e);
        showToast('保存失败');
        hideLoading();
      });
  }

  function onPreviewImage(index: number, hasTakeSame: boolean) {
    setPreviewImageModalVisible(true);
    showPreviewImages<PhotosItem>({
      index: index,
      list: photos,
      // isFromPublish: true,
      renderTopRightSlot: (photo, callbacks) => (
        <View style={{ ...rowStyle, gap: 15 }}>
          {/* <Icon
            icon="delete"
            size={16}
            onPress={() => {
              showConfirm({
                title: '确认删除图片？',
                confirmText: '确认',
                cancelText: '取消',
                onConfirm: ({ close }) => {
                  if (photo && (photo.photoId || photo.imageId)) {
                    const newPhotos = photos
                      .map(i =>
                        i.photoId === photo.photoId ||
                        (i.imageId && i.imageId === photo.imageId)
                          ? false
                          : i
                      )
                      .filter((i => Boolean(i)) as <T>(i: T | false) => i is T);
                    usePublishStore.getState().setPhotos(newPhotos);
                  }
                  close();
                  hidePreviewImages();
                }
              });
            }}
            preset="circle"
            containerStyle={$iconContainerStyle}
          /> */}
          {photo && photo.albumType === AlbumType.history && !photo.archived ? (
            <SaveButtonPure
              disabled={!!photo.subPhotoIds?.length}
              onPress={() => {
                persistPhotos(photo).then(() => {
                  usePublishStore.getState().getAlbumPhotos(true);
                  usePublishStore.getState().getHistoryPhotos(true);
                });
              }}
            />
          ) : null}
        </View>
      ),
      renderBottomRightSlot: (photo, cb) => {
        console.log(scene, '===photo.gameType');
        if (hasTakeSame) {
          return (
            <TakePhotoButton
              style={{
                width: PREVIEW_TAKE_PHOTO_BUTTON_WIDTH,
                height: 44,
                opacity: photo?.subPhotoIds?.length ? 0 : 1
              }}
              gameType={photo?.gameType}
              onPress={() => {
                cb.close();
                if (photo?.photoId && photo.gameType !== undefined) {
                  // @ts-ignore
                  go2Create({
                    gameType: photo.gameType,
                    gameParams: {
                      photoId: photo.photoId,
                      photoUrl: photo.url,
                      size: aspectRatioSizeMap.get(
                        photo.aspect || ImageAspectRatio.ASPECT_RATION_3x4
                      )
                    },
                    params: {
                      source: Source.DRAWING_WITH_PROMPT
                    }
                  });
                }

                hidePreviewImages();
              }}
            />
          );
        }
        return null;
      },
      renderBottomLeftSlot: (target, { saveImage }) => (
        <ImageSharePanel
          image={target}
          saveImage={saveImage}
          reportParams={{
            save_page: '3'
          }}
        />
      ),
      onClose: () => {
        setPreviewImageModalVisible(false);
      }
    });
  }
}

export default memo(Publish);

const st = StyleSheet.create({
  contentClearWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 26
  },
  loadingStyle: {
    position: 'absolute',
    top: 0,
    left: 0
  },
  loadingItemStyle: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)'
  },
  $previewItem: {
    position: 'relative',
    width: dp2px(75.51),
    height: dp2px(100),
    borderRadius: 6,
    marginRight: 6,
    overflow: 'hidden',
    backgroundColor: StyleSheet.hex('#2A1D1B', 0.77)
  },
  $itemDel: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 15,
    height: 15,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderBottomLeftRadius: 5
  },
  $addImageIcon: {
    backgroundColor: StyleSheet.darkTheme.background.input,
    ...StyleSheet.columnStyle,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 2
  },
  $previewCont: {
    width: '100%',
    transformOrigin: '0px 0px'
  },
  $previewWrap: {
    width: '100%'
  },
  $formItem: {
    // marginHorizontal: 9,
    // backgroundColor: bgColor,
    marginHorizontal: 20
  },
  $tip: {
    position: 'absolute',
    bottom: 11,
    right: 10
  },
  $text: {
    fontSize: 12,
    fontWeight: '400',
    color: StyleSheet.hex(StyleSheet.currentColors.white, 0.4)
  },
  $textinput: {
    color: StyleSheet.currentColors.white,
    verticalAlign: 'middle',
    textAlign: 'justify',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 20
  },
  $textinput2: {
    color: StyleSheet.hex(StyleSheet.currentColors.white, 0.87),
    textAlign: 'justify',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 24
  },
  $checkWrap: {
    ...StyleSheet.circleStyle,
    ...StyleSheet.centerStyle,
    width: 12,
    height: 12,
    backgroundColor: '#ccc'
  },
  $checkedWrap: {
    backgroundColor: '#6ED4FF'
  },
  $tipWrap: {
    ...StyleSheet.rowStyle,
    justifyContent: 'space-between',
    height: 44,
    marginHorizontal: 20,
    ...StyleSheet.rowStyle
  },
  $tipText: {
    marginLeft: StyleSheet.spacing.xxs,
    color: StyleSheet.hex(StyleSheet.currentColors.white, 0.7)
  },
  $publishButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: StyleSheet.currentColors.white
  },
  $publishButtonTextSm: {
    fontSize: 12,
    lineHeight: 18
  },
  $error: {
    color: StyleSheet.currentColors.red
  },
  $publishButon: {},
  $validateSafe: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: '#00000066'
  }
});
