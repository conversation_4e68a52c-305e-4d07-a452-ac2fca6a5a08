import { useMemoizedFn } from 'ahooks';
import {
  ReactNode,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef
} from 'react';
import {
  FlatList,
  FlatListProps,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  View
} from 'react-native';
import Animated, {
  Extrapolate,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import LiveScreen from '@/src/bizComponents/liveScreen';
import MagicVideoScreen from '@/src/bizComponents/magic-video';
import { SCREEN_HEIGHT } from '@/src/bizComponents/nestedScrollView';
import { BoomWen } from '@/src/bizComponents/playgoundScreen/boomWen';
import { DreamMirrorPolaroid } from '@/src/bizComponents/playgoundScreen/dreamMirrorPolaroid';
import { HouseDance } from '@/src/bizComponents/playgoundScreen/houseDance';
import JellyCat from '@/src/bizComponents/playgoundScreen/jellycat';
import Meme from '@/src/bizComponents/playgoundScreen/meme';
import MoreGame from '@/src/bizComponents/playgoundScreen/moreGame';
import MemoNormalGame from '@/src/bizComponents/playgoundScreen/normalGame';
import SoulMaker from '@/src/bizComponents/soulmakerScreen';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { usePersistFn, useScreenSize } from '@/src/hooks';
import { useGameFeatureGate } from '@/src/hooks/useGameFeatureGate';
import { useParams } from '@/src/hooks/useParams';
import { useScrollDownDetector } from '@/src/hooks/useScrollDetector';
import { useGameEntryStore } from '@/src/store/gameEntry';
import { useNormalGameStore } from '@/src/store/normalGame';
import { PageState, useSoulMakerStore } from '@/src/store/soulmaker';
import { darkTheme } from '@/src/theme';
import { GameType } from '@/src/types';
import { ReportError, errorReport } from '@/src/utils/error-log';
import {
  addCommonReportParams,
  reportClick,
  reportExpo
} from '@/src/utils/report';
import { useShallow } from 'zustand/react/shallow';

type GameEntryConfig = {
  gameType?: string;
  Component:
    | (({
        isCurrent,
        gameType
      }: {
        isCurrent: boolean;
        gameType: GameType;
      }) => ReactNode)
    | null;
  expoFunc?: (params?: Record<string, string | number>) => void;
};

const GameEntryList: GameEntryConfig[] = [
  {
    gameType: `${GameType.LIVE_PHOTO}`,
    Component: LiveScreen,
    expoFunc: () => {
      reportExpo('video_template_preview', { module: 'park' });
    }
  },
  {
    gameType: `${GameType.JELLYCAT}`,
    Component: JellyCat,
    expoFunc: params => {
      const { source } = params || {};
      reportExpo('expo', {
        module: 'pendant',
        source
      });
    }
  },
  {
    gameType: `${GameType.OTAKUDANCE}`,
    Component: HouseDance,
    expoFunc: () => {
      reportExpo('dance_template_preview', {
        module: 'park'
      });
    }
  },
  {
    gameType: `${GameType.REIMAGINE}`,
    Component: MagicVideoScreen,
    expoFunc: () => {
      reportExpo('expo', {
        module: 'video'
      });
    }
  },
  {
    gameType: `${GameType.DREAMCORE}`,
    Component: DreamMirrorPolaroid,
    expoFunc: params => {
      const { source } = params || {};
      reportExpo('expo', { module: 'dreamphoto', source });
    }
  },
  {
    gameType: `${GameType.MEME}`,
    Component: Meme,
    expoFunc: () => {
      reportExpo('meme', { module: 'park' });
    }
  },
  {
    gameType: `${GameType.REVIVE}`,
    Component: SoulMaker,
    expoFunc: () => {
      reportExpo('makesoul', { module: 'park' });
    }
  },
  {
    gameType: `${GameType.DANCE_TOGETHER}`,
    Component: BoomWen,
    expoFunc: () => {
      reportExpo('start', {
        module: 'dance'
      });
    }
  }
];

const useAvailableGameType = () => {
  const { enableCreate } = useGameFeatureGate();
  const { enableGameTypes } = useNormalGameStore(
    useShallow(state => ({
      enableGameTypes: state.enableGameTypes
    }))
  );
  const availableGameEntryList = useMemo(() => {
    return enableGameTypes
      .map(gameType => {
        return {
          gameType: `${gameType}`,
          Component: MemoNormalGame,
          expoFunc: params => {
            const { source } = params || {};
            reportExpo('expo', {
              module: 'AI-play',
              source,
              game_type: gameType
            });
          }
        } as GameEntryConfig;
      })
      .concat(GameEntryList)
      .filter(item => {
        const featureEnabled = enableCreate.includes(Number(item.gameType));
        const entriesOnline = Object.keys(
          useGameEntryStore.getState().gameEntries
        ).includes(item.gameType ?? '');
        return featureEnabled && entriesOnline;
      });
  }, [enableCreate, enableGameTypes]);

  const getGameTypeToScreenIndex = useMemoizedFn((gameType: `${GameType}`) => {
    const idx = getGameSequence(gameType).findIndex(
      item => item.gameType === gameType
    );
    return idx === -1 ? 0 : idx;
  });

  const getGameTypeComponent = useMemoizedFn((gameType: `${GameType}`) => {
    const entry = availableGameEntryList.find(
      item => item.gameType === gameType
    );
    return entry?.Component || availableGameEntryList[0].Component;
  });

  const getGameSequence = useMemoizedFn((firstGameType: `${GameType}`) => {
    let firstItem: GameEntryConfig | null = null;
    const gameList = availableGameEntryList.filter(g => {
      if (g.gameType === firstGameType) {
        firstItem = g;
        return false;
      }
      return !!g.Component;
    });
    if (firstItem) {
      return [firstItem, ...gameList]
        .map(g => g)
        .concat({ Component: MoreGame });
    }
    return gameList.map(g => g).concat({ Component: MoreGame });
  });

  return {
    availableGameEntryList,
    getGameTypeToScreenIndex,
    getGameTypeComponent,
    getGameSequence
  };
};

const PlaygroundPageItem = memo(
  ({
    item: ScreenFunction,
    index,
    data,
    scrollY,
    maxLen
  }: {
    item: ({
      isCurrent,
      gameType
    }: {
      isCurrent: boolean;
      gameType: GameType;
    }) => ReactNode;
    data: GameEntryConfig;
    index: number;
    scrollY: number;
    maxLen: number;
  }) => {
    const $pageShareOpacity = useSharedValue(1);
    const $pageShareScroll = useSharedValue(0);

    const screenSize = useScreenSize('screen');
    const isCurrent = useMemo(() => {
      return Math.round(scrollY / screenSize.height) === index;
    }, [index, scrollY]);

    useEffect(() => {
      $pageShareScroll.value = withTiming(scrollY, {
        duration: 250
      });

      if (index === 0) {
        $pageShareOpacity.value = interpolate(
          scrollY,
          [0, SCREEN_HEIGHT / 2, SCREEN_HEIGHT],
          [1, 0.5, 0.5],
          Extrapolate.CLAMP
        );
      } else if (index === maxLen - 1) {
        $pageShareOpacity.value = interpolate(
          scrollY,
          [
            SCREEN_HEIGHT * 3 - SCREEN_HEIGHT / 2,
            SCREEN_HEIGHT * 3,
            SCREEN_HEIGHT * 3 + SCREEN_HEIGHT / 2
          ],
          [0.5, 1, 1],
          Extrapolate.CLAMP
        );
      } else {
        $pageShareOpacity.value = interpolate(
          scrollY,
          [
            SCREEN_HEIGHT * index - SCREEN_HEIGHT / 2,
            SCREEN_HEIGHT * index,
            SCREEN_HEIGHT * index + SCREEN_HEIGHT / 2
          ],
          [0.5, 1, 0.5],
          Extrapolate.CLAMP
        );
      }
      return () => {
        $pageShareOpacity.value = 1;
        $pageShareScroll.value = 0;
      };
    }, [scrollY]);

    const $pagerAnimateStyle = useAnimatedStyle(
      () => ({
        opacity: $pageShareOpacity.value
      }),
      [$pageShareOpacity]
    );

    return (
      <View
        style={[screenSize, { backgroundColor: darkTheme.background.page }]}
        key={index}
      >
        <Animated.View style={[{ flex: 1 }, $pagerAnimateStyle]}>
          {Platform.OS === 'ios' ? (
            <ScreenFunction
              isCurrent={isCurrent}
              gameType={Number(data.gameType ?? GameType.UNKNOWN)}
            />
          ) : (
            isCurrent && (
              <ScreenFunction
                isCurrent={isCurrent}
                gameType={Number(data.gameType ?? GameType.UNKNOWN)}
              />
            )
          )}
        </Animated.View>
      </View>
    );
  }
);

PlaygroundPageItem.displayName = 'PlaygroundPageItem';

const PlaygroundFlatList = memo(() => {
  const { gameType, firstGameType, source } = useParams();

  const { getGameTypeToScreenIndex, getGameSequence, availableGameEntryList } =
    useAvailableGameType();

  const { pageState } = useSoulMakerStore(
    useShallow(state => ({ pageState: state.pageState }))
  );

  const initialIndex = useMemo(
    () => getGameTypeToScreenIndex(gameType as `${GameType}`),
    [availableGameEntryList]
  );

  const isInSoulMakerLoading = useMemo(() => {
    return pageState !== PageState.init;
  }, [pageState]);

  const scrollRef = useRef<FlatList>(null);
  // 部分android机型使用window不准确。eg. OPPO Reno11 5G
  const screenSize = useScreenSize('screen');
  const screenHeight = screenSize.height;
  const currentIndexRef = useRef(initialIndex);
  const reportIndexRef = useRef('');
  const firstTimeRef = useRef<NodeJS.Timeout>();
  const firstScrollTimeRef = useRef<NodeJS.Timeout>();
  const firstScrollUpTimeRef = useRef<NodeJS.Timeout>();

  const gamesScreen = useMemo(() => {
    const data = getGameSequence(gameType as `${GameType}`);
    return data;
  }, [availableGameEntryList]);

  const scollDownReport = useMemoizedFn(() => {
    switch (currentIndexRef.current) {
      case getGameTypeToScreenIndex(`${GameType.MEME}`): {
        reportClick('meme_swipe', { module: 'park' });
        return;
      }
      case getGameTypeToScreenIndex(`${GameType.REVIVE}`): {
        reportClick('makesoul_swipe', { module: 'park' });
        return;
      }
      case getGameTypeToScreenIndex(`${GameType.LIVE_PHOTO}`): {
        reportClick('livephoto_swipe', { module: 'park' });
        return;
      }
    }
  });

  const { handler: onScroll, scrollY } = useScrollDownDetector(scollDownReport);

  const onTouchEvent = useCallback(() => {
    if (firstTimeRef.current) {
      clearTimeout(firstTimeRef.current);
    }
    if (firstScrollTimeRef.current) {
      clearTimeout(firstScrollTimeRef.current);
    }
    if (firstScrollUpTimeRef.current) {
      clearTimeout(firstScrollUpTimeRef.current);
    }
  }, []);

  const onViewableItemsChanged: FlatListProps<GameEntryConfig>['onViewableItemsChanged'] =
    useMemoizedFn(({ viewableItems }) => {
      if (viewableItems?.length === 1) {
        const { index, item } = viewableItems?.[0] ?? {};
        currentIndexRef.current = index ?? 0;

        if (reportIndexRef.current === index) return;
        reportIndexRef.current = index;
        const { expoFunc } = item || {};
        if (expoFunc) {
          expoFunc({ source });
        }
      } else {
        return;
      }
    });

  const getItemLayout = useCallback(
    (_: unknown, index: number) => ({
      length: screenHeight,
      offset: screenHeight * index,
      index
    }),
    [screenHeight]
  );

  const onMomentumScrollEnd = usePersistFn(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { contentOffset } = e.nativeEvent;
      const index = Math.round(contentOffset.y / screenHeight);
      currentIndexRef.current = index;
    }
  );

  return (
    <View style={{ height: screenSize.height }}>
      {currentIndexRef?.current > -1 && (
        <FlatList<GameEntryConfig>
          data={gamesScreen}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={16}
          pagingEnabled={true}
          initialScrollIndex={initialIndex}
          scrollEnabled={!isInSoulMakerLoading}
          onViewableItemsChanged={onViewableItemsChanged}
          initialNumToRender={1}
          windowSize={2}
          // removeClippedSubviews
          onResponderStart={onTouchEvent}
          onResponderMove={onTouchEvent}
          onResponderEnd={onTouchEvent}
          maxToRenderPerBatch={1}
          onScrollToIndexFailed={e => {
            errorReport(
              '[playground] scrollToIndex failed, index: ' + e.index,
              ReportError.PLAYGROUND
            );
          }}
          keyExtractor={(item, index) => item.gameType || `park${index}`}
          bounces={false}
          onScroll={onScroll}
          extraData={scrollY}
          ref={scrollRef}
          renderToHardwareTextureAndroid
          getItemLayout={getItemLayout}
          onMomentumScrollEnd={onMomentumScrollEnd}
          renderItem={({ item: { Component: ScreenFunction }, index }) => (
            <PlaygroundPageItem
              key={index}
              item={ScreenFunction!}
              index={index}
              data={gamesScreen[index]}
              scrollY={scrollY}
              maxLen={gamesScreen?.length || 0}
            />
          )}
        />
      )}
    </View>
  );
});

PlaygroundFlatList.displayName = 'PlaygroundFlatList';

const Playground = () => {
  const { activityId } = useParams<{ activityId?: string }>();
  useEffect(() => {
    if (activityId) {
      addCommonReportParams('park', { activityid: activityId });
    }
  }, [activityId]);
  return (
    <PagePerformance pathname="playground/index">
      <View
        style={{
          flex: 1,
          backgroundColor: darkTheme.background.page
        }}
      >
        <PlaygroundFlatList />
      </View>
    </PagePerformance>
  );
};

export default memo(Playground);
