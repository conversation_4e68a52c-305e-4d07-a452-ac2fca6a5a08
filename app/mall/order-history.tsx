import dayjs from 'dayjs';
import * as Clipboard from 'expo-clipboard';
import { useEffect, useState } from 'react';
import {
  FlatList,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import { pageQueryOrder } from '@/src/api/payment';
import { OrderStatus, OrderStatusMsg } from '@/src/api/payment/type';
import { Icon, showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { usePersistFn } from '@/src/hooks';
import { darkColors, darkTheme, typography } from '@/src/theme';
import { $USE_FONT, $flexHBetween, $flexHCenter } from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { Screen } from '@Components/screen';
import { OrderInfo } from '@/proto-registry/src/web/raccoon/order/common_pb';

export default function OrderHistory() {
  const [nextCursor, setNextCursor] = useState<undefined | string>('');
  const [orderList, setOrderList] = useState<OrderInfo[]>([]);

  const getOrderHistory = usePersistFn(async (cursor?: undefined | string) => {
    if (cursor === '') return;
    try {
      const res = await pageQueryOrder({
        pagination: {
          size: 20,
          cursor: cursor
        }
      });
      setOrderList([...orderList, ...res.orders]);
      setNextCursor(res.pagination?.nextCursor ?? '');
    } catch (error) {
      errorReport(
        'load order history error',
        ReportError.REQUEST,
        JSON.stringify(error)
      );
    }
  });

  useEffect(() => {
    getOrderHistory();
  }, []);

  const copyOrderId = usePersistFn(async (orderId: string) => {
    await Clipboard.setStringAsync(orderId);
    showToast('复制订单号成功');
  });

  return (
    <PagePerformance pathname="mall/order-history">
      <View
        style={{
          flex: 1,
          backgroundColor: darkTheme.background.page
        }}
      >
        <Screen
          backButton={false}
          headerLeft={() => (
            <TouchableOpacity onPress={safeGoBack}>
              <Icon
                icon="back"
                size={24}
                style={{
                  tintColor: '#fff'
                }}
              />
            </TouchableOpacity>
          )}
          title="我的订单记录"
          titleStyle={
            {
              color: darkTheme.text.primary
            } as ViewStyle
          }
          safeAreaEdges={['top']}
        >
          <FlatList
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            scrollEventThrottle={200}
            onEndReached={() => {
              getOrderHistory(nextCursor);
            }}
            contentContainerStyle={{
              paddingHorizontal: 16,
              gap: 12,
              marginTop: 24
            }}
            keyExtractor={(item, index) =>
              item && item.orderSn.toString() + index
            }
            data={orderList}
            renderItem={({ item, index }) => {
              return (
                <View style={[$orderItem]} key={index}>
                  <View style={{}}>
                    <View
                      style={[
                        $flexHBetween,
                        {
                          marginBottom: 8
                        }
                      ]}
                    >
                      <Text style={$name}>{item.products[0].name}</Text>
                    </View>
                    {item?.products[0].giveaways?.length ? (
                      <View style={[$flexHCenter, { marginBottom: 8 }]}>
                        <Text style={$subText}>
                          {'加赠商品：' + item?.products[0].giveaways[0].name}
                        </Text>
                      </View>
                    ) : null}
                    <View
                      style={[
                        $flexHBetween,
                        {
                          marginBottom: 8
                        }
                      ]}
                    >
                      <Text style={$subText}>
                        {'下单时间：' +
                          dayjs(Number(item.createdAt)).format(
                            'YYYY-MM-DD HH:mm:ss'
                          )}
                      </Text>
                    </View>
                    <View style={$flexHCenter}>
                      <Text style={$subText}>
                        {'订单编号：' + item.orderSn}
                      </Text>
                      <Icon
                        icon={'mall_copy'}
                        size={14}
                        style={{
                          marginLeft: 4
                        }}
                        onPress={() => {
                          copyOrderId(item.orderSn);
                        }}
                      />
                    </View>
                  </View>
                  <View>
                    <Text
                      style={[
                        $name,
                        {
                          alignSelf: 'flex-end',
                          marginBottom: 8
                        }
                      ]}
                    >
                      {'¥' + item.payPrice}
                    </Text>
                    <Text
                      style={[
                        $subText,
                        {
                          color:
                            item.orderStatusCode ===
                            OrderStatus.ORDER_STATUS_FAIL
                              ? '#FD5E5B'
                              : darkTheme.text.disabled,
                          fontWeight: '600',
                          alignSelf: 'flex-end'
                        }
                      ]}
                    >
                      {OrderStatusMsg[item.orderStatusCode]}
                    </Text>
                  </View>
                </View>
              );
            }}
            ListFooterComponent={() => {
              return (
                <Text
                  style={[
                    $noMore,
                    {
                      textAlign: 'center',
                      marginTop: 12
                    }
                  ]}
                >
                  暂无更多记录～
                </Text>
              );
            }}
          />
        </Screen>
      </View>
    </PagePerformance>
  );
}

const $orderItem: ViewStyle = {
  backgroundColor: '#FFFFFF0F',
  borderRadius: 12,
  padding: 16,
  justifyContent: 'space-between',
  flexDirection: 'row'
};

const $name: TextStyle = $USE_FONT(
  darkTheme.text.primary,
  typography.fonts.pingfangSC.normal,
  15,
  'normal',
  isIos ? '600' : 'bold',
  20
);

const $subText: TextStyle = $USE_FONT(
  darkTheme.text.disabled,
  typography.fonts.pingfangSC.normal,
  13,
  'normal',
  '400',
  undefined
);

const $noMore: TextStyle = $USE_FONT(
  darkTheme.text.placeholder,
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  '400',
  16
);
