import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import {
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { pointsClient } from '@/src/api/points';
import { SurplusText } from '@/src/bizComponents/mall/battery/surplusText';
import { Icon } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { usePersistFn } from '@/src/hooks';
import { darkTheme, typography } from '@/src/theme';
import { $USE_FONT } from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import {
  EBatteryChangeStatus,
  whichModeBattery,
  whichSymbolBattery
} from '@/src/utils/mall/const';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { Screen } from '@Components/screen';
import {
  InvoiceType,
  PointsInvoice
} from '@/proto-registry/src/web/raccoon/points/points_pb';

export default function BatteryRecord() {
  const [nextCursor, setNextCursor] = useState<undefined | string>('');
  const [recordList, setRecordList] = useState<PointsInvoice[]>([]);

  const getBatteryRecord = usePersistFn(async (cursor?: undefined | string) => {
    try {
      // 返回
      if (cursor === '') return;
      const res = await pointsClient.getPointsInvoices({
        invoiceType: InvoiceType.INVOICE_ALL,
        pagination: {
          size: 20,
          cursor: cursor
        }
      });

      setNextCursor(res?.pagination?.nextCursor);
      setRecordList((list: PointsInvoice[]) => {
        return [...list, ...res.invoices];
      });
    } catch (error) {
      errorReport(
        'load more battery record error',
        ReportError.REQUEST,
        JSON.stringify(error)
      );
    }
  });

  useEffect(() => {
    getBatteryRecord();
  }, []);

  return (
    <PagePerformance pathname="mall/battery-record">
      <View
        style={{
          flex: 1,
          backgroundColor: darkTheme.background.page
        }}
      >
        <Screen
          backButton={false}
          headerLeft={() => (
            <TouchableOpacity onPress={safeGoBack}>
              <Icon
                icon="back"
                size={24}
                style={{
                  tintColor: '#fff'
                }}
              />
            </TouchableOpacity>
          )}
          title="我的狸电池记录"
          titleStyle={
            {
              color: darkTheme.text.primary
            } as ViewStyle
          }
          safeAreaEdges={['top']}
        >
          <FlatList
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            scrollEventThrottle={200}
            onEndReached={() => {
              getBatteryRecord(nextCursor);
            }}
            keyExtractor={(item, index) =>
              item && item.createdTimestamp.toString() + index
            }
            data={recordList}
            renderItem={({ item, index }) => {
              const whichMode = whichModeBattery(item.changedPoints);
              const prefix = whichSymbolBattery(item.changedPoints);
              return (
                <View
                  style={[
                    $recordItem,
                    {
                      borderBottomWidth:
                        index === recordList.length - 1 ? 0 : 0.5
                    }
                  ]}
                  key={index}
                >
                  <View>
                    <Text style={$action}>{item.cause}</Text>
                    <Text style={$time}>
                      {dayjs(item.createdTimestamp * 1000).format(
                        'YYYY-MM-DD HH:mm:ss'
                      )}
                    </Text>
                  </View>
                  <View>
                    <SurplusText
                      mode={whichMode}
                      text={prefix + item.changedPoints.toString()}
                    />
                  </View>
                </View>
              );
            }}
            ListFooterComponent={() => {
              return (
                <Text
                  style={[
                    $noMore,
                    {
                      textAlign: 'center',
                      marginTop: 12
                    }
                  ]}
                >
                  暂无更多记录～
                </Text>
              );
            }}
          />
        </Screen>
      </View>
    </PagePerformance>
  );
}

const $recordItem: ViewStyle = {
  height: 80,
  marginHorizontal: 16,
  justifyContent: 'space-between',
  flexDirection: 'row',
  alignItems: 'center',
  borderBottomColor: '#FFFFFF14',
  borderBottomWidth: 0.5
};

const $action: TextStyle = {
  color: darkTheme.text.primary,
  fontFamily: typography.fonts.pingfangSC.normal,
  fontSize: 14,
  fontWeight: isIos ? '600' : 'bold',
  lineHeight: 20
};

const $time: TextStyle = {
  color: darkTheme.text.disabled,
  fontFamily: typography.fonts.pingfangSC.normal,
  fontSize: 12,
  fontWeight: '400',
  lineHeight: 16,
  marginTop: 2
};

const $noMore: TextStyle = $USE_FONT(
  darkTheme.text.placeholder,
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  '400',
  16
);
