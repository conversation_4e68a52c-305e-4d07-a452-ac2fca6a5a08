/**
 * 商城 V1
 */
import { router } from 'expo-router';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  ImageStyle,
  Pressable,
  ScrollView,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { pointsClient } from '@/src/api/points';
import { BatteryIntro } from '@/src/bizComponents/mall/batteryIntro';
import { PurchaseArea } from '@/src/bizComponents/mall/purchaseArea';
import { ShopSign } from '@/src/bizComponents/mall/shopSign';
import { Icon } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { CountDown } from '@/src/components/countdown';
import { Image } from '@/src/components/image';
import { RACOON_PAYMENT_URL, RACOON_RULE_URL } from '@/src/constants';
import { usePersistFn } from '@/src/hooks';
import { useWorklet } from '@/src/hooks/useWorklet';
import { darkTheme, typography } from '@/src/theme';
import { $USE_FONT, $flexHCenter } from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { RacoonMallSources } from '@/src/utils/mall/resource';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { clearTimeoutWorklet } from '@/src/utils/worklet';
import { Screen, ScreenProps } from '@Components/screen';
import { PartialMessage } from '../../src/types';
import {
  InvoiceType,
  PointsInvoice
} from '@/proto-registry/src/web/raccoon/points/points_pb';

const RacoonRule = memo(() => {
  const enterBatteryRule = useCallback(() => {
    // 有气泡过期 定位锚点过期规则 ，没有则默认

    const screenParams = JSON.stringify({
      screenStyle: {
        backgroundColor: '#16161A'
      },
      backButton: false
    } as Partial<ScreenProps>);
    router.navigate({
      pathname: '/webview',
      params: {
        url: RACOON_RULE_URL,
        screenParams: screenParams,
        darkThemeStatus: 'turnon',
        title: '狸电池规则'
      }
    });
  }, []);

  return (
    <TouchableOpacity
      onPress={enterBatteryRule}
      style={$flexHCenter}
      activeOpacity={0.8}
    >
      <Text
        style={$USE_FONT(
          darkTheme.text.disabled,
          typography.fonts.pingfangSC.normal,
          11,
          undefined,
          400,
          16
        )}
      >
        狸电池规则
      </Text>
      <Icon
        icon="mall_question"
        size={14}
        style={{
          tintColor: '#fff',
          marginLeft: 4
        }}
      />
    </TouchableOpacity>
  );
});

const PopTipRule = memo(() => {
  const [popText, setPopText] = useState('20000活动电池即将过期 ');

  const $popAnimateOpacity = useSharedValue(0);
  const $popAnimateFadeIn = useAnimatedStyle(() => ({
    opacity: $popAnimateOpacity.value
  }));

  useEffect(() => {
    $popAnimateOpacity.value = withTiming(1, {
      duration: 200
    });
  }, []);

  const enterRuleHasAnchor = usePersistFn(() => {
    const screenParams = JSON.stringify({
      screenStyle: {
        backgroundColor: '#16161A'
      },
      backButton: false
    } as Partial<ScreenProps>);
    router.navigate({
      pathname: '/webview',
      params: {
        url: RACOON_RULE_URL + '?anchor=就是有点奇怪',
        screenParams: screenParams,
        darkThemeStatus: 'turnon',
        title: '狸电池规则'
      }
    });
  });

  const safeTop = useSafeAreaInsets().top;

  return (
    <Animated.View
      style={[
        $popAnimateFadeIn,
        {
          position: 'absolute',
          zIndex: 0,
          top: safeTop + 50,
          opacity: 1,
          width: '100%',
          height: 40
        }
      ]}
    >
      <Pressable onPress={enterRuleHasAnchor}>
        <View style={[$popTip]}>
          <Text style={[$popTipText]}>{popText}</Text>
          <CountDown
            targetTime={1742599997890}
            renderWay="ui"
            style={{
              fontSize: 12
            }}
          />
          <Icon
            icon={'mall_arrow_right'}
            size={14}
            color={'rgba(0, 0, 0, 0.3)'}
            style={{ marginLeft: 2 }}
          />
        </View>
      </Pressable>
      <Image
        source={RacoonMallSources['MALL_TRIANGLE']}
        tosSize="size10"
        contentFit="cover"
        style={$triangle}
      />
    </Animated.View>
  );
});

export default function Mall() {
  // TODO: 电池量变更状态
  const whichStatusRaccon = useMemo(() => {
    return RacoonMallSources['HIGH_BATTERY'];
  }, []);

  const [firstRecord, setFirstRecord] =
    useState<PartialMessage<PointsInvoice>>();

  const getLatestFirstRecord = usePersistFn(async () => {
    try {
      const res = await pointsClient.getPointsInvoices({
        invoiceType: InvoiceType.INVOICE_ALL,
        pagination: {
          size: 20
        }
      });
      setFirstRecord(res.invoices[0]);
      return res.invoices[0];
    } catch (error) {
      errorReport(
        'load first battery record error',
        ReportError.REQUEST,
        JSON.stringify(error)
      );
      return undefined;
    }
  });

  useEffect(() => {
    getLatestFirstRecord();
  }, []);

  return (
    <PagePerformance pathname="mall/index">
      <View
        style={{
          flex: 1,
          backgroundColor: darkTheme.background.page
        }}
      >
        <Image
          source={RacoonMallSources['MALL_TOP_LINEAR']}
          tosSize="size1"
          contentFit="fill"
          style={$topLinearBg}
        />
        <Image
          source={RacoonMallSources['MALL_TOP_BG']}
          tosSize="size1"
          contentFit="fill"
          style={$topBg}
        />
        <Screen
          backButton={false}
          headerLeft={() => (
            <TouchableOpacity onPress={safeGoBack}>
              <Icon
                icon="back"
                size={24}
                style={{
                  tintColor: '#fff'
                }}
              />
            </TouchableOpacity>
          )}
          headerStyle={{
            justifyContent: 'space-between'
          }}
          headerRight={() => {
            return <RacoonRule />;
          }}
          style={{ flex: 1 }}
          safeAreaEdges={['top', 'bottom']}
        >
          <ScrollView showsVerticalScrollIndicator={false}>
            <BatteryIntro firstRecord={firstRecord} />
            {/* <View style={{ paddingHorizontal: 16 }}>
              <ShopSign></ShopSign>
            </View> */}
            <Image
              source={whichStatusRaccon}
              tosSize={'size2'}
              contentFit="cover"
              transition={{
                timing: 'ease-in'
              }}
              style={$racoonBattery}
            />
            <PurchaseArea />
          </ScrollView>
        </Screen>
        <PopTipRule />
      </View>
    </PagePerformance>
  );
}
const $racoonBattery: ImageStyle = {
  width: 146,
  height: 124,
  position: 'absolute',
  zIndex: -1,
  right: 0,
  transform: [
    {
      translateY: 32
    }
  ]
};

const $topBg: ImageStyle = {
  position: 'absolute',
  width: 468,
  minHeight: 264,
  height: 'auto',
  transform: [
    {
      translateX: -74
    },
    {
      translateY: -4
    }
  ]
};

const $topLinearBg: ImageStyle = {
  position: 'absolute',
  width: '100%',
  minHeight: 143,
  height: 'auto',
  transform: [
    {
      translateY: 67
    }
  ]
};

const $triangle: ImageStyle = {
  position: 'absolute',
  width: 14,
  height: 6.5,
  right: 52,
  top: -16
};

const $popTip: ViewStyle = {
  position: 'absolute',
  backgroundColor: '#EDEDED',
  minWidth: 178,
  height: 30,
  paddingLeft: 12,
  paddingRight: 8,
  paddingVertical: 6.5,
  right: 16,
  top: -10,
  borderRadius: 20,
  shadowColor: '#000000',
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.25,
  shadowRadius: 12,
  elevation: 12, // Android,
  justifyContent: 'space-between',
  flexDirection: 'row',
  alignItems: 'center'
};

const $popTipText: TextStyle = $USE_FONT(
  '#1F1F1F',
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  isIos ? '500' : 'bold',
  undefined
);
