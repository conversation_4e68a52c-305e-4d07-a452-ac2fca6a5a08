import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useMemo, useState } from 'react';
import { Pressable, ScrollView, Text, TextInput, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { updatePkTheme } from '@/src/api/pk';
import { expandPublish, showPublish } from '@/src/bizComponents/globalPublish';
import { ImagePicker } from '@/src/bizComponents/globalPublish/components/imagePicker';
import { initPKNominateSelector } from '@/src/bizComponents/pkNominateScreen/_utils/initPKNominateSelector';
import PKNominateItem from '@/src/bizComponents/pkNominateScreen/pkNominateItem';
import { usePKExpoParams } from '@/src/bizComponents/pkScreen/_hooks/pkExpo';
import { pkColors } from '@/src/bizComponents/pkScreen/constants';
import { toastPKPublishResult } from '@/src/bizComponents/pkScreen/utilts/publishToast';
import { collectNomineeReportParams } from '@/src/bizComponents/pkScreen/utilts/report';
import { Screen, hideLoading, showLoading } from '@/src/components';
import { Icon, Image } from '@/src/components';
import { showToast } from '@/src/components';
import { AlbumFromType } from '@/src/components/album/const';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { getMediaImageUrl } from '@/src/components/mediaImage';
import Button, { EButtonType } from '@/src/components/v2/button';
import { useKeyboardCompatibleAndroid } from '@/src/hooks';
import { usePerformanceStore } from '@/src/store/performance';
import { usePKStore } from '@/src/store/pk';
import {
  transformMediaToPkImage,
  useCreatePkNominateStore
} from '@/src/store/pk/pkNominate';
import {
  createPKListItem,
  useCreatePkResultStore
} from '@/src/store/pk/pkResult';
import { PublishPhotoSet, usePublishStore } from '@/src/store/publish';
import { usePublishGlobalStore } from '@/src/store/publishGlobal';
import { typography } from '@/src/theme';
import { $USE_FONT } from '@/src/theme/variable';
import { CommonMediaItem, CommonMediaType, MediaPhotosItem } from '@/src/types';
import { StyleSheet } from '@/src/utils';
import { hasDuplicate } from '@/src/utils/opt/array';
import { uniqueArray } from '@/src/utils/opt/uniqueArray';
import {
  addCommonReportParams,
  report,
  reportClick,
  reportExpo
} from '@/src/utils/report';
import { useParams } from '../../src/hooks/useParams';
import {
  PkCensorState,
  PkobjRank
} from '@/proto-registry/src/web/raccoon/pk/pk_pb';
import { useShallow } from 'zustand/react/shallow';

export interface PKNominateSearchParams {
  cardId?: string;
}

export default function PKNominate() {
  // 路由参数
  const { cardId } = useParams();
  const [showAlbum, setShowAlbum] = useState(false);

  const safeBottom = useSafeAreaInsets().bottom;

  const { pkImages, reset: resetNominate } = useCreatePkNominateStore(
    useShallow(state => ({
      reset: state.reset,
      pkImages: state.pkImages
    }))
  );

  const { pkNominateList, pkRankList } = useCreatePkResultStore(
    useShallow(state => ({
      pkNominateList: state.pkNominateList,
      pkRankList: state.pkRankList
    }))
  );

  const { showKeyboard, onInputBlur, onInputFocus } =
    useKeyboardCompatibleAndroid();

  // 状态判定
  const { isNameEmpty, isRoleInsufficient, isNameDuplicate, disabledSave } =
    useMemo(() => {
      const isNameEmpty = pkImages.some(item => !item.name);

      const nameList = [
        ...pkImages,
        ...(pkRankList?.map(item => {
          return { name: item?.objInfo?.name ?? '' };
        }) ?? [])
      ];
      const isNameDuplicate = hasDuplicate(nameList, item => item?.name);
      const isRoleInsufficient = pkRankList.length + pkImages.length < 2;

      const disabledSave = isNameEmpty || isRoleInsufficient || isNameDuplicate;

      return { isNameEmpty, isRoleInsufficient, isNameDuplicate, disabledSave };
    }, [pkImages, pkRankList]);

  const backFn = () => {
    if (!cardId) {
      expandPublish();
    }
    router.back();
    resetNominate();
  };

  const saveFn = async () => {
    try {
      const validPkImages = pkImages.filter(item => item.name);
      useCreatePkNominateStore.setState({ pkImages: validPkImages });

      if (cardId && validPkImages.length) {
        console.log(
          'updatePkTheme===>req',
          JSON.stringify({
            cardId: cardId as string,
            pkObjs: validPkImages
          })
        );
        showLoading();
        const res = await updatePkTheme({
          cardId: cardId as string,
          pkObjs: validPkImages
        });

        reportClick('propose_success', {
          module: 'pk',
          ...collectNomineeReportParams(validPkImages)
        });
        reportExpo('publish_success', {
          module: 'publish',
          sourceid: cardId,
          game_type: '41'
        });

        const dateStr = new Date().toString();

        toastPKPublishResult({
          censorState: res.censorState,
          passName: res.passName,
          pkImages
        });

        if (res.censorState === PkCensorState.PASS) {
          const passList: PkobjRank[] = [];

          validPkImages.forEach(item => {
            if (res.passName.indexOf(item.name) >= 0) {
              passList.push(
                createPKListItem({
                  name: item.name,
                  image: item.image as CommonMediaItem,
                  createTime: dateStr
                })
              );
            }
          });

          useCreatePkResultStore.setState({
            pkNominateList: [...passList, ...pkNominateList],
            pkRankList: [...pkRankList, ...passList]
          });

          backFn();
        }
      } else {
        usePublishGlobalStore.setState({ pkImages: validPkImages });
        backFn();
      }
    } catch (e) {
      console.log('saveFn===>err', e);
    }
  };

  const $submitFn = useDebounceFn(async () => {
    if (isNameEmpty) {
      showToast('请输入角色昵称');
      return;
    }
    if (isRoleInsufficient) {
      showToast('请至少提名两个角色');
      return;
    }
    if (isNameDuplicate) {
      showToast('角色昵称重复');
      return;
    }
    try {
      // onBack();
      await saveFn();
    } catch (e) {
      console.log();
    } finally {
      hideLoading();
    }
  });

  const handlePickImage = useMemoizedFn(
    (photos: Partial<MediaPhotosItem>[]) => {
      const appendPhotos = photos
        .map(p => transformMediaToPkImage(p))
        .filter(p => !!p);

      if (appendPhotos.length) {
        const newPkImages = uniqueArray(
          [...pkImages, ...appendPhotos],
          (a, b) => a.image?.mediaId === b.image?.mediaId
        );
        useCreatePkNominateStore.getState().setPkImages(newPkImages);
        setShowAlbum(false);
      }
    }
  );

  const handleDelete = useMemoizedFn(({ index }: { index: number }) => {
    const newList = [...pkImages];
    newList.splice(index, 1);
    useCreatePkNominateStore.setState({ pkImages: newList });
  });

  const handleTextChange = useMemoizedFn(
    ({ text, index }: { text: string; index: number }) => {
      const newList = [...pkImages];
      newList[index].name = text;
      useCreatePkNominateStore.setState({ pkImages: newList });
    }
  );

  usePKExpoParams({ cardId: cardId as string });

  return (
    <PagePerformance pathname="pk-nominate/index">
      <Screen
        theme="dark"
        title="提名角色"
        preset="auto"
        safeAreaEdges={['top']}
        style={[styles.container]}
        ScrollViewProps={{
          scrollEnabled: false
        }}
        backgroundView={
          <View
            style={[
              { backgroundColor: 'rgba(22, 22, 26, 1)' },
              StyleSheet.absoluteFill
            ]}
          />
        }
        onBack={backFn}
      >
        <View style={{ flex: 1 }}>
          <Text style={styles.tipText}>
            {`支持批量添加多个角色形象${isRoleInsufficient ? '（至少2个）' : ''}，角色昵称不允许重复且不可二次修改`}
          </Text>
          <ScrollView style={{ flex: 1 }}>
            <View style={{ paddingHorizontal: 18 }}>
              {pkImages.map((item, index) => (
                <PKNominateItem
                  key={index}
                  item={item}
                  index={index}
                  isInputActive={showKeyboard}
                  onDelete={handleDelete}
                  onInputBlur={onInputBlur}
                  onInputFocus={onInputFocus}
                  onChangeText={handleTextChange}
                />
              ))}

              <View style={{ marginBottom: 100 }}>
                <Pressable
                  style={[styles.addButton]}
                  onPress={() => {
                    initPKNominateSelector({ pkImages });
                    setShowAlbum(true);
                    usePerformanceStore
                      .getState()
                      .recordStart('make_photo_photo_set_render', {
                        performance_type: 'render',
                        performance_key: AlbumFromType.PK
                      });
                  }}
                >
                  <Icon
                    icon="icon_add"
                    size={16}
                    color="rgba(255, 255, 255, 1)"
                  />
                  <Text style={styles.addButtonText}>继续提名</Text>
                </Pressable>
              </View>
            </View>
          </ScrollView>

          <View style={[styles.btnBox, { paddingBottom: safeBottom }]}>
            <Button
              style={[
                disabledSave && {
                  opacity: 0.6
                }
              ]}
              type={EButtonType.LINEAR}
              $customBtnTextStyle={{
                color: StyleSheet.currentColors.white,
                fontSize: 14,
                fontWeight: '600'
              }}
              onPress={$submitFn.run}
            >
              保存
            </Button>
          </View>

          <ImagePicker
            show={showAlbum}
            setPhotos={handlePickImage}
            callWhere={AlbumFromType.PK}
            containerStyle={{ height: 'auto' }}
            onClose={() => {
              setShowAlbum(false);
            }}
            multipleCount={100}
          />
        </View>
      </Screen>
    </PagePerformance>
  );
}
const styles = StyleSheet.create({
  tipText: {
    ...$USE_FONT(
      'rgba(255, 255, 255, 0.2)',
      typography.fonts.pingfangSC.normal,
      12,
      'normal',
      '400',
      16
    ),
    paddingHorizontal: 32,
    marginTop: 20,
    marginBottom: 12
  },
  container: {},
  content: {
    padding: 16,
    gap: 4,
    flex: 1
  },
  characterItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.04)',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 68,
    marginBottom: 4
  },
  avatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.04)',
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 10
  },
  input: {
    flex: 1,
    height: '100%',
    ...$USE_FONT(
      'rgba(255, 255, 255, 0.9)',
      typography.fonts.pingfangSC.normal,
      14,
      'normal',
      '400',
      19.6
    )
  },
  textRightTip: $USE_FONT(
    'rgba(255, 255, 255, 0.4)',
    typography.fonts.pingfangSC.normal,
    12,
    'normal',
    '400',
    16
  ),
  btnBox: {
    paddingHorizontal: 16,
    paddingTop: 16,
    borderTopWidth: 0.5,
    borderColor: 'rgba(255, 255, 255, 0.04)'
  },
  addButton: {
    borderWidth: 1.5,
    borderStyle: 'dashed',
    borderColor: 'rgba(255,255,255,0.16)',
    borderRadius: 12,
    height: 66,
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    justifyContent: 'center',
    marginTop: 12
  },
  addButtonText: $USE_FONT(
    'rgba(255, 255, 255, 1)',
    typography.fonts.pingfangSC.normal,
    14,
    'normal',
    '600',
    19.6
  ),
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600'
  }
});
