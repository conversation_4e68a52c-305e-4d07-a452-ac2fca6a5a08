import { ScrollView, TextStyle, TouchableOpacity, View } from 'react-native';
import { SvgUri } from 'react-native-svg';
import { LevelProgress } from '@/src/bizComponents/achievement/level-progress';
import { SCREEN_WIDTH } from '@/src/bizComponents/nestedScrollView';
import TarotEntry from '@/src/bizComponents/tarot/tarotEntry';
import { BannerPart } from '@/src/bizComponents/welfare/bannerPart';
import { SIGN_PART_RESOURCES } from '@/src/bizComponents/welfare/resource';
import { SignPart } from '@/src/bizComponents/welfare/signPart';
import { TaskPart } from '@/src/bizComponents/welfare/taskPart';
import { Icon } from '@/src/components';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { darkTheme, typography } from '@/src/theme';
import { Theme } from '@/src/theme/colors/type';
import { $USE_FONT, $flexHCenter } from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { reportClick } from '@/src/utils/report';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { Image } from '@Components/image';
import { Screen } from '@Components/screen';
import { Text } from '@Components/text';
import { AvatarSwitch } from '../../src/bizComponents/achievement/avatar-switch';
import { Decorations } from '../../src/bizComponents/achievement/decoration';
import { DRESS_UP_SOURCE } from '../../src/bizComponents/achievement/resource';

export default function DressUpCenter() {
  return (
    <PagePerformance pathname="dressup-center/index">
      <View
        style={{
          flex: 1,
          backgroundColor: darkTheme.background.page
        }}
      >
        <Image
          source={DRESS_UP_SOURCE['TOP_BG']}
          tosSize="size1"
          contentFit="cover"
          style={{
            width: SCREEN_WIDTH,
            position: 'absolute',
            minHeight: 187
          }}
        />
        <Screen
          backButton={false}
          theme={Theme.DARK}
          headerLeft={() => (
            <TouchableOpacity
              onPress={() => {
                reportClick('back_button');
                safeGoBack();
              }}
            >
              <Icon
                icon="back"
                size={24}
                style={{
                  tintColor: '#fff'
                }}
              />
            </TouchableOpacity>
          )}
          headerTitle={() => <Text style={$pageTitle}>装扮中心</Text>}
          headerStyle={{
            justifyContent: 'space-between'
          }}
          headerRight={() => (
            <TouchableOpacity
              style={$flexHCenter}
              activeOpacity={0.75}
              onPress={() => {
                // TODO: show 奖励弹窗
              }}
            >
              <Text
                style={[
                  $ruleText,
                  {
                    opacity: 0.4
                  }
                ]}
              >
                装饰说明
              </Text>
              <SvgUri
                width={14}
                height={14}
                uri={SIGN_PART_RESOURCES['SVG_RULE_QUESTION']}
                style={{ marginLeft: 4 }}
              />
            </TouchableOpacity>
          )}
          style={{ flex: 1 }}
          safeAreaEdges={['top']}
        >
          <AvatarSwitch />
          <LevelProgress />
          <View
            style={{
              backgroundColor: 'red',
              opacity: 0,
              height: 40,
              marginHorizontal: 20,
              marginTop: -20
            }}
          />
          <Decorations />
        </Screen>
      </View>
    </PagePerformance>
  );
}

const $pageTitle: TextStyle = $USE_FONT(
  darkTheme.text.primary,
  typography.fonts.pingfangSC.normal,
  16,
  'normal',
  isIos ? '600' : 'bold',
  undefined
);

const $ruleText: TextStyle = $USE_FONT(
  darkTheme.text.primary,
  typography.fonts.pingfangSC.normal,
  11,
  'normal',
  '400',
  16
);
