import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  GestureResponderEvent,
  ImageStyle,
  KeyboardAvoidingView,
  NativeSyntheticEvent,
  ScrollView,
  TextInputFocusEventData,
  TextInputSubmitEditingEventData,
  View
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ESearchResourceType, getQuerySourceByType } from '@/src/api/search';
import SearchBar from '@/src/bizComponents/search/search-bar';
import { Image, Screen } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useKeyboard } from '@/src/hooks';
import { useSearchStore } from '@/src/store/search';
import {
  $CREF_SEARCH_COLORS,
  $SEARCH_COLORS,
  $flex
} from '@/src/theme/variable';
import { dp2px, isIos } from '@/src/utils';
import { decodeSP, encodeSP } from '@/src/utils/placeholder';
import { reportClick, reportDiy, reportExpo } from '@/src/utils/report';
import { useRoute } from '@react-navigation/native';

const LANDING_SOURCE = require('@Assets/image/cref-search/landing.png');

export default function CrefSearch() {
  const [searchText, setSearchText] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [isFirstPress, setIsFirstPress] = useState(true);

  const route = useRoute();

  useEffect(() => {
    setSearchText(decodeSP((route.params as any)?.keywords));
    setSearchValue(decodeSP((route.params as any)?.result));
  }, [route.params]);

  const onSearch = (
    e:
      | GestureResponderEvent
      | NativeSyntheticEvent<TextInputSubmitEditingEventData>,
    keywords: string
  ) => {
    setIsFirstPress(true);
    const v = keywords;
    clearWord();
    reportClick('search_confirm_button', {
      module: 'role_search',
      keywords
    });

    router.replace({
      pathname: '/cref-search/result',
      params: {
        keywords: encodeSP(v)
      }
    });
  };

  const [beFocus, setBeFocus] = useState(true);

  const onFocus = (
    e: NativeSyntheticEvent<TextInputFocusEventData>,
    keywords: string
  ) => {
    reportClick('search_button', { module: 'role_search' });
    setBeFocus(true);
  };

  const clearWord = () => {
    setSearchValue('');
  };

  const onChange = (v: string) => {
    setSearchValue(v);
    if (isFirstPress) {
      setIsFirstPress(false);
    }
  };

  const tabOpacity = useSharedValue(0.9);

  const $tabOpacity = useAnimatedStyle(() => ({
    opacity: tabOpacity.value
  }));

  return (
    <PagePerformance pathname="cref-search/index">
      <SafeAreaView
        style={[$flex, { position: 'relative', backgroundColor: '#16161A' }]}
      >
        <SearchBar
          value={searchValue}
          onSearch={onSearch}
          onChange={onChange}
          onFocus={onFocus}
          placeHolderContent={'来搜索更多角色吧～'}
          autoFocus={true}
          clearWord={clearWord}
          ellipseWidth={dp2px(210)}
          $customStyle={{
            backgroundColor: '#FFFFFF1A'
          }}
          backIcon="cref_back"
          clearIcon="cref_search_clear"
          searchIcon="cref_search"
          searchColors={{
            selectionColor: beFocus
              ? $CREF_SEARCH_COLORS.white
              : $CREF_SEARCH_COLORS.white_40,
            placeholderTextColor: $CREF_SEARCH_COLORS.white_40,
            cursorColor: $CREF_SEARCH_COLORS.white,
            searchTextColor: $CREF_SEARCH_COLORS.white
          }}
          $buttonStyle={{
            backgroundColor: '#FF6A3B',
            borderRadius: 16,
            opacity: searchValue && searchValue.length > 0 ? 1 : 0.6
          }}
          onBlur={() => {
            setBeFocus(false);
          }}
          hiddenRightPlaceContent={true}
        />
      </SafeAreaView>
    </PagePerformance>
  );
}

const $bg: ImageStyle = {
  position: 'absolute',
  zIndex: -1,
  flex: 1,
  bottom: 0,
  top: 0,
  left: 0,
  right: 0
};
