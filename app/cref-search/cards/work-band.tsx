import { useState } from 'react';
import { ImageStyle, TextStyle, View, ViewStyle } from 'react-native';
import { Image } from '@/src/components';
import Button, { EButtonType } from '@/src/components/v2/button';
import { typography } from '@/src/theme';
import { $CREF_SEARCH_COLORS, $USE_FONT } from '@/src/theme/variable';
import { Text } from '@Components/text';

export default function WorkBand() {
  const workName = '守护最好的樱';
  const workUser = '极品大蟑螂';
  const workAddNum = '1024';

  const [notAdd, setNotAdd] = useState(true);

  return (
    <View style={$workBand}>
      <View style={$sticky}>
        <View style={$stickyPin}>
          <Text
            style={[
              $workName,
              {
                marginBottom: 8
              }
            ]}
          >
            {workName}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Image
              source="https://media-test.tos-cn-shanghai.volces.com/aigc/IMAGE/20240702/72d1f12eef819a776be3031b368e3be9.jpg"
              contentFit="fill"
              style={$workAvatar}
              tosSize="size6"
            />
            <Text
              style={[
                $workUser,
                {
                  marginLeft: 4
                }
              ]}
            >
              {workUser}
            </Text>
          </View>
        </View>
        <View style={$stickyPin}>
          {notAdd ? (
            <Button
              type={EButtonType.LINEAR}
              linearColors={['#FF6A3B', '#FF8A65']}
              style={{
                paddingHorizontal: 16,
                paddingVertical: 4
              }}
              $customBtnTextStyle={{
                fontSize: 12,
                fontFamily: typography.fonts.pingfangSC.normal,
                fontWeight: '600'
              }}
              onPress={() => setNotAdd(false)}
            >
              <Text
                style={[
                  $workAddPlusText,
                  {
                    marginBottom: 2
                  }
                ]}
              >
                + 添加
              </Text>
            </Button>
          ) : (
            <Button
              type={EButtonType.NORMAL}
              $customBtnStyle={{
                paddingHorizontal: 16,
                paddingVertical: 4,
                backgroundColor: $CREF_SEARCH_COLORS.wihte_08,
                borderColor: $CREF_SEARCH_COLORS.wihte_12,
                borderWidth: 0.5,
                borderRadius: 36,
                minHeight: 26
              }}
              onPress={() => setNotAdd(true)}
            >
              <Text
                style={{
                  color: $CREF_SEARCH_COLORS.white_36,
                  fontSize: 12,
                  fontFamily: typography.fonts.pingfangSC.normal,
                  fontWeight: '500'
                }}
              >
                已添加
              </Text>
            </Button>
          )}
          <Text
            style={[
              $workAddNum,
              {
                marginTop: 6
              }
            ]}
          >
            {workAddNum}人已添加
          </Text>
        </View>
      </View>
      <View style={$series} />
    </View>
  );
}

const $workBand: ViewStyle = {
  width: '100%',
  height: 235,
  backgroundColor: $CREF_SEARCH_COLORS.workBand,
  borderRadius: 12
};

const $sticky: ViewStyle = {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingTop: 26,
  paddingHorizontal: 18
};

const $stickyPin: ViewStyle = {
  justifyContent: 'flex-start'
};

const $workName: TextStyle = $USE_FONT(
  $CREF_SEARCH_COLORS.white,
  typography.fonts.pingfangSC.normal,
  16,
  'normal',
  '500',
  undefined
);

const $workUser: TextStyle = $USE_FONT(
  $CREF_SEARCH_COLORS.white_57,
  typography.fonts.pingfangSC.normal,
  13,
  'normal',
  '400',
  undefined
);

const $workAvatar: ImageStyle = {
  width: 16,
  height: 16,
  borderRadius: 8
};

const $workAddPlusText: TextStyle = $USE_FONT(
  $CREF_SEARCH_COLORS.white,
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  '600',
  undefined
);

const $workAddNum: TextStyle = $USE_FONT(
  $CREF_SEARCH_COLORS.white_60,
  typography.fonts.pingfangSC.normal,
  10,
  'normal',
  '400',
  undefined
);

const $series: ViewStyle = {
  backgroundColor: $CREF_SEARCH_COLORS.workCard,
  flex: 1,
  marginTop: 12
};
