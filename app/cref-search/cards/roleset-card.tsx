import { router } from 'expo-router';
import { useState } from 'react';
import { Pressable, Text, View, ViewStyle } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { addRoleSetToAlbum, deleteUgcRoleSet } from '@/src/api/role';
import { searchClient } from '@/src/api/search';
import { Icon, hideToast, showToast } from '@/src/components';
import { Avatar } from '@/src/components/avatar';
import Button, { EButtonType } from '@/src/components/v2/button';
import { useAuthState, useScreenSize } from '@/src/hooks';
import { useRoleHomeStore } from '@/src/store/roleHome';
import { darkTheme, typography } from '@/src/theme';
import {
  $CREF_SEARCH_COLORS,
  $USE_FONT,
  $flex,
  $flexHBetween,
  $flexHCenter
} from '@/src/theme/variable';
import { StyleSheet, isIos } from '@/src/utils';
import { formatRole } from '@/src/utils/formatRole';
import { encodeSP } from '@/src/utils/placeholder';
import { RoleSource, reportClick, reportPage } from '@/src/utils/report';
import { Image } from '@Components/image';
import {
  RoleInfo,
  SearchRoleSetCard
} from '@/proto-registry/src/web/raccoon/common/role_pb';
import RoleCard from './role-card';

interface IRoleSetCardProps {
  roleSet: SearchRoleSetCard;
  keywords: string;
}

export default function RoleSetCard({ roleSet, keywords }: IRoleSetCardProps) {
  const maxRoleCardWidth = (useScreenSize('screen').width - 20 - 30) / 3;

  const { loginIntercept } = useAuthState();

  const [isSave, setIsSave] = useState(roleSet?.roleSet?.baseInfo?.isSave);

  const addRoleSet = async (id?: number) => {
    hideToast();

    try {
      if (id) {
        const res = await addRoleSetToAlbum({
          brandId: id
        });
        if (res) {
          showToast('已添加到我的角色集');
          setIsSave(true);
        }
      }
    } catch (e) {
      showToast('发生了一些异常，请稍候重试');
      console.log(e, 'save roleset error');
    }
  };

  const enterRole = (role: RoleInfo) => {
    loginIntercept(() => {
      reportClick('character_card_button', {
        module: 'role_search',
        role_id: role.id,
        keywords
      });
      const roleInfo = formatRole(role);
      useRoleHomeStore.getState().getRoleInfos(role?.id);
      useRoleHomeStore.getState().updateRoleInfo(role?.id, roleInfo);
      router.navigate({
        pathname: '/role',
        params: {
          roleId: role?.id,
          source: RoleSource.SEARCH
        }
      });
    });
  };

  const deleteRoleSet = async (id?: number) => {
    hideToast();

    try {
      const res = await deleteUgcRoleSet({
        brandId: id
      });
      if (res) {
        showToast('已从我的角色集移除');
        setIsSave(false);
      }
    } catch (error) {
      showToast('发生了一些异常，请稍候重试');
      console.log(error, 'delete roleset error');
    }
  };

  const enterUser = (uid: string) => {
    router.navigate({
      pathname: '/user/[id]',
      params: {
        id: String(uid)
      }
    });
  };

  return (
    <View style={$roleSetContainer}>
      <View
        style={[
          $flexHBetween,
          $flexHCenter,
          {
            width: '100%',
            paddingTop: 16,
            paddingHorizontal: 12
          }
        ]}
      >
        <View style={StyleSheet.rowStyle}>
          <Avatar size={42} profile={roleSet?.roleSet?.creator} />
          <View style={{ marginLeft: 7 }}>
            <Text
              style={$USE_FONT(
                darkTheme.text.solid,
                typography.fonts.pingfangSC.normal,
                16,
                undefined,
                isIos ? '500' : 'bold',
                undefined
              )}
            >
              {roleSet?.roleSet?.baseInfo?.displayName}
            </Text>
            <Text
              style={$USE_FONT(
                darkTheme.text.tertiary,
                typography.fonts.pingfangSC.normal,
                13,
                undefined,
                '400',
                undefined
              )}
            >
              创建者：{roleSet?.roleSet?.creator?.name}
            </Text>
          </View>
        </View>
        <View
          style={{
            justifyContent: 'space-between'
          }}
        >
          <Button
            type={EButtonType.NORMAL}
            style={{
              backgroundColor: '#ff6a3b',
              height: 28,
              borderRadius: 47,
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 2
            }}
            onPress={() => {
              reportPage('add_charactercollection_button', {
                status: isSave ? '0' : '1'
              });

              loginIntercept(() => {
                if (isSave) {
                  deleteRoleSet(roleSet?.roleSet?.baseInfo?.id);
                } else {
                  addRoleSet(roleSet?.roleSet?.baseInfo?.id);
                }
              });
            }}
          >
            <View style={$flexHCenter}>
              {!isSave ? (
                <Icon icon={'cref_roleset_plus'} size={10}></Icon>
              ) : null}
              <Text
                style={$USE_FONT(
                  isSave ? darkTheme.text.tertiary : darkTheme.text.solid,
                  typography.fonts.pingfangSC.normal,
                  12,
                  undefined,
                  isIos ? '600' : 'bold',
                  undefined
                )}
              >
                {isSave ? '已添加' : '添加'}
              </Text>
            </View>
          </Button>
          <Text
            style={[
              $USE_FONT(
                darkTheme.text.secondary,
                typography.fonts.pingfangSC.normal,
                10,
                undefined,
                400,
                22
              ),
              { textAlign: 'center' }
            ]}
          >
            {(roleSet?.roleSet?.baseInfo?.saveCnt || 0) + '人已添加'}
          </Text>
        </View>
      </View>
      <ScrollView
        horizontal
        style={{
          padding: 12,
          paddingRight: 10
        }}
        contentContainerStyle={{
          gap: 10,
          minWidth: '100%',
          paddingRight: 26
        }}
        showsHorizontalScrollIndicator={false}
      >
        {roleSet?.roleInfos.map((role: RoleInfo, roleIndex) => {
          return (
            <View key={roleIndex}>
              <RoleCard
                role={role}
                cardStyle={{
                  width: 96,
                  height: 126,
                  borderRadius: 12
                }}
                enterRole={() => enterRole(role)}
                hideAddIcon
                hideTakeButton
              />
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
}

const $roleSetContainer: ViewStyle = {
  marginHorizontal: 12,
  backgroundColor: '#2D2D33',
  borderRadius: 16,
  paddingRight: 0,
  overflow: 'hidden'
};
