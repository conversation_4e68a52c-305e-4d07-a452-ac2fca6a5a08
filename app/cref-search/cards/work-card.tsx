import { ImageStyle, Text, TextStyle, View, ViewStyle } from 'react-native';
import { Image } from '@/src/components';
import { typography } from '@/src/theme';
import {
  $CREF_SEARCH_COLORS,
  $SEARCH_COLORS,
  $USE_FONT
} from '@/src/theme/variable';

const TAKE_SAME_ICON = require('@Assets/image/cref-search/take-same-icon.png');
const MASK = require('@Assets/image/cref-search/mask.png');

export default function WorkCard() {
  const takesameNum = 313;
  const takesameTitle = '伽利略';
  return (
    <View style={$workCard}>
      <Image
        contentFit="fill"
        style={$workCardUrl}
        source={
          'https://mediafile.lipuhome.com/aigc/IMAGE/20241009/394b2de6d5b87113e0282684d190ee67.png'
        }
        tosSize="size1"
      />
      <Image source={MASK} contentFit="fill" style={$mask} />
      <View style={$head}>
        <View style={$takeSame}>
          <View style={$takeSameBg} />
          <Image
            source={TAKE_SAME_ICON}
            contentFit="fill"
            style={$takeSameIcon}
          />
          <Text
            style={[
              $takeSameText,
              {
                marginLeft: 5
              }
            ]}
          >
            {takesameNum}
          </Text>
        </View>
      </View>
      <View style={$tail}>
        <Text
          style={[
            $tailTitle,
            {
              marginBottom: 12
            }
          ]}
        >
          {takesameTitle}
        </Text>
      </View>
    </View>
  );
}

const $workCard: ViewStyle = {
  width: 113,
  height: 151,
  borderRadius: 10,
  backgroundColor: '#fff',
  overflow: 'hidden'
};

const $head: ViewStyle = {
  width: '100%',
  height: 18,
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: 16,
  paddingHorizontal: 8,
  flexDirection: 'row'
};

const $takeSame: ViewStyle = {
  width: 46,
  height: 18,
  position: 'relative',
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: $CREF_SEARCH_COLORS.black_40,
  borderRadius: 12
};

const $takeSameIcon: ImageStyle = {
  width: 16,
  height: 16,
  borderRadius: 8
};

const $mask: ImageStyle = {
  position: 'absolute',
  bottom: 0,
  width: '100%',
  height: 71,
  zIndex: 1
};

const $takeSamePlus: ImageStyle = {
  width: 16,
  height: 16,
  borderRadius: 8
};

const $takeSameBg: ViewStyle = {
  width: '100%',
  height: '100%',
  position: 'absolute'
};

const $workCardUrl: ImageStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  zIndex: -1
};

const $blur: ViewStyle = {
  height: 24,
  marginBottom: 12,
  paddingVertical: 0,
  paddingHorizontal: 12,
  borderRadius: 12,
  borderColor: '#FFFFFF66',
  borderWidth: 0.83
};

const $takeSameText: TextStyle = $USE_FONT(
  $CREF_SEARCH_COLORS.white_90,
  typography.fonts.pingfangSC.normal,
  10,
  'normal',
  '500',
  undefined
);

const $tail: ViewStyle = {
  width: '100%',
  flex: 1,
  justifyContent: 'flex-end',
  alignItems: 'center',
  position: 'relative',
  zIndex: 10
};

const $tailTitle: TextStyle = $USE_FONT(
  $CREF_SEARCH_COLORS.white,
  typography.fonts.pingfangSC.normal,
  14,
  'normal',
  '500',
  undefined
);
