import { useEffect, useMemo, useState } from 'react';
import {
  ImageStyle,
  Pressable,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import { ContentBottomPendant } from '@/src/bizComponents/feedcard/ugcCard/PaiPendant';
import { Icon, IconTypes, Image, Text, showToast } from '@/src/components';
import { Reminder } from '@/src/components/reminder';
import { useAuthState, usePersistFn } from '@/src/hooks';
import { useRoleStore } from '@/src/store/role';
import { darkTheme, typography } from '@/src/theme';
import { StyleSheet, dp2px } from '@/src/utils';
import { clickEffect } from '@/src/utils/clickeffect';
import { requestWithTimeout } from '@/src/utils/requestWithTimeout';
import { RoleInfo } from '@/proto-registry/src/web/raccoon/common/role_pb';
import { CensoredState } from '@/proto-registry/src/web/raccoon/common/state_pb';
import { useShallow } from 'zustand/react/shallow';

const SELECT_COVER = require('@Assets/image/role/role-selected.png');
const FIRE = require('@Assets/icon/goods-fire.png');
const CARD_COVER = require('@Assets/image/role/role-card-cover.png');
export enum RoleStylePresets {
  Large,
  Small
}
const getCensorText = (state: CensoredState) => {
  switch (state) {
    case CensoredState.CENSORED_BLOCKED:
      return '待修改';
    case CensoredState.CENSORED_EXCEPTIONED:
    case CensoredState.CENSORED_PROCESSING:
      return '审核中';
    default:
      return '';
  }
};
const HOT_CONFIGS: Record<
  RoleStylePresets,
  {
    iconSize: number;
    fontSize: number;
    gap: number;
    style: ViewStyle;
  }
> = {
  [RoleStylePresets.Large]: {
    iconSize: 19,
    fontSize: 14,
    gap: 3,
    style: { height: 22 }
  },
  [RoleStylePresets.Small]: {
    iconSize: 16,
    fontSize: 12,
    gap: 2,
    style: { height: 18 }
  }
};
export const TEXT_STYLES_WITH_PRESETS: Record<RoleStylePresets, TextStyle> = {
  [RoleStylePresets.Large]: {
    bottom: dp2px(8),
    lineHeight: dp2px(32),
    height: dp2px(32),
    fontSize: 15,
    width: dp2px(110)
  },
  [RoleStylePresets.Small]: {
    lineHeight: dp2px(26),
    bottom: dp2px(7),
    height: dp2px(26)
  }
};

interface IRoleCardProps {
  width: number;
  role: RoleInfo;
  desc?: string;
  showAddToRoleSet?: boolean;
  isSelected?: boolean;
  onLongPress?: (role: RoleInfo) => void;
  onPress?: (role: RoleInfo) => void;
  preset?: RoleStylePresets;
  addRoleOnSuccess?: (role: RoleInfo) => void;
  // toCreateByRole?: (role: RoleInfo) => void;
  enterRole?: (role: RoleInfo) => void;
  cardStyle?: ViewStyle;
  selectedText?: string;
  hideAddIcon?: boolean;
  selectedIcon?: IconTypes;
  // hideTakeButton?: boolean;
}

export default function RoleCardV2({
  role,
  preset = RoleStylePresets.Small,
  width,
  desc,
  showAddToRoleSet = false,
  selectedText,
  isSelected,
  onPress,
  onLongPress,
  addRoleOnSuccess,
  enterRole,
  cardStyle = {},
  selectedIcon = 'makephoto_checked'
}: IRoleCardProps) {
  const { loginIntercept } = useAuthState();

  const { addRoleToAlbum } = useRoleStore(
    useShallow(state => ({
      addRoleToAlbum: state.addRoleToAlbum
    }))
  );

  const rolePlusClick = async () => {
    clickEffect();

    loginIntercept(async () => {
      const mineAlbumId = 200;
      if (
        await requestWithTimeout(
          addRoleToAlbum({
            roleId: role?.id,
            brandId: mineAlbumId
          })
        ).catch(e => {
          console.log('addRoleToAlbum===>err', e);
        })
      ) {
        setIsRoleSave(true);
        addRoleOnSuccess?.(role);
      } else {
        showToast('角色添加失败!');
      }
    });
  };

  const [isRoleSave, setIsRoleSave] = useState(true);

  useEffect(() => {
    setIsRoleSave(role.isSave);
  }, [role]);
  const onPressRole = usePersistFn(() => {
    if (onPress) {
      onPress(role);
    } else {
      enterRole?.(role);
    }
  });
  const censorText = useMemo(() => {
    return getCensorText(role.censored);
  }, [role.censored]);
  return (
    <View>
      {role.isNew && <Reminder text={'New'} top={-4} right={-10} />}
      <Pressable
        style={[$roleCardPress, cardStyle, { width, height: width * 2.35 }]}
        onLongPress={() => {
          onLongPress?.(role);
        }}
        delayLongPress={200}
        onPress={onPressRole}
      >
        <Image
          contentFit={role.material ? 'cover' : 'contain'}
          style={[
            $roleCardUrl,
            {
              borderTopLeftRadius: Math.floor((13 / 90) * width),
              borderBottomRightRadius: Math.floor((13 / 90) * width)
            }
          ]}
          source={role.material}
          tosSize="size2"
          contentPosition={'center'}
        />
        <Text
          numberOfLines={1}
          style={[styles.text, TEXT_STYLES_WITH_PRESETS[preset]]}
        >
          {role?.displayName || role?.name || ''}
        </Text>
        <Image
          contentFit={'fill'}
          style={[$roleCardUrl]}
          source={CARD_COVER}
          contentPosition={'center'}
        />
        {isSelected && (
          <View style={[$roleCardUrl]}>
            <Image
              contentFit={role.material ? 'cover' : 'contain'}
              style={$roleCardUrl}
              source={SELECT_COVER}
              contentPosition={'center'}
            />
            {selectedText ? (
              <Text style={styles.selectedText}>{selectedText}</Text>
            ) : (
              <Icon
                containerStyle={{
                  position: 'absolute',
                  top: 4,
                  right: 4
                }}
                icon={selectedIcon}
                size={8}
              />
            )}
          </View>
        )}
        <View style={$head}>
          <ContentBottomPendant
            source={FIRE}
            {...HOT_CONFIGS[preset]}
            copyCount={role?.heat ? Number(role.heat) : 0}
          />
          {!isSelected &&
            showAddToRoleSet &&
            (isRoleSave ? (
              <View style={$roleSaveIconWrapper}>
                <Icon
                  icon="cref_search_confirm2"
                  size={preset === RoleStylePresets.Large ? 24 : 20}
                />
              </View>
            ) : (
              <Pressable onPress={rolePlusClick} style={$roleSaveIconWrapper}>
                <Icon
                  icon={'cref_search_plus2'}
                  size={preset === RoleStylePresets.Large ? 24 : 20}
                />
              </Pressable>
            ))}
        </View>
        {censorText && (
          <View style={[$roleCardUrl, styles.censor]}>
            <Text style={styles.censorText}>{censorText}</Text>
          </View>
        )}
      </Pressable>
      {desc && (
        <Text numberOfLines={1} style={styles.by}>
          {desc}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  text: {
    width: 85,
    height: 20,
    lineHeight: 20,
    bottom: 9,
    position: 'absolute',
    textShadowColor: '#453E54',
    textAlign: 'center',
    textShadowRadius: 1,
    textShadowOffset: {
      width: 1,
      height: 1
    },
    transform: [
      {
        rotateZ: '6deg'
      }
    ],

    color: darkTheme.text.primary,
    fontSize: 14,
    zIndex: 10,
    fontFamily: typography.fonts.feed
  },
  selectedText: {
    fontSize: 12,
    position: 'absolute',
    top: 2,
    right: 5,
    fontWeight: '700',
    fontFamily: typography.fonts.feed,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center'
  },
  by: {
    fontSize: 12,
    marginTop: 10,
    width: 91,
    height: 17,
    fontWeight: '500',
    fontFamily: typography.fonts.pingfangSC.normal,
    color: 'rgba(255, 255, 255, 0.5)',
    textAlign: 'center'
  },
  censor: {
    backgroundColor: '#00000080',
    alignItems: 'center',
    justifyContent: 'center'
  },
  censorText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
    opacity: 0.6
  }
});
const $roleCardPress: ViewStyle = { width: '100%', height: '100%' };

const $head: ViewStyle = {
  alignItems: 'center',
  margin: 6,
  justifyContent: 'space-between',
  flexDirection: 'row',
  minWidth: 80,
  position: 'relative'
};

const $roleSaveIconWrapper: ViewStyle = {
  alignSelf: 'center'
};

const $roleCardUrl: ImageStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%'
};
