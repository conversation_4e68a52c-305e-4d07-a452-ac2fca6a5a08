import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  ImageStyle,
  Pressable,
  Text,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { ContentBottomPendant } from '@/src/bizComponents/feedcard/ugcCard/PaiPendant';
import { Icon, Image, showToast } from '@/src/components';
import { CustomBlurView } from '@/src/components/image/CustomBlurView';
import { LOGIN_SCENE } from '@/src/constants';
import { useAuthState } from '@/src/hooks';
import { useCrefSheetStore } from '@/src/store/cref-sheet';
import { useMakePhotoStoreV2 } from '@/src/store/makePhotoV2';
import { useRoleStore } from '@/src/store/role';
import { typography } from '@/src/theme';
import { $CREF_SEARCH_COLORS, $USE_FONT } from '@/src/theme/variable';
import { isIos } from '@/src/utils';
import { clickEffect } from '@/src/utils/clickeffect';
import { formatRole } from '@/src/utils/formatRole';
import { formatLargeNumber } from '@/src/utils/opt/transNum';
import { requestWithTimeout } from '@/src/utils/requestWithTimeout';
import { RoleInfo } from '@/proto-registry/src/web/raccoon/common/role_pb';
import { useShallow } from 'zustand/react/shallow';

const TAKE_SAME_ICON = require('@Assets/image/cref-search/take-same-icon.png');
const MASK = require('@Assets/image/cref-search/mask.png');
const EMPTY_ROLE = require('@Assets/image/cref-search/empty-role.png');

interface IRoleCardProps {
  role: RoleInfo;
  addRoleOnSuccess?: (role: RoleInfo) => void;
  toCreateByRole?: (role: RoleInfo) => void;
  enterRole?: (role: RoleInfo) => void;
  cardStyle?: ViewStyle;
  hideAddIcon?: boolean;
  hideTakeButton?: boolean;
}
const FIRE = require('@Assets/icon/goods-fire.png');
export default function RoleCard({
  role,
  addRoleOnSuccess,
  toCreateByRole,
  enterRole,
  cardStyle = {},
  hideAddIcon = false,
  hideTakeButton = false
}: IRoleCardProps) {
  const { loginIntercept } = useAuthState();

  const { roleSets, addRoleToAlbum } = useRoleStore(
    useShallow(state => ({
      roleSets: state.roleSets,
      addRoleToAlbum: state.addRoleToAlbum
    }))
  );

  const rolePlusClick = async () => {
    clickEffect();

    loginIntercept(async () => {
      // 默认添加到我的角色
      const mineAlbumId = 200;
      const defaultAlbum = roleSets.find(rs => Number(rs.id) === mineAlbumId);
      if (defaultAlbum?.id) {
        // TODO 添加到我的角色
        if (
          await requestWithTimeout(
            addRoleToAlbum({
              roleId: role?.id,
              brandId: defaultAlbum?.id
            })
          ).catch(() => false)
        ) {
          setIsRoleSave(true);

          addRoleOnSuccess?.(role);
        } else {
          showToast('角色添加失败!');
        }
      }
    });
  };

  const [isRoleSave, setIsRoleSave] = useState(true);

  useEffect(() => {
    setIsRoleSave(role.isSave);
  }, [role]);
  return (
    <View style={[$roleCard, cardStyle]}>
      <Pressable style={$roleCardPress} onPress={() => enterRole?.(role)}>
        <Image
          contentFit={role.material ? 'cover' : 'contain'}
          style={$roleCardUrl}
          source={role.material}
          tosSize="size4"
          contentPosition={'center'}
          placeholder={EMPTY_ROLE}
        />
        <Image source={MASK} contentFit="fill" style={$mask} />
        <View style={$head}>
          <ContentBottomPendant
            source={FIRE}
            gap={2}
            copyCount={role?.heat ? Number(role.heat) : 0}
          />
        </View>

        <>
          {!hideAddIcon ? (
            isRoleSave ? (
              <View
                style={[
                  $roleSaveIconWrapper,
                  {
                    justifyContent: 'center',
                    alignItems: 'center'
                  }
                ]}
              >
                <Icon icon="cref_search_confirm" size={18} />
              </View>
            ) : (
              <Pressable
                onPress={() => rolePlusClick()}
                style={$roleSaveIconWrapper}
              >
                <View
                  style={{
                    width: 24,
                    height: 24,
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                >
                  <Icon icon={'cref_search_plus'} size={18} />
                </View>
              </Pressable>
            )
          ) : null}
        </>
        <View style={$tail}>
          <Text
            style={[
              $tailTitle,
              {
                marginBottom: 6
              }
            ]}
          >
            {role?.displayName || role?.name || ''}
          </Text>
          {!hideTakeButton ? (
            <Pressable
              style={{
                overflow: 'hidden',
                borderColor: 'transparent',
                height: 26,
                marginBottom: 12
              }}
              onPress={() => toCreateByRole?.(role)}
            >
              <View style={$blur}>
                <CustomBlurView
                  style={$blurBg}
                  ios={{ blurMount: 5 }}
                  android={{
                    style: { backgroundColor: 'rgba(255, 255, 255, 0.3)' }
                  }}
                />
                <Text
                  style={[
                    $tailText,
                    {
                      height: 20
                    }
                  ]}
                >
                  去拍Ta
                </Text>
              </View>
            </Pressable>
          ) : null}
        </View>
      </Pressable>
    </View>
  );
}

const $roleCard: ViewStyle = {
  width: 113,
  height: 151,
  borderRadius: 10,
  backgroundColor: '#fff',
  overflow: 'hidden',
  position: 'relative'
};
const $roleCardPress: ViewStyle = { width: '100%', height: '100%' };

const $head: ViewStyle = {
  height: 18,
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: 16,
  paddingLeft: 8,
  flexDirection: 'row',
  minWidth: 80,
  position: 'relative'
};

const $takeSame: ViewStyle = {
  minWidth: 46,
  height: 18,
  paddingRight: 12,
  position: 'relative',
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: $CREF_SEARCH_COLORS.black_40,
  borderRadius: 12
};

const $takeSameIcon: ImageStyle = {
  width: 16,
  height: 16,
  borderRadius: 8
};

const $roleSaveIconWrapper: ViewStyle = {
  width: 24,
  height: 24,
  position: 'absolute',
  right: 6,
  top: 4,
  zIndex: 1000
};

const $mask: ImageStyle = {
  position: 'absolute',
  bottom: -1,
  width: '100%',
  height: 71,
  zIndex: 1
};

const $takeSamePlus: ImageStyle = {
  width: 24,
  height: 24,
  borderRadius: 8,
  backgroundColor: 'red'
};

const $takeSameBg: ViewStyle = {
  width: '100%',
  height: '100%',
  position: 'absolute'
};

const $roleCardUrl: ImageStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  zIndex: -1
};

const $blur: ViewStyle = {
  height: 24,
  marginBottom: 12,
  paddingVertical: 0,
  paddingHorizontal: 12,
  justifyContent: 'center',
  alignItems: 'center',
  overflow: 'hidden'
};

const $blurBg: ViewStyle = {
  position: 'absolute',
  top: 0,
  right: 0,
  bottom: 0,
  left: 0,
  borderRadius: 12,
  borderColor: '#FFFFFF66',
  borderWidth: 1
};

const $tail: ViewStyle = {
  width: '100%',
  flex: 1,
  justifyContent: 'flex-end',
  alignItems: 'center',
  position: 'relative',
  zIndex: 10
};

const $tailTitle: TextStyle = $USE_FONT(
  $CREF_SEARCH_COLORS.white,
  typography.fonts.pingfangSC.normal,
  14,
  'normal',
  '500',
  undefined
);

const $tailText: TextStyle = $USE_FONT(
  $CREF_SEARCH_COLORS.white,
  typography.fonts.pingfangSC.normal,
  12,
  'normal',
  '500'
);

const $iosBlur: ViewStyle = {
  height: 24,
  paddingVertical: 0,
  paddingHorizontal: 12,
  borderRadius: 12,
  borderColor: '#FFFFFF66',
  borderWidth: 0.83,
  overflow: 'hidden',
  justifyContent: 'center',
  alignItems: 'center'
};
