import { router } from 'expo-router';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef
} from 'react';
import { Pressable, TextStyle, View } from 'react-native';
import AddAlbumSheet from '@/src/bizComponents/cref/add-alblum-sheet';
import AddRoleSheet from '@/src/bizComponents/cref/add-role-sheet';
import SingleAlbumSheet from '@/src/bizComponents/cref/single-album-sheet';
import { Icon, showToast } from '@/src/components';
import { Toast, ToastMethods } from '@/src/components/v2/toast';
import { useCrefSheetStore } from '@/src/store/cref-sheet';
import { PageState, useMakePhotoStoreV2 } from '@/src/store/makePhotoV2';
import { useRoleStore } from '@/src/store/role';
import { typography } from '@/src/theme';
import { $USE_FONT, $flexHCenter } from '@/src/theme/variable';
import { RoleSet } from '@/src/types';
import { Text } from '@Components/text';
import { RoleInfo } from '@/proto-registry/src/web/raccoon/common/role_pb';
import { useShallow } from 'zustand/react/shallow';

export interface RoleCardOperationsActions {
  afterAdd: (role: RoleInfo) => void;
}

export const RoleCardOperations = forwardRef<RoleCardOperationsActions, {}>(
  (props, ref) => {
    const toastRef = useRef<ToastMethods>();

    const {
      openRoleSheet,
      roleShow,
      closeRoleSheet,
      albumShow,
      closeAlbumSheet,
      singleAlbumShow,
      closeSingleAlbumSheet,
      passRole
    } = useCrefSheetStore(
      useShallow(state => ({
        openRoleSheet: state.openRoleSheet,
        roleShow: state.roleShow,
        closeRoleSheet: state.closeRoleSheet,
        albumShow: state.albumShow,
        openAlbumSheet: state.openAlbumSheet,
        closeAlbumSheet: state.closeAlbumSheet,
        singleAlbumShow: state.singleAlbumShow,
        openSingleAlbumSheet: state.openSingleAlbumSheet,
        closeSingleAlbumSheet: state.closeSingleAlbumSheet,
        passRole: state.passRole
      }))
    );

    const { roleSets, fetchAllRoleSetList, createRoleSet, addRoleToAlbum } =
      useRoleStore(
        useShallow(state => ({
          roleSets: state.roleSets,
          roles: state.roles,
          fetchAllRoleSetList: state.fetchAllRoleSetList,
          fetchRoleFromSet: state.fetchRoleFromSet,
          createRoleSet: state.createRoleSet,
          addRoleToAlbum: state.addRoleToAlbum
        }))
      );

    const addRoleOnSuccess = (role: RoleInfo) => {
      toastRef.current?.showToast({
        text: `已添加至“我的角色”`,
        position: 'bottom',
        duration: 3000,
        prefix: 'toast_success',
        cornerNode: (
          <Pressable
            onPress={() => {
              openRoleSheet(role);
            }}
            style={[
              $flexHCenter,
              {
                marginLeft: 12
              }
            ]}
          >
            <Text style={$moreText}>编辑</Text>
            <Icon size={16} icon="toast_more" style={{ marginLeft: 4 }} />
          </Pressable>
        )
      });
    };

    const successAddtoRoleSet = async (roleSet: RoleSet) => {
      closeRoleSheet();
      if (
        await addRoleToAlbum({
          roleId: passRole?.id,
          brandId: roleSet.id
        })
      ) {
        toastRef.current?.showToast({
          text: `已添加至"${roleSet.name}"`,
          position: 'bottom',
          duration: 3000,
          prefix: 'toast_success',
          cornerNode: (
            <Pressable
              onPress={() => {
                useMakePhotoStoreV2.getState().reset();
                router.navigate({
                  pathname: '/make-photo/role-select'
                });
                // 不要改成 string
                useMakePhotoStoreV2.getState().setIp(roleSet.id);
              }}
              style={[
                $flexHCenter,
                {
                  marginLeft: 12
                }
              ]}
            >
              <Text style={$moreText}>去看看</Text>
              <Icon size={16} icon="toast_more" style={{ marginLeft: 4 }} />
            </Pressable>
          )
        });
      }
    };

    const toCreateAlbum = async (name: string) => {
      const albumId = await createRoleSet(name);
      if (albumId) {
        if (
          await addRoleToAlbum({
            roleId: passRole?.id,
            brandId: Number(albumId)
          })
        ) {
          toastRef.current?.showToast({
            text: `添加成功`,
            position: 'bottom',
            duration: 3000,
            prefix: 'toast_success',
            cornerNode: (
              <Pressable
                onPress={() => {
                  useMakePhotoStoreV2.getState().reset();
                  router.navigate({
                    pathname: '/make-photo'
                  });
                  router.navigate({
                    pathname: '/make-photo/role-select'
                  });
                  useMakePhotoStoreV2.getState().setIp(Number(albumId));
                }}
                style={[
                  $flexHCenter,
                  {
                    marginLeft: 12
                  }
                ]}
              >
                <Text style={$moreText}>去看看</Text>
                <Icon size={16} icon="toast_more" style={{ marginLeft: 4 }} />
              </Pressable>
            )
          });
        } else {
          showToast('添加到新作品失败了～');
        }
      } else {
        showToast('创建失败～');
      }
    };

    useImperativeHandle(
      ref,
      () => ({
        afterAdd: (role: RoleInfo) => {
          addRoleOnSuccess(role);
        }
      }),
      []
    );

    useEffect(() => {
      fetchAllRoleSetList(true);
    }, []);

    return (
      <>
        <Toast ref={toastRef} />
        {roleShow && (
          <AddRoleSheet
            visible={roleShow}
            onClose={() => {
              closeRoleSheet();
            }}
            list={roleSets.filter(r => r.canAddRole)}
            onSuccess={successAddtoRoleSet}
            onLoadMore={fetchAllRoleSetList}
          />
        )}
        {albumShow && (
          <AddAlbumSheet
            visible={albumShow}
            onClose={closeAlbumSheet}
            onPressCb={(name: string) => {
              closeAlbumSheet();
              toCreateAlbum(name);
              // openSingleAlbumSheet();
            }}
            // rightCorner={
            //   <Pressable
            //     onPress={() => {
            //       closeAlbumSheet();
            //       toastRef.current?.showToast({
            //         text: 'gggg',
            //         position: 'bottom',
            //         duration: 15000,
            //         prefix: 'toast_success',
            //         cornerNode: (
            //           <View style={{ ...$flexHCenter }}>
            //             <Pressable
            //               onPress={() => {
            //                 router.navigate('/feed');
            //               }}
            //               style={{ ...$flexHCenter }}
            //             >
            //               <Text style={$moreText}>去看看</Text>
            //               <Icon
            //                 size={16}
            //                 icon="toast_more"
            //                 style={{ marginLeft: 4 }}
            //               ></Icon>
            //             </Pressable>
            //           </View>
            //         )
            //       });
            //     }}
            //   >
            //     <View>
            //       <Text>删除</Text>
            //     </View>
            //   </Pressable>
            // }
          />
        )}
        {singleAlbumShow && (
          <SingleAlbumSheet
            visible={singleAlbumShow}
            onClose={closeSingleAlbumSheet}
            onOk={() => {}}
          />
        )}
      </>
    );
  }
);

const $moreText: TextStyle = $USE_FONT(
  '#FF4D00',
  typography.fonts.pingfangSC.normal,
  14,
  'normal',
  '600'
);
