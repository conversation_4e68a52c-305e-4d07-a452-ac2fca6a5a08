import { useDebounceFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import {
  ImageStyle,
  KeyboardAvoidingView,
  Pressable,
  Text,
  TextStyle,
  View
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';
import SearchBar from '@/src/bizComponents/search/search-bar';
import { Icon, Image, hideToast, showToast } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import TabScene, {
  ETabHeaderArrange,
  IIinitialStateItemType
} from '@/src/components/v2/tabScene';
import { LoginState, useAuthStore } from '@/src/store/authInfo';
import { typography } from '@/src/theme/typography';
import { $CREF_SEARCH_COLORS, $USE_FONT, $flex } from '@/src/theme/variable';
import { ERoleSearchTabScene, RoleSet } from '@/src/types';
import { dp2px, isIos } from '@/src/utils';
import { decodeSP, encodeSP } from '@/src/utils/placeholder';
import { reportClick } from '@/src/utils/report';
import CrefSearchRoleList from './parts/role-list';
import CrefSearchRoleListSet from './parts/role-list-set';
import { RoleInfo } from '@/proto-registry/src/web/raccoon/common/role_pb';
import { useRoute } from '@react-navigation/native';
import {
  RoleCardOperations,
  RoleCardOperationsActions
} from './RoleCardOperations';

const LANDING_SOURCE = require('@Assets/image/cref-search/landing.png');
const UNDER_NODE = require('@Assets/image/cref-search/underline.png');

const resourseTabs = [
  {
    key: '0',
    title: '角色',
    tabType: ERoleSearchTabScene.CREF_ROLE
  },
  {
    key: '1',
    title: '角色集',
    tabType: ERoleSearchTabScene.CREF_ROLE_SET
  }
];

export default function CrefSearchResult() {
  const route = useRoute();

  const [keywords, setKeywords] = useState<string | null>();

  useEffect(() => {
    setKeywords(decodeSP((route.params as any)?.keywords));
  }, [route.params]);

  const clearWord = () => {
    setKeywords(null);
    router.replace({
      pathname: '/cref-search' as RelativePathString,
      params:
        typeof keywords === 'string' ? { keywords: encodeSP(keywords) } : {}
    });
  };

  const [resetSignal, setResetSignal] = useState(0);

  const focusWord = () => {
    setResetSignal(r => r + 1);
    router.replace({
      pathname: '/cref-search' as RelativePathString,
      params: {
        result: encodeSP(keywords!)
      }
    });
  };

  const tabOpacity = useSharedValue(0.9);

  const $tabOpacity = useAnimatedStyle(() => ({
    opacity: tabOpacity.value
  }));

  useEffect(() => {
    tabOpacity.value = withTiming(1, {
      duration: 600
    });
    if (resourseTabs.length === 0) {
      showToast('哎呀，小狸走丢了');
    }
  }, []);

  const [activeTabIndex, setActiveTabIndex] = useState(0);

  const handleTabChange = useDebounceFn(
    (index: number) => {
      if (activeTabIndex !== index) {
        setActiveTabIndex(index);
      }
    },
    { wait: 50 }
  );

  const renderRoleList = () => {
    return (
      <CrefSearchRoleList
        key={uid}
        keywords={keywords!}
        addRoleOnSuccess={addRoleOnSuccess}
      />
    );
  };

  const uid = useAuthStore(state => state.uid);

  const isLoggedIn = Boolean(uid);

  const renderRoleListSet = () => {
    return <CrefSearchRoleListSet key={uid} keywords={keywords!} />;
  };

  const renderTabScene = (item: IIinitialStateItemType) => {
    if (item.tabType === ERoleSearchTabScene.CREF_ROLE) {
      return renderRoleList();
    }
    if (item.tabType === ERoleSearchTabScene.CREF_ROLE_SET) {
      return renderRoleListSet();
    }
  };

  const addRoleOnSuccess = (role: RoleInfo) => {
    reportClick('add_character_button', {
      module: 'role_search',
      keywords: keywords || '',
      role_id: role.id
    });
    operationRef.current?.afterAdd(role);
  };

  const operationRef = useRef<RoleCardOperationsActions>(null);

  return (
    <PagePerformance pathname="cref-search/result">
      <Animated.View
        style={[
          $flex,
          { position: 'relative', backgroundColor: '#16161A' },
          $tabOpacity
        ]}
      >
        <SafeAreaView style={[$flex, { backgroundColor: '#16161A' }]}>
          <View style={[$flex]}>
            <KeyboardAvoidingView behavior={isIos ? 'height' : undefined}>
              <SearchBar
                value={keywords!}
                placeLoop={false}
                placeTexts={[]}
                hiddenRight
                clearWord={clearWord}
                onParentFocus={focusWord}
                canbeEdit={false}
                autoFocus={false}
                ellipseMode={true}
                ellipseWidth={dp2px(275)}
                $customStyle={{
                  backgroundColor: '#FFFFFF1A'
                }}
                placeHolderContent="来搜索更多角色吧～"
                backIcon="cref_back"
                clearIcon="cref_search_clear"
                searchIcon="cref_search"
                searchColors={{
                  selectionColor: $CREF_SEARCH_COLORS.white_40,
                  placeholderTextColor: $CREF_SEARCH_COLORS.white_40,
                  cursorColor: $CREF_SEARCH_COLORS.white,
                  searchTextColor: $CREF_SEARCH_COLORS.white
                }}
              />
            </KeyboardAvoidingView>
            <TabScene
              activeIndex={activeTabIndex}
              tabNames={resourseTabs.map(s => {
                return {
                  title: s.title,
                  key: s.key,
                  tabType: s.tabType
                };
              })}
              tabHeaderArrange={ETabHeaderArrange.LEFT}
              renderScene={renderTabScene}
              onIndexChange={handleTabChange.run}
              gap={30}
              underNodeWidth={24}
              underNodeHeight={2}
              underNode={UNDER_NODE}
              $underNodeStyle={{
                width: 24,
                height: 2,
                bottom: 4,
                left: 0
              }}
              $tabActiveTextStyle={{
                color: $CREF_SEARCH_COLORS.white
              }}
              $tabNormalTextStyle={{
                color: $CREF_SEARCH_COLORS.white_57
              }}
            />
            {isLoggedIn && <RoleCardOperations ref={operationRef} />}
          </View>
        </SafeAreaView>

        <Image source={LANDING_SOURCE} style={$bg} contentFit="fill" />
      </Animated.View>
    </PagePerformance>
  );
}

const $bg: ImageStyle = {
  position: 'absolute',
  zIndex: -1,
  flex: 1,
  bottom: 0,
  top: 0,
  left: 0,
  right: 0
};
