import { router } from 'expo-router';
import { add } from 'lodash-es';
import { memo, useEffect, useRef, useState } from 'react';
import {
  FlatList,
  Pressable,
  ScrollView,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue
} from 'react-native-reanimated';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import { IPList } from '@/src/components/makePhoto/roleSelector/IPList';
import { RoleSkeleton } from '@/src/components/makePhoto/roleSelector/RoleSkeleton';
import {
  SPECIAL_FEED_ID,
  SpecialRoleSets,
  useIpDataHandler
} from '@/src/components/makePhoto/roleSelector/useIpDataHandler';
import { useAuthState } from '@/src/hooks';
import { useAuthStore } from '@/src/store/authInfo';
import { useRoleHomeStore } from '@/src/store/roleHome';
import { darkSceneColor } from '@/src/theme/colors/common';
import { StyleSheet, dp2px, isIos } from '@/src/utils';
import { formatRole } from '@/src/utils/formatRole';
import { RoleSource, reportClick } from '@/src/utils/report';
import { Loading } from '@Components/promptLoading';
import { Text } from '@Components/text';
import RoleCardV2, { RoleStylePresets } from '../cards/role-cardV2';
import { RoleInfo } from '@/proto-registry/src/web/raccoon/common/role_pb';
import { useShallow } from 'zustand/react/shallow';

export interface IRoleFeedListProps {
  addRoleOnSuccess?: (role: RoleInfo, key: number) => void;
}

const RoleFeedList = memo(({ addRoleOnSuccess }: IRoleFeedListProps) => {
  const emptyOpacity = useSharedValue(1);
  const ref = useRef<FlatList>(null);
  const { loginIntercept } = useAuthState();
  const opacityValue = useSharedValue(1);
  const $emptyOpacityStyle = useAnimatedStyle(() => ({
    opacity: emptyOpacity.value
  }));
  const [subIp, setSubIp] = useState<number>();
  const {
    currentIP,
    changeIp,
    roleList,
    onLoadMore,
    validRoleSets,
    loading,
    cursor,
    hasError,
    refreshAll,
    onLoadMoreRoles
  } = useIpDataHandler({
    initialIP: SpecialRoleSets.HotRoleFeed,
    filterOfficialRoles: false,
    subSet: subIp,
    listRef: ref,
    fromFeed: true,
    filterOfficialRoleSets: true,
    filterRoleSets: [
      SpecialRoleSets.EmptyRole,
      SpecialRoleSets.HotRole,
      SpecialRoleSets.MineRole,
      SpecialRoleSets.RoleSetTag
    ]
  });
  useEffect(() => {
    if (currentIP !== SpecialRoleSets.MineRoleFeed) {
      setSubIp(undefined);
    } else {
      setSubIp(SpecialRoleSets.MineRoleFeed);
    }
  }, [currentIP]);
  const { uid } = useAuthStore(
    useShallow(state => ({
      uid: state.uid
    }))
  );
  const renderMyRolesHeader = () => {
    if (currentIP === SpecialRoleSets.MineRoleFeed) {
      return (
        <View style={styles.myTabContainer}>
          <Pressable
            onPress={() => {
              setSubIp(SpecialRoleSets.MineRoleFeed);
            }}
          >
            <Text
              style={
                subIp !== SpecialRoleSets.MineRoleSaveFeed
                  ? styles.myTabActive
                  : styles.myTabInactive
              }
            >
              我的创建
            </Text>
          </Pressable>
          <View style={styles.gapLine} />
          <Pressable
            onPress={() => {
              setSubIp(SpecialRoleSets.MineRoleSaveFeed);
            }}
          >
            <Text
              style={
                subIp === SpecialRoleSets.MineRoleSaveFeed
                  ? styles.myTabActive
                  : styles.myTabInactive
              }
            >
              我的收藏
            </Text>
          </Pressable>
        </View>
      );
    }
    return undefined;
  };
  const renderItem = ({ item, index }: { item: RoleInfo; index: number }) => {
    return (
      <View key={index} style={$roleItem}>
        <RoleCardV2
          showAddToRoleSet
          width={dp2px(91)}
          role={item}
          preset={RoleStylePresets.Small}
          hideAddIcon={uid === item?.creator?.uid}
          addRoleOnSuccess={role => {
            if (currentIP) {
              addRoleOnSuccess?.(role, SPECIAL_FEED_ID[currentIP]);
            }
          }}
          enterRole={enterRole}
        />
      </View>
    );
  };

  const renderContent = () => {
    if (roleList.length === 0 && hasError) {
      return (
        <EmptyPlaceHolder
          type="crefRole"
          style={{ height: 500 }}
          buttonText="刷新"
          button={Boolean(currentIP)}
          onButtonPress={refreshAll}
        >
          <Text style={$hintText}>哎呀，小狸走丢了</Text>
        </EmptyPlaceHolder>
      );
    } else {
      return (
        <>
          {
            <View
              style={{
                flex: 1,
                opacity: loading ? 1 : 0
              }}
            >
              <RoleSkeleton isLarge={false} />
            </View>
          }
          <FlatList
            showsVerticalScrollIndicator={false}
            horizontal={false}
            data={roleList}
            ref={ref}
            style={{
              flex: 1,
              position: 'absolute',
              height: '100%',
              width: '100%',
              top: 0,
              opacity: loading ? 0 : 1
            }}
            initialNumToRender={8}
            keyExtractor={(item, index) => `${index}`}
            renderItem={renderItem}
            numColumns={3}
            contentContainerStyle={[{ paddingTop: 10, paddingBottom: 100 }]}
            onEndReachedThreshold={0.5}
            onEndReached={onLoadMoreRoles}
            ListHeaderComponent={renderMyRolesHeader}
            ListFooterComponent={
              <View
                style={{
                  paddingVertical: 12,
                  width: '100%',
                  alignItems: 'center'
                }}
              >
                {cursor === '' ? (
                  <Text
                    style={{
                      color: darkSceneColor.fontColor3
                    }}
                  >
                    小狸也是有底线的～
                  </Text>
                ) : (
                  <></>
                )}
              </View>
            }
            scrollEventThrottle={32}
          />
        </>
      );
    }
  };
  return (
    <Animated.View style={[$userBg, $emptyOpacityStyle]}>
      <IPList
        currentIP={currentIP}
        IPData={validRoleSets}
        onPress={changeIp}
        onLoadMore={onLoadMore}
        allowCreateRole={false}
        opacityValue={opacityValue}
      />
      <View
        style={[
          {
            position: 'relative',
            flex: 1,
            marginLeft: dp2px(isIos ? 82 : 86)
          }
        ]}
      >
        {renderContent()}
      </View>
    </Animated.View>
  );

  function enterRole(role: RoleInfo) {
    loginIntercept(() => {
      reportClick('character_card_button', {
        module: 'role_search',
        role_id: role.id
      });
      const roleInfo = formatRole(role);
      useRoleHomeStore.getState().getRoleInfos(role?.id);
      useRoleHomeStore.getState().updateRoleInfo(role?.id, roleInfo);
      router.navigate({
        pathname: '/role',
        params: {
          roleId: role?.id,
          source: RoleSource.ROLE_FEED
        }
      });
    });
  }
});

export default RoleFeedList;
const $userBg: ViewStyle = {
  flex: 1
};

const $roleItem: ViewStyle = { marginRight: 6, marginBottom: 6 };

const $hintText: TextStyle = {
  color: '#FFFFFF',
  opacity: 0.4,
  fontSize: 14,
  fontWeight: '500'
};

const styles = StyleSheet.create({
  myTabActive: {
    fontSize: 13,
    fontWeight: '500',
    color: '#ff6a3b',
    textAlign: 'center'
  },
  myTabInactive: {
    fontSize: 13,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.5)',
    textAlign: 'center'
  },
  gapLine: {
    width: 1,
    height: 13,
    opacity: 0.2,
    backgroundColor: '#fff'
  },
  myTabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 12,
    paddingBottom: 18
  }
});
