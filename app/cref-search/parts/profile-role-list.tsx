import { useLockFn } from 'ahooks';
import { router } from 'expo-router';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  useWindowDimensions
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue
} from 'react-native-reanimated';
import { LayoutProvider } from 'recyclerlistview-masonrylayoutmanager';
import { useTabRefreshEffect } from '@/src/bizComponents/userScreen/hooks/useTabRefreshEffect';
import { hideLoading, showLoading } from '@/src/components';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import { InfiniteList } from '@/src/components/infiniteList';
import {
  InfiniteListRef,
  RequestScene
} from '@/src/components/infiniteList/typing';
import { RoleSkeleton } from '@/src/components/makePhoto/roleSelector/RoleSkeleton';
import {
  SkeletonColumn,
  SkeletonRow,
  SkeletonSpan
} from '@/src/components/skeletion';
import { useAuthState, useSafeAreaInsetsStyle } from '@/src/hooks';
import { useAuthStore } from '@/src/store/authInfo';
import { useRoleHomeStore } from '@/src/store/roleHome';
import { typography } from '@/src/theme';
import { darkSceneColor } from '@/src/theme/colors/common';
import { Theme } from '@/src/theme/colors/type';
import { $USE_FONT, $flexCenter } from '@/src/theme/variable';
import { Pagination } from '@/src/types';
import { dp2px } from '@/src/utils';
import { formatRole } from '@/src/utils/formatRole';
import { encodeSP } from '@/src/utils/placeholder';
import { RoleSource, reportClick, reportPage } from '@/src/utils/report';
import { Text } from '@Components/text';
import { NestedScrollView } from '../../../src/bizComponents/nestedScrollView';
import { UserPageTab } from '../../../src/bizComponents/userScreen/constants';
import { RefreshTrigger } from '../../../src/bizComponents/userScreen/types';
import RoleCardV2, {
  RoleStylePresets,
  TEXT_STYLES_WITH_PRESETS
} from '../cards/role-cardV2';
import { RoleInfo } from '@/proto-registry/src/web/raccoon/common/role_pb';
import { GetRoleFromSetReq } from '@/proto-registry/src/web/raccoon/crole/crole_pb';
import type { PartialMessage } from '@bufbuild/protobuf';
import { useShallow } from 'zustand/react/shallow';

type ExtraRoleReqParams = Omit<
  PartialMessage<GetRoleFromSetReq>,
  'id' | 'page'
>;

const CARD_GAP = dp2px(8);

export interface IRoleFeedListProps {
  brandId: number;
  addRoleOnSuccess?: (role: RoleInfo) => void;
  fetchRoleList: (props: {
    isInit: boolean;
    id: number;
    reqParams?: ExtraRoleReqParams;
  }) => Promise<void | RoleInfo[]>;
  pagin: Partial<Pagination>;
  scrollContainerStyle?: StyleProp<ViewStyle>;
  isRender?: boolean;
  roleList: RoleInfo[];
  reqParams?: ExtraRoleReqParams;
  isActiveTab?: boolean;
  queryRefresh?: string;
  queryTimestamp?: string;
  refreshTrigger: RefreshTrigger;
  nestedScrollViewRef: React.RefObject<NestedScrollView>;
}

const ProfileRoleList = memo(
  ({
    brandId,
    scrollContainerStyle: $scrollContainerStyle,
    isRender = true,
    addRoleOnSuccess,
    fetchRoleList,
    pagin,
    roleList,
    reqParams = {},
    isActiveTab,
    queryRefresh,
    queryTimestamp,
    refreshTrigger,
    nestedScrollViewRef
  }: IRoleFeedListProps) => {
    const emptyOpacity = useSharedValue(1);
    const { loginIntercept } = useAuthState();

    const $emptyOpacityStyle = useAnimatedStyle(() => ({
      opacity: emptyOpacity.value
    }));

    const [loading, setLoading] = useState(false);
    const infiniteListRef = useRef<InfiniteListRef>(null);

    const $containerInsets = useSafeAreaInsetsStyle(['bottom']);

    // 默认走热门
    const loadHotRoleList = useLockFn(async (isInit = false) => {
      console.log('hot load once');
      // 没有数据了
      if ((!isInit && pagin?.nextCursor === '') || loading) {
        return;
      }

      setHasError(false);

      try {
        // 热门 id

        // showLoading();
        await fetchRoleList({ isInit, id: brandId, reqParams });
      } catch (e) {
        setHasError(true);
        console.log('请求 热门role列表错误: ', e);
      }
    });

    const { uid } = useAuthStore(
      useShallow(state => ({
        uid: state.uid
      }))
    );

    useEffect(() => {
      if (!roleList?.length) {
        setLoading(true);
        showLoading();
        loadHotRoleList(true).finally(() => {
          hideLoading();
          setLoading(false);
        });
      }
    }, []);

    useEffect(() => {
      if (isActiveTab && queryRefresh) {
        infiniteListRef.current?.forceRefresh();
      }
    }, [isActiveTab, queryRefresh, queryTimestamp]);

    useTabRefreshEffect(
      isActiveTab || false,
      refreshTrigger,
      infiniteListRef,
      nestedScrollViewRef
    );

    const [hasError, setHasError] = useState(false);
    const { width: winW } = useWindowDimensions();
    // 卡片间距 8，左右间距 10，每行3个卡片，需要2个间距
    // 考虑到小米机型的兼容性问题，确保卡片宽度计算更精确
    const cardWidth = useMemo(
      () => Math.floor((winW - 10 * 2 - CARD_GAP * 2) / 3),
      [winW]
    );
    const renderItem = ({ item, index }: { item: RoleInfo; index: number }) => {
      return (
        <View key={index} style={$roleItem}>
          <RoleCardV2
            showAddToRoleSet
            preset={RoleStylePresets.Large}
            width={cardWidth}
            role={item}
            hideAddIcon={uid === item?.creator?.uid}
            addRoleOnSuccess={addRoleOnSuccess}
            enterRole={enterRole}
          />
        </View>
      );
    };

    const layoutProvider = useMemo(() => {
      return new LayoutProvider(
        () => 0,
        (_, dim, index) => {
          // 为了解决小米机型显示问题，调整卡片宽度和间距
          // index % 3: 0-第一个，1-第二个，2-第三个
          dim.width = index % 3 !== 2 ? cardWidth + CARD_GAP : cardWidth;
          dim.height = cardWidth * 2.35 + CARD_GAP; // 加上间距
        }
      );
    }, [cardWidth]);
    const getLayoutProvider = useCallback(() => {
      return layoutProvider;
    }, [layoutProvider]);

    const renderContent = () => {
      return (
        <View style={[{ position: 'relative', flex: 1 }]}>
          <InfiniteList
            ref={infiniteListRef}
            renderLoading={renderSkeleton}
            showLoadingWhenHasMoreEmpty={true}
            renderEmpty={() =>
              hasError ? (
                <EmptyPlaceHolder
                  type="crefRole"
                  style={{ height: 300 }}
                  buttonText="刷新"
                  button={Boolean(brandId)}
                  onButtonPress={() => {
                    loadHotRoleList?.(true);
                  }}
                >
                  <Text style={$hintText}>哎呀，小狸走丢了</Text>
                </EmptyPlaceHolder>
              ) : (
                <EmptyPlaceHolder type="crefRole" style={{ height: 300 }}>
                  <View style={$flexCenter}>
                    <Text style={[$hintText, $hint]}>
                      小狸没找到ta哦，来试试创建ta吧！
                    </Text>
                  </View>
                </EmptyPlaceHolder>
              )
            }
            data={roleList}
            loading={loading}
            error={hasError ? new Error('加载失败') : undefined}
            hasMore={pagin?.nextCursor !== ''}
            onRequest={scene => {
              if (scene === RequestScene.LOAD_MORE) {
                return loadHotRoleList();
              } else if (
                scene === RequestScene.REFRESHING ||
                scene === RequestScene.INIT
              ) {
                return loadHotRoleList(true);
              }
            }}
            getLayoutProvider={getLayoutProvider}
            renderItem={(_, item, index) => renderItem({ item, index })}
            customListProps={{
              initialRenderIndex: 0,
              disableRecycling: false
            }}
            footerStyle={{
              height: 42,
              width: dp2px(375)
            }}
            scrollViewProps={{
              contentStyle: [
                {
                  paddingBottom: Number($containerInsets.paddingBottom ?? 0),
                  flexDirection: 'row',
                  flexWrap: 'wrap'
                },
                $scrollContainerStyle
              ],
              // NOTE(fuxiao): 禁止弹性效果后无法在短的列表中上下滚动，先保留
              bounces: true,
              scrollViewName: UserPageTab.MY_ROLE
            }}
            enablePullRefresh={false}
          />
        </View>
      );
    };

    if (!isRender) return null;

    return (
      <Animated.View style={[$userBg, $emptyOpacityStyle]}>
        {renderContent()}
      </Animated.View>
    );

    function enterRole(role: RoleInfo) {
      loginIntercept(() => {
        reportClick('character_card_button', {
          module: 'role_search',
          role_id: role.id
        });
        const roleInfo = formatRole(role);
        useRoleHomeStore.getState().getRoleInfos(role?.id);
        useRoleHomeStore.getState().updateRoleInfo(role?.id, roleInfo);
        router.navigate({
          pathname: '/role',
          params: {
            roleId: role?.id,
            source: RoleSource.ROLE_FEED
          }
        });
      });
    }
  },
  (prev, cur) =>
    prev.pagin === cur.pagin &&
    prev.brandId === cur.brandId &&
    prev.isRender === cur.isRender &&
    prev.roleList === cur.roleList &&
    prev.refreshTrigger.timestamp === cur.refreshTrigger.timestamp &&
    prev.queryRefresh === cur.queryRefresh &&
    prev.queryTimestamp === cur.queryTimestamp &&
    prev.isActiveTab === cur.isActiveTab
);

export default ProfileRoleList;

const renderSkeleton = () => {
  return <RoleSkeleton isLarge />;
};

const $userBg: ViewStyle = {
  flex: 1,
  paddingHorizontal: 10
};

const $roleItem: ViewStyle = { marginRight: CARD_GAP, marginBottom: CARD_GAP };

const $hintText: TextStyle = {
  color: '#FFFFFF',
  opacity: 0.4,
  fontSize: 14,
  fontWeight: '500'
};

const $hint: TextStyle = $USE_FONT(
  '#FFFFFF',
  typography.fonts.pingfangSC.normal,
  14,
  undefined,
  '500',
  undefined
);
