import { useLockFn } from 'ahooks';
import { router } from 'expo-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  FlatList,
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  useWindowDimensions
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue
} from 'react-native-reanimated';
import { queryClient } from '@/src/api/query';
import { searchClient } from '@/src/api/search';
import { RecSceneName } from '@/src/bizComponents/feedScreen/type';
import { Icon } from '@/src/components';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import { RoleSkeleton } from '@/src/components/makePhoto/roleSelector/RoleSkeleton';
import {
  SkeletonColumn,
  SkeletonRow,
  SkeletonSpan
} from '@/src/components/skeletion';
import Button, { EButtonType } from '@/src/components/v2/button';
import { useAuthState, useSafeAreaInsetsStyle } from '@/src/hooks';
import { useAuthStore } from '@/src/store/authInfo';
import { useMakePhotoStoreV2 } from '@/src/store/makePhotoV2';
import { useRoleHomeStore } from '@/src/store/roleHome';
import { typography } from '@/src/theme';
import { Theme } from '@/src/theme/colors/type';
import { $USE_FONT, $flexCenter, $flexHCenter } from '@/src/theme/variable';
import { Pagination, PlainType, RoleCreateFrom } from '@/src/types';
import { dp2px } from '@/src/utils';
import { formatRole } from '@/src/utils/formatRole';
import { RoleSource, reportClick, reportPage } from '@/src/utils/report';
import { Text } from '@Components/text';
import RoleCardV2, { RoleStylePresets } from '../cards/role-cardV2';
import { RoleInfo } from '@/proto-registry/src/web/raccoon/common/role_pb';
import {
  SearchResult,
  SearchResultType
} from '@/proto-registry/src/web/raccoon/query/query_pb';
import { useShallow } from 'zustand/react/shallow';

interface ICrefSearchRoleListProps {
  keywords: string;
  addRoleOnSuccess?: (role: RoleInfo) => void;
  resetSignal?: number;
  needShowLoading?: boolean;
  scrollContainerStyle?: StyleProp<ViewStyle>;
}

export function CrefSearchRoleList({
  keywords,
  addRoleOnSuccess,
  scrollContainerStyle: $scrollContainerStyle
}: ICrefSearchRoleListProps) {
  const emptyOpacity = useSharedValue(1);
  const { loginIntercept } = useAuthState();

  const $emptyOpacityStyle = useAnimatedStyle(() => ({
    opacity: emptyOpacity.value
  }));

  const [roleList, setRoleList] = useState<PlainType<RoleInfo>[]>([]);
  const [loading, setLoading] = useState(false);

  const $containerInsets = useSafeAreaInsetsStyle(['bottom']);

  // 默认走热门
  const loadHotRoleList = useLockFn(async (isInit?: boolean) => {
    console.log('hot load once');
    // 没有数据了
    if ((!isInit && nextPageToken?.current?.nextCursor === '') || loading) {
      return;
    }

    setHasError(false);
    setLoading(true);

    try {
      // 热门 id
      const res = await queryClient.recRoles({
        recSceneName: 'roleRecTab',
        pagination: { size: 100, ...nextPageToken.current }
      });
      const allRoles = res?.roleCard
        .map(r => r.roleInfo!)
        .filter(v => v !== undefined);

      nextPageToken.current = {
        ...nextPageToken.current,
        cursor: res.pagination?.nextCursor, // 用于下次请求
        nextCursor: res.pagination?.nextCursor
      };

      if (isInit) {
        const nextValue = (allRoles || []) as PlainType<RoleInfo>[];
        setRoleList(nextValue);
        reportPage('page_preview', {
          module: 'cref_search',
          role_id: allRoles?.map(i => i.id).join(',') ?? '',
          keywords
        });
      } else {
        const nextValue = [
          ...roleList,
          ...(allRoles || [])
        ] as PlainType<RoleInfo>[];
        setRoleList(nextValue);
      }
    } catch (e) {
      setHasError(true);
      console.log('请求 热门role列表错误: ', e);
    } finally {
      setLoading(false);
    }
  });

  const loadRoleList = async (keyword: string, isInit?: boolean) => {
    console.log('role load once');
    // 没有数据了
    if (loading || nextPageToken?.current?.nextCursor === '') {
      return;
    }

    // 先 mock roleList
    try {
      setLoading(true);
      const res = await searchClient.search({
        searchSceneName: RecSceneName.CREF_SEARCH,
        features: {
          type_tab: 'role'
        },
        pagination: nextPageToken.current,
        keyword: keyword
      });

      nextPageToken.current = {
        ...nextPageToken.current,
        cursor: res.pagination?.nextCursor, // 用于下次请求
        nextCursor: res.pagination?.nextCursor
      };

      const roles = res.result
        ?.filter((item: SearchResult) => item.type === SearchResultType.Role)
        .map((i: SearchResult) => i.item.value! as RoleInfo);

      // console.log(roles, 'rolesroles');
      if (isInit) {
        const nextValue = (roles || []) as PlainType<RoleInfo>[];
        setRoleList(nextValue);
        reportPage('characterlist-expo', {
          module: 'role_search',
          role_id: roles?.map(i => i.id).join(',') ?? '',
          keywords,
          status: (roles?.length ? 1 : 0) + ''
        });
      } else {
        const nextValue = [
          ...roleList,
          ...(roles || [])
        ] as PlainType<RoleInfo>[];
        setRoleList(nextValue);
      }
      setHasError(false);
    } catch (e) {
      setHasError(true);
      console.log('请求role列表错误: ', e, {
        searchSceneName: RecSceneName.CREF_SEARCH,
        features: {
          mock: 'role_searcj_test'
        },
        pagination: nextPageToken.current,
        keyword: keyword
      });
    } finally {
      setLoading(false);
    }
  };

  const nextPageToken = useRef<Partial<Pagination>>({
    size: 30,
    cursor: ''
  });

  useEffect(() => {
    resetToken();
    setRoleList([]);
  }, [keywords]);

  const { uid } = useAuthStore(
    useShallow(state => ({
      uid: state.uid
    }))
  );

  const loadMore = () => {
    if (keywords) {
      loadRoleList(keywords);
    } else {
      loadHotRoleList();
    }
  };

  const resetToken = () => {
    nextPageToken.current = {
      size: 30,
      cursor: ''
    };
  };
  useEffect(() => {
    if (!roleList.length) {
      if (keywords) {
        loadRoleList(keywords, true);
      } else if (keywords === '') {
        loadHotRoleList(true);
      }
    }
  }, [keywords]);

  const toCreateRole = () => {
    reportClick('create_character_button', {
      module: 'role_search',
      keywords,
      status: roleList.length ? 1 : 0
    });

    loginIntercept(() => {
      router.navigate({
        pathname: '/role-create',
        params: { from: RoleCreateFrom.RoleTab }
      });
    });
  };

  const [hasError, setHasError] = useState(false);
  const { width: winW } = useWindowDimensions();
  // 卡片间距 8，左右间距 10
  const maxRoleCardWidth = useMemo(() => (winW - 8 * 2 - 10 * 2) / 3, [winW]);
  const renderItem = ({ item, index }: { item: RoleInfo; index: number }) => {
    return (
      <View key={index} style={$roleItem}>
        <RoleCardV2
          showAddToRoleSet
          role={item}
          preset={RoleStylePresets.Large}
          width={maxRoleCardWidth}
          hideAddIcon={uid === item?.creator?.uid}
          addRoleOnSuccess={addRoleOnSuccess}
          enterRole={enterRole}
        />
      </View>
    );
  };

  const renderContent = () => {
    if (roleList.length === 0 && loading) {
      return renderSkeleton();
    } else if (roleList.length === 0 && hasError) {
      return (
        <EmptyPlaceHolder
          type="crefRole"
          style={{ height: 400 }}
          buttonText="刷新"
          button={Boolean(loadRoleList)}
          onButtonPress={() => {
            if (keywords) {
              loadRoleList?.(keywords, true);
            } else {
              loadHotRoleList?.(true);
            }
          }}
        >
          <Text style={$hintText}>哎呀，小狸走丢了</Text>
        </EmptyPlaceHolder>
      );
    } else if (roleList.length === 0) {
      return (
        <EmptyPlaceHolder type="crefRole">
          <View style={$flexCenter}>
            <Text style={[$hintText, $hint]}>
              小狸没找到ta哦，来试试创建ta吧！
            </Text>
            <Button
              type={EButtonType.NORMAL}
              $customBtnStyle={$create}
              onPress={toCreateRole}
            >
              <View style={$flexHCenter}>
                <Text
                  style={[
                    $hintText,
                    $hint,
                    {
                      opacity: 1,
                      color: '#BB847D'
                    }
                  ]}
                >
                  自创角色
                </Text>
                <Icon
                  icon="cref_search_right"
                  size={16}
                  style={{
                    marginLeft: 4
                  }}
                />
              </View>
            </Button>
          </View>
        </EmptyPlaceHolder>
      );
    } else {
      return (
        <View
          style={[
            {
              flex: 1
            }
          ]}
        >
          <FlatList
            showsVerticalScrollIndicator={false}
            horizontal={false}
            // @ts-expect-error ignore
            data={roleList as unknown as RoleInfo}
            initialNumToRender={8}
            keyExtractor={(item, index) => String(index)}
            renderItem={renderItem}
            numColumns={3}
            // style={[$searchContainer]}
            contentContainerStyle={[
              {
                paddingBottom: Number($containerInsets.paddingBottom ?? 0)
              },
              $scrollContainerStyle
            ]}
            onEndReachedThreshold={0.5}
            onEndReached={loadMore}
            scrollEventThrottle={32}
          />
        </View>
      );
    }
  };

  return (
    <Animated.View style={[$userBg, $emptyOpacityStyle]}>
      {renderContent()}
    </Animated.View>
  );

  function toCreateByRole(role: RoleInfo) {
    loginIntercept(() => {
      reportClick('paint_button', {
        module: 'role_search',
        role_id: role.id,
        keywords
      });
      useMakePhotoStoreV2.getState().reset();
      useMakePhotoStoreV2.getState().selectRole(formatRole(role));
      router.navigate({
        pathname: '/make-photo',
        params: {
          from: 'cref-search'
        }
      });
    });
  }

  function enterRole(role: RoleInfo) {
    loginIntercept(() => {
      reportClick('character_card_button', {
        module: 'role_search',
        role_id: role.id,
        keywords
      });
      const roleInfo = formatRole(role);
      useRoleHomeStore.getState().getRoleInfos(role?.id);
      useRoleHomeStore.getState().updateRoleInfo(role?.id, roleInfo);
      router.navigate({
        pathname: '/role',
        params: {
          roleId: role?.id,
          source: RoleSource.SEARCH
        }
      });
    });
  }
}

const renderSkeleton = () => {
  return <RoleSkeleton isLarge />;
};

export default CrefSearchRoleList;
const $userBg: ViewStyle = {
  flex: 1,
  paddingHorizontal: 10
};

const $roleItem: ViewStyle = { marginRight: 8, marginBottom: 8 };

const $hintText: TextStyle = {
  color: '#FFD6CD',
  opacity: 0.4
};

const $hint: TextStyle = $USE_FONT(
  '#FFD6CD',
  typography.fonts.pingfangSC.normal,
  14,
  undefined,
  '500',
  undefined
);

const $create: ViewStyle = {
  borderWidth: 1.8,
  borderColor: '#bb847db3',
  width: 114,
  height: 37,
  marginTop: 18,
  justifyContent: 'center',
  alignItems: 'center'
};
