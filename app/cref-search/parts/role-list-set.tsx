import { router } from 'expo-router';
import { memo, useEffect, useRef, useState } from 'react';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  StyleProp,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue
} from 'react-native-reanimated';
import { searchClient } from '@/src/api/search';
import { RecSceneName } from '@/src/bizComponents/feedScreen/type';
import { Icon, Text, hideLoading, showLoading } from '@/src/components';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import Button, { EButtonType } from '@/src/components/v2/button';
import { useAuthState, useSafeAreaInsetsStyle } from '@/src/hooks';
import { rowStyle, typography } from '@/src/theme';
import { $USE_FONT, $flexCenter, $flexHCenter } from '@/src/theme/variable';
import { Pagination, RoleCreateFrom, RoleInfo } from '@/src/types';
import { reportClick, reportPage } from '@/src/utils/report';
import RoleSetCard from '../cards/roleset-card';
import { SearchRoleSetCard } from '@/proto-registry/src/web/raccoon/common/role_pb';

interface ICrefSearchRoleListSetProps {
  keywords: string;
  scrollContainerStyle?: StyleProp<ViewStyle>;
  needShowLoading?: boolean;
}

const CrefSearchRoleListSet = memo(
  ({
    keywords,
    scrollContainerStyle: $container,
    needShowLoading = true
  }: ICrefSearchRoleListSetProps) => {
    const emptyOpacity = useSharedValue(1);

    const { loginIntercept } = useAuthState();

    const $emptyOpacityStyle = useAnimatedStyle(() => ({
      opacity: emptyOpacity.value
    }));

    const [hasError, setHasError] = useState(false);

    const [isEmpty, setIsEmpty] = useState(false);

    const nextPageToken = useRef<Partial<Pagination>>({
      size: 30,
      cursor: ''
    });

    const [roleSetList, setRoleSetList] = useState<SearchRoleSetCard[]>([]);

    const resetToken = () => {
      nextPageToken.current = {
        size: 30,
        cursor: ''
      };
    };

    useEffect(() => {
      resetToken();
      setRoleSetList([]);
      setIsEmpty(false);
    }, [keywords]);

    useEffect(() => {
      if (!roleSetList.length) {
        if (!isEmpty && keywords) {
          if (needShowLoading) {
            showLoading();
          }
          loadRolesetList(keywords, true);
        }
      }
    }, [keywords]);

    const loadRolesetList = async (keyword: string, isInit?: boolean) => {
      console.log('rigger once===');
      // 没有数据了
      if (nextPageToken?.current?.nextCursor === '') {
        return;
      }

      // 先 mock roleList
      try {
        const res = await searchClient.search({
          searchSceneName: RecSceneName.CREF_SEARCH_SET,
          features: {
            type_tab: 'role'
          },
          pagination: nextPageToken.current,
          keyword: keyword
        });

        if (!res.result.length && !roleSetList.length) {
          setIsEmpty(true);
        } else {
          setIsEmpty(false);
        }

        nextPageToken.current = {
          ...nextPageToken.current,
          cursor: res.pagination?.nextCursor, // 用于下次请求
          nextCursor: res.pagination?.nextCursor
        };

        const roleSets = res.result.map(result => {
          return result.item.value as SearchRoleSetCard;
        });

        console.log(roleSets, 'rolesroles');
        if (isInit) {
          setRoleSetList(roleSets);
          reportPage('characterlist-expo', {
            module: 'role_search',
            rolecollection_id:
              roleSets?.map(i => i?.roleSet?.baseInfo?.id).join(',') ?? '',
            keywords,
            status: (roleSets?.length ? 1 : 0) + ''
          });
        } else {
          setRoleSetList([...roleSetList, ...roleSets]);
        }
        setHasError(false);
        setEndReached(false);
      } catch (e) {
        setHasError(true);
        console.log('请求roleset列表错误: ', e, {
          searchSceneName: RecSceneName.CREF_SEARCH_SET,
          features: {
            type_tab: 'role'
          },
          pagination: nextPageToken.current,
          keyword: keyword
        });
      } finally {
        hideLoading();
      }
    };

    const [endReached, setEndReached] = useState(false);

    useEffect(() => {
      if (endReached) {
        loadRolesetList(keywords);
      }
    }, [endReached]);

    const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { layoutMeasurement, contentOffset, contentSize } =
        event.nativeEvent;
      const offsetY = contentOffset.y;
      const contentHeight = contentSize.height;
      const scrollHeight = layoutMeasurement.height;

      if (scrollHeight + offsetY > contentHeight - 200) {
        setEndReached(true);
      }
    };

    const toCreateRole = () => {
      reportClick('create_character_button', {
        module: 'role_search',
        keywords,
        status: roleSetList.length ? 1 : 0
      });

      loginIntercept(() => {
        router.navigate({
          pathname: '/role-create',
          params: { from: RoleCreateFrom.RoleSelect }
        });
      });
    };

    const $containerInsets = useSafeAreaInsetsStyle(['bottom']);

    return (
      <Animated.View style={[$userBg, $emptyOpacityStyle]}>
        {hasError ? (
          <EmptyPlaceHolder
            type="crefRole"
            style={{ height: 400 }}
            buttonText="刷新"
            button={Boolean(loadRolesetList)}
            onButtonPress={() => {
              if (keywords) {
                loadRolesetList?.(keywords, true);
              }
            }}
          >
            <Text style={$hintText}>哎呀，小狸走丢了</Text>
          </EmptyPlaceHolder>
        ) : !isEmpty ? (
          <ScrollView
            onScroll={onScroll}
            style={[$searchContainer]}
            contentContainerStyle={[
              {
                height: 'auto',
                paddingBottom: Number($containerInsets.paddingBottom ?? 0)
              },
              $container
            ]}
            scrollEventThrottle={100}
            showsVerticalScrollIndicator={false}
            nestedScrollEnabled
          >
            <View style={[$roleList, { gap: 8 }]}>
              {roleSetList
                ?.filter(rs => rs?.roleInfos?.length > 0)
                .map((roleSet: SearchRoleSetCard, roleIndex) => {
                  return (
                    <View key={roleIndex} style={{ width: '100%' }}>
                      <RoleSetCard roleSet={roleSet} keywords={keywords} />
                    </View>
                  );
                })}
            </View>
          </ScrollView>
        ) : (
          <EmptyPlaceHolder type="crefRole">
            <View style={$flexCenter}>
              <Text style={[$hintText, $hint]}>
                小狸没找到ta哦，来试试创建ta吧！
              </Text>
              <Button
                type={EButtonType.NORMAL}
                $customBtnStyle={$create}
                onPress={toCreateRole}
              >
                <View style={[$flexCenter, rowStyle]}>
                  <Text
                    style={[
                      $hintText,
                      $hint,
                      {
                        opacity: 1,
                        color: '#BB847D'
                      }
                    ]}
                  >
                    自创角色
                  </Text>
                  <Icon
                    icon="cref_search_right"
                    size={16}
                    style={{
                      marginLeft: 4
                    }}
                  />
                </View>
              </Button>
            </View>
          </EmptyPlaceHolder>
        )}
      </Animated.View>
    );
  }
);

CrefSearchRoleListSet.displayName = 'CrefSearchRoleListSet';

export default CrefSearchRoleListSet;

const $userBg: ViewStyle = {
  flex: 1,
  paddingHorizontal: 8
};

const $roleList: ViewStyle = {
  flexDirection: 'row',
  flexWrap: 'wrap',
  justifyContent: 'flex-start',
  alignItems: 'center',
  width: '100%'
};

const $hintText: TextStyle = {
  color: '#FFD6CD',
  opacity: 0.4
};

const $hint: TextStyle = $USE_FONT(
  '#FFD6CD',
  typography.fonts.pingfangSC.normal,
  14,
  undefined,
  '500',
  undefined
);

const $searchContainer: ViewStyle = {};

const $create: ViewStyle = {
  borderWidth: 1.8,
  borderColor: '#bb847db3',
  width: 114,
  flexDirection: 'row',
  height: 37,
  marginTop: 18,
  justifyContent: 'center',
  alignItems: 'center'
};
