import {
  useLatest,
  useMemoizedFn,
  useMount,
  useThrottleFn,
  useUnmount
} from 'ahooks';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  Dimensions,
  InteractionManager,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  TextInput,
  TextInputKeyPressEventData,
  TouchableOpacity,
  View
} from 'react-native';
import { ShadowedView } from 'react-native-fast-shadow';
import { IOScrollView } from 'react-native-intersection-observer';
import LinearGradient from 'react-native-linear-gradient';
import { Icon, hideLoading, showLoading } from '@/src/components';
import { hideToast, showToast } from '@/src/components';
import { Image, Text } from '@/src/components';
import { AiTag } from '@/src/components/aiTag';
import AudioPlayer, { clearSounds } from '@/src/components/audioPlayer';
import {
  InputStatus,
  RenderFlatListMessagesMemo,
  useBot
} from '@/src/components/bot';
import { Animated<PERSON>iaoli } from '@/src/components/bot/components/animatedXiaoli';
import { DeepThinkButton } from '@/src/components/bot/components/deepThinkButton';
import {
  BotEvent,
  BotEventBus
} from '@/src/components/bot/components/utils/eventBus';
import { BotStatus } from '@/src/components/bot/hooks';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useSafeBottomArea } from '@/src/hooks';
import { useKeyboard } from '@/src/hooks';
import { useBotStore } from '@/src/store/bot';
import { typography } from '@/src/theme';
import { Theme } from '@/src/theme/colors/type';
import { $flexRow } from '@/src/theme/variable';
import { StyleSheet, dp2px } from '@/src/utils';
import { reportExpo } from '@/src/utils/report';
import { RecommendList } from '@Components/bot/components/recommend';
import {
  hideConfirm,
  showConfirm
} from '@Components/popup/confirmModalGlobal/Confirm';
import { Screen } from '@Components/screen';
import { useParams } from '../../src/hooks/useParams';
import { ChatMessageContent } from '@/proto-registry/src/web/raccoon/chatbot/chatbot_pb';
import { useShallow } from 'zustand/react/shallow';

const bg = require('@Assets/image/bot/bg.png');
const deepseek = require('@Assets/image/bot/deepseek.png');

const MAX_INPUT_LENGTH = 200;
const ONE_DAY_DURATION = 1000 * 60 * 60 * 24;

const { width: screenWidth } = Dimensions.get('window');
const imageBottom = 155 + (screenWidth - 32) * 0.56;

export default function Bot() {
  const [text, setText] = useState('');
  const [useDs, setUseDs] = useState(true);
  const $safePaddingBottom = useSafeBottomArea();

  const {
    taskId,
    inputStatus,
    setInputStatus,
    streamingMsgId,
    setStreamingMsgId,
    setTaskId,
    setSession,
    newSession,
    listenResponse,
    appendBotMessage,
    appendUserMessage,
    appendErrorMessage,
    interruptResponding
  } = useBot();

  const {
    messages,
    getBotStatus,
    fetchMessages,
    sendMessage,
    appendMessages,
    markMessages,
    getPrefetchedMessages,
    updateMessage,
    setState
  } = useBotStore(
    useShallow(state => ({
      messages: state.messages,
      getBotStatus: state.getBotStatus,
      fetchMessages: state.fetchMessages,
      sendMessage: state.sendMessage,
      appendMessages: state.appendMessages,
      markMessages: state.markMessages,
      getPrefetchedMessages: state.getPrefetchedMessages,
      updateMessage: state.updateMessage,
      setState: state.setState
    }))
  );

  const streamingBotStatus = useMemo(() => {
    if (!streamingMsgId || !messages?.length) {
      return BotStatus.Finished;
    }
    const currentMsg = messages.find(msg => msg.msgId === streamingMsgId);
    return currentMsg?.botStatus || BotStatus.Finished;
  }, [streamingMsgId, messages]);

  const showKeyboard = useKeyboard();

  const inputStatusMemo = useMemo(() => {
    return {
      typing: taskId ? false : inputStatus === InputStatus.Typing,
      idle: taskId ? false : inputStatus === InputStatus.Idle,
      pending: !!taskId
    };
  }, [inputStatus, taskId]);

  const mountRef = useRef(false);
  const nextPageTokenRef = useRef<string | undefined>(undefined);
  const lastoffestYRef = useRef(0);
  const isReadingHistoryRef = useRef(false);
  const needScrollToBottom = useRef(false);
  const manualRef = useRef(false);
  // const isAdjustingScrollRef = useRef(false);

  const loadingRef = useRef(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const latestTaskId = useLatest(taskId);
  const inputRef = useRef<TextInput>(null);
  const [inputHeight, setInputHeight] = useState(0);
  const [loading, setLoading] = useState(true);
  const [animatedXiaoLiVisible, setAnimatedXiaoLiVisible] = useState(false);

  const {
    source
  }: {
    source?: string;
  } = useParams<{ source: string }>();

  useMount(async () => {
    try {
      showLoading();
      const [messageResponse, botStatus] = await Promise.all([
        loadMessages(),
        getBotStatus()
      ]);
      const messages = messageResponse || [];
      //   用户打开窗口距离上次聊天最后一条消息>=24h，开启新话题
      const expired =
        new Date().getTime() -
          Number(
            messages[messages.length - 1]?.createdAt || new Date().getTime()
          ) >=
        ONE_DAY_DURATION;
      setSession(expired);

      const { currentRespondingTaskId = '', userMessage } = botStatus || {};
      if (userMessage) {
        appendUserMessage(userMessage);
      }
      setTaskId(currentRespondingTaskId);
    } catch (error) {
      showToast('获取历史消失失败，请稍后再试哦~');
    } finally {
      setLoading(false);
      hideLoading();
    }
    reportExpo(
      'expo',
      {
        source,
        module: 'libot-page'
      },
      true
    );
  });

  useEffect(() => {
    BotEventBus.on(
      BotEvent.SCROLL_TO_NEXT_LINE,
      ({ height }: { height: number }) => {
        if (!manualRef.current) {
          scrollViewRef.current?.scrollToEnd({
            animated: true
          });
        }
      }
    );
    return () => {
      BotEventBus.off(BotEvent.SCROLL_TO_NEXT_LINE);
    };
  }, []);

  useUnmount(() => {
    useBotStore.getState().reset();
    batchMarkMessages('', true);
    hideToast();
    hideConfirm();
    clearTtsAudioInstance();
  });

  useEffect(() => {
    if (taskId) {
      listen();
    }
  }, [taskId]);

  const listen = useMemoizedFn(async () => {
    const { msgId } = appendBotMessage('');
    try {
      setStreamingMsgId(msgId);
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd();
      }, 100);
      await listenResponse(msgId);
    } catch (error) {
      setTaskId('');
      setStreamingMsgId('');
      // @ts-ignore
      if (error.code === 10005) {
        showToast('检测到敏感词汇，请重新输入哦~');
        setState({
          messages: [...messages.slice(0, -2)]
        });
      } else {
        updateMessage(msgId, {
          botStatus: BotStatus.Error,
          content: new ChatMessageContent({
            content: '小狸在开小差，无法回答你的问题... '
          })
        });
      }
    }
  });

  useEffect(() => {
    // 首次获取数据后，滚动到底部
    if (!mountRef.current && messages.length) {
      mountRef.current = true;
      setTimeout(() => {
        // infiniteListRef.current?.scrollToEnd();
        scrollViewRef.current?.scrollToEnd();
      }, 100);
    }
  }, [messages]);

  useEffect(() => {
    if (!text) {
      setInputStatus(InputStatus.Idle);
    }
  }, [text]);

  const clearTtsAudioInstance = () => {
    if (!AudioPlayer.instances) return;
    for (const [scope, instance] of Object.entries(AudioPlayer.instances)) {
      const sounds = instance?.sounds || [];
      if (sounds.length) {
        clearSounds(sounds);
      }
      AudioPlayer.instances[scope] = null;
    }
  };

  const oldContentHeightRef = useRef(0);
  const newContentHeightRef = useRef(0);

  const loadMessages = useMemoizedFn(
    async (appended?: boolean, offsetY?: number) => {
      try {
        if (
          loadingRef.current ||
          (appended && !nextPageTokenRef.current)
          // ||
          // isAdjustingScrollRef.current
        )
          return;

        loadingRef.current = true;
        oldContentHeightRef.current = newContentHeightRef.current;
        const res = await (appended
          ? fetchMessages({
              nextPageToken: nextPageTokenRef.current
            })
          : getPrefetchedMessages());
        const { messages = [], nextPageToken = '' } = res || {};
        nextPageTokenRef.current = nextPageToken;
        const sortMessages = appendMessages(messages, appended);
        return sortMessages;
      } catch (error) {
        throw error;
      } finally {
        // console.error('结束');
        loadingRef.current = false;
      }
    }
  );

  const onChangeText = useMemoizedFn((currentText: string) => {
    setText(currentText);
    if (currentText.length) {
      setInputStatus(InputStatus.Typing);
    }
  });

  const onKeyPress = ({
    nativeEvent: { key }
  }: NativeSyntheticEvent<TextInputKeyPressEventData>) => {
    if (key === 'Enter') {
      onSubmitThrottle();
    } else if (key !== 'Backspace' && text.length >= MAX_INPUT_LENGTH) {
      showToast(`最多输入${MAX_INPUT_LENGTH}字~`);
    }
  };

  const createChat = useMemoizedFn(() => {
    inputRef.current?.blur();
    showConfirm({
      modalStyle: {
        width: dp2px(244)
      },
      confirmText: '确认',
      cancelText: '取消',
      theme: Theme.DARK,
      content: <Text style={style.$createText}>确定开启新话题吗？</Text>,
      maskClose: true,
      onConfirm: ({ close }) => {
        setSession(true);
        useBotStore.getState().appendNewTopic();
        close();
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({
            animated: true
          });
        }, 200);
      }
    });
  });

  const onSubmit = useMemoizedFn(async () => {
    hideToast();
    if (!text.trim()) {
      showToast(<Text style={style.toastText}>请输入文字和我聊天哦~</Text>);
      return;
    }
    if (inputStatusMemo.pending) {
      showToast(
        <View
          style={{
            alignItems: 'center'
          }}
        >
          <Text style={style.toastText}>小狸正在输出中哦～</Text>
          <Text style={style.toastText}>请稍后操作或点击停止回答</Text>
        </View>
      );
      return;
    }
    appendUserMessage(text);
    try {
      manualRef.current = false;
      const res = await sendMessage({
        msg: text,
        newSession: newSession.current,
        useDs: useDs
      });
      setTaskId(res?.taskId || '');
    } catch (error) {
      appendErrorMessage('小狸在开小差，无法回答你的问题... ');
    } finally {
      setSession(false);
      setText('');
    }
  });

  const { run: onSubmitThrottle } = useThrottleFn(onSubmit, {
    wait: 3000
  });

  const onScroll = useMemoizedFn(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { contentOffset, contentSize } = e.nativeEvent;
      const offsetY = contentOffset.y;
      const contentHeight = contentSize.height;
      const scrollUp = lastoffestYRef.current > offsetY;
      if (nextPageTokenRef.current && scrollUp && offsetY < 800) {
        throttleLoadMessages(true, offsetY);
      }
      // 标识用户开始阅读历史消息(向上滚动)
      isReadingHistoryRef.current = scrollUp;
      needScrollToBottom.current = contentHeight - offsetY < 1000;
      lastoffestYRef.current = offsetY;
      if (scrollUp) {
        manualRef.current = true;
      }
    }
  );

  const { run: throttleLoadMessages } = useThrottleFn(loadMessages, {
    wait: 1000,
    trailing: false
  });

  const msgIdRef = useRef<string[]>([]);

  const batchMarkMessages = useMemoizedFn(
    (msgId?: string, immediate?: boolean) => {
      if (msgId) {
        msgIdRef.current.push(msgId);
      }
      if (
        msgIdRef.current.length >= 5 ||
        (immediate && msgIdRef.current.length)
      ) {
        markMessages(msgIdRef.current);
        msgIdRef.current = [];
      }
    }
  );

  const InputIconMemo = useMemo(() => {
    const { idle, pending, typing } = inputStatusMemo;
    return (
      <View style={style.$inputBtnsContainer}>
        {idle && (
          <TouchableOpacity
            onPressIn={() => {
              inputRef.current?.focus();
            }}
          >
            <Icon
              icon="comment_keyboard"
              style={[{ tintColor: StyleSheet.darkColors.white[500] }]}
            />
          </TouchableOpacity>
        )}
        {typing && (
          <TouchableOpacity onPressIn={onSubmitThrottle}>
            <Icon icon="makephoto_submit" size={24} />
          </TouchableOpacity>
        )}
        {pending && (
          <TouchableOpacity
            onPressIn={() => {
              const taskId = latestTaskId.current;
              interruptResponding(taskId);
            }}
          >
            <Icon
              icon="bot_pause"
              style={{
                tintColor: 'rgba(255, 106, 59, 1)'
              }}
              size={24}
            />
          </TouchableOpacity>
        )}
      </View>
    );
  }, [inputStatusMemo]);

  const MessageContent = useMemo(() => {
    if (loading) {
      return null;
    }
    if (messages.length === 0) {
      return (
        <RecommendList
          onPress={text => {
            onChangeText(text);
          }}
        />
      );
    }
    return messages.map((data, index) => (
      <RenderFlatListMessagesMemo
        onRead={batchMarkMessages}
        message={data}
        index={index}
        key={data.createdAt}
      />
    ));
  }, [loading, messages]);

  return (
    <PagePerformance pathname="bot/index">
      <Screen
        theme="dark"
        withWaterMark
        preset="fixed"
        maskAreaShown={false}
        safeAreaEdges={['bottom', 'top']}
        wholePageStyle={{
          backgroundColor: StyleSheet.darkColors.gray[22]
        }}
        backButton={true}
        headerRight={() =>
          messages.length > 0 && (
            <TouchableOpacity onPressIn={createChat}>
              <Icon icon="bot_chat" size={24} style={style.$headerIcon} />
            </TouchableOpacity>
          )
        }
        headerLeft={() => (
          <View style={[$flexRow, style.$headerLeft]}>
            <Text style={style.$headerText}>小狸</Text>
            <View style={style.$deepseekWrap}>
              <Image
                source={deepseek}
                contentFit="contain"
                style={style.$deepseek}
              />
            </View>
          </View>
        )}
        leftStyle={{
          width: 60
        }}
        backgroundView={
          <Image style={[style.$bg]} source={bg} contentFit="cover" />
        }
      >
        <IOScrollView
          bounces={false}
          ref={scrollViewRef}
          contentContainerStyle={[style.$pageContainer]}
          indicatorStyle="black"
          showsVerticalScrollIndicator={true}
          keyboardShouldPersistTaps="handled"
          scrollEventThrottle={200}
          onTouchStart={() => {
            inputRef.current?.blur();
          }}
          onContentSizeChange={(width, height) => {
            if (
              oldContentHeightRef.current > 0 &&
              lastoffestYRef.current < 800
            ) {
              const heightDiff = height - oldContentHeightRef.current;
              // 调整滚动位置
              scrollViewRef.current?.scrollTo({
                y: lastoffestYRef.current + heightDiff,
                animated: false
              });
            }
            newContentHeightRef.current = height;
          }}
          onScroll={onScroll}
        >
          {/* 推荐词条列表 */}
          {MessageContent}
          <View style={{ height: $safePaddingBottom + 85, zIndex: -1 }} />
        </IOScrollView>

        <ShadowedView
          style={[style.$inputWrapper]}
          onLayout={e => {
            setInputHeight(e.nativeEvent.layout.height);
            InteractionManager.runAfterInteractions(() => {
              setAnimatedXiaoLiVisible(true);
            });
          }}
        >
          <TextInput
            ref={inputRef}
            style={[style.$input]}
            value={text}
            selectionColor="#FF6A3B"
            returnKeyType="send"
            returnKeyLabel="发送"
            placeholderTextColor={StyleSheet.darkColors.white[300]}
            placeholder="与我聊聊你喜欢的角色吧~"
            multiline
            maxLength={MAX_INPUT_LENGTH}
            onKeyPress={onKeyPress}
            onChangeText={onChangeText}
            onSubmitEditing={onSubmitThrottle}
            onFocus={() => {
              //滚动到底部
              if (needScrollToBottom.current) {
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd();
                }, 100);
              }
            }}
          />
          {InputIconMemo}
        </ShadowedView>

        <LinearGradient
          colors={[
            StyleSheet.hex(StyleSheet.darkColors.gray[22], 0),
            StyleSheet.hex(StyleSheet.darkColors.gray[22], 1),
            StyleSheet.hex(StyleSheet.darkColors.gray[22], 1)
          ]}
          locations={[0, 0.25, 1]}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={{
            height: $safePaddingBottom + 85,
            zIndex: 0,
            position: 'absolute',
            left: 0,
            right: 0,
            bottom: 0
          }}
        />

        {animatedXiaoLiVisible && (
          <>
            <AnimatedXiaoli
              // 为什么要写两个组件呢？ 为了防止布局抖动
              style={[!useDs && { display: 'none' }]}
              botStatus={streamingBotStatus}
              inputHeight={inputHeight}
              useDs={true}
            />

            <AnimatedXiaoli
              style={[useDs && { display: 'none' }]}
              botStatus={streamingBotStatus}
              inputHeight={inputHeight}
              useDs={false}
            />
            <DeepThinkButton
              active={useDs}
              style={{
                position: 'absolute',
                right: 29,
                bottom: $safePaddingBottom + inputHeight - 12,
                zIndex: 1
              }}
              onPress={() => {
                setUseDs(!useDs);
              }}
            />
          </>
        )}
      </Screen>
    </PagePerformance>
  );
}

const style = StyleSheet.create({
  $bg: {
    position: 'absolute',
    left: 0,
    top: 0,
    right: 0,
    height: 120
  },
  $headerLeft: {
    gap: 8,
    alignItems: 'center'
  },
  $headerText: {
    fontSize: 18,
    lineHeight: 27,
    fontFamily: typography.fonts.feed,
    fontWeight: '400',
    color: StyleSheet.darkColors.white[1000]
  },
  $deepseekWrap: {
    backgroundColor: 'rgba(13, 19, 50, 0.49)',
    borderRadius: 99,
    paddingHorizontal: 7,
    paddingVertical: 3,
    top: 0.5,
    borderColor: 'rgba(44, 53, 99, 1)',
    borderWidth: 1
  },
  $deepseek: {
    width: 49,
    height: 10
  },
  $headerIcon: {
    tintColor: StyleSheet.darkColors.white[1000]
  },
  $pageContainer: {
    paddingHorizontal: 16
  },
  $botFish: {
    position: 'absolute',
    top: 100,
    left: 16,
    right: 16,
    bottom: imageBottom,
    height: 200
  },
  $messageWrap: {
    paddingHorizontal: 16
  },
  $inputWrapper: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 12,
    left: 12,
    right: 12,
    borderRadius: 60,
    backgroundColor: StyleSheet.darkColors.gray[35],
    borderColor: StyleSheet.darkColors.gray[54],
    borderWidth: 0.5,
    height: 'auto',
    paddingRight: 13,
    paddingLeft: 20,
    paddingVertical: 14,
    alignItems: 'center',
    gap: 16,
    zIndex: 1
  },
  $input: {
    fontSize: Platform.OS === 'ios' ? 15 : 14,
    lineHeight: 20,
    color: StyleSheet.darkColors.white[1000],
    height: 'auto',
    minHeight: 28,
    maxHeight: 80,
    fontWeight: '400',
    flex: 1,
    top: -1
  },
  $inputBtnsContainer: {},
  $createText: {
    fontFamily: typography.fonts.pingfangSC.normal,
    fontSize: 15,
    lineHeight: 21,
    fontWeight: '600',
    textAlign: 'center',
    color: StyleSheet.darkColors.white[900]
  },
  toastText: {
    fontSize: 14,
    lineHeight: 19.6,
    fontWeight: '600',
    color: StyleSheet.darkColors.white[900]
  }
});
