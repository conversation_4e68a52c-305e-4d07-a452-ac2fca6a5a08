import { router } from 'expo-router';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import PagerView from 'react-native-pager-view';
import { feedClient } from '@/src/api';
import { Icon, Screen } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { Tabs } from '@/src/components/tabs';
import { useSafeAreaInsetsStyle } from '@/src/hooks';
import { useAuthStore } from '@/src/store/authInfo';
import { darkColors, darkTheme } from '@/src/theme';
import { getThemeColorV2 } from '@/src/theme/colors/common';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { log } from '@/src/utils/logger';
import { reportExpo } from '@/src/utils/report';
import { StyleSheet } from '@Utils/StyleSheet';
import { useParams } from '../../src/hooks/useParams';
import { Theme } from '../../src/theme/colors/type';
import { useShallow } from 'zustand/react/shallow';
import { FansList, FollowList } from './components';

const items = [
  {
    key: 'follow',
    title: '关注',
    label: '关注'
  },
  {
    key: 'fans',
    title: '粉丝',
    label: '粉丝'
  }
];
export default (props: { theme: 'light' | 'dark' } = { theme: 'dark' }) => {
  const themeColor = getThemeColorV2(
    props.theme === 'light' ? Theme.LIGHT : Theme.DARK
  );
  const { defaultTab = 'follow', uid } = useParams();

  const $containerInsets = useSafeAreaInsetsStyle(['top', 'bottom']);
  const pagerRef = useRef<PagerView>(null);
  const [current, setCurrentTab] = useState<string>(defaultTab as string);
  const [stat, setStat] = useState<{ fans: number; followings: number }>({
    fans: 0,
    followings: 0
  });

  const { uid: mineUid } = useAuthStore(
    useShallow(state => ({
      uid: state.uid
    }))
  );
  const isMine = useMemo(() => mineUid === uid, [mineUid, uid]);

  useEffect(() => {
    feedClient
      .userSocialInfo({ uid: uid as string })
      .then(res => {
        if (res.stat) {
          setStat({
            fans: Number(res.stat.fans),
            followings: Number(res.stat.followings)
          });
        }
      })
      .catch(e => {
        errorReport('userSocialInfo_error', ReportError.FOLLOW_FAN, e, {
          params: { uid }
        });
      });
  }, [uid, current]);

  const onChangeTab = (key: string) => {
    setCurrentTab(key);
    pagerRef.current?.setPage(key === 'follow' ? 0 : 1);
  };

  useEffect(() => {
    reportExpo(current, {
      module: 'user',
      identity_status: isMine ? '0' : '1'
    });
  }, [current, isMine]);

  const getContent = () => {
    return (
      <PagerView
        initialPage={defaultTab === 'follow' ? 0 : 1}
        ref={pagerRef}
        style={{ flex: 1 }}
        onPageSelected={e => {
          setCurrentTab(e.nativeEvent.position === 0 ? 'follow' : 'fans');
        }}
      >
        <View style={{ flex: 1 }}>
          <FollowList
            uid={uid as string}
            current={current}
            count={stat.followings}
            isMine={isMine}
          />
        </View>
        <View style={{ flex: 1 }}>
          <FansList
            uid={uid as string}
            current={current}
            count={stat.fans}
            isMine={isMine}
          />
        </View>
      </PagerView>
    );
  };

  return (
    <PagePerformance pathname="follow-fan/index">
      <Screen
        safeAreaEdges={[]}
        theme={props.theme}
        StatusBarProps={{ style: props.theme === 'light' ? 'dark' : 'light' }}
        headerShown={false}
        style={{ backgroundColor: themeColor.background.page }}
      >
        <View style={[{ marginTop: $containerInsets.paddingTop }, st.header]}>
          <TouchableOpacity
            style={st.back}
            onPress={() => {
              router.back();
            }}
          >
            <Icon icon="back" color={themeColor.text.primary}></Icon>
          </TouchableOpacity>
          {/* <Animated.View style={[]}> */}
          <Tabs
            items={items}
            current={current}
            itemStyle={tabStyles.$tabItemStyle}
            onPressTab={(_, key) => onChangeTab(key!)}
            tabBarStyle={tabStyles.$tabStyle}
            itemTextStyle={tabStyles.$tabItemTextStyle}
            tabWidth={60}
          />
          {getContent()}
          {/* </Animated.View> */}
        </View>
      </Screen>
    </PagePerformance>
  );
};

const st = StyleSheet.create({
  header: {
    position: 'relative',
    flex: 1
  },
  back: {
    position: 'absolute',
    left: 12,
    top: 0,
    height: 44,
    justifyContent: 'center',
    zIndex: 10
  }
});

const tabStyles = StyleSheet.create({
  $tabStyle: {
    ...StyleSheet.rowStyle,
    // height: 44,
    width: '100%',
    justifyContent: 'center'
  },
  $tabItemStyle: {
    flex: 0,
    width: 60
  },
  $tabItemTextStyle: {
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 26
  },
  $tabActiveStyle: {
    color: '#222222'
  },
  $tabActiveBorder: {
    ...StyleSheet.rowStyle,
    position: 'absolute',
    width: '100%',
    height: '100%',
    bottom: -16,
    justifyContent: 'center'
  }
});
