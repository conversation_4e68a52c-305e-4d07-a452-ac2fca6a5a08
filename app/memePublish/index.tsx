import { memo, useEffect, useMemo } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { BackGroundView } from '@/src/components/image/BackgroundImage';
import { EditPageState, useMakePhotoEdit } from '@/src/store/makePhotoEdit';
import { PhotoRealSizeMap, PhotoSize } from '@/src/store/makePhotoV2';
import {
  MemeDefaultRatio,
  MemePhotoSizeMap,
  screenWidth,
  useMemeStore
} from '@/src/store/meme';
import { $Z_INDEXES } from '@/src/theme/variable';
import { PlainType } from '@/src/types';
import { reportExpo } from '@/src/utils/report';
import { StyleSheet } from '@Utils/StyleSheet';
import { PhotoProgress } from '@/proto-registry/src/web/raccoon/meme/meme_pb';
import Edit from '@BizComponents/playgoundScreen/meme/MemeEdit';
import Publish from '@BizComponents/playgoundScreen/meme/MemePublish';
import { PartialMessage } from '@bufbuild/protobuf';
import { useShallow } from 'zustand/react/shallow';

const BG_IMG = require('@Assets/playground/meme-publish-bg.png');

const MemePublish = memo(() => {
  const { editPageState } = useMakePhotoEdit(
    useShallow(state => ({
      editPageState: state.editPageState
    }))
  );

  const { photo } = useMemeStore(
    useShallow(state => ({
      photo: state.currentPhoto as PlainType<PartialMessage<PhotoProgress>>
    }))
  );

  const { photoHeight, photoWidth } = useMemo(() => {
    const photoInfo = MemePhotoSizeMap.get(
      PhotoRealSizeMap.get(photo.size as string) || PhotoSize.Default
    );

    return {
      photoHeight: photoInfo?.height || screenWidth * 0.76 * MemeDefaultRatio,
      photoWidth: photoInfo?.width || screenWidth
    };
  }, [photo.size, screenWidth]);

  useEffect(() => {
    return () => {
      useMakePhotoEdit.getState().reset();
    };
  }, []);

  useEffect(() => {
    reportExpo('publish', { module: 'publish' });
  }, []);

  const opacity = useSharedValue(0);

  useEffect(() => {
    if (editPageState === EditPageState.editing) {
      opacity.value = withTiming(1, { duration: 200, easing: Easing.linear });
    } else {
      opacity.value = withTiming(0, { duration: 200, easing: Easing.linear });
    }
  }, [editPageState]);

  const editOpacityStyle = useAnimatedStyle(() => ({
    opacity: opacity.value
  }));

  const publishOpcacityStyle = useAnimatedStyle(() => ({
    opacity: 1 - opacity.value
  }));

  const editMode = editPageState === EditPageState.editing;

  const publishStyle: StyleProp<ViewStyle> = [
    StyleSheet.absoluteFill,
    editMode && {
      pointerEvents: 'none'
    },
    { zIndex: editMode ? -1 * $Z_INDEXES.z20 : 1 * $Z_INDEXES.z20 },
    publishOpcacityStyle
  ];

  const editStyle: StyleProp<ViewStyle> = [
    StyleSheet.absoluteFill,
    { zIndex: editMode ? $Z_INDEXES.z20 : -1 * $Z_INDEXES.z20 },
    !editMode && {
      pointerEvents: 'none'
    },
    editOpacityStyle
  ];

  const photoProps = {
    photoWidth,
    photoHeight
  };

  return (
    <PagePerformance pathname="memePublish/index">
      <BackGroundView image={BG_IMG}>
        <>
          <Animated.View style={editStyle}>
            <Edit {...photoProps} />
          </Animated.View>

          <Animated.View style={publishStyle}>
            <Publish {...photoProps} />
          </Animated.View>
        </>
      </BackGroundView>
    </PagePerformance>
  );
});

MemePublish.displayName = 'MemePublish';

export default MemePublish;
