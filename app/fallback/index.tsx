import { SplashScreen, router, useNavigation, usePathname } from 'expo-router';
import { useEffect } from 'react';
import { TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { Image, ImageStyle, Text } from '@/src/components';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { useAppUpdateInfo } from '@/src/hooks/useCheckUpdate';
import { useAppStore } from '@/src/store/app';
import { centerStyle } from '@/src/theme';
import { CommonColor } from '@/src/theme/colors/common';
import { EmptyPlaceHolder } from '@Components/Empty';
import { Screen } from '@Components/screen';
import { CommonActions } from '@react-navigation/native';

const TipImage = require('@Assets/empty/userProductDark.png');

const $img: ImageStyle = {
  width: 308,
  height: 144,
  resizeMode: 'contain'
};

const $tip: TextStyle = {
  marginTop: 12,
  fontSize: 13,
  color: CommonColor.textGray,
  lineHeight: 18
};

const $text: TextStyle = {
  fontSize: 13,
  color: '#FFFFFF',
  lineHeight: 18
};

const $btn: ViewStyle = {
  marginTop: 18,
  width: 110,
  height: 36,
  borderRadius: 36,
  backgroundColor: '#FF6A3B',
  ...centerStyle
};

export default function UpdatePage() {
  const checkUpdate = useAppStore(state => state.checkUpdate);
  const { goToUpdate } = useAppUpdateInfo();
  const handleClick = () => {
    checkUpdate().then(res => {
      console.log('=====res========', res);
      goToUpdate();
    });
  };

  const navigation = useNavigation();
  const pathName = usePathname();

  useEffect(() => {
    if (/^\/a\//.test(pathName)) {
      resetRoute();
    }
  }, [pathName]);

  return (
    <PagePerformance pathname="fallback/index">
      <Screen
        title=""
        screenStyle={{
          backgroundColor: CommonColor.white
        }}
        headerStyle={{
          borderBottomColor: 'rgba(210,210,210,1)',
          borderBottomWidth: 0.2
        }}
      >
        <View style={{ width: '100%', height: '100%', ...centerStyle }}>
          <View style={centerStyle}>
            <Image style={$img} source={TipImage} />
            <Text style={$tip}>请更新后体验全新功能！</Text>

            <TouchableOpacity style={$btn} onPress={handleClick}>
              <Text style={$text}>立即更新</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Screen>
    </PagePerformance>
  );

  function resetRoute() {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: 'feed/index', params: { cardId: '1' } }]
      })
    );
  }
}
