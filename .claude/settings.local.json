{"permissions": {"allow": ["Bash(git -C /Users/<USER>/dev/pr-agent status)", "Bash(git -C /Users/<USER>/dev/pr-agent remote -v)", "Bash(git -C /Users/<USER>/dev/pr-agent checkout main)", "Bash(git -C /Users/<USER>/dev/pr-agent pull)", "Bash(git -C /Users/<USER>/dev/pr-agent merge feat/add-mr-report-generator)", "Bash(git -C /Users/<USER>/dev/pr-agent remote rename origin upstream)", "Bash(git -C /Users/<USER>/dev/pr-agent remote <NAME_EMAIL>:fuxiao/pr-agent.git)", "Bash(ls:*)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(node:*)"], "deny": []}}