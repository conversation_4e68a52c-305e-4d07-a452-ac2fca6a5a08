import { collapsePublish } from '.';
import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import {
  memo,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {
  LayoutChangeEvent,
  Pressable,
  StyleProp,
  TextInput,
  View,
  ViewStyle
} from 'react-native';
import { createPkTheme } from '@/src/api/pk';
import {
  Icon,
  IconTypes,
  hideLoading,
  showLoading,
  showToast
} from '@/src/components';
import { AlbumFromType } from '@/src/components/album/const';
import {
  AfterPublishToastEnum,
  afterPublishToastPrioritizer
} from '@/src/components/popup/prioritize';
import Button, { EButtonType } from '@/src/components/v2/button';
import { showMessage } from '@/src/components/v2/systemMessage';
import { useKeyboardCompatibleAndroid } from '@/src/hooks';
import { useOneRunning } from '@/src/hooks/useOneRunning';
import { usePerformanceStore } from '@/src/store/performance';
import {
  transformMediaToPkImage,
  useCreatePkNominateStore
} from '@/src/store/pk/pkNominate';
import { usePublishStore } from '@/src/store/publish';
import { usePublishGlobalStore } from '@/src/store/publishGlobal';
import { SetIps, SetTopics } from '@/src/store/publishGlobal';
import { SPECIAL_TOPIC, useTopicStore } from '@/src/store/topic';
import { getThemeColor } from '@/src/theme/colors/common';
import { Theme } from '@/src/theme/colors/type';
import { GameType } from '@/src/types';
import { MediaPhotosItem } from '@/src/types';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { hasDuplicate } from '@/src/utils/opt/array';
import { reportClick, reportExpo } from '@/src/utils/report';
import { To } from '@/src/utils/to';
import { Text } from '@Components/text';
import { StyleSheet } from '@Utils/StyleSheet';
import { CommonEventBus } from '../../utils/event';
import { initPKNominateSelector } from '../pkNominateScreen/_utils/initPKNominateSelector';
import { toastPKPublishResult } from '../pkScreen/utilts/publishToast';
import { useTopicSelect } from '../topicSelector/topicSelect.hook';
import { AddRoleCard } from './components/addRoleCard';
import { AvatarInput } from './components/avatarInput';
import { ImagePicker } from './components/imagePicker';
import { Topic } from './components/topic';
import {
  PkCensorState,
  PkImage
} from '@/proto-registry/src/web/raccoon/pk/pk_pb';
import { CommonMediaType } from '@step.ai/proto-gen/raccoon/common/assets_pb';
import { useShallow } from 'zustand/react/shallow';
import { BbsPublishTabEnum } from './types';

export interface PublishProps {
  theme?: Theme;
  publishBtnText?: string;
  keyboardHeight: number;
  style?: StyleProp<ViewStyle>;
  active?: boolean;
  onClose?: () => void;
  onPublishCallback?: ({ cardId }: { cardId: string }) => void;
  onLayout?: (event: LayoutChangeEvent) => void;
}

export const PkPublish = memo((props: PublishProps) => {
  const {
    theme,
    publishBtnText = '发布',
    keyboardHeight,
    style,
    active,
    onClose,
    onPublishCallback,
    onLayout
  } = props;
  const [operationVisible, setOperationVisible] = useState(false);
  const { showKeyboard, onInputFocus, onInputBlur } =
    useKeyboardCompatibleAndroid();

  const getRecommendParams = useMemoizedFn(() => ({
    gameId: pkImages?.map(i => i.pkObjId),
    gameType: GameType.PK
  }));

  const {
    selectedIps: pkIps,
    selectedTopics: pkTopics,
    handleSelectTopic,
    setSelectedTopics,
    handleInitTopic,
    handleSelectIp
  } = useTopicSelect({
    defaultFold: false,
    max: 5,
    getRecommendParams
    // setSelectedTopics: setSelectedTopics,
    // setSelectedIps: setSelectedIps,
  });

  const activeTime = useRef<number>();

  const {
    // pkImages,
    setPkImages,
    resetPkNominate
  } = useCreatePkNominateStore(state => ({
    pkImages: state.pkImages,
    setPkImages: state.setPkImages,
    resetPkNominate: state.reset
  }));

  const {
    // pkTopics,
    pkImages,
    // pkIps,
    setState,
    reset,
    pkText
  } = usePublishGlobalStore(
    useShallow(state => ({
      pkTopics: state.pkTopics,
      pkIps: state.pkIps,
      pkImages: state.pkImages,
      setState: state.setState,
      reset: state.reset,
      pkText: state.pkText
    }))
  );

  const { reset: resetPublishStore } = usePublishStore(
    useShallow(state => ({
      reset: state.reset
    }))
  );

  const [showAlbum, setShowAlbum] = useState(false);

  const afterPublish = () => {
    reset();
    resetPkNominate();
    resetPublishStore();
    onClose?.();
  };

  const textInputRef = useRef<TextInput>(null);

  const [visibleMap, setVisibleMap] = useState<Record<string, boolean>>({
    topic: false,
    vote: false,
    image: false
  });

  const hasDuplicateName = hasDuplicate(pkImages, item => item?.name);

  const disableSubmit = !pkText || pkImages.length < 2 || hasDuplicateName;

  useEffect(() => {
    CommonEventBus.on('globalPublishTabChanged', ({ key }) => {
      if (key === BbsPublishTabEnum.PK) {
        textInputRef.current?.focus();
      }
    });
  }, []);

  useEffect(() => {
    if (showKeyboard) {
      setOperationVisible(false);
    }
  }, [showKeyboard]);

  useEffect(() => {
    if (active) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 200);
      activeTime.current = new Date().getTime();
    }
  }, [active]);

  const FoldTopic = useMemo(() => {
    return (
      <View style={{ paddingLeft: 4 }}>
        <Topic
          gameType={GameType.PK}
          isFold={true}
          selectedTopics={pkTopics}
          // defaultTopics={defaultTopics}
          type={SPECIAL_TOPIC.PK}
          selectedIps={pkIps}
          onTopicInit={handleInitTopic}
          setSelectedTopics={handleSelectTopic}
          setSelectedIps={handleSelectIp}
        />
      </View>
    );
  }, [pkTopics, pkIps, showKeyboard]);

  const UnfoldTopic = useMemo(() => {
    return (
      <Topic
        isFold={false}
        selectedTopics={pkTopics}
        gameType={GameType.PK}
        onTopicInit={handleInitTopic}
        type={SPECIAL_TOPIC.PK}
        selectedIps={pkIps}
        setSelectedTopics={handleSelectTopic}
        setSelectedIps={handleSelectIp}
      />
    );
  }, [pkTopics, pkIps]);

  const $publishNewPk = useOneRunning(async () => {
    reportClick('publish_button', {
      module: 'bbs_pk',
      staytime: activeTime.current
        ? Date.now() - activeTime.current
        : 'unknown',
      tag_name: pkTopics.map(item => item.title).join(','),
      pic_local_num: pkImages.filter(
        item => item.image?.mediaType === CommonMediaType.USER_IMAGE
      ).length,
      pic_num: pkImages.length,
      pic_name: pkImages.map(item => item.name).join(',')
    });
    if (!pkText.trim()) {
      showToast('请输入PK主题哦~');
      return;
    }
    if (pkImages.length < 2) {
      showToast('请至少选择2张图片哦~');
      return;
    }

    if (hasDuplicateName) {
      showToast('存在名称重复的角色哦~');
      return;
    }

    showLoading();

    const [error, res] = await To(
      createPkTheme({
        name: pkText,
        pkObjs: pkImages,
        topics: pkTopics,
        brandips: pkIps.map(ip => ip.brand.toString())
      })
    );
    hideLoading();
    if (!error) {
      toastPKPublishResult({
        censorState: res.censorState,
        passName: res.passName,
        pkImages
      });

      reportExpo('publish_success', {
        module: 'bbs_pk',
        staytime: activeTime.current
          ? Date.now() - activeTime.current
          : 'unknown',
        tag_name: pkTopics.map(item => item.title).join(','),
        pic_local_num: pkImages.filter(
          item => item.image?.mediaType === CommonMediaType.USER_IMAGE
        ).length,
        pic_num: pkImages.length,
        pic_name: pkImages.map(item => item.name).join(',')
      });
      reportExpo('publish_success', {
        module: 'publish',
        sourceid: res.cardId,
        game_type: '40'
      });

      // 入库成功才会返回cardId
      if (res.censorState === PkCensorState.PASS) {
        afterPublishToastPrioritizer(
          showMessage,
          AfterPublishToastEnum.publishEnd
        )({
          type: 'link',
          coverImgUrl:
            'https://resource.lipuhome.com/resource/img/prod/20250120/cd00fd8530d3dcd5d219ce9c7d7f236a.png',
          title: '发布成功',
          position: 'bottom',
          actionText: '查看PK详情',
          imageStyle: {
            width: 20,
            height: 20
          },
          duration: 4000,
          onClick: ({ onClose: onCloseMessage, onNavigate }) => {
            onCloseMessage?.();
            if (res?.cardId) {
              onNavigate.go2Detail({
                gameType: GameType.PK,
                gameParams: {
                  cardId: res.cardId
                }
              });
            }
          }
        });

        afterPublish();
        onPublishCallback?.({ cardId: res.cardId });
        CommonEventBus.emit('cardPublished');
      }
    } else {
      console.error(error, 'error');
      errorReport('pk_publish_error', ReportError.PUBLISH, error);
      showToast('发起pk失败，请稍后再试');
    }
  });

  const handleChangeText = useMemoizedFn((text: string) => {
    setState({ pkText: text });
  });

  const showBottomOperation = useMemoizedFn((cb?: () => void) => {
    setOperationVisible(true);
    textInputRef.current?.blur();
    cb?.();
  });

  const renderContent = useMemoizedFn(() => {
    return (
      <AvatarInput
        value={pkText}
        inputStyle={{
          height: 32,
          flexGrow: 10
        }}
        showAvatar={false}
        placeholder={'请输入PK主题...'}
        maxLength={15}
        textRef={textInputRef}
        onChange={handleChangeText}
        onFocus={onInputFocus}
        onBlur={onInputBlur}
      />
    );
  });

  const renderOther = useMemoizedFn(() => {
    return (
      <>
        <AddRoleCard
          pkImages={pkImages}
          onImgPress={() => {
            useCreatePkNominateStore.setState({ pkImages: pkImages });
            router.navigate('/pk-nominate');
            textInputRef.current?.blur();
            collapsePublish();
          }}
          onPress={() => {
            initPKNominateSelector({ pkImages });
            setShowAlbum(true);

            textInputRef.current?.blur();
            usePerformanceStore
              .getState()
              .recordStart('make_photo_photo_set_render', {
                performance_type: 'render',
                performance_key: AlbumFromType.PK
              });
          }}
        />
        {FoldTopic}
      </>
    );
  });

  const renderIcon = useMemoizedFn(() => {
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          gap: 24
        }}
      >
        <IconComponent
          icon="topic"
          text="话题"
          onPress={() =>
            showBottomOperation(() => setVisibleMap({ topic: true }))
          }
        />
      </View>
    );
  });

  const handlePhotoSelect = useMemoizedFn((photos: MediaPhotosItem[]) => {
    if (photos.length) {
      // 存在的返回已存在的值
      const newPkImages = photos.map(appendItem => {
        const existIndex = pkImages.findIndex(
          item => item.image?.mediaId === appendItem.photoId
        );
        if (existIndex >= 0) {
          return pkImages[existIndex];
        } else {
          return transformMediaToPkImage(appendItem) as PkImage;
        }
      });

      setPkImages(newPkImages);
      router.navigate('/pk-nominate');
      collapsePublish();
    }
  });

  const renderOperation = useMemoizedFn(() => {
    if (!operationVisible) return null;
    return (
      <View
        style={[
          {
            height: keyboardHeight
          },
          styles.operation
        ]}
      >
        {visibleMap['topic'] && UnfoldTopic}
      </View>
    );
  });

  return (
    <View style={[styles.wrapper, style]} onLayout={onLayout}>
      <View style={styles.content}>{renderContent()}</View>
      <View style={styles.other}>{renderOther()}</View>
      <View style={styles.bottom}>
        {renderIcon()}
        <Button
          style={[
            styles.button,
            disableSubmit && {
              opacity: 0.6
            }
          ]}
          type={EButtonType.NORMAL}
          $customBtnTextStyle={{
            color: StyleSheet.currentColors.white,
            fontSize: 14,
            fontWeight: '600',
            lineHeight: 25
          }}
          $customBtnStyle={{
            width: 62,
            height: 30,
            borderRadius: 100,
            paddingVertical: 0,
            paddingHorizontal: 0,
            backgroundColor: '#FF6A3B',
            justifyContent: 'center',
            alignItems: 'center'
          }}
          // disabled={disableSubmit}
          onPress={$publishNewPk}
        >
          {publishBtnText}
        </Button>
      </View>

      {renderOperation()}

      <ImagePicker
        show={showAlbum}
        callWhere={AlbumFromType.PK}
        onClose={() => {
          setShowAlbum(false);
          // setTimeout(() => {
          //   textInputRef.current?.focus();
          // }, 200);
        }}
        setPhotos={handlePhotoSelect}
        multipleCount={100}
      />
    </View>
  );
});

export const IconComponent = ({
  icon,
  text,
  theme = Theme.LIGHT,
  onPress
}: {
  icon: IconTypes;
  text?: string;
  theme?: Theme;
  onPress?: () => void;
}) => {
  const themeConfig = getThemeColor(theme);
  return (
    <Pressable onPress={onPress} style={styles.topic}>
      <Icon icon={icon} size={16} color="rgba(255, 255, 255, 1)" />
      <Text style={[styles.iconText]}>{text}</Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  wrapper: {},
  content: {
    padding: 16
  },
  other: {},
  bottom: {
    marginTop: 16,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 0.5,
    borderColor: 'rgba(255, 255, 255, 0.04)',
    height: 54
  },
  button: {},
  topic: {
    gap: 4,
    flexDirection: 'row'
  },
  iconText: {
    fontSize: 13,
    lineHeight: 16,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.6)'
  },
  operation: {
    padding: 16,
    paddingTop: 32,
    backgroundColor: 'rgba(22, 22, 26, 1)'
  },
  tabItemStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 60,
    height: 40,
    borderRadius: 8,
    marginRight: 12,
    overflow: 'hidden'
  },
  tabRowStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
    paddingHorizontal: 16
  }
});
