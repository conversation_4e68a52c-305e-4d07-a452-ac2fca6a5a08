import { router } from 'expo-router';
import { set } from 'lodash-es';
import <PERSON><PERSON><PERSON>ieView from 'lottie-react-native';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import React from 'react';
import { BackHandler, ModalBaseProps, Pressable, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming
} from 'react-native-reanimated';
import { captureRef } from 'react-native-view-shot';
import { uploadMakePhotoImg } from '@/src/api';
import { goodsClient } from '@/src/api/goods';
import {
  Icon,
  Text,
  hideLoading,
  showLoading,
  showToast
} from '@/src/components';
import { showConfirm } from '@/src/components/popup/confirmModalGlobal/Confirm';
import { hideCommonShare, showCommonShare } from '@/src/components/share';
import { BasicChannelItem } from '@/src/components/share/basicChannelItem';
import {
  channels,
  shareCompPresets
} from '@/src/components/share/channelConfig';
import {
  ChannelConfig,
  ShareInfoV2Props,
  ShareType
} from '@/src/components/share/typings';
import { useAndroidBackHandler, usePersistFn } from '@/src/hooks';
import { GOODS_NAME, useGoodsStore } from '@/src/store/goods';
import { AlbumType, usePublishStore } from '@/src/store/publish';
import { typography } from '@/src/theme';
import { getThemeColor } from '@/src/theme/colors/common';
import { Theme } from '@/src/theme/colors/type';
import { ShareInfo } from '@/src/types';
import { StyleSheet, dp2px, isIos } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { savePicture } from '@/src/utils/savePicture';
import { Modal } from '@Components/modal';
import { GoodsDetailFE, GoodsDetailShowMode } from '../types';
import { DeleteIcon } from '@/assets/image/svg';
import { CensoredState } from '@/proto-registry/src/web/raccoon/common/state_pb';
import { GameType } from '@/proto-registry/src/web/raccoon/common/types_pb';
import { GoodsType } from '@/proto-registry/src/web/raccoon/goods/common_pb';
import { GoodsProductType } from '@/proto-registry/src/web/raccoon/goods/common_pb';
import { GoodsView } from './GoodsView';
import { GuestLike } from './GuestLike';
import { MeLike } from './MeLike';
import { MaterializeButton } from '@/src/components/materialize/MaterializeButton';

type Props = {
  mode: GoodsDetailShowMode;
  detail?: GoodsDetailFE;
  onDelete: (id: string, type: GoodsType) => void;
  onClose?: () => null;
} & ModalBaseProps;

export const GoodsDetailModal = memo(
  ({
    visible,
    onClose,
    mode,
    onDelete,
    detail: detailOrigin,
    ...rest
  }: Props) => {
    const { createData } = useGoodsStore(state => {
      return { createData: state.createData };
    });
    // 这个ref有隐藏的bug，要后续处理下 @丹妮
    const detail = useRef<GoodsDetailFE>();
    useEffect(() => {
      detail.current = detailOrigin;
    }, [detailOrigin]);
    const productType = useMemo(() => {
      if (mode === GoodsDetailShowMode.Create) {
        return useGoodsStore.getState().createData?.template.productType;
      }
      return detailOrigin?.productType;
    }, [detailOrigin, mode]);

    const [loadEnd, setLoadEnd] = useState(false);
    const flowerRef = useRef<AnimatedLottieView>(null);
    const shineRef = useRef<AnimatedLottieView>(null);
    const goodsRef = useRef<View>(null);
    const width = useRef(0);
    const height = useRef(0);
    const scaleValue = useSharedValue(1);
    const opacityValue = useSharedValue(1);
    const coverOpacityValue = useSharedValue(0);
    const transValue = useSharedValue(0);
    const logParams = useMemo(() => {
      const params: Record<string, string | number | boolean | undefined> = {
        is_wall_goods: '0'
      };
      if (mode === GoodsDetailShowMode.Create) {
        params['cover_type'] = createData?.effectId;
        params['from'] = createData?.photo?.from;
        params['goods_type'] =
          GOODS_NAME[createData?.template.productType ?? ''];
        params['source'] = createData?.source;
        params['module'] = 'create_goods_result';
        params['goods_id'] = createData?.id;
      }
      if (mode === GoodsDetailShowMode.Me) {
        params['module'] = 'my_goods_detail';
        params['goods_id'] = detailOrigin?.goodsId;
      }
      if (mode === GoodsDetailShowMode.Guest) {
        params['module'] = 'user_goods_detail';
        params['goods_id'] = detailOrigin?.goodsId;
      }

      return params;
    }, [mode]);
    const scaleAnimatedStyle = useAnimatedStyle(() => {
      return {
        transform: [
          {
            scale: scaleValue.value
          }
        ]
      };
    });
    const coverAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: coverOpacityValue.value
      };
    });
    const descAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: opacityValue.value,
        transform: [
          {
            translateY: transValue.value
          }
        ]
      };
    });
    const images = useMemo(() => {
      if (mode === GoodsDetailShowMode.Create) {
        return createData?.images;
      } else {
        return detailOrigin?.displayImages.map(r => r.url);
      }
    }, [mode]);

    const id = useMemo(() => {
      if (mode === GoodsDetailShowMode.Create) {
        return createData?.id;
      } else {
        return detailOrigin?.goodsId;
      }
    }, [visible]);
    useEffect(() => {
      if (mode === GoodsDetailShowMode.Create && loadEnd) {
        reportExpo('expo', {
          ...logParams
        });
        scaleValue.value = 1.17;
        coverOpacityValue.value = 1;
        const DELAY_MS = 200;
        const COVER_DELAY_MS = 0;
        const ANIMATE_MS = 300;
        const FN = Easing.bezier(0.46, 0.3, 0.57, 0.1);
        scaleValue.value = withDelay(
          DELAY_MS,
          withTiming(1, {
            duration: ANIMATE_MS,
            easing: FN
          })
        );
        coverOpacityValue.value = withDelay(
          DELAY_MS + COVER_DELAY_MS,
          withTiming(0, {
            duration: ANIMATE_MS,
            easing: FN
          })
        );
        opacityValue.value = 0;
        transValue.value = 200;
        opacityValue.value = withDelay(
          DELAY_MS,
          withTiming(1, {
            duration: ANIMATE_MS,
            easing: Easing.ease
          })
        );
        transValue.value = withDelay(
          DELAY_MS,
          withTiming(0, {
            duration: ANIMATE_MS,
            easing: Easing.ease
          })
        );
        setTimeout(() => {
          flowerRef?.current?.play();
          setTimeout(
            () => {
              shineRef?.current?.play();
            },
            DELAY_MS / 2 + ANIMATE_MS
          );
        }, DELAY_MS / 2);
      }
    }, [mode, loadEnd]);
    useEffect(() => {
      if (mode === GoodsDetailShowMode.Create) {
        goodsClient.getGoodsDetail({ goodsId: id }).then(d => {
          detail.current = { ...d.detail, goodsId: id ?? '' } as GoodsDetailFE;
        });
      }
    }, [mode]);
    const goodsType = useMemo(() => {
      if (mode === GoodsDetailShowMode.Create) {
        return createData?.template.goodsType;
      } else {
        return detailOrigin?.goodsType;
      }
    }, [mode]);
    const censoredState = useMemo(() => {
      if (mode === GoodsDetailShowMode.Create) {
        return createData?.censoredState;
      } else {
        return detailOrigin?.censoredState;
      }
    }, [mode]);
    useAndroidBackHandler(onClose ?? (() => false), visible);
    const likes = detailOrigin?.likes;
    const isLiked = detailOrigin?.liked;
    const showTopLikes = (likes ?? 0) > 0 && mode === GoodsDetailShowMode.Me;
    const isFirst = mode === GoodsDetailShowMode.Create && createData?.isFirst;
    const code = useMemo(() => {
      if (mode === GoodsDetailShowMode.Create) {
        return createData?.code;
      } else {
        return detailOrigin?.code;
      }
    }, [mode]);
    const onPublish = usePersistFn(async () => {
      showLoading();
      reportExpo('publish', {
        ...logParams,
        module: 'my_goods'
      });
      try {
        const res = await goodsClient.genPublishImage({
          goodsId: id
        });

        const albumPhotos = usePublishStore.getState().albumPhotos;
        const ids = albumPhotos.map(i => i.photoId);
        const generatedPhoto = {
          ...res,
          gameType: GameType.GOODS,
          albumType: ids.includes(res.photoId)
            ? AlbumType.album
            : AlbumType.history,
          num: 1
        };

        const originPhoto = await goodsClient
          .getSourcePhoto({ goodsId: id })
          .then(res2 => {
            if (res2.photoId === '') {
              return undefined;
            }
            return {
              ...res2,
              albumType: ids.includes(res2.photoId)
                ? AlbumType.album
                : AlbumType.history,
              num: 2
            };
          })
          .catch(() => {
            return undefined;
          });

        const newPhotos = originPhoto
          ? [generatedPhoto, originPhoto]
          : [generatedPhoto];

        usePublishStore.getState().setPhotos(newPhotos);
        reportClick('publish', {
          module: 'my_goods',
          ...logParams,
          template_id: res.templateId
        });
        router.navigate({
          pathname: '/publish',
          params: {
            photoId: res.photoId,
            scene: 'goods',
            fromType: 'goods',
            extraId:
              detail.current?.productType === GoodsProductType.FESTIVAL_BADGE
                ? (id ?? '')
                : '',
            log_params: JSON.stringify({
              ...logParams,
              module: 'publish',
              template_id: res.templateId
            })
          }
        });
        onClose?.();
      } catch (e) {
        showToast('发布失败');
      } finally {
        hideLoading();
      }
    });
    function genDeleteDetailItemConfig() {
      return {
        text: '删除',
        icon: (theme: Theme) => (
          <BasicChannelItem theme={theme} style={{ padding: 11 }}>
            <DeleteIcon
              width={24}
              height={24}
              color={getThemeColor(theme).fontColor}
            />
          </BasicChannelItem>
        ),
        onPress: () => {
          showConfirm({
            title: '确认删除谷子?',
            confirmText: '确认',
            cancelText: '取消',
            simultaneous: true,
            onConfirm: ({ close }) => {
              deleteGoods();
              close();
            }
          });
        }
      } as ChannelConfig;
    }
    const fixedConfigs = useMemo(() => {
      return [channels[ShareType.saveEmoji], genDeleteDetailItemConfig()];
    }, []);

    useEffect(() => {
      const deleteDetailItemConfig = genDeleteDetailItemConfig();
      const fixedConfigs = [
        channels[ShareType.saveEmoji],
        deleteDetailItemConfig
      ];

      const config: ShareInfoV2Props = {
        onCloseSharing() {
          onClose?.();
        },
        modalProps: {
          hideBanner: true,
          maskShown: true,
          style: {
            backgroundColor: '#262629'
          },
          maskChildren: renderGoods()
        },
        async onSaveCustom() {
          const imgs = detail.current?.rawImages ?? [];
          if (imgs.length < 1) {
            showToast('出错啦，请重试');
            return;
          }
          showLoading();
          await Promise.all(
            imgs.map(i => {
              return savePicture(i.url, false);
            })
          )
            .then(() => {
              showToast('保存成功');
            })
            .catch(() => {
              showToast('出错啦，请重试');
            })
            .finally(() => {
              hideLoading();
            });
        },
        theme: Theme.DARK,
        compConfigs:
          censoredState === CensoredState.CENSORED_PASSED
            ? [
                [
                  channels[ShareType.wx],
                  channels[ShareType.pyq],
                  channels[ShareType.qq],
                  channels[ShareType.qzone],
                  channels[ShareType.xhs],
                  channels[ShareType.douyinChat]
                ],
                fixedConfigs
              ]
            : [fixedConfigs], 
        getShareInfo: async () => {
          if (goodsRef.current) {
            const url = (
              await uploadMakePhotoImg(
                await captureRef(goodsRef.current, {
                  quality: 1,
                  width: isIos ? Math.floor(width.current) : undefined,
                  height: isIos ? Math.floor(height.current) : undefined
                })
              )
            ).image?.url;

            return {
              title: '',
              description: '',
              imageIndex: -1,
              images: [url],
              url: ''
            } as ShareInfo;
          }
        },
        logParams
      };
      if (visible && mode !== GoodsDetailShowMode.Guest) {
        showCommonShare(config);
      } else {
        hideCommonShare();
      }
    }, [visible]);
    const deleteGoods = usePersistFn(() => {
      onClose?.();
      if (id && goodsType) {
        onDelete(id, goodsType);
      }
    });
    if (!visible || mode !== GoodsDetailShowMode.Guest) {
      return <></>;
    }
    return (
      <Modal
        style={{ backgroundColor: 'transparent' }}
        animationType={'fade'}
        transparent={true}
        {...rest}
      >
        {renderGoods()}
      </Modal>
    );

    function renderGoods() {
      return (
        <Pressable style={[styles.shareContainer]}>
          <View
            style={[
              StyleSheet.absoluteFill,
              StyleSheet.centerStyle,
              styles.container
            ]}
          >
            <View style={styles.bgTop} />
            <View style={styles.bgBottom} />
            <View
              ref={goodsRef}
              collapsable={false}
              onLayout={e => {
                width.current = e.nativeEvent.layout.width;
                height.current = e.nativeEvent.layout.height;
              }}
              style={[
                { width: '100%' },
                mode === GoodsDetailShowMode.Guest && StyleSheet.absoluteFill,
                StyleSheet.centerStyle
              ]}
            >
              <LinearGradient
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'absolute'
                }}
                locations={[0, 1]}
                colors={['#252526', '#19191a']}
                useAngle={true}
                angle={180}
              />
              <View style={[styles.goodsContainer]}>
                <GoodsView
                  images={images ?? []}
                  onLoadEnd={() => {
                    setLoadEnd(true);
                  }}
                  mode={mode}
                  animatedStyle={scaleAnimatedStyle}
                  coverAnimatedStyle={coverAnimatedStyle}
                  goodsType={goodsType ?? GoodsType.UNKNOWN}
                  productType={productType}
                />
                {mode === GoodsDetailShowMode.Create && (
                  <AnimatedLottieView
                    ref={shineRef}
                    style={[
                      styles.shine,
                      {
                        left: goodsType === GoodsType.BADGE ? -5 : -24,
                        top: goodsType === GoodsType.BADGE ? -13 : -23
                      }
                    ]}
                    autoPlay={false}
                    loop={false}
                    source={require('@Assets/lottie/goods/shine.json')}
                  />
                )}
              </View>
              <Animated.View style={[descAnimatedStyle, styles.descContainer]}>
                {isFirst && (
                  <Text style={styles.descText}>我的第一个「专属」谷子!</Text>
                )}
                <View
                  style={[
                    styles.frameParent,
                    {
                      marginTop: isFirst ? dp2px(16) : dp2px(4)
                    }
                  ]}
                >
                  <LinearGradient
                    style={[styles.wrapper, styles.wrapperPosition]}
                    locations={[0, 1]}
                    colors={['#777c88', '#2e363d']}
                    useAngle={true}
                    angle={252.41}
                  >
                    <Text style={styles.text}>编号</Text>
                  </LinearGradient>
                  <LinearGradient
                    style={[styles.frameChild, styles.wrapperPosition]}
                    locations={[0, 0.56, 1]}
                    colors={['#f0eff4', '#b9bfcd', '#7c8595']}
                    useAngle={true}
                    angle={259.1}
                  >
                    <Text numberOfLines={1} style={styles.text2}>
                      {code}
                    </Text>
                  </LinearGradient>
                </View>
              </Animated.View>
            </View>
            <Animated.View style={descAnimatedStyle}>
              {[GoodsDetailShowMode.Me, GoodsDetailShowMode.Create].includes(
                mode
              ) && (
                <View style={styles.publishContainer}>
                  <MaterializeButton logParams={{goods_id:id??'',module:`${logParams.module}`}} />
                <Pressable onPress={onPublish} style={styles.publish}>
                  <View style={styles.publishInner}>
                    <Icon icon="publish" size={15} />
                    <Text style={styles.publishText}>发布作品</Text>
                  </View>
                  </Pressable>
                </View>
              )}
            </Animated.View>
            {showTopLikes && (
              <View style={styles.actionContainer}>
                <MeLike wall={{ likes }} />
              </View>
            )}
            <View style={styles.actionContainerRight}>
              {mode === GoodsDetailShowMode.Guest && (
                <Icon icon="input_clear" size={30} onPress={onClose} />
              )}
            </View>
            {mode === GoodsDetailShowMode.Guest && (
              <GuestLike wall={{ isLiked, likes }} onLike={onLike} />
            )}
          </View>
          {mode === GoodsDetailShowMode.Create && (
            <View style={[styles.absoluteFull, { pointerEvents: 'none' }]}>
              <AnimatedLottieView
                style={[styles.absoluteFull]}
                resizeMode="contain"
                ref={flowerRef}
                autoPlay={false}
                loop={false}
                source={require('@Assets/lottie/goods/flowers.json')}
              />
            </View>
          )}
        </Pressable>
      );
    }

    function onLike(isLike: boolean) {
      if (isLike) {
        reportClick('like', {
          ...logParams
        });
      }

      goodsClient.likeGoods({
        goodsId: id,
        like: isLike
      });
    }
  }
);

const styles = StyleSheet.create({
  container: {
    paddingBottom: 220
  },
  actionContainer: {
    width: '100%',
    height: 44,
    alignItems: 'center',
    paddingHorizontal: 12,
    top: 54,
    justifyContent: 'center',
    position: 'absolute',
    flexDirection: 'row'
  },
  actionContainerRight: {
    height: 30,
    width: 30,
    right: 16,
    paddingHorizontal: 12,
    top: 54,
    position: 'absolute',
    justifyContent: 'flex-end'
  },
  close: {},
  descContainer: {
    marginTop: 20,
    paddingBottom: 24
  },
  descText: {
    fontSize: 26,
    letterSpacing: -0.5,
    lineHeight: 30,
    fontFamily: typography.fonts.feed,
    color: '#fff',
    textAlign: 'center'
  },
  shareContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 0
  },
  bgTop: {
    backgroundColor: '#252526',
    position: 'absolute',
    width: '100%',
    pointerEvents: 'none',
    height: 200,
    top: 0
  },
  bgBottom: {
    backgroundColor: '#19191a',
    position: 'absolute',
    width: '100%',
    height: 400,
    pointerEvents: 'none',
    bottom: 0
  },
  publishContainer: {
    gap:15,
    alignItems: 'center',
    zIndex: 100, 
    flexDirection: 'row', 
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 45,
  },
  goodsContainer: { pointerEvents: 'none' },
  wrapperPosition: {
    backgroundColor: 'transparent',
    alignItems: 'center',
    height: 26
  },
  absoluteFull: {
    width: '100%',
    height: '100%'
  },
  shine: {
    position: 'absolute',
    width: 144,
    height: 144
  },
  frameChild: {
    paddingHorizontal: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8
  },
  text: {
    paddingTop: 1,
    fontSize: 14,
    lineHeight: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: typography.fonts.baba.heavy,
    textAlign: 'center',
    height: 16,
    opacity: 0.9
  },
  text2: {
    color: 'rgba(31, 36, 41, 1)',
    fontFamily: typography.fonts.baba.heavy,
    fontSize: 16,
    lineHeight: 21,
    letterSpacing: 0,
    fontWeight: '800',
    textAlign: 'center',
    opacity: 0.9
  },
  wrapper: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10
  },
  frameParent: {
    marginTop: 4,
    borderStyle: 'solid',
    borderColor: 'rgba(255, 255, 255, 0)',
    borderWidth: 1,
    alignSelf: 'center',
    height: 28,
    flexDirection: 'row',
    borderRadius: 8
  },
  publish: {
    borderRadius: 100,
    backgroundColor: '#ff6a3b',
    width: 136,
    height: 50,
    elevation: 100,
    flexDirection: 'row',
    justifyContent: 'center',
    alignSelf: 'center'
  },
  publishInner: {
    width: '100%',
    height: '100%',
    gap: 6,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  publishText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#fff',
    textAlign: 'center'
  }
});
