import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import React, { memo, useEffect } from 'react';
import {
  InteractionManager,
  Pressable,
  Image as RNImage,
  TouchableOpacity,
  View
} from 'react-native';
import { getRankVersion } from '@/src/api/rank';
import { Image } from '@/src/components';
import { useAppActive } from '@/src/hooks/global/useAppActive';
import { useAuthState } from '@/src/hooks/useAuthState';
import { useAuthStore } from '@/src/store/authInfo';
import { useBehaviorStore } from '@/src/store/behavior';
import { SwitchName, useControlStore } from '@/src/store/control';
import { useRankVersionStore } from '@/src/store/rankVersionStore';
import { StyleSheet } from '@/src/utils';
import { useIsFocused } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';

const RANKLIST_ICON = require('@Assets/image/rank-list/ranklist_entr.png');
const SCHOOL_RANK_BUBBLE = require('@Assets/image/rank-list/school/bubble.png');

export const RankListButton = memo(function RankListButton() {
  const {
    hasNewRank,
    setHasNewRank,
    workRankVersion,
    userStarRankVersion,
    userNewRankVersion,
    decorationRankVersion,
    setRankVersion,
    updatedRanking
  } = useRankVersionStore();

  // 登录拦截
  const { loginIntercept } = useAuthState();
  const isFocus = useIsFocused();

  const disableSchoolRank = useControlStore(
    useShallow(state => state.checkIsOpen(SwitchName.DISABLE_SCHOOL_RANK))
  );

  const schoolRankVisited = useBehaviorStore(
    useShallow(state => state.schoolRankVisited)
  );

  /**
   * 点击排行榜按钮
   */
  const handleRankPress = useMemoizedFn(() => {
    loginIntercept(
      () => {
        router.navigate({
          pathname: '/rank-list',
          params: {
            // 如果 updatedRanking = '', 就默认 'dachu'
            type: updatedRanking || 'dachu'
          }
        });
      },
      { scene: 'rankList' }
    );
  });

  const { run: debounceIconLoadEnd } = useDebounceFn(
    () => {
      InteractionManager.runAfterInteractions(() => {
        if (useAuthStore.getState().checkLogin()) {
          handleIconLoadEnd();
        }
      });
    },
    {
      wait: 2000
    }
  );

  const nav2SchoolRank = useMemoizedFn(() => {
    loginIntercept(() => {
      router.navigate('/rank-list/school');
    });
  });

  useEffect(() => {
    debounceIconLoadEnd();
  }, []);

  useEffect(() => {
    if (isFocus) {
      debounceIconLoadEnd();
    }
  }, [isFocus]);

  useAppActive(() => {
    debounceIconLoadEnd();
    return () => {};
  });

  /**
   * 图片加载完毕后 -> 调用接口检查最新排行榜版本
   * 若有差异，就 setHasNewRank(true) (红点出现)
   */
  const handleIconLoadEnd = useMemoizedFn(async () => {
    try {
      console.log('旧版本号:', {
        workRankVersion,
        userStarRankVersion,
        userNewRankVersion
      });

      // 获取最新版本信息
      const latest = await getRankVersion({});
      console.log('最新版本号:', latest);

      // 先自己做「红点」逻辑
      if (
        latest.workRankVersion !== workRankVersion ||
        latest.userStarRankVersion !== userStarRankVersion ||
        latest.userNewRankVersion !== userNewRankVersion ||
        latest.goodsFandomWallRankVersion !== decorationRankVersion
      ) {
        // 有变动 => 让红点显示
        setHasNewRank(true);
      }

      // 新版本信息存到 store
      // 更新 updatedRanking
      setRankVersion({
        workRankVersion: latest.workRankVersion,
        userStarRankVersion: latest.userStarRankVersion,
        userNewRankVersion: latest.userNewRankVersion,
        decorationRankVersion: latest.goodsFandomWallRankVersion
      });
    } catch (err) {
      console.log('getRankVersion error:', err);
    }
  });

  return (
    <View style={styles.ranklistBtn}>
      <TouchableOpacity
        style={styles.ranklistIconWrapper}
        onPress={handleRankPress}
      >
        <RNImage
          source={RANKLIST_ICON}
          style={styles.ranklistIcon}
          // onLoadEnd={handleIconLoadEnd}
        />
        {hasNewRank && <View style={styles.redDot} />}
      </TouchableOpacity>
    </View>
  );
});

const styles = StyleSheet.create({
  ranklistBtn: {
    padding: 4
  },
  ranklistIconWrapper: {
    position: 'relative'
  },
  ranklistIcon: {
    width: 34,
    height: 34
  },
  redDot: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 8,
    height: 8,
    backgroundColor: '#FD4C29',
    borderRadius: 4
  }
});
