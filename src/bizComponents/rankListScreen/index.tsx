import { useMemoizedFn } from 'ahooks';
import { router, useFocusEffect } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  ImageSourcePropType,
  Pressable,
  Text,
  View
} from 'react-native';
import PagerView from 'react-native-pager-view';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { goodsClient } from '@/src/api/goods';
import { getUserRankList, getWorkRankList } from '@/src/api/rank';
import { Screen } from '@/src/components';
import {
  SkeletonColumn,
  SkeletonRow,
  SkeletonSpan
} from '@/src/components/skeletion';
import { Ranking, useRankVersionStore } from '@/src/store/rankVersionStore';
import { CommonColor } from '@/src/theme/colors/common';
import { Theme } from '@/src/theme/colors/type';
import { StyleSheet, createStyle, dp2px } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { Image } from '@Components/image';
import { reportClick, reportExpo } from '@Utils/report';
import { useParams } from '../../hooks/useParams';
import SchoolRankEntry from '../schoolRankScreen/schoolRankEntry';
import { DecorationRankPage } from './decoration/DecorationRankPage';
import { Pagination } from '@/proto-registry/src/web/raccoon/common/utils_pb';
import { useShallow } from 'zustand/react/shallow';
import { MemoCreationRankPage } from './CreationRankPage';
import { RankUpPopup } from './RankUpPopup';
import { MemoWorksRankPage } from './WorksRankPage';

const BG = require('@/assets/image/rank-list/rank-list-bg.png');
const ICON_BACK = require('@/assets/icon/cref-search/back.png');
const ICON_QUESTION = require('@/assets/image/rank-list/rank-list-question.png');

type RankListScreenProps = {
  onPressBack?: () => void;
  rankListTabBg?: ImageSourcePropType;
};

const TAB_STATES = [
  { top: '创作榜', sub: '大触榜' },
  { top: '创作榜', sub: '新人榜单' },
  { top: '作品榜', sub: null },
  { top: '装扮榜', sub: null }
];

// 根据 type 计算 PagerView index
function mapTypeToIndex(type?: string) {
  switch (type) {
    case 'dachu':
      return 0;
    case 'xinren':
      return 1;
    case 'works':
      return 2;
    case 'decoration':
      return 3;
    default:
      return 0;
  }
}

function getRuleText(index: number): string[] {
  if (index === 0) {
    // 大触榜
    return [
      '榜单每天更新',
      '根据用户的粉丝量、获赞数、声望值、曝光、互动、更新频率等数据综合排名'
    ];
  } else if (index === 1) {
    // 新人榜
    return [
      '榜单每天更新',
      '根据用户的粉丝量、获赞数、声望值、曝光、互动、更新频率等数据综合排名，且用户账号注册时间在最近30天内'
    ];
  } else if (index === 2) {
    // 作品榜
    return ['榜单每天更新', '根据作品的曝光、点击、互动、原创性等数据综合排名'];
  } else if (index === 3) {
    return ['榜单实时更新', '根据痛墙的曝光、点赞、谷子数量等数据综合排名'];
  }
  return [];
}

export function RankListScreen({
  onPressBack,
  rankListTabBg = require('@Assets/image/rank-list/ranklist_tab.png')
}: RankListScreenProps) {
  // 从 url params 里拿 type
  const { type } = useParams<{
    type?: Ranking;
  }>();
  // 首次进入时，就决定 PagerView 默认页（赋值到 state）
  const [currentIndex, setCurrentIndex] = useState(mapTypeToIndex(type));
  const pagerRef = useRef<PagerView>(null);

  const [dachuData, setDachuData] = useState<any>(null);
  const [xinrenData, setXinrenData] = useState<any>(null);
  const [worksData, setWorksData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [isXinren, setIsXinren] = useState(false);
  // PagerView + 指示器
  const offsetX = useSharedValue(0);
  const primaryTabOffset = useSharedValue(dp2px(2));
  const secondaryTabOffset = useSharedValue(dp2px(149));
  const firstPageSubOpacityValue = useSharedValue(1);
  // ========== 上榜动画相关状态 ==========
  const [showRankAnimation, setShowRankAnimation] = useState(false);
  const [rankNumber, setRankNumber] = useState(0);
  const [rankAvatar, setRankAvatar] = useState<string>('');
  const [isWorkRank, setIsWorkRank] = useState(false);
  const [workImagesCount, setWorkImagesCount] = useState(0);
  const [workIsVideo, setWorkIsVideo] = useState(false);
  const [showRules, setShowRules] = useState(false);

  const {
    rankAnimShown,
    setRankAnimShown,
    userStarRankVersion,
    userNewRankVersion,
    workRankVersion,
    setHasNewRank,
    updatedRanking,
    clearUpdatedRanking
  } = useRankVersionStore(
    useShallow(state => ({
      rankAnimShown: state.rankAnimShown,
      setRankAnimShown: state.setRankAnimShown,
      userStarRankVersion: state.userStarRankVersion,
      userNewRankVersion: state.userNewRankVersion,
      workRankVersion: state.workRankVersion,
      setHasNewRank: state.setHasNewRank,
      updatedRanking: state.updatedRanking,
      clearUpdatedRanking: state.clearUpdatedRanking
    }))
  );

  // ========== 进来后清空 updatedRanking ==========
  useEffect(() => {
    if (updatedRanking) {
      useRankVersionStore.getState().clearUpdatedRanking();
    }
  }, [updatedRanking]);

  // 切换 Tab
  const changeTab = useMemoizedFn(
    (index: number, trigger?: 'move' | 'click') => {
      if (index < 2 && currentIndex >= 2) {
        firstPageSubOpacityValue.value = withTiming(1);
      } else if (index >= 2) {
        firstPageSubOpacityValue.value = 0;
      }
      if (index === 1) {
        setIsXinren(true);
      } else if (index === 0) {
        setIsXinren(false);
      }
      console.log('changeTab', index);
      if (trigger !== 'move') {
        pagerRef.current?.setPage(index);
      }
      setCurrentIndex(index);
      effectPrimary(index);
      effectSecondary(index);
      // 每次切换 tab 或滑动时，关闭规则弹窗
      setShowRules(false);
    }
  );
  const opacityAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: firstPageSubOpacityValue.value
    };
  });

  const effectPrimary = useMemoizedFn((index: number) => {
    if (index === 0 || index === 1) {
      primaryTabOffset.value = withTiming(dp2px(2));
    } else if (index === 2) {
      primaryTabOffset.value = withTiming(dp2px(62));
    } else {
      primaryTabOffset.value = withTiming(dp2px(122));
    }
  });

  const effectSecondary = useMemoizedFn((index: number) => {
    if (index === 0) {
      secondaryTabOffset.value = withTiming(dp2px(149));
    } else if (index === 1) {
      secondaryTabOffset.value = withTiming(dp2px(215));
    } else {
      // 第三页(作品榜)隐藏二级tab
    }
  });

  const topIndicatorStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: primaryTabOffset.value }]
    };
  });
  const subIndicatorStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: secondaryTabOffset.value }]
    };
  });

  // ========== 初始化请求 3 个榜单数据 ==========
  useEffect(() => {
    async function fetchNewUserRanks() {
      try {
        const resXinren = await getUserRankList({ rankType: 2 });
        setXinrenData(resXinren);
      } catch (error) {
        errorReport('getUserRankListError2', ReportError.REQUEST, error);
      }
    }
    async function fetchWorkRanks() {
      try {
        const resWorks = await getWorkRankList({});
        setWorksData(resWorks);
      } catch (error) {
        errorReport('getWorkRankListError', ReportError.REQUEST, error);
      }
    }
    async function fetchAllRanks() {
      try {
        setLoading(true);
        const resDachu = await getUserRankList({ rankType: 1 });
        setDachuData(resDachu);
        setLoading(false);
        fetchNewUserRanks();
        fetchWorkRanks();
      } catch (err) {
        errorReport('getUserRankListError', ReportError.REQUEST, err);
        console.error('请求排行榜失败:', err);
        setLoading(false);
      }
    }
    fetchAllRanks();
    setHasNewRank(false);
  }, [setHasNewRank]);

  // ========== 监听 currentIndex + 数据加载完毕 => 判断是否弹出上榜动画 ==========
  useEffect(() => {
    if (!loading) {
      // 1) 大触榜
      if (currentIndex === 0 && dachuData?.currentUidRank?.rank) {
        const rank = Number(dachuData.currentUidRank.rank);
        const version = userStarRankVersion || 'v1';
        const key = `dachu_${version}`;

        if (rank > 0 && !rankAnimShown[key]) {
          setShowRankAnimation(true);
          setRankNumber(rank);
          setRankAvatar(dachuData.currentUidRank.profile?.avatar || '');
          setIsWorkRank(false); // 大触/新人 => false
          setRankAnimShown(key);
        }
      }
      // 2) 新人榜
      else if (currentIndex === 1 && xinrenData?.currentUidRank?.rank) {
        const rank = Number(xinrenData.currentUidRank.rank);
        const version = userNewRankVersion || 'v1';
        const key = `xinren_${version}`;

        if (rank > 0 && !rankAnimShown[key]) {
          setShowRankAnimation(true);
          setRankNumber(rank);
          setRankAvatar(xinrenData.currentUidRank.profile?.avatar || '');
          setIsWorkRank(false);
          setRankAnimShown(key);
        }
      }
      // 3) 作品榜
      else if (currentIndex === 2 && worksData?.curUidCard?.rank) {
        const rank = Number(worksData.curUidCard.rank);
        const version = workRankVersion || 'v1';
        const key = `works_${version}`;

        if (rank > 0 && !rankAnimShown[key]) {
          setShowRankAnimation(true);
          setRankNumber(rank);

          const images = worksData.curUidCard.cardImages || [];
          setRankAvatar(images[0] || '');

          // 解析 extInfo => 拿到图片张数
          let extInfo = worksData.curUidCard.cardInfo?.card?.extInfo;
          let imgCount = 0;
          if (extInfo) {
            try {
              const parsed = JSON.parse(extInfo);
              imgCount = parsed?.image_count || 0;
            } catch (err) {
              console.warn('解析作品 extInfo 出错:', err);
            }
          }
          setWorkImagesCount(imgCount);

          // 判断是否视频 => type === 2
          const type = worksData.curUidCard.cardInfo?.card?.type;
          setWorkIsVideo(type === 2);

          // 标记这是作品榜
          setIsWorkRank(true);

          // 标记已经弹过
          setRankAnimShown(key);
        }
      }
    }
  }, [
    loading,
    currentIndex,
    dachuData,
    xinrenData,
    worksData,
    rankAnimShown,
    setRankAnimShown,
    userStarRankVersion,
    userNewRankVersion,
    workRankVersion
  ]);

  // ========== 在页面 focus 时打点 ==========
  useEffect(() => {
    if (currentIndex === 0) {
      reportExpo('creator', { module: 'rank_list', creator_type: 1 }, true); // 大触榜
    } else if (currentIndex === 1) {
      reportExpo('creator', { module: 'rank_list', creator_type: 2 }, true); // 新人榜
    } else if (currentIndex === 2) {
      reportExpo('creation', { module: 'rank_list' }, true); // 作品榜
    }
  }, [currentIndex]);

  return (
    <Screen
      headerShown={false}
      safeAreaEdges={['top']}
      theme="dark"
      wholePageStyle={{ backgroundColor: StyleSheet.darkTheme.background.page }}
      backgroundView={
        <Image
          source={BG}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            resizeMode: 'cover'
          }}
        />
      }
    >
      {/*
        在大触榜 (currentIndex === 0) 时插入左侧透明区域
        pointerEvents="none"
      */}
      {currentIndex === 0 && (
        <View style={styles.leftOverlay} pointerEvents="none" />
      )}

      {/* 顶部导航 */}
      <View style={styles.topRow}>
        <Pressable style={styles.backButton} onPress={onPressBack}>
          <Image source={ICON_BACK} style={styles.backIcon} />
        </Pressable>

        <View style={{ flex: 1, alignItems: 'center', position: 'relative' }}>
          {!loading && (
            <>
              <View style={styles.topTabsContainer}>
                <Animated.View style={[styles.topTabBg, topIndicatorStyle]} />
                <Pressable
                  style={[styles.topTabItem]}
                  onPress={() => {
                    reportClick('button', {
                      module: 'rank',
                      rank_button: 1
                    });
                    changeTab(0);
                  }}
                >
                  <Text
                    style={[
                      styles.topTabText,
                      currentIndex < 2
                        ? styles.topTabTextActive
                        : styles.topTabTextInactive
                    ]}
                  >
                    创作榜
                  </Text>
                </Pressable>

                <Pressable
                  style={[styles.topTabItem]}
                  onPress={() => {
                    reportClick('button', {
                      module: 'rank',
                      rank_button: 2
                    });
                    changeTab(2);
                  }}
                >
                  <Text
                    style={[
                      styles.topTabText,
                      currentIndex === 2
                        ? styles.topTabTextActive
                        : styles.topTabTextInactive
                    ]}
                  >
                    作品榜
                  </Text>
                </Pressable>
                <Pressable
                  style={[styles.topTabItem]}
                  onPress={() => {
                    reportClick('button', {
                      module: 'rank',
                      rank_button: 11
                    });
                    changeTab(3);
                  }}
                >
                  <Text
                    style={[
                      styles.topTabText,
                      currentIndex === 3
                        ? styles.topTabTextActive
                        : styles.topTabTextInactive
                    ]}
                  >
                    装扮榜
                  </Text>
                </Pressable>
              </View>

              <Pressable
                style={styles.updateCycleContainer}
                onPress={() => {
                  reportClick('rule', {
                    module: 'rank_list'
                  });
                  setShowRules(prev => !prev);
                }}
                hitSlop={15}
              >
                <Text style={styles.updateCycleText}>规则说明</Text>
                <Image source={ICON_QUESTION} style={styles.updateCycleIcon} />
              </Pressable>
            </>
          )}
        </View>
      </View>

      {/* 如果在加载，就显示骨架屏 */}
      {loading ? (
        <PageSkeleton />
      ) : (
        <>
          {/* PagerView */}
          <View style={[styles.contentContainer]}>
            <PagerView
              ref={pagerRef}
              initialPage={currentIndex}
              style={styles.pagerView}
              scrollEnabled={false}
              onPageSelected={e => {
                // 切页时隐藏规则弹窗
                setShowRules(false);
                changeTab(e.nativeEvent.position, 'move');
              }}
            >
              <View style={styles.pageContainer} key="page0">
                <MemoCreationRankPage
                  title={TAB_STATES[0].top}
                  subTitle={TAB_STATES[0].sub || ''}
                  type="dachu"
                  data={dachuData}
                />
              </View>
              <View style={styles.pageContainer} key="page1">
                <MemoCreationRankPage
                  title={TAB_STATES[1].top}
                  subTitle={TAB_STATES[1].sub || ''}
                  type="xinren"
                  data={xinrenData}
                />
              </View>
              <View style={styles.pageContainer} key="page2">
                <MemoWorksRankPage data={worksData} />
              </View>
              <View style={styles.pageContainer} key="page3">
                <DecorationRankPage visible={currentIndex === 3} />
              </View>
            </PagerView>
          </View>
          <Animated.View
            style={[
              styles.subRow,
              opacityAnimatedStyle,
              currentIndex > 1 && {
                opacity: 0
              }
            ]}
          >
            <Pressable
              style={styles.subTabItem}
              onTouchStart={() => {
                reportClick('button', { module: 'rank', rank_button: 4 });
                changeTab(0);
              }}
            >
              <Text
                style={[
                  styles.subTabText,
                  !isXinren
                    ? styles.subTabTextActive
                    : styles.subTabTextInactive
                ]}
              >
                大触榜
              </Text>
            </Pressable>

            <Pressable
              style={styles.subTabItem}
              onTouchStart={() => {
                reportClick('button', { module: 'rank', rank_button: 3 });
                changeTab(1);
              }}
            >
              <Text
                style={[
                  styles.subTabText,
                  isXinren ? styles.subTabTextActive : styles.subTabTextInactive
                ]}
              >
                新人榜
              </Text>
            </Pressable>
            <Animated.View style={[styles.subIndicator, subIndicatorStyle]} />
          </Animated.View>
        </>
      )}

      {/* ========== 上榜动画弹窗 ========== */}
      <RankUpPopup
        visible={showRankAnimation}
        onClose={() => setShowRankAnimation(false)}
        rank={rankNumber}
        avatar={rankAvatar}
        rankType={isWorkRank ? 'work' : 'user'}
        workImagesCount={workImagesCount}
        workIsVideo={workIsVideo}
      />

      {/* ========== 规则说明浮层 ========== */}
      {showRules && (
        <View style={StyleSheet.absoluteFill} pointerEvents="auto">
          <Pressable style={{ flex: 1 }} onPress={() => setShowRules(false)} />

          <View style={styles.rulePopup}>
            <View style={styles.rulePopupInner}>
              {getRuleText(currentIndex).map((line, idx) => (
                <View style={styles.bulletRow} key={idx}>
                  <Text style={styles.bulletSymbol}>•</Text>
                  <Text style={styles.bulletContent}>{line}</Text>
                </View>
              ))}
            </View>
          </View>
          <View style={styles.rulePopupArrow} />
        </View>
      )}

      {/* 左侧空白占位 */}
      <View
        style={{
          position: 'absolute',
          width: 20,
          top: 0,
          bottom: 0
        }}
      />
    </Screen>
  );
}

// 骨架屏
const PageSkeleton = () => {
  return (
    <View style={{ paddingHorizontal: 20, paddingTop: 50 }}>
      <SkeletonColumn gap={30} repeat={6}>
        <SkeletonRow gap={12}>
          <SkeletonSpan height={80} width={80} radius={8} theme={Theme.DARK} />
          <SkeletonColumn style={{ flex: 1 }} gap={6}>
            <SkeletonSpan
              height={16}
              width="50%"
              radius={3}
              theme={Theme.DARK}
            />
            <SkeletonSpan
              height={16}
              width="60%"
              radius={3}
              theme={Theme.DARK}
            />
            <SkeletonSpan
              height={16}
              width="30%"
              radius={3}
              theme={Theme.DARK}
            />
          </SkeletonColumn>
        </SkeletonRow>
      </SkeletonColumn>
    </View>
  );
};

const styles = StyleSheet.create({
  topRow: {
    width: '100%',
    height: 44,
    flexDirection: 'row'
  },
  backButton: {
    width: 24,
    height: 24,
    marginLeft: 16,
    marginVertical: 10,
    justifyContent: 'center',
    alignItems: 'center'
  },
  backIcon: {
    width: 24,
    height: 24
  },
  topTabsContainer: {
    marginLeft: -40,
    width: 184,
    paddingHorizontal: 2,
    height: 34,
    borderRadius: 100,
    backgroundColor: '#392220',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative'
  },
  topTabBg: {
    position: 'absolute',
    width: 60,
    height: 30,
    top: 2,
    left: 0,
    backgroundColor: CommonColor.brand1,
    borderRadius: 27
  },
  topTabItem: {
    width: 60,
    height: 34,
    justifyContent: 'center',
    alignItems: 'center'
  },
  topTabText: {
    fontSize: 14
  },
  topTabTextActive: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14
  },
  topTabTextInactive: {
    color: 'rgba(255,255,255,0.6)'
  },

  updateCycleContainer: {
    position: 'absolute',
    left: 262,
    top: (34 - 15.4) / 2,
    flexDirection: 'row',
    alignItems: 'center'
  },
  updateCycleText: {
    fontSize: 12,
    lineHeight: 16,
    color: 'rgba(255,255,255,0.4)',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    textAlign: 'left'
  },
  updateCycleIcon: {
    marginLeft: 4.25,
    width: 14,
    height: 14,
    resizeMode: 'contain'
  },

  subRow: {
    position: 'absolute',
    height: 30,
    top: 40,
    left: 0,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  subTabItem: {
    width: 66,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center'
  },
  subTabText: {
    fontSize: 14,
    textAlign: 'center'
  },
  subTabTextActive: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14
  },
  subTabTextInactive: {
    color: 'rgba(255,255,255,0.4)',
    fontSize: 14
  },
  subIndicator: {
    position: 'absolute',
    bottom: -2,
    left: 0,
    width: 11,
    height: 4,
    backgroundColor: '#fff',
    borderRadius: 2
  },

  contentContainer: {
    flex: 1,
    marginTop: 10
  },
  pagerView: {
    flex: 1
  },
  pageContainer: {
    flex: 1
  },

  // 大触榜蒙层
  leftOverlay: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 30,
    backgroundColor: 'transparent',
    zIndex: 9999
  },

  rulePopup: {
    position: 'absolute',
    width: 258,
    right: 13,
    top: 38,
    borderRadius: 12,
    backgroundColor: '#3E3E42',
    paddingHorizontal: 12,
    paddingVertical: 6,
    zIndex: 9999,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3
  },
  rulePopupArrow: {
    position: 'absolute',
    top: 33.5,
    right: 33,
    width: 0,
    height: 0,
    borderLeftWidth: 7.5,
    borderRightWidth: 7.5,
    borderBottomWidth: 6.5,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#3E3E42'
  },
  rulePopupInner: {
    flex: 1,
    justifyContent: 'center'
  },

  bulletRow: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  bulletSymbol: {
    color: '#FFFFFFE5',
    fontSize: 12,
    lineHeight: 18,
    fontWeight: '500',
    marginRight: 4,
    includeFontPadding: false,
    textAlignVertical: 'center'
  },
  bulletContent: {
    flex: 1,
    color: '#FFFFFFE5',
    fontSize: 12,
    lineHeight: 18,
    fontWeight: '500'
  }
});

const $schoolEntryStyles = createStyle({
  box: {
    position: 'absolute',
    width: 90,
    right: 0,
    top: 24,
    height: 100,
    zIndex: 1000
  },
  img: { width: '100%', height: '100%' }
});
