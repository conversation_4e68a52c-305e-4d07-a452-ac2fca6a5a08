import { router } from 'expo-router';
import React, { useEffect, useMemo, useState } from 'react';
import { memo } from 'react';
import { Screen, hideLoading, showLoading, showToast } from '@/src/components';
import { usePersistFn } from '@/src/hooks';
import { useCheckCredit } from '@/src/hooks/useCheckCredit';
import { GOODS_NAME, PhotoFrom, useGoodsStore } from '@/src/store/goods';
import { typography } from '@/src/theme';
import { GameType, InvokeType } from '@/src/types';
import { StyleSheet, isIos } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { useParams } from '../../../hooks/useParams';
import { GoodHomeFrom, GoodsHomeState } from '../../goodsHome/types';
import { ENABLE_ROLE_TEMPLATES, GOODS_IP } from '../consts';
import { GenerateScreen } from '../generate';
import { CropperScreen } from './components/Cropper';
import { MaterialScreen } from './components/material';
import { NO_EFFECT_PRODUCT_TYPES } from './components/material/consts';
import {
  GoodsProductType,
  GoodsType
} from '@/proto-registry/src/web/raccoon/goods/common_pb';
import { useShallow } from 'zustand/react/shallow';
import { ImageType } from './type';

enum EditMode {
  Crop = 'crop',
  Loading = 'loading',
  Material = 'material'
}
const BOTTOM_MARGIN = isIos ? 44 : 14;
export const GoodsEdit = memo(() => {
  const [editMode, setEditMode] = useState<EditMode>();
  const { source } = useParams();
  const { template, selectedPhoto } = useGoodsStore(
    useShallow(state => {
      return {
        template: state.selectedTemplate,
        selectedPhoto: state.selectedPhoto
      };
    })
  );
  const invokeType = useMemo(() => {
    switch (template?.productType) {
      case GoodsProductType.BADGE:
        return InvokeType.INVOKE_UNKNOWN;
      case GoodsProductType.STICKER:
        return InvokeType.INVOKE_GOODS_STICKER_REDO;
      case GoodsProductType.LASER_TICKET:
        return InvokeType.INVOKE_GOODS_LASER_TICKET_REDO;
    }
    return InvokeType.INVOKE_UNKNOWN;
  }, [template]);
  const { check } = useCheckCredit();
  const [generateImgUrl, setGenerateImgUrl] = useState<string>(
    selectedPhoto?.url ?? ''
  );
  const [materialImgs, setMaterialImgs] = useState<ImageType[]>([]);
  useEffect(() => {
    generate();
  }, [template]);
  const generate = usePersistFn(async (isRegen: boolean = false) => {
    !isRegen &&
      reportExpo('expo', {
        module: 'create_goods',
        goods_type: GOODS_NAME[template?.productType ?? ''],
        source,
        from: useGoodsStore.getState().selectedPhoto?.from
      });
    useGoodsStore.setState({
      selectedEffect: undefined
    });
    if (template?.goodsType === GoodsType.BADGE) {
      setEditMode(EditMode.Crop);
    } else {
      if (isRegen) {
        const res = await check(invokeType, GameType.GOODS, GOODS_IP)
          .then(r => {
            return r;
          })
          .catch(() => {
            showToast('出错了，请重试');
            return false;
          });
        if (!res) {
          return;
        }
      }
      if (
        Object.keys(ENABLE_ROLE_TEMPLATES).includes(`${template?.productType}`)
      ) {
        const roles = useGoodsStore.getState().roles;
        roles.length > 0 && setGenerateImgUrl(roles[0].material);
      }
      setEditMode(EditMode.Loading);
    }
  });
  const onFinishCreate = usePersistFn(() => {
    router.back();
    router.back();
    if (useGoodsStore.getState().selectedPhoto?.from === PhotoFrom.MakePhoto) {
      router.back();
    }
    useGoodsStore.getState().reset();
    router.navigate({
      pathname: '/goods/home',
      params: {
        from: GoodHomeFrom.Create,
        state: GoodsHomeState.Me
      }
    });
  });
  const title = useMemo(() => {
    switch (editMode) {
      case EditMode.Crop:
        return '调整图片';
      case EditMode.Material:
        return template?.productType &&
          NO_EFFECT_PRODUCT_TYPES.includes(template?.productType)
          ? '预览谷子'
          : '选择材质';
    }
    return '';
  }, [editMode]);
  const onCropConfirm = usePersistFn(async (img?: string) => {
    reportClick('generate_button', {
      module: 'create_goods',
      goods_type: GOODS_NAME[template?.productType ?? ''],
      source,
      from: useGoodsStore.getState().selectedPhoto?.from
    });
    if (!img) {
      return;
    }
    const formatterImg = img.startsWith('file://') ? img : `file://${img}`;
    showLoading();
    setMaterialImgs([
      {
        url: formatterImg,
        photoId: selectedPhoto?.photoId
      }
    ]);
    hideLoading();
    setEditMode(EditMode.Material);
  });
  const onGenerateFinish = usePersistFn((images: ImageType[]) => {
    setMaterialImgs(images);
    setEditMode(EditMode.Material);
  });
  if (editMode === EditMode.Loading) {
    return (
      <GenerateScreen
        onCancel={back}
        image={generateImgUrl}
        onGenerateFinish={onGenerateFinish}
      />
    );
  }
  return (
    <Screen
      theme="dark"
      onBack={back}
      withWaterMark={editMode === EditMode.Material}
      title={title}
      screenStyle={styles.screen}
      headerStyle={styles.header}
      safeAreaEdges={['top']}
    >
      {editMode === EditMode.Crop && (
        <CropperScreen onCropConfirm={onCropConfirm} />
      )}
      {editMode === EditMode.Material && (
        <MaterialScreen
          onFinishCreate={onFinishCreate}
          onRegenerate={generate}
          images={materialImgs ?? []}
        />
      )}
    </Screen>
  );
  function back() {
    router.back();
  }
});

const styles = StyleSheet.create({
  screen: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(14, 14, 17, 1)'
  },
  imageIcon: {
    opacity: 0.9,
    width: 15,
    height: 13.5
  },
  imageFull: {
    position: 'absolute',
    width: '100%',
    height: '100%'
  },
  button: {
    height: 56,
    gap: 6,
    width: 343,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row'
  },

  buttonText: {
    fontSize: 16,
    letterSpacing: -0.2,
    fontFamily: typography.fonts.feed,
    color: '#fff',
    textAlign: 'center',
    opacity: 0.9
  },
  header: {
    minHeight: 44,
    zIndex: 1
  },
  container: {
    zIndex: 1,
    elevation: 1,
    position: 'absolute',
    bottom: BOTTOM_MARGIN,
    width: '100%',
    gap: 30
  },
  titleContainer: {
    width: 260,
    alignSelf: 'center'
  },
  templateText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: {
      width: 0,
      height: 1
    },
    position: 'absolute',
    bottom: 8,
    width: '100%',

    textShadowRadius: 2
  },
  title: {
    fontSize: 24,
    width: '100%',
    letterSpacing: -0.5,
    lineHeight: 30,
    fontFamily: typography.fonts.feed, // 'YEFONTAoYeHei'
    color: '#fff',
    textAlign: 'center'
  },
  blush: {
    position: 'absolute',
    width: 96,
    height: 13,
    bottom: -5,
    right: 40
  },
  imageItem: {
    borderRadius: 10,
    backgroundColor: '#a27a7f',
    width: 93,
    height: 124,
    overflow: 'hidden'
  },
  gradient: {
    height: '35%',
    width: '100%',
    position: 'absolute',
    bottom: 0,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10
  },
  templateContainer: {
    flex: 1,
    width: '100%',
    height: 132,
    overflow: 'visible',
    justifyContent: 'center',
    alignItems: 'center'
  },
  wrapper: {
    borderColor: 'rgba(255, 106, 59, 1)',
    borderWidth: 2,
    width: 101,
    padding: 0,
    alignItems: 'center',
    justifyContent: 'center'
  }
});
