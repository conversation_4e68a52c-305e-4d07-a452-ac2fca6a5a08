import { useFocusEffect } from 'expo-router';
import { useNavigation } from 'expo-router';
import { debounce } from 'lodash-es';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import React from 'react';
import { View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import Animated, { FadeIn } from 'react-native-reanimated';
import { uploadMakePhotoImg } from '@/src/api';
import { goodsClient } from '@/src/api/goods';
import { Text, hideLoading, showLoading, showToast } from '@/src/components';
import { AiTag } from '@/src/components/aiTag';
import { PrimaryButton } from '@/src/components/primaryButton';
import CreditWrapper from '@/src/components/v2/credit-wrapper';
import { usePersistFn, useScreenSize } from '@/src/hooks';
import { GOODS_NAME, useGoodsStore } from '@/src/store/goods';
import { StyleSheet } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { reportClick, reportExpo } from '@/src/utils/report';
import { useParams } from '../../../../../hooks/useParams';
import { GOODS_IP } from '../../../consts';
import { ImageType } from '../../type';
import { RoleInfo } from '@/proto-registry/src/web/raccoon/common/role_pb';
import { InvokeType } from '@/proto-registry/src/web/raccoon/common/types_pb';
import { GameType } from '@/proto-registry/src/web/raccoon/common/types_pb';
import { GoodsType } from '@/proto-registry/src/web/raccoon/goods/common_pb';
import { GoodsProductType } from '@/proto-registry/src/web/raccoon/goods/common_pb';
import { CreateGoodsRsp } from '@/proto-registry/src/web/raccoon/goods/goods_pb';
import { useShallow } from 'zustand/react/shallow';
import { BadgeView } from './BadgeView';
import { MaterialsPanel } from './MaterialsPanel';
import { RayView } from './RayView';
import { StickerView } from './StickerView';
import { NO_EFFECT_PRODUCT_TYPES } from './consts';

type Props = {
  images: ImageType[];
  onRegenerate: (isRegen: boolean) => void;
  onFinishCreate: () => void;
};
export interface MakeHandle {
  make: () => Promise<string[]>;
  makeRawImages: () => Promise<string[]>;
}

export const MaterialScreen = memo((props: Props) => {
  const { images, onRegenerate, onFinishCreate } = props;
  const ref = useRef<MakeHandle>(null);
  const [loadFinish, setLoadFinish] = useState(false);
  const loadFinishRef = useRef(false);
  const [enable, setEnable] = useState(true);
  const { source } = useParams();
  const navigation = useNavigation();
  const { template } = useGoodsStore(
    useShallow(state => {
      return {
        template: state.selectedTemplate
      };
    })
  );
  const invokeType = useMemo(() => {
    switch (template?.productType) {
      case GoodsProductType.BADGE:
        return InvokeType.INVOKE_UNKNOWN;
      case GoodsProductType.STICKER:
        return InvokeType.INVOKE_GOODS_STICKER_REDO;
      case GoodsProductType.LASER_TICKET:
        return InvokeType.INVOKE_GOODS_LASER_TICKET_REDO;
    }
    return InvokeType.INVOKE_UNKNOWN;
  }, [template]);
  useEffect(() => {
    reportExpo('generate_result', {
      module: 'create_goods',
      goods_type: GOODS_NAME[template?.productType ?? ''],
      source,
      from: useGoodsStore.getState().selectedPhoto?.from
    });
  }, [template]);

  const beforeRemove = usePersistFn(() => {
    loadFinishRef.current = true;
    hideLoading();
    navigation.removeListener('beforeRemove', beforeRemove);
  });

  useEffect(() => {
    if (loadFinish) {
      hideLoading();
    } else {
      setTimeout(() => !loadFinishRef.current && showLoading(), 200);
      navigation.addListener('beforeRemove', beforeRemove);
    }
  }, [loadFinish]);

  const onImageLoadEnd = usePersistFn(() => {
    setLoadFinish(true);
    loadFinishRef.current = true;
  });
  const regen = usePersistFn(() => {
    reportClick('remake', {
      module: 'create_goods',
      goods_type: GOODS_NAME[template?.productType ?? ''],
      source
    });
    onRegenerate(true);
  });
  const confirm = usePersistFn(
    debounce(
      async () => {
        reportClick('generate_result', {
          module: 'create_goods',
          goods_type: GOODS_NAME[template?.productType ?? ''],
          source,
          from: useGoodsStore.getState().selectedPhoto?.from,
          cover_type: useGoodsStore.getState().selectedEffect?.effectId
        });
        if (!enable) {
          return;
        }
        showLoading();
        setEnable(false);
        let imageUrls: string[] = [];
        let rawImageUrls: string[] = [];
        try {
          imageUrls = (await ref.current?.make()) ?? [];
          rawImageUrls = (await ref.current?.makeRawImages()) ?? [];
          const res = await Promise.all(
            imageUrls.map(async img => {
              return (await uploadMakePhotoImg(img)).image;
            })
          );
          const rawRes = await Promise.all(
            rawImageUrls.map(async img => {
              return (await uploadMakePhotoImg(img)).image;
            })
          );

          let createData: CreateGoodsRsp | undefined = undefined;
          const isUpload =
            useGoodsStore.getState().selectedPhoto?.photoType === 'user';
          const idParams = isUpload
            ? { sourceImageId: images[0].photoId }
            : { sourcePhotoId: images[0].photoId };
          if (res.length === 1) {
            createData = await goodsClient.createGoods({
              ...template,
              extra: {
                roles: images[0].roles as RoleInfo[] | undefined
              },
              displayImageId: res[0]?.id,
              rawImageId: rawRes[0]?.id,
              ...idParams
            });
          } else if (res.length > 1) {
            createData = await goodsClient.createGoods({
              ...template,
              displayImageId: res[0]?.id,
              rawImageId: rawRes[0]?.id,
              extra: {
                backImageId: res[1]?.id,
                rawBackImageId: rawRes[1]?.id
              },
              ...idParams
            });
          }
          useGoodsStore.setState({
            createData: {
              id: createData?.goodsId ?? '',
              code: createData?.code ?? '',
              censoredState: createData?.censoredState,
              images: res.map(r => r?.url ?? ''),
              template: template!,
              isFirst: createData?.first ?? false,
              photo: useGoodsStore.getState().selectedPhoto,
              source,
              effectId: useGoodsStore.getState().selectedEffect?.effectId
            }
          });
          onFinishCreate();
        } catch (e) {
          errorReport('goods generate eror', ReportError.REQUEST, e, {
            template
          });
          showToast('出错啦,请重试');
        } finally {
          hideLoading();
          setEnable(true);
        }
      },
      200,
      { leading: true, trailing: false }
    )
  );
  if (images.length < 1) {
    return <></>;
  }
  return (
    <View style={[{ flex: 1 }]}>
      <Animated.View
        entering={FadeIn}
        style={[styles.container, StyleSheet.centerStyle]}
      >
        {template?.goodsType === GoodsType.BADGE && (
          <BadgeView
            ref={ref}
            imageUrl={images[0].url}
            onImageLoadFinish={onImageLoadEnd}
            showOverlay={loadFinish}
          />
        )}
        {template?.goodsType === GoodsType.LASER_TICKET && (
          <RayView
            ref={ref}
            onImageLoadFinish={onImageLoadEnd}
            imageUrls={images.map(i => {
              return i.url;
            })}
          />
        )}
        {template?.goodsType === GoodsType.STICKER && (
          <StickerView
            ref={ref}
            onImageLoadFinish={onImageLoadEnd}
            imageUrl={images[0].url}
          />
        )}
      </Animated.View>

      {template?.productType &&
        !NO_EFFECT_PRODUCT_TYPES.includes(template?.productType) && (
          <MaterialsPanel />
        )}
      <View
        style={[styles.confirm, StyleSheet.centerStyle, StyleSheet.rowStyle]}
      >
        {invokeType !== InvokeType.INVOKE_UNKNOWN && (
          <CreditWrapper
            invokeType={invokeType}
            gameType={GameType.GOODS}
            ip={GOODS_IP}
            buttonContainer={
              <TouchableOpacity onPress={regen} style={styles.regen}>
                <Text style={styles.regenText}>重新生成</Text>
              </TouchableOpacity>
            }
          />
        )}
        <View
          style={[
            styles.button,
            StyleSheet.centerStyle,
            {
              opacity: enable && loadFinish ? 1 : 0.3
            }
          ]}
        >
          <PrimaryButton
            width={invokeType !== InvokeType.INVOKE_UNKNOWN ? 212 : 343}
            height={54}
            usingGradient={false}
            onPress={confirm}
          >
            {template?.productType &&
            !NO_EFFECT_PRODUCT_TYPES.includes(template?.productType)
              ? '确认'
              : '制作'}
          </PrimaryButton>
        </View>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginTop: 15
  },
  confirm: {
    position: 'absolute',
    bottom: 36,
    height: 54,
    gap: 16,
    paddingHorizontal: 16,
    width: '100%'
  },
  button: {
    borderRadius: 43
  },
  regenText: {
    fontSize: 15,
    fontWeight: '500',
    fontFamily: 'PingFang SC',
    color: '#fff',
    textAlign: 'center'
  },
  regen: {
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    height: 54,
    flexDirection: 'row',
    width: 120,
    alignItems: 'center',
    justifyContent: 'center'
  }
});
