import { useMemoizedFn } from 'ahooks';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Dimensions, TouchableOpacity, View } from 'react-native';
import {
  GestureDetector,
  GestureHandlerRootView
} from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import Animated from 'react-native-reanimated';
import Carousel, {
  CarouselRenderItem,
  ICarouselInstance
} from 'react-native-reanimated-carousel';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import UGCRolePhotoSet from '@/src/bizComponents/role-create/ugcRolePhotoSet';
import { Icon, Text, showToast } from '@/src/components';
import { AiTag } from '@/src/components/aiTag';
import { BaseModal } from '@/src/components/modal/BaseModal';
import { Confirm } from '@/src/components/popup/confirmModalGlobal/CustomConfirm';
import CreditWrapper from '@/src/components/v2/credit-wrapper';
import { ROLE_CREATE_PORTAL } from '@/src/constants';
import { usePersistFn, useScreenSize } from '@/src/hooks';
import { showNoBatteryTip } from '@/src/hooks/useCheckCredit';
import { useOneRunning } from '@/src/hooks/useOneRunning';
import { usePanelShowHide } from '@/src/hooks/usePanelShowHide';
import { selectState } from '@/src/store/_utils';
import { useCreditStore } from '@/src/store/credit';
import { UGCPhotoInfo, useCreateRoleStore } from '@/src/store/role/role-create';
import { Theme } from '@/src/theme/colors/type';
import { GameType, InvokeType } from '@/src/types';
import { dp2px } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { reportClick, reportExpo } from '@/src/utils/report';
import { Image } from '@Components/image';
import { StyleSheet, createStyle } from '@Utils/StyleSheet';
import ToastInner from '../../credit/toast';
import { PointsCode } from '@/proto-registry/src/web/raccoon/errorcode/errorcode_pb';
import { useShallow } from 'zustand/react/shallow';

const POINT_ACTIVE = require('@Assets/makephoto/icon-active.png');
const POINT_NORMAL = require('@Assets/makephoto/icon-point.png');

const REGEN = require('@Assets/role/regen_btn.png');
const PANEL_HEIGHT = dp2px(660);

const { width: viewerWidth } = Dimensions.get('window');

const cardWidth = (viewerWidth - 30) / 2;

const viewerHeight = (cardWidth * 2) / 0.75 + 10;

export const UGCRoleSelectModal = ({
  visible,
  onClose,
  onChoose
}: {
  visible: boolean;
  onClose: () => void;
  onChoose: (photo: UGCPhotoInfo) => void;
}) => {
  const [selected, setSelected] = useState<UGCPhotoInfo | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { syncCredits } = useCreditStore(
    useShallow(state => ({
      totalCredits: state.totalCredits,
      syncCredits: state.syncCredits
    }))
  );
  const [confirmVisible, setConfirmVisible] = useState(false);
  const carouselRef = useRef<ICarouselInstance>(null);

  const {
    createFormData,
    isUgcPhotosLoading,
    genRolePhotos,
    ugcPhotos,
    abortController,
    resetRolePhotos,
    changeCreateFormData,
    roleImgInfo
  } = useCreateRoleStore(
    useShallow(state =>
      selectState(state, [
        'createFormData',
        'changeCreateFormData',
        'genRolePhotos',
        'ugcPhotos',
        'abortController',
        'resetRolePhotos',
        'isUgcPhotosLoading',
        'isModify',
        'reset',
        'createOrModifyRole',
        'roleImgInfo'
      ])
    )
  );

  const submitDisabled = isUgcPhotosLoading || !selected?.imageId;
  const { width } = useScreenSize();
  const buttonSize = useMemo(() => {
    return width - 52 - 52;
  }, [width]);
  const onSubmit = useOneRunning(async () => {
    reportClick('image_confirm', {
      module: 'character_create',
      photo_id: selected?.imageId
    });
    if (selected) {
      changeCreateFormData({
        ...createFormData,
        imageFromUpload: false
      });
      onChoose(selected);
    }
  });

  const handleNavToView = (idx: number) => {
    carouselRef.current?.scrollTo({
      index: idx,
      animated: true
    });
    setCurrentIndex(idx);
  };

  const showCreditToast = useMemoizedFn(() => {
    showNoBatteryTip();
  });

  const generate = usePersistFn((regen: boolean) => {
    genRolePhotos(
      {
        ...createFormData,
        refImageId: roleImgInfo?.picId || roleImgInfo?.id,
        regen
      },
      msg => {
        if (msg.finished) {
          syncCredits();
        }
      },
      err => {
        syncCredits();
        if (err instanceof ErrorRes) {
          errorReport('[createRole error boundary]', ReportError.CERDIT, err);
          const reason = err.reason;
          if (
            reason?.includes(PointsCode.POINTS_ERR_INSUFFICIENT_POINTS + '')
          ) {
            showCreditToast();
          }
        }
      }
    );
  });

  const handleRegen = () => {
    reportClick('repaint_button', { module: 'character_create' });
    generate(true);
    if (isUgcPhotosLoading) {
      showToast('正在努力生成中～');
      return;
    }

    setTimeout(() => {
      const newUgcPhotos = useCreateRoleStore.getState().ugcPhotos;
      handleNavToView(newUgcPhotos.length - 1);
    });
  };

  const renderItem: CarouselRenderItem<(UGCPhotoInfo | null)[]> = useMemoizedFn(
    ({ item: photoSet }) => (
      <UGCRolePhotoSet
        width={viewerWidth}
        cardWidth={cardWidth}
        photoSet={photoSet}
        selectedPhoto={selected}
        onPhotoSelect={setSelected}
      />
    )
  );

  const { hideGesture, showHideAnimatedStyle, hidePanel, showPanel } =
    usePanelShowHide({
      height: PANEL_HEIGHT,
      inoutTime: 200,
      enablePullClose: false,
      onPanelShow() {},
      onPanelHide() {
        abortController?.abort();
        resetRolePhotos();
        setSelected(null);
        setCurrentIndex(0);
        onClose();
      }
    });
  useEffect(() => {
    if (visible) {
      reportExpo('choose_image', { module: 'character_create' });
      showPanel();
      generate(false);
    }
  }, [visible]);
  const onPressClose = usePersistFn(() => {
    setConfirmVisible(true);
  });
  const onConfirm = usePersistFn(() => {
    onConfirmClose();
    hidePanel();
  });
  const onConfirmClose = usePersistFn(() => {
    setConfirmVisible(false);
  });
  return (
    <>
      <View
        style={[
          StyleSheet.absoluteFill,
          {
            pointerEvents: visible ? 'auto' : 'none',
            opacity: visible ? 1 : 0
          }
        ]}
      >
        <View
          style={[
            StyleSheet.absoluteFill,
            {
              backgroundColor: StyleSheet.hex(
                StyleSheet.currentColors.black,
                0.8
              )
            }
          ]}
        />
        <Animated.View style={[showHideAnimatedStyle, $styles.screenContainer]}>
          <GestureDetector gesture={hideGesture}>
            <GestureHandlerRootView
              style={[
                $styles.container2,
                { width: viewerWidth },
                $styles.screenContainer
              ]}
            >
              <View style={[$styles.parent]}>
                <Text style={$styles.text}>选择角色形象</Text>
                <TouchableOpacity
                  style={$styles.frameChild}
                  onPress={onPressClose}
                >
                  <Icon size={26} icon={'close_dark_fill'} />
                </TouchableOpacity>
              </View>
              <Carousel
                data={ugcPhotos || []}
                renderItem={renderItem}
                ref={carouselRef}
                onSnapToItem={setCurrentIndex}
                width={viewerWidth}
                height={viewerHeight}
                loop={false}
                mode="parallax"
                modeConfig={{
                  parallaxScrollingScale: 1,
                  parallaxScrollingOffset: 15,
                  parallaxAdjacentItemScale: 0.95
                }}
              />
              {ugcPhotos.length > 1 && (
                <View style={[StyleSheet.rowStyle, $styles.pointerBox]}>
                  {ugcPhotos.map((item, index) => {
                    if (index === currentIndex) {
                      return (
                        <Image
                          key={`point-${index}-active`}
                          style={{ width: 18, height: 18, resizeMode: 'cover' }}
                          source={POINT_ACTIVE}
                        />
                      );
                    }
                    return (
                      <Image
                        key={`point-${index}-normal`}
                        style={{ width: 18, height: 18, resizeMode: 'cover' }}
                        source={POINT_NORMAL}
                      />
                    );
                  })}
                </View>
              )}

              <View style={[$styles.regenBox, StyleSheet.rowStyle]}>
                <CreditWrapper
                  invokeType={InvokeType.INVOKE_DRAWING_UGC_IMAGE_REDO}
                  gameType={GameType.DRAWING}
                  $containerStyle={{
                    overflow: 'visible',
                    opacity: isUgcPhotosLoading ? 0.3 : 1
                  }}
                  $cornerstyle={{
                    position: 'absolute',
                    top: -5,
                    left: 25,
                    width: 42
                  }}
                  buttonContainer={
                    <TouchableOpacity
                      disabled={isUgcPhotosLoading}
                      onPress={handleRegen}
                      style={$styles.regenBtn}
                    >
                      <Image
                        source={REGEN}
                        style={$styles.regenBtnInner}
                        resizeMode="contain"
                      />
                    </TouchableOpacity>
                  }
                />
                <TouchableOpacity
                  disabled={submitDisabled}
                  style={[
                    $styles.submit,
                    {
                      opacity: submitDisabled ? 0.3 : 1
                    }
                  ]}
                  onPress={onSubmit}
                >
                  <LinearGradient
                    style={[
                      $styles.btn,
                      StyleSheet.centerStyle,
                      { width: buttonSize }
                    ]}
                    locations={[0, 1]}
                    colors={['#ff6a3b', '#ff8a65']}
                    useAngle={true}
                    angle={49.41}
                  >
                    <Text style={$styles.ta}>就Ta了</Text>
                  </LinearGradient>
                </TouchableOpacity>
                <View />
              </View>
            </GestureHandlerRootView>
          </GestureDetector>
        </Animated.View>
        <AiTag />
      </View>
      {confirmVisible && (
        <Confirm
          outerVisible={true}
          theme={Theme.DARK}
          title={'确认退出吗？'}
          content="取消后生成记录不保留"
          confirmText="确认"
          onClose={onConfirmClose}
          onConfirm={onConfirm}
        />
      )}
    </>
  );
};

const $styles = createStyle({
  screenContainer: {
    position: 'absolute',
    bottom: 0,
    padding: 0,
    paddingBottom: 0,
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    height: PANEL_HEIGHT,
    backgroundColor: 'rgba(37, 38, 42, 1)'
  },
  mask: {},
  container2: {
    height: PANEL_HEIGHT
  },
  btn: { height: 54, borderRadius: 47, flex: 1 },
  ta: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'PingFang SC',
    color: '#fff',
    textAlign: 'center'
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'PingFang SC',
    color: '#fff',
    textAlign: 'center',
    zIndex: 0
  },
  regenBtn: {
    borderRadius: 100,
    height: 52,
    width: 52,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 11,
    paddingVertical: 8,
    overflow: 'hidden',
    backgroundColor: 'transparent'
  },
  regenBg: { width: 52, height: 52, overflow: 'visible' },
  regenBtnInner: {
    width: 54,
    aspectRatio: 1,
    overflow: 'visible'
  },
  frameChild: {
    position: 'absolute',
    top: 14,
    right: 16
  },
  parent: {
    height: 54,
    alignItems: 'center',
    justifyContent: 'center'
  },
  container: {
    flex: 1,
    borderColor: 'red',
    alignItems: 'center',
    paddingHorizontal: 20,
    justifyContent: 'center'
  },
  pointerBox: {
    justifyContent: 'center',
    paddingVertical: 12,
    width: '100%'
  },
  regenBox: {
    position: 'absolute',
    bottom: 30,
    gap: 20,
    marginLeft: 16,
    marginRight: 16,
    width: '100%'
  },
  regenDeco: { width: 150, height: 54 },
  submit: {
    height: 54
  }
});
