import { router } from 'expo-router';
import React, { FC, memo, useEffect, useMemo, useRef, useState } from 'react';
import { KeyboardAvoidingView, TouchableOpacity, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { danceTogetherClient } from '@/src/api/boomWen';
import { jellyCatClient } from '@/src/api/jellycat';
import { publishLivePhoto, regenLivePhoto } from '@/src/api/livephoto';
import { publishClient } from '@/src/api/makephotov2';
import { otakudanceClient } from '@/src/api/otakudance';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import ToastInner from '@/src/bizComponents/credit/toast';
import {
  Icon,
  Image,
  Text,
  hideLoading,
  showLoading,
  showToast
} from '@/src/components';
import { handleGenerateError } from '@/src/components/makePhoto/utils';
import CreditWrapper from '@/src/components/v2/credit-wrapper';
import { ExtendedEdge, usePersistFn, useScreenSize } from '@/src/hooks';
import { Go2HomeScene, useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useLiveStore } from '@/src/store/live';
import { useNormalGameStore } from '@/src/store/normalGame';
import { fullStyle, imageFullStyle } from '@/src/theme';
import { GameType, InvokeType, TabItemType } from '@/src/types';
import { dp2px } from '@/src/utils';
import { StyleSheet } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { CommonEventBus } from '@/src/utils/event';
import { reportClick, reportExpo } from '@/src/utils/report';
import { savePicture } from '@/src/utils/savePicture';
import { EffectRender } from '@Components/effectRender';
import { Screen } from '@Components/screen';
import { TrackType } from '../liveScreen/constants';
import { BgmData, BgmView } from './bgmComponents/BgmView';
import { PhotosHeader } from './components/PhotosHeader';
import { RegenBtn } from './components/RegenBtn';
import { SpecialEffectsPanel } from './components/specialEffectsPanel';
import { DanceTogetherExtInfo } from '@/proto-registry/src/web/raccoon/common/dance_together_pb';
import { JellycatExtInfo } from '@/proto-registry/src/web/raccoon/common/jellycat_pb';
import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb';
import { OtakudanceExtInfo } from '@/proto-registry/src/web/raccoon/common/otakudance_pb';
import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
import { PointsCode } from '@/proto-registry/src/web/raccoon/errorcode/errorcode_pb';
import { MagicFlowExtInfo } from '@/proto-registry/src/web/raccoon/magicflow/magicflow_common_pb';
import { BgmSelectPanel } from './BgmSelectPanel';
import { PublishButton } from './PublishButton';
import PublishDetail, { PublishInfo } from './PublishDetail';
import { VideoHandle, VideoHeader } from './VideoHeader';
import { REGEN_INVOKE_TYPE_MAP } from './consts';
import { useEditHandler } from './useEditHandler';
import { usePlayHandler } from './usePlayHandler';
import { usePublishHandler } from './usePublishHandler';
import { getGameIdFromExtInfo, isOwnAudioPlayType } from './utils';
import { MaterializeXiaoli } from '@/src/components/materialize/MaterializeXiaoli';

const SafeAreaEdges: ExtendedEdge[] = ['top', 'bottom'];

export type Edit = {
  bgm?: BgmData;
} & PublishInfo;
type Props = {
  extData?:
    | LivePhotoExtInfo
    | OtakudanceExtInfo
    | JellycatExtInfo
    | ReimagineExtInfo
    | DanceTogetherExtInfo
    | MagicFlowExtInfo;
  showRandomBgm: boolean;
  height: number;
  width: number;
  videoUrl: string;
  editInfo?: Edit;
  showVideo: boolean;
  cover?: string;
  gameType: `${GameType}`;
};

const LivePhotoPublish: FC<Props> = props => {
  const {
    extData,
    height,
    width,
    videoUrl,
    editInfo,
    showRandomBgm,
    cover,
    showVideo,
    gameType
  } = props;
  useEffect(() => () => useLiveStore.getState().clearBmgInfo(), []);
  const initPublishInfo: PublishInfo = useMemo(() => {
    return {
      title: editInfo?.title ?? '',
      content: editInfo?.content ?? '',
      topics: editInfo?.topics ?? [],
      saveToLocal: editInfo?.saveToLocal ?? false
    };
  }, [editInfo]);
  const insets = useSafeAreaInsets();
  const { width: screenWidth, height: screenHeight } = useScreenSize();
  const videoRef = useRef<VideoHandle>(null);
  const { selectedBgm, selectBgm } = usePlayHandler(
    showVideo,
    videoRef,
    editInfo?.bgm,
    showRandomBgm,
    (extData as LivePhotoExtInfo)?.livephotoId,
    (extData as LivePhotoExtInfo)?.musicTags,
    gameType,
    (extData as LivePhotoExtInfo)?.bgmTagInfo
  );

  const bgmInfo = useMemo(() => {
    // 这里特别恶心，后端每个玩法都返回一个不同的类型并且数据结构不一致
    if (isOwnAudioPlayType(gameType)) {
      // 新春
      if ((extData as DanceTogetherExtInfo)?.bgmTitle) {
        const videoData = extData as DanceTogetherExtInfo;
        return {
          bgmId: '',
          bgmName: videoData?.bgmTitle ?? '匿名歌曲',
          noSelected: true
        };
      }
      // 宅舞
      if ((extData as OtakudanceExtInfo)?.hybTpl) {
        const videoData = extData as OtakudanceExtInfo;
        return {
          bgmId: videoData?.hybTpl?.hybId ?? '',
          bgmName: videoData?.hybTpl?.hybAudioName ?? '匿名歌曲',
          noSelected: true
        };
      }
    }
    return selectedBgm;
  }, [extData, gameType, selectedBgm]);

  useEffect(() => {
    if (isOwnAudioPlayType(gameType)) {
      return;
    }
    editInfo?.bgm && selectBgm(editInfo.bgm);
  }, [editInfo?.bgm, gameType]);
  const [bgmPanelVisible, setBgmPanelVisible] = useState(false);
  const [effectId, selectEffectId] = useState('');
  const ref = useRef<ScrollView>(null);
  const videoViewHeight = useMemo(() => {
    return screenHeight - insets.top - insets.bottom - dp2px(56) - dp2px(285);
  }, [screenHeight]);
  const maxWidth = useMemo(() => {
    return screenWidth - 40;
  }, [screenWidth]);
  const { editHandler, enable } = useEditHandler({
    onSubmit
  });
  const isNormalGame = useMemo(() => {
    return useNormalGameStore
      .getState()
      .enableGameTypes.includes(Number(gameType));
  }, [gameType]);
  const ratio = useMemo(() => {
    if (!width || !height) {
      return 1;
    }
    return Math.min(width / height, 1);
  }, [height, width]);

  const videoViewWidth = useMemo(() => {
    return Math.min(ratio * videoViewHeight, maxWidth);
  }, [ratio, videoViewHeight, screenWidth]);

  const VideoHeightInner = useMemo(() => {
    return videoViewWidth / ratio;
  }, [ratio, videoViewWidth]);
  const bgmWidth = useMemo(() => {
    return Math.min(120, (videoViewWidth ?? 0) * 0.6);
  }, [videoViewWidth]);
  const onContentSizeChange = usePersistFn(() => {
    ref?.current?.scrollToEnd({ animated: false });
  });
  const onBgmPanelHide = usePersistFn(() => {
    opacitySharedValue.value = withTiming(1);
    setTimeout(() => {
      setBgmPanelVisible(false);
    }, 200);
  });
  const onBgmPanelShow = usePersistFn(() => {
    if (isOwnAudioPlayType(gameType)) return;
    setBgmPanelVisible(true);
    opacitySharedValue.value = withTiming(0);
  });
  const { onRetry, publish } = usePublishHandler({
    gameType: Number(gameType),
    extData,
    cover: cover ?? ''
  });
  const opacitySharedValue = useSharedValue(1);
  const $opacityAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacitySharedValue.value
    };
  });
  const onPressSubmit = usePersistFn(() => {
    editHandler.onSubmit();
  });
  const [openSpecialEffectsModal, setOpenSpecialEffectsModal] = useState(false);
  const onCloseSpecialEffectsModal = usePersistFn(() => {
    setOpenSpecialEffectsModal(false);
    // videoRef.current?.replay();
  });
  const getRecommendParams = usePersistFn(() => {
    const gameId = getGameIdFromExtInfo(gameType, extData);
    const photos = (extData as MagicFlowExtInfo)?.photos ?? [];
    return {
      photoId: photos.map(i => i.photoId),
      gameId: gameId ? [gameId] : [],
      gameType: Number(gameType) as unknown as GameType
    };
  });
  const renderRightScene = usePersistFn(() => {
    if (gameType === `${GameType.OTAKUDANCE}`) {
      return (
        <TouchableOpacity
          style={st.specialEffectWrap}
          onPress={() => setOpenSpecialEffectsModal(true)}
        >
          <Icon icon="special_effects" style={st.specialIcon} />
          <Text style={st.specialText}>添加特效</Text>
        </TouchableOpacity>
      );
    }
    return <MaterializeXiaoli logParams={{module:'publish', game_type:gameType}}/>;
  });

  return (
    <>
      <Screen
        theme="dark"
        title="发布"
        onBack={router.back}
        headerStyle={st.headerStyle}
        safeAreaEdges={SafeAreaEdges}
        rightStyle={st.headerRightStyle}
        headerRight={renderRightScene}
      >
        <KeyboardAvoidingView
          style={{
            elevation: 100,
            overflow: 'hidden'
          }}
          behavior={'position'}
        >
          <ScrollView
            keyboardShouldPersistTaps="handled"
            bounces={false}
            ref={ref}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={onContentSizeChange}
            style={{
              width: screenWidth,
              height: screenHeight - insets.top - insets.bottom - dp2px(114)
            }}
          >
            <View
              style={{
                position: 'relative',
                height: videoViewHeight,
                width: screenWidth
              }}
            >
              {isNormalGame ? (
                <PhotosHeader
                  data={(extData as MagicFlowExtInfo)?.photos ?? []}
                  viewHeight={videoViewHeight}
                  maxWidth={maxWidth}
                  gameType={Number(gameType)}
                  onRetry={onRetry}
                ></PhotosHeader>
              ) : (
                <View style={[StyleSheet.absoluteFill, StyleSheet.centerStyle]}>
                  <Image
                    style={{
                      height: VideoHeightInner,
                      width: videoViewWidth,
                      borderRadius: 20,
                      position: 'absolute',
                      top: 0
                    }}
                    source={cover}
                  />
                  {videoUrl && (
                    <VideoHeader
                      ref={videoRef}
                      posterUrl={cover}
                      videoUrl={videoUrl ?? ''}
                      videoHeight={VideoHeightInner}
                      videoWidth={videoViewWidth}
                      gameType={gameType}
                    />
                  )}
                  {videoUrl && effectId && (
                    <EffectRender
                      width={videoViewWidth}
                      height={VideoHeightInner}
                      source={videoUrl ?? ''}
                      id={effectId}
                    />
                  )}
                  {showVideo && (
                    <BgmView
                      onCancel={cancelBgm}
                      bgmInfo={bgmInfo}
                      onPressSelect={onBgmPanelShow}
                      width={bgmWidth}
                      gameType={gameType}
                    />
                  )}
                  <RegenBtn
                    gameType={Number(gameType)}
                    viewWidth={videoViewWidth}
                    onRetry={onRetry}
                  />
                </View>
              )}
            </View>

            <Animated.View style={$opacityAnimatedStyle}>
              <PublishDetail
                disabled={openSpecialEffectsModal}
                handler={editHandler}
                initPublishInfo={initPublishInfo}
                gameType={gameType}
                getTopicExtraQueryParams={getRecommendParams}
              />
            </Animated.View>
          </ScrollView>
          <View style={st.publishButtonContainer}>
            <PublishButton onSubmit={onPressSubmit} enable={enable} />
          </View>
        </KeyboardAvoidingView>
      </Screen>
      {bgmPanelVisible && showVideo && (
        <BgmSelectPanel
          setCancelBgm={cancelBgm}
          setPanelHide={onBgmPanelHide}
          onBgmSelect={selectBgm}
          selectBgm={selectedBgm}
          gameType={gameType}
          musicTags={(extData as LivePhotoExtInfo)?.musicTags}
          bgmTagInfo={(extData as LivePhotoExtInfo)?.bgmTagInfo}
        />
      )}
      {openSpecialEffectsModal && (
        <SpecialEffectsPanel
          onClose={onCloseSpecialEffectsModal}
          onChange={id => {
            selectEffectId(id);
            if (id) {
              videoRef.current?.replay();
            }
            // videoRef.current?.stop();
          }}
          value={effectId}
        />
      )}
    </>
  );
  function cancelBgm() {
    selectBgm();
  }

  function onSubmit(publishInfo: PublishInfo) {
    publish({
      publishInfo,
      effectId,
      selectedBgm
    });
    if (selectedBgm) {
      useLiveStore.getState().addUsedBgm(selectedBgm);
    }
  }
};

const st = StyleSheet.create({
  specialText: {
    fontSize: 14,
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.65)'
  },
  specialIcon: {
    width: 20,
    height: 20
  },
  specialEffectWrap: {
    position: 'absolute',
    width: 100,
    height: 20,
    gap: 6,
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  headerRightStyle: {
    height: 54,
    position: 'relative',
    justifyContent: 'center'
  },
  headerStyle: {
    height: 54,
    position: 'relative'
  },
  publishButtonContainer: {
    marginTop: 10
  },
  $regenWrap: {
    width: 56,
    height: 56,
    position: 'absolute',
    bottom: 0,
    right: 0
  }
});
export default memo(LivePhotoPublish);
