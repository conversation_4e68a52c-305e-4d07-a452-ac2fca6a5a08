import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import LottieView from 'lottie-react-native';
import React, { RefObject, useEffect, useMemo, useRef, useState } from 'react';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import { ShadowedView, shadowStyle } from 'react-native-fast-shadow';
import { ScrollView } from 'react-native-gesture-handler';
import Animated, { useSharedValue } from 'react-native-reanimated';
import { SharedElement } from 'react-navigation-shared-element';
import { NewPublishWork } from '@/src/api/makephotov2';
import { PersistPhotos } from '@/src/api/makephotov2';
import { checkSecurity } from '@/src/api/utils';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import { TopicSelector } from '@/src/bizComponents/topicSelector';
import { hideLoading, showLoading, showToast } from '@/src/components';
import { AiTag } from '@/src/components/aiTag';
import { SaveButton } from '@/src/components/album/SaveButton';
import { BounceView } from '@/src/components/animation';
import { CustomBlurView } from '@/src/components/image/CustomBlurView';
import { MakePhotoEvents } from '@/src/components/makePhoto/constant';
import Toggle from '@/src/components/toggle';
import { LOADING_SYMBOL } from '@/src/constants';
import { useKeyboard } from '@/src/hooks';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useSafeAreaInsetsStyle } from '@/src/hooks/useSafeAreaInsetsStyle';
import { EditPageState, useMakePhotoEdit } from '@/src/store/makePhotoEdit';
import { useMemeStore } from '@/src/store/meme';
import { usePublishStore } from '@/src/store/publish';
import { useTopicStore } from '@/src/store/topic';
import { $Z_INDEXES, $flexCenter, $flexHBetween } from '@/src/theme/variable';
import { GameType, TabItemType } from '@/src/types';
import { PlainType } from '@/src/types';
import { dp2px, isIos } from '@/src/utils';
import { ReportError, catchErrorLog } from '@/src/utils/error-log';
import { formatTosUrl } from '@/src/utils/getTosUrl';
import { getPageID, reportClick, reportExpo } from '@/src/utils/report';
import { safeGoBack } from '@/src/utils/safeGoBack';
import { Button } from '@Components/button';
import { Icon } from '@Components/icons';
import { Image, ImageStyle } from '@Components/image';
import { Screen } from '@Components/screen';
import { Text } from '@Components/text';
import { StyleSheet } from '@Utils/StyleSheet';
import { savePicture } from '@Utils/savePicture';
import { uuid } from '@Utils/uuid';
import { useTopicSelect } from '../../topicSelector/topicSelect.hook';
import { ExtendPhotoID } from '@/proto-registry/src/web/raccoon/common/assets_pb';
import { PhotoProgress } from '@/proto-registry/src/web/raccoon/meme/meme_pb';
import { CreatePhotosWorkReq } from '@/proto-registry/src/web/raccoon/workpublisher/workpublisher_pb';
import { PartialMessage } from '@bufbuild/protobuf';
import { useShallow } from 'zustand/react/shallow';

const REGEN_IMG = require('@Assets/makephoto/new-regen-btn.png');
const LOTTIE_EDITOR_MOD = require('@Assets/lottie/editor_mod.json');

const $shadowStyle = shadowStyle({
  opacity: 1,
  radius: 10,
  offset: [0, 2],
  color: 'rgba(0, 0, 0, 0.2)'
});

const { width: screenWidth } = Dimensions.get('window');

let PIC_WRITE_FLAG = 0;

export default function MemePublish({
  photoWidth,
  photoHeight
}: {
  photoWidth: number;
  photoHeight: number;
}) {
  const [isSave, setSaveState] = useState(false);
  const {
    photo,
    story: previousStory,
    title: previousTitle
  } = useMemeStore(
    useShallow(state => ({
      photo: state.currentPhoto as PlainType<PartialMessage<PhotoProgress>>,
      title: state.title,
      story: state.story
    }))
  );

  const imageStyle: ImageStyle = {
    width: photoWidth,
    height: photoHeight,
    borderRadius: 12
  };

  const [showKeyboard, setShowKeyboard] = useState(false);
  const nativeShowKeyBoard = useKeyboard();

  useEffect(() => {
    if (Platform.OS === 'android') {
      setShowKeyboard(nativeShowKeyBoard);
    }
  }, [nativeShowKeyBoard]);

  const { paddingBottom } = useSafeAreaInsetsStyle(['bottom'], 'padding');
  const titleRef = useRef<TextInput>(null);
  const storyRef = useRef<TextInput>(null);
  const pageScrollRef = useRef<ScrollView>(null);

  const titleTextRef = useRef('');
  const storyTextRef = useRef('');
  const submitDisRef = useRef(false);
  const autoTopicRef = useRef(false);
  const [title, setTitle] = useState('');
  const [story, setStory] = useState('');
  const [showAdviceInput, setShowAdviceInput] = useState(false);
  const { afterPublish } = useChangeRoute();

  const showRecommend = useSharedValue(false);
  const showStoryTag = useSharedValue(false);

  const picwriteIdRef = useRef('');

  useEffect(() => {
    if (previousStory) {
      setStory(previousStory);
    }
  }, [previousStory]);

  useEffect(() => {
    if (previousTitle) {
      setTitle(previousTitle);
    }
  }, [previousTitle]);

  useEffect(() => {
    titleTextRef.current = title;
  }, [title]);

  useEffect(() => {
    storyTextRef.current = story;
  }, [story]);

  const validTitle = useMemo(() => {
    if (!title.length) return false;
    if (title.length > 30) return false;
    return true;
  }, [title]);

  const validStory = useMemo(() => {
    // if (!story.length) return false;
    if (story.length > 500) return false;
    return true;
  }, [story]);

  const blurAll = () => {
    storyRef.current?.blur();
    titleRef.current?.blur();
  };

  const submitDis = useMemo(() => {
    return !validTitle || !validStory || !photo;
  }, [validTitle, validStory, photo]);

  useEffect(() => {
    submitDisRef.current = submitDis;
  }, [submitDis]);
  const getRecommendParams = useMemoizedFn(() => ({
    photoId: photo?.photoId ? [photo.photoId] : [],
    gameType: GameType.MEME
  }));

  const {
    isFold,

    toggleTopicSelect,
    handleSelectTopic,
    selectedTopics: topics,
    setSelectedTopics: setTopics,
    handleInitTopic
  } = useTopicSelect({
    defaultFold: true,
    getRecommendParams
  });

  const publishButton = () => {
    return (
      <BounceView style={$topButton}>
        <Button
          style={{
            backgroundColor: StyleSheet.currentColors.brand1,
            borderRadius: 500,
            paddingHorizontal: 16,
            paddingVertical: 4,
            // alignItems: 'center',
            width: dp2px(70),
            height: dp2px(26)
          }}
          onPress={() => {
            blurAll();
            onSubmit();
          }}
          disabled={submitDis}
        >
          <View
            style={[
              StyleSheet.rowStyle,
              { height: dp2px(18), alignItems: 'center', gap: 3 }
            ]}
          >
            <Icon icon="publish" size={12} />
            <Text style={[st.$publishButtonText, st.$publishButtonTextSm]}>
              发布
            </Text>
          </View>
        </Button>
      </BounceView>
    );
  };

  const primaryPublishButton = () => {
    if (showKeyboard) return null;
    return (
      <View
        style={[
          StyleSheet.rowStyle,
          {
            position: 'absolute',
            bottom: +(paddingBottom || 0) + 15,
            width: '100%'
          }
        ]}
      >
        <SaveButton
          onPress={() => {
            persistPhotos(photo.photoId);
          }}
        />
        <Button
          style={{
            backgroundColor: StyleSheet.currentColors.brand1,
            borderRadius: 500,
            paddingHorizontal: 16,
            paddingVertical: 4,
            // alignItems: 'center',
            width: dp2px(290),
            height: dp2px(50)
          }}
          onPress={() => {
            onSubmit();
          }}
          disabled={submitDis}
        >
          <View
            style={[StyleSheet.rowStyle, { justifyContent: 'center', gap: 5 }]}
          >
            <Icon icon="publish" size={15} />
            <Text style={st.$publishButtonText}>发布</Text>
          </View>
        </Button>
      </View>
    );
  };

  const onFocus = useMemoizedFn((inputInstance: RefObject<TextInput>) => {
    if (
      (storyRef.current?.isFocused() || titleRef.current?.isFocused()) &&
      showKeyboard
    ) {
      return;
    }
    setShowKeyboard(true);
    setTimeout(() => {
      pageScrollRef.current?.scrollTo({
        y: 200,
        animated: true
      });
      inputInstance.current?.focus();
    }, 200);
  });

  const onBlur = useMemoizedFn(() => {
    if (!storyRef.current?.isFocused() && !titleRef.current?.isFocused()) {
      setShowKeyboard(false);
    }
  });

  const lottieRef = useRef<LottieView>(null);
  const [finishCount, setFinishCount] = useState(0);

  useEffect(() => {
    if (finishCount < 3) {
      lottieRef.current?.play();
    }
  }, [finishCount]);

  const finishCb = () => {
    setFinishCount(v => v + 1);
  };

  const FoldTopic = useMemo(() => {
    if (showKeyboard) return null;
    return (
      <TopicSelector
        selectedTopics={topics}
        gameType={GameType.MEME}
        onTopicSelect={handleSelectTopic}
        isFold={true}
        onTopicInit={handleInitTopic}
        style={{
          marginLeft: -12,
          marginRight: -12
        }}
        foldTopicNonPressable={true}
      />
    );
  }, [topics, showKeyboard]);

  const UnfoldTopic = useMemo(() => {
    return (
      <TopicSelector
        selectedTopics={topics}
        gameType={GameType.MEME}
        onTopicSelect={handleSelectTopic}
        onTopicInit={handleInitTopic}
        isFold={false}
        foldTopicNonPressable={true}
      />
    );
  }, [topics]);

  return (
    <>
      <Screen
        theme="dark"
        title="发布"
        headerRight={() => showKeyboard && publishButton()}
        onBack={() => {
          setTopics([]);
          safeGoBack();
        }}
        withWaterMark
        safeAreaEdges={['top']}
      >
        <KeyboardAvoidingView
          behavior={isIos ? undefined : 'height'}
          style={{ flex: 1 }}
        >
          <ScrollView
            bounces={false}
            keyboardShouldPersistTaps="handled"
            ref={pageScrollRef}
          >
            <Animated.View style={[st.$imageWrapper]}>
              <View style={[$flexCenter]}>
                <Image
                  source={formatTosUrl(photo.url || '', {
                    size: 'size1'
                  })}
                  contentFit="cover"
                  style={[imageStyle]}
                />

                <View
                  style={[
                    $flexHBetween,
                    {
                      position: 'absolute',
                      bottom: 0,
                      width: '100%',
                      paddingLeft: (screenWidth - photoWidth) / 2 + 16,
                      paddingRight: (screenWidth - photoWidth) / 2 + 10
                    }
                  ]}
                >
                  <ShadowedView
                    style={[
                      $shadowStyle,
                      {
                        position: 'relative'
                      }
                    ]}
                  >
                    <CustomBlurView
                      style={st.$blurBg}
                      ios={{
                        blurMount: 10,
                        style: {
                          borderRadius: 14
                        }
                      }}
                      android={{
                        style: {
                          backgroundColor: '#00000080',
                          borderRadius: 14
                        }
                      }}
                    />
                    <Button
                      text="编辑"
                      iconText="icon_photo_edit"
                      style={st.$editButton}
                      textStyle={st.$editText}
                      iconSize={dp2px(12)}
                      onPress={() => {
                        useMakePhotoEdit.getState().setState({
                          editPageState: EditPageState.editing
                        });
                      }}
                    />
                    <LottieView
                      source={LOTTIE_EDITOR_MOD}
                      ref={lottieRef}
                      loop={false}
                      resizeMode="contain"
                      style={{
                        position: 'absolute',
                        height: 22,
                        width: 52.8,
                        top: -10,
                        right: -12,
                        zIndex: $Z_INDEXES.z100,
                        pointerEvents: 'none'
                      }}
                      onAnimationFinish={finishCb}
                    />
                  </ShadowedView>

                  {photo.photoId && (
                    <TouchableOpacity
                      onPress={() => {
                        // 重炖保存正文
                        if (title || story) {
                          useMemeStore.getState().setState({
                            title: title || '',
                            story: story || ''
                          });
                        }
                        useMemeStore.getState().regenMemePhoto({});
                        router.replace('/meme/effect');
                      }}
                    >
                      <Image
                        source={REGEN_IMG}
                        style={{ height: 56, width: 56 }}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </Animated.View>

            <View style={[st.$formItem, { paddingBottom: 12 }]}>
              <TextInput
                allowFontScaling={false}
                style={[
                  st.$textinput,
                  {
                    paddingRight: 78
                  }
                ]}
                numberOfLines={1}
                placeholder="好的标题会获得更多赞哦"
                placeholderTextColor="rgba(255,255,255,0.5)"
                ref={titleRef}
                maxLength={30}
                value={title}
                onChangeText={text => {
                  setTitle(text);
                }}
                onFocus={() => {
                  onFocus(titleRef);
                  showRecommend.value = true;
                  setShowAdviceInput(true);
                  toggleTopicSelect(true);
                  picwriteIdRef.current = uuid();
                  PIC_WRITE_FLAG = 1;
                  reportExpo(
                    'picwrite_title_edit',
                    {
                      module: 'publish',
                      title_edit_id: picwriteIdRef.current,
                      title
                    },
                    'start'
                  );
                }}
                onBlur={() => {
                  onBlur();
                  showRecommend.value = false;
                  setShowAdviceInput(false);
                  PIC_WRITE_FLAG = 0;
                  reportExpo(
                    'picwrite_title_edit',
                    {
                      module: 'publish',
                      title_edit_id: picwriteIdRef.current,
                      title
                    },
                    'end'
                  );
                }}
              />

              <Text
                style={[
                  st.$tip,
                  {
                    bottom: Platform.OS === 'ios' ? 8 : 13
                  },
                  st.$text,
                  !validTitle && title.length ? st.$error : null
                ]}
              >
                {!title.length
                  ? '必填'
                  : showAdviceInput
                    ? `字数(${title.length}/30)`
                    : ''}
              </Text>
            </View>

            <View style={[st.$formItem, $line, { paddingTop: 10 }]}>
              <View
                style={{
                  minHeight: 60,
                  flexDirection: 'column',
                  gap: 12
                }}
              >
                <TextInput
                  allowFontScaling={false}
                  style={[
                    st.$textinput2,
                    {
                      minHeight: 60
                    }
                  ]}
                  multiline
                  placeholder="添加正文"
                  placeholderTextColor="rgba(255,255,255,0.5)"
                  value={story}
                  ref={storyRef}
                  textAlignVertical="top"
                  onChangeText={setStory}
                  onFocus={() => {
                    onFocus(storyRef);
                    showStoryTag.value = true;
                    toggleTopicSelect(true);
                  }}
                  onBlur={() => {
                    onBlur();
                    showStoryTag.value = false;
                  }}
                />
                {FoldTopic}
              </View>
              {showKeyboard && <View style={{ height: 100 }} />}
            </View>

            {!showKeyboard && (
              <View>
                <Pressable
                  style={[st.$tipWrap]}
                  onPress={() => {
                    toggleTopicSelect(!isFold);
                  }}
                >
                  <View style={[StyleSheet.rowStyle, { gap: 4, opacity: 0.6 }]}>
                    <Icon
                      icon="topic_tag"
                      size={11}
                      color={StyleSheet.currentColors.white}
                    />
                    <Text
                      style={{
                        color: StyleSheet.currentColors.white,
                        fontSize: 14,
                        fontWeight: '400'
                      }}
                    >
                      添加话题
                    </Text>
                  </View>
                  <View>
                    {isFold ? (
                      <Icon
                        icon="back"
                        color={'rgba(256, 256, 256, 0.6)'}
                        size={16}
                        style={{ transform: [{ rotateY: '180deg' }] }}
                      />
                    ) : (
                      <Text style={{ color: 'rgba(256, 256, 256, 0.6)' }}>
                        收起
                      </Text>
                    )}
                  </View>
                </Pressable>
                <View
                  style={{
                    paddingHorizontal: 8,
                    paddingBottom: !isFold || topics.length > 0 ? 24 : 0
                  }}
                >
                  {!isFold && UnfoldTopic}
                </View>
              </View>
            )}

            {!showKeyboard && (
              <View style={[st.$tipWrap, $line]}>
                <View style={[StyleSheet.rowStyle, { gap: 4, opacity: 0.6 }]}>
                  <Icon icon="makephoto_download" size={11}></Icon>
                  <Text
                    style={{
                      color: StyleSheet.currentColors.white,
                      fontSize: 14,
                      fontWeight: '400'
                    }}
                  >
                    保存至相册
                  </Text>
                </View>
                <Toggle
                  onToggle={isSave => {
                    reportClick(MakePhotoEvents.save_image, {
                      isSave,
                      module: 'publish'
                    });
                    setSaveState(isSave);
                  }}
                  isOn={isSave}
                />
              </View>
            )}
          </ScrollView>
        </KeyboardAvoidingView>
        {!showKeyboard && (
          <View style={{ height: dp2px(+(paddingBottom || 0) + 68) }} />
        )}
        {primaryPublishButton()}
      </Screen>
    </>
  );

  function onSubmit(
    isHideActivity?: boolean,
    extraArgs: PartialMessage<CreatePhotosWorkReq> = {}
  ) {
    if (submitDisRef.current) return;

    const currentPhoto = useMemeStore.getState().currentPhoto;
    const newPhotoIds: PartialMessage<ExtendPhotoID>[] =
      currentPhoto === LOADING_SYMBOL
        ? []
        : [{ photoId: currentPhoto.photoId }];

    const story = storyTextRef.current || ' ';
    const title = titleTextRef.current || story.slice(0, 30);
    if (!newPhotoIds.length) {
      showToast('请选择图片');
      return;
    }
    submitDisRef.current = true;
    showLoading();

    if (PIC_WRITE_FLAG) {
      reportExpo(
        'picwrite_title_edit',
        {
          module: 'publish',
          title_edit_id: picwriteIdRef.current,
          title
        },
        'end'
      );
      PIC_WRITE_FLAG = 0;
    }
    reportClick('submit', {
      module: 'publish',
      story,
      title,
      photo_ids: JSON.stringify(newPhotoIds)
    });
    return NewPublishWork({
      photoIds: newPhotoIds,
      story,
      title,
      topics: topics,
      pt: {
        page: getPageID()
      },
      ...extraArgs
    })
      .then(res => {
        reportExpo('publish_success', {
          module: 'publish',
          sourceid: res.cardId,
          game_type: GameType.MEME
        });
        if (isSave) {
          saveImages();
        }
        usePublishStore.getState().reset();
        useMakePhotoEdit.getState().reset();
        useMemeStore.getState().reset();

        if (res.cardId) {
          afterPublish({
            tab: TabItemType.HOME,
            appendId: res.cardId,
            appendImageUrl:
              currentPhoto !== LOADING_SYMBOL ? currentPhoto.url : ''
          });
        }
      })
      .catch((e: ErrorRes) => {
        console.warn(e, '发布失败');
        if (checkSecurity(e)) {
          showToast('安全审核不通过，请重新编辑~');
        } else {
          showToast('发布失败，请重试');
        }
        catchErrorLog('makephoto_publish', e, {
          tag: ReportError.PUBLISH,
          params: {
            photoIds: newPhotoIds,
            story,
            title,
            topics: topics
          }
        });
      })
      .finally(() => {
        submitDisRef.current = false;
        hideLoading();
      });
  }

  function saveImages() {
    const url = photo.url;
    if (url) {
      savePicture(url, true);
    }
  }

  async function persistPhotos(photoId?: string): Promise<void> {
    if (!photoId) {
      showToast('保存失败，请重试');
      return Promise.resolve();
    }

    showLoading();

    reportClick('image_generate_save', {
      module: 'publish',
      save_image_id: photoId
    });

    return PersistPhotos({ photoIds: [photoId] })
      .then(res => {
        showToast('保存成功');
      })
      .catch(e => {
        showToast('保存失败');
      })
      .finally(() => {
        hideLoading();
      });
  }
}

const st = StyleSheet.create({
  $editButton: {
    paddingHorizontal: 14,
    paddingVertical: 2,
    width: 63,
    height: 26,
    borderRadius: 14,
    backgroundColor: '#00000080'
  },
  $editText: {
    fontSize: 12,
    lineHeight: 22,
    color: StyleSheet.currentColors.white
  },
  $imageWrapper: {
    width: '100%',
    height: 412,
    alignItems: 'center'
  },
  $previewWrap: {
    width: '100%'
  },
  $formItem: {
    // marginHorizontal: 9,
    // backgroundColor: bgColor,
    marginHorizontal: 20
  },
  $tip: {
    position: 'absolute',
    bottom: 11,
    right: 10
  },
  $text: {
    fontSize: 12,
    fontWeight: '400',
    color: StyleSheet.hex(StyleSheet.currentColors.white, 0.4)
  },
  $textinput: {
    color: StyleSheet.currentColors.white,
    verticalAlign: 'middle',
    textAlign: 'justify',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 20
  },
  $textinput2: {
    color: StyleSheet.hex(StyleSheet.currentColors.white, 0.87),
    textAlign: 'justify',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 24
  },
  $checkWrap: {
    ...StyleSheet.circleStyle,
    ...StyleSheet.centerStyle,
    width: 12,
    height: 12,
    backgroundColor: '#ccc'
  },
  $checkedWrap: {
    backgroundColor: '#6ED4FF'
  },
  $tipWrap: {
    ...StyleSheet.rowStyle,
    justifyContent: 'space-between',
    height: 44,
    marginHorizontal: 20,
    ...StyleSheet.rowStyle
  },
  $tipText: {
    marginLeft: StyleSheet.spacing.xxs,
    color: StyleSheet.hex(StyleSheet.currentColors.white, 0.7)
  },
  $publishButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: StyleSheet.currentColors.white
  },
  $publishButtonTextSm: {
    fontSize: 12,
    lineHeight: 18
  },
  $error: {
    color: StyleSheet.currentColors.red
  },
  $publishButon: {},
  $blurBg: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    borderRadius: 14,
    width: 63,
    height: 26,
    zIndex: 0,
    backgroundColor: '#00000080'
  }
});

const $line: ViewStyle = {
  borderTopWidth: 0.5,
  borderColor: StyleSheet.hex(StyleSheet.currentColors.white, 0.08)
};

const $topButton: ViewStyle = {
  position: 'absolute',
  top: -12,
  right: 0
};
