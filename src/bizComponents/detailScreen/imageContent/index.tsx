import { useMemoizedFn } from 'ahooks';
// import Swiper from 'react-native-swiper';
import { router, useNavigation } from 'expo-router';
import stableStringify from 'json-stable-stringify';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Dimensions,
  GestureResponderEvent,
  LayoutChangeEvent,
  Platform,
  View
} from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  cancelAnimation,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { SwiperFlatList } from 'react-native-swiper-flatlist';
import { BlurImage } from '@/src/components/image/BlurImage';
import { PreviewImageProps } from '@/src/components/previewImageModal';
import { LOGIN_SCENE } from '@/src/constants';
import { useAuthState, useScreenSize } from '@/src/hooks';
import { useScreenCapture } from '@/src/hooks/useScreenCapture';
import { DetailInfo, PhotoInfo, useDetailStore } from '@/src/store/detail';
import { useKitchenStore } from '@/src/store/kitchen';
import {
  GameType,
  ShareImageType,
  ShareInfo,
  ShareInfoProps,
  ShareTemplateName,
  TopicInfo
} from '@/src/types';
import { StyleSheet, isIos } from '@/src/utils';
import { clickEffect } from '@/src/utils/clickeffect';
import { ImageSizeType, formatTosUrl } from '@/src/utils/getTosUrl';
import { getWaterMark } from '@/src/utils/getWaterMark';
import { Image, ImageStyle } from '@Components/image';
import { showShare } from '@Components/share';
import {
  Source,
  addCommonReportParams,
  reportClick,
  reportExpo
} from '@Utils/report';
import { useParams } from '../../../hooks/useParams';
import { OriginCardTag } from '../originCardTag';
import { PKTag } from '../pkTag';
import { usePKTagData } from '../pkTag/pkTag.hook';
import { ToFirstDetailTag } from '../toFristDetailTag';
import { useDetailShareCompConfig } from '../useDetailShareCompConfig';
import { PhotoExtype } from '@/proto-registry/src/web/raccoon/common/assets_pb';
import { CommonActions, useIsFocused } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';
import { ImageItem } from './ImageItem';
import { SlidePagination } from './SlidePagination';
import {
  DEFAULT_RATIO,
  MID_RATIO_SECTION,
  getImageContentRatios
} from './utils';
import { MaterlizeMask } from '@/src/components/materialize/MaterlizeMask';

export type LikeCallbackConfig = {
  doubleLike?: {
    offsetX: number;
    offsetY: number;
  };
};

enum ImageType {
  photo = 'photo',
  image = 'image'
}

type ImageContentProps = {
  data?: DetailInfo;
  onLike?: (config?: LikeCallbackConfig) => void;
  onScreenCapture?: () => void;
  autoPlay?: boolean;
  updateAutoPlay?: (autoPlay: boolean) => void;
  isVisible?: boolean;
  onPreviewImage?: (payload: PreviewImageProps) => void;
  // 图片区域布局回调，用于禁用导航手势
  onImageAreaLayout?: (layout: {
    x: number;
    y: number;
    width: number;
    height: number;
  }) => void;
  markPerformanceTimestamp?: (key: string, index?: number) => void;
  onImageAllLoad?: () => void;
};

export function ImageContent(props: ImageContentProps) {
  const { currentIdx, activityType, inviteType, fromId } = useParams();
  const { width } = useScreenSize();
  const { loginIntercept } = useAuthState();

  const cardId = props.data?.cardId;

  const [index, setIndex] = useState(Number(currentIdx) || 1);
  const [sharing, setSharing] = useState(false);
  const isFocus = useIsFocused();

  const swiperRef = useRef<SwiperFlatList>(null);
  const timerRef = useRef<NodeJS.Timeout>();

  const indexRef = useRef(index);
  const exposureRecord = useRef<Record<string, boolean>>({});
  const lastScrollX = useRef(0);
  const canBackRef = useRef(false);
  const loopCount = useRef(0);
  const imageReadyRef = useRef<boolean[]>([]);
  const blurImageReadyRef = useRef<boolean[]>([]);
  const performanceReportFlag = useRef({
    imageAll: false,
    blurImageAll: false,
    swipe: false
  }); // 第一次滚动到下一张图片

  const { detail, commonInfo } = useDetailStore(
    useShallow(state => {
      const content = cardId ? state.getDetail(cardId) : undefined;
      // const info = state.getDetail(cardId as string);
      return {
        detail: content?.detail || undefined,
        commonInfo: content?.commonInfo || undefined
        // imageIndex: info?.imageIndex
      };
    })
  );

  const { handleShare, getShareInfo } = useDetailShareCompConfig(
    detail || undefined,
    commonInfo || undefined,
    {
      onCloseSharing: () => setSharing(false),
      onSharing: () => setSharing(true),
      imageIndex: index,
      activityType: String(activityType)
    }
  );

  const ratios = useMemo(() => {
    if (props.data?.gameType === GameType.PUBLICATION) {
      // 官号图片没有返回比例信息，按默认尺寸展示
      return undefined;
    }
    return getImageContentRatios(props.data?.photos, props.data?.protos);
  }, [props.data]);

  const maxRatio = useMemo(() => {
    if (!ratios) return DEFAULT_RATIO;
    return ratios.reduce((pre, cur) => {
      return Math.max(pre, cur);
    }, 1);
  }, [ratios]);

  const validImages = useMemo(
    () =>
      props.data?.images
        ? {
            type: ImageType.image as ImageType.image,
            imgs: props.data?.images ?? []
          }
        : {
            type: ImageType.photo as ImageType.photo,
            imgs: props.data?.photos ?? []
          },
    [props.data?.images, props.data?.photos]
  );

  const curImgInfo = useMemo(() => {
    if (validImages.type === ImageType.image) {
      return { curImg: validImages.imgs[index - 1], curProto: undefined };
    } else {
      const photo = validImages.imgs[index - 1];
      const proto = props.data?.protos.find(
        item => item?.protoId === validImages?.imgs?.[index]?.protoId
      );
      return { curImg: photo, curProto: proto };
    }
  }, [index, props.data?.protos, validImages.imgs, validImages.type]);

  const { pkTagData } = usePKTagData({
    cardId: cardId as string,
    photoId: (curImgInfo.curImg as PhotoInfo)?.photoId ?? ''
  });

  const height = maxRatio * width;
  const disableShare = detail?.viewerCtrl?.sharable === false;
  const auditing = detail?.viewerCtrl?.censoring === true;
  const total = validImages?.imgs?.length || 0;

  const loaded = useSharedValue(0);
  const photosDataLoaded = useSharedValue(0);

  const $shareNodeStyle = useAnimatedStyle(() => ({
    opacity: 1 - Math.min(loaded.value, photosDataLoaded.value)
  }));

  useEffect(() => {
    if (props.data?.placeholder) return;

    timerRef.current = setTimeout(() => {
      if (validImages.type === ImageType.image) {
        validImages.imgs.forEach(img => {
          Image.prefetch(img + getWaterMark());
        });
      } else {
        validImages.imgs.forEach(img => {
          Image.prefetch(img?.url + getWaterMark());
        });
      }
    }, 100);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [validImages]);

  useEffect(() => {
    if (detail?.placeholder !== 1) {
      photosDataLoaded.value = withTiming(1, { duration: 300 });
    }
    return () => {
      cancelAnimation(photosDataLoaded);
    };
  }, [detail?.placeholder]);

  useEffect(() => {
    indexRef.current = index;
    if (cardId) {
      useDetailStore.getState().setImageIndex(cardId, index);
    }
    if (!exposureRecord.current[index]) {
      const photoId =
        validImages.type === ImageType.image
          ? undefined
          : validImages?.imgs[index - 1]?.photoId;
      const eles = photoId
        ? props.data?.elements?.anchors?.[photoId]
        : undefined;

      setTimeout(() => {
        reportExpo('pic', {
          module: 'detail',
          contentid: cardId,
          imageid:
            validImages.type === ImageType.image
              ? validImages?.imgs[index - 1]
              : validImages?.imgs[index - 1]?.url,
          status: disableShare ? 'disable' : 'normal',
          // 是否有关联元素
          is_element_tag: eles?.eles?.length ? 1 : 0,
          // 关联元素数量
          element_num: eles?.eles?.length || 0,
          game_type: detail?.gameType,
          source: Source.DRAWING_WITH_PROMPT
        });
      });
    }
    exposureRecord.current[index] = true;
    reportClick('pic_slip', {
      module: 'detail',
      contentid: cardId
    });
  }, [index]);

  // todo: 传到预览节点，不太好，要改
  const renderShareNode = useMemoizedFn(() => {
    const firstImage =
      validImages.type === ImageType.image
        ? validImages?.imgs[index - 1]
        : validImages?.imgs[index - 1]?.url;
    const gameType = props.data?.photos?.[0]?.gameType;
    const isEmoji = gameType === GameType.EMOJI;
    const isReviveCompose =
      validImages.type !== ImageType.image &&
      validImages?.imgs[index - 1]?.exType === PhotoExtype.COMPOSED_REVIVE;

    const needAddBlurBg = true;
    const imageHeight = (ratios?.[0] || DEFAULT_RATIO) * width;
    const realHeight = isReviveCompose
      ? width * 1.1
      : isEmoji
        ? width
        : imageHeight;

    const style: ImageStyle =
      (isEmoji || isReviveCompose) &&
      maxRatio < MID_RATIO_SECTION[1] &&
      maxRatio > MID_RATIO_SECTION[0]
        ? {
            bottom: EMOJI_BOTTOM_DIST,
            position: 'absolute'
          }
        : {};

    return (
      <View
        style={{
          width,
          height,
          position: 'relative',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        {needAddBlurBg ? (
          <BlurImage
            url={firstImage}
            style={[st.$bgBlur]}
            size={'size6'}
            onLoad={() => onBlurImageLoad?.(0)}
          />
        ) : null}
        <Image
          source={formatTosUrl(firstImage || '', {
            size: 'size2'
          })}
          style={[
            {
              width: '100%',
              height: realHeight,
              ...style,
              resizeMode: 'contain'
            }
          ]}
          onLoad={() => onImageLoad?.(0)}
        />
      </View>
    );
  });

  // #region handlers
  const onScreenCapture = useMemoizedFn(() => {
    props.onScreenCapture?.();
    onShare(ShareImageType.image);
  });

  useScreenCapture(
    !disableShare && !auditing && !!props.isVisible,
    onScreenCapture
  );

  const onTapImage = useMemoizedFn(() => {
    const data = props.data;
    reportClick('pic', {
      module: 'detail',
      contentid: cardId,
      imageid:
        validImages.type === ImageType.image
          ? validImages.imgs?.[index - 1]
          : validImages.imgs?.[index - 1]?.photoId,
      gameType:
        validImages.type === ImageType.image
          ? GameType.PUBLICATION
          : validImages?.imgs?.[index - 1]?.gameType
    });

    props.updateAutoPlay?.(false);

    const list =
      data?.images?.map(item => ({ url: item, gameType: data?.gameType })) ||
      data?.photos ||
      [];
    props?.onPreviewImage?.({
      index: Math.max(index - 1, 0),
      list,
      onDoubleClickLike: onLike,
      reportParams: {
        contentid: data?.cardId || ''
      },
      removeMark: disableShare
    });
  });

  const onLike = useMemoizedFn((config?: LikeCallbackConfig) => {
    clickEffect();
    reportClick('pic_taptwice', {
      module: 'detail',
      contentid: cardId,
      imageid:
        validImages.type === ImageType.image
          ? validImages?.imgs?.[index - 1]
          : validImages?.imgs[index - 1]?.photoId
    });
    props.onLike?.(config);
  });

  const onShare = useMemoizedFn((type?: ShareImageType) => {
    clickEffect();
    //埋点
    reportExpo('share_component', {
      contentid: cardId,
      share_scene: '3'
    });

    if (type === ShareImageType.image) {
      const shareParams: ShareInfoProps = {
        shareInfo: getShareInfo(),
        extra: stableStringify(commonInfo?.profile || {}),
        type: type || ShareImageType.common,
        shareTemplateName: ShareTemplateName.detail,
        onCloseSharing: () => setSharing(false),
        onShare: () => setSharing(true)
      };

      // 裂变分享来源参数
      addCommonReportParams('invite', {
        invite_page: 2
      });

      showShare(shareParams);
    } else {
      handleShare();
    }
  });

  const onLongPressImage = useMemoizedFn(e => {
    if (disableShare) {
      return;
    }

    reportClick('pic_longpress', {
      module: 'detail',
      contentid: cardId,
      imageid:
        validImages.type === ImageType.image
          ? validImages?.imgs?.[index - 1]
          : validImages?.imgs[index - 1]?.photoId
    });
    onShare();
  });

  const onImageLoad = useMemoizedFn((index: number) => {
    if (!imageReadyRef.current[index]) {
      props.markPerformanceTimestamp?.(`detail_image_render_timestamp`, index);
      imageReadyRef.current[index] = true;
    }

    if (
      !performanceReportFlag.current.imageAll &&
      imageReadyRef.current.filter(v => v).length >= validImages.imgs.length
    ) {
      props.markPerformanceTimestamp?.(`detail_image_all_render_timestamp`);
      performanceReportFlag.current.imageAll = true;
      props.onImageAllLoad?.();
    }
  });

  const onBlurImageLoad = useMemoizedFn((index: number) => {
    if (!blurImageReadyRef.current[index]) {
      props.markPerformanceTimestamp?.(
        `detail_blur_image_render_timestamp`,
        index
      );
      blurImageReadyRef.current[index] = true;
    }

    if (
      !performanceReportFlag.current.blurImageAll &&
      blurImageReadyRef.current.filter(v => v).length >= validImages.imgs.length
    ) {
      props.markPerformanceTimestamp?.(
        `detail_blur_image_all_render_timestamp`
      );
      performanceReportFlag.current.blurImageAll = true;
    }
  });

  const onChangeIndex = useMemoizedFn(
    (params: { changed: { index: number }[] }) => {
      const nextIndex = params.changed?.[0]?.index + 1;
      if (!swiperRef.current || nextIndex === index) return;
      setIndex(nextIndex);
      loopCount.current++;

      if (cardId) {
        useDetailStore.getState().setImageIndex(cardId, nextIndex);
      }

      if (!performanceReportFlag.current.swipe) {
        props.markPerformanceTimestamp?.(
          `detail_swiper_first_scroll_timestamp`
        );
        performanceReportFlag.current.swipe = true;
      }
    }
  );

  const onChangeAndroidIndex = useMemoizedFn(
    ({ index: _index }: { index: number }) => {
      const nextIndex = _index + 1;
      if (!swiperRef.current || nextIndex === index) return;
      setIndex(nextIndex);
      loopCount.current++;

      if (cardId) {
        useDetailStore.getState().setImageIndex(cardId, nextIndex);
      }

      if (!performanceReportFlag.current.swipe) {
        props.markPerformanceTimestamp?.(
          `detail_swiper_first_scroll_timestamp`
        );
        performanceReportFlag.current.swipe = true;
      }
    }
  );

  // 处理图片区域布局变化
  const handleImageAreaLayout = useMemoizedFn((e: LayoutChangeEvent) => {
    const { x, y, width, height } = e.nativeEvent.layout;
    props.onImageAreaLayout?.({ x, y, width, height });
  });

  const onPressTag = useMemoizedFn((photoId, eid, fid) => {
    loginIntercept(
      () => {
        const anchors = props.data?.elements?.anchors || {};
        const validPhotos =
          props.data?.photos?.filter(p => Boolean(anchors[p.photoId])) || [];
        useKitchenStore.getState().updateRefInfo({
          refCardId: cardId,
          refPhotos: validPhotos,
          refEleInfos: props.data?.elements,
          refProto: props.data?.protos,
          refTopics: props.data?.topics as TopicInfo[] | undefined
        });
        useKitchenStore.getState().updateSelect({
          selectPhoto: photoId,
          select: fid
        });
        router.navigate({
          pathname: '/kitchen',
          params: {
            source: 'element_tag'
          }
        });
      },
      { scene: LOGIN_SCENE.KITCHEN }
    );
  });

  const onTouchStart = useMemoizedFn((e: GestureResponderEvent) => {
    props.updateAutoPlay?.(false);
    lastScrollX.current = e.nativeEvent.pageX;
  });

  const onTouchMove = useMemoizedFn((e: GestureResponderEvent) => {
    if (index === 1 && e.nativeEvent.pageX - lastScrollX.current > 100) {
      canBackRef.current = true;
    }
  });

  const onTouchEnd = useMemoizedFn(() => {
    lastScrollX.current = 0;
    // 发送手势结束信号
    // props.parentGestureValue.value = -99999;
  });

  const onPressSlidePagination = useMemoizedFn((target: number) => {
    swiperRef.current?.scrollToIndex({
      index: target,
      animated: true
    });
  });
  // #endregion handlers

  const tapGesture = Gesture.Tap()
    .numberOfTaps(1)
    .onEnd(() => {
      runOnJS(onTapImage)();
    });
  const tapTwiceGesture = Gesture.Tap()
    .numberOfTaps(2)
    .onEnd(params => {
      runOnJS(onLike)({
        doubleLike: {
          offsetX: params.absoluteX,
          offsetY: params.absoluteY
        }
      });
    });

  const longTapGesture = Gesture.LongPress().onStart(e => {
    runOnJS(onLongPressImage)(e);
  });

  const composed = Gesture.Exclusive(
    tapTwiceGesture,
    longTapGesture,
    tapGesture
  );

  return (
    <>
      <View>
        <GestureDetector gesture={composed}>
          <View
            style={[
              st.$swiperWrap,
              {
                width,
                height
              }
            ]}
            onLayout={handleImageAreaLayout}
          >
            <Animated.View style={[st.$imageContainer, $shareNodeStyle]}>
              {/* <SharedElement
          id={`item.${props.data?.cardId}.photo`}
          style={{ position: 'absolute', top: 0, left: 0 }}
          pointerEvents="none"
        > */}
              {renderShareNode()}
              {/* </SharedElement> */}
            </Animated.View>
            {/* <SharedImage {...props} width={width} height={height} /> */}
            {/* todo 要提取一下 */}
            {/* @ts-ignore */}
            <SwiperFlatList
              key={cardId}
              ref={swiperRef}
              initialNumToRender={1}
              maxToRenderPerBatch={10}
              // windowSize={3}
              onTouchStart={onTouchStart}
              index={Number(currentIdx) ? Number(currentIdx) - 1 : 0}
              onTouchMove={onTouchMove}
              onTouchEnd={onTouchEnd}
              autoplay={
                props.autoPlay && !sharing && isFocus && props.isVisible
              }
              autoplayLoop={true}
              autoplayLoopKeepAnimation={true}
              onScrollEndDrag={() => {
                // 在滑动结束时也发送结束信号
                // props.parentGestureValue.value = -99999;
                // 这个会有Bug
                if (canBackRef.current) {
                  setTimeout(() => {
                    router.back();
                  }, 200);
                }
              }}
              data={validImages?.imgs ?? []}
              {...(Platform.OS === 'android'
                ? { onChangeIndex: onChangeAndroidIndex }
                : { onViewableItemsChanged: onChangeIndex })}
              // onViewableItemsChanged={onChangeIndex}
              renderItem={({
                item,
                index: _index
              }: {
                item: string | PhotoInfo;
                index: number;
              }) => {
                return (
                  <ImageItem
                    contentId={cardId}
                    item={item}
                    index={_index}
                    containerWidth={width}
                    containerRatio={maxRatio}
                    imageRatio={ratios?.[_index]}
                    updateAutoPlay={props.updateAutoPlay}
                    onLoad={(isBlurBg?: boolean) => {
                      if (isBlurBg) {
                        onBlurImageLoad?.(_index);
                      } else {
                        if (_index === 0) {
                          loaded.value = withTiming(1, { duration: 300 });
                        }
                        onImageLoad?.(_index);
                      }
                    }}
                    elements={props.data?.elements}
                    onPressTag={onPressTag}
                    active={index - 1 === _index}
                    gesture={longTapGesture}
                  />
                );
              }}
              keyExtractor={item => item.photoId}
            />
            <View
              style={[
                StyleSheet.absoluteFill,
                {
                  width: 40
                }
              ]}
            />
            <ToFirstDetailTag detailId={cardId} fromId={fromId as string} />
            <OriginCardTag
              detailId={cardId}
              protoId={
                props.data?.photos?.[index - 1]?.subPhotos?.[0]?.protoId ||
                props.data?.photos?.[index - 1]?.protoId
              }
              protos={props.data?.protos}
              style={{
                position: 'absolute',
                right: 12, 
                bottom: 54
              }}
              fromId={fromId as string}
            />

            {total > 1 && (
              <View style={st.$pagin}>
                <SlidePagination
                  current={index - 1}
                  total={total}
                  onPress={onPressSlidePagination}
                />
              </View>
            )}
            {validImages.type === ImageType.photo && (
              <PKTag pks={pkTagData} photo={curImgInfo.curImg as PhotoInfo} />
            )}
        <MaterlizeMask logParams={{game_type:`${detail?.gameType ?? ''}`,card_id:cardId??''}}/>
          </View>
        </GestureDetector>
      </View>
    </>
  );
}

const EMOJI_BOTTOM_DIST = 24;

const st = StyleSheet.create({
  $imageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    pointerEvents: 'none',
    zIndex: 1
  },
  $bgBlur: {
    width: '100%',
    height: '100%',
    bottom: 0,
    left: 0,
    position: 'absolute',
    resizeMode: 'cover'
  },
  $swiperWrap: {
    position: 'relative',
    width: '100%',
    zIndex: 1
  },
  $slide: {
    width: '100%',
    height: '100%'
  },
  $pagin: {
    position: 'absolute',
    bottom: 44,
    width: '100%',
    zIndex: 2
  }
});
