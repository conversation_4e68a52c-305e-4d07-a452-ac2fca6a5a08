import { useMemoizedFn } from 'ahooks';
import { useNavigation } from 'expo-router';
import { useEffect, useMemo } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, { FadeIn, FadeOut, runOnJS } from 'react-native-reanimated';
import { Text } from '@/src/components';
import { CommonColor } from '@/src/theme/colors/common';
import { createStyle } from '@/src/utils';
import { reportClick, reportExpo } from '@/src/utils/report';
import { ArrowIcon } from '@/assets/image/svg';
import { CommonActions } from '@react-navigation/native';

const DETAIL_PATH_NAME = 'detail/[id]';

export const ToFirstDetailTag = ({
  fromId,
  detailId,
  pagePath = DETAIL_PATH_NAME,
  style
}: {
  fromId?: string;
  detailId?: string;
  pagePath?: string;
  style?: StyleProp<ViewStyle>;
}) => {
  const navigation = useNavigation();

  const state = navigation.getState();

  const { firstDetailIdx, isMultiLevelDetail } = useMemo(() => {
    const routesLength = state.routes.length;

    // 第一个详情页
    const firstDetailIdx = state.routes.findIndex(r => {
      return fromId && r.name === pagePath && r.params?.cardId === fromId;
    });

    // 是否存在多级详情页
    const isMultiLevelDetail =
      firstDetailIdx >= 0 && routesLength - 1 > firstDetailIdx;

    return { routesLength, firstDetailIdx, isMultiLevelDetail };
  }, [state.routes, fromId]);

  // 嵌套详情页回退
  const handleGoBackToFirst = useMemoizedFn(() => {
    navigation.dispatch(state => {
      reportClick('fanhui', {
        module: 'detail',
        contentid: detailId,
        fanhuiid: fromId
      });
      // 多级详情页: 回退到第一个详情页
      const routes = state.routes.slice(0, firstDetailIdx + 1);
      return CommonActions.reset({
        ...state,
        routes,
        index: firstDetailIdx
      });
    });
  });

  const gesture = Gesture.Tap().onBegin(() => {
    runOnJS(handleGoBackToFirst)();
  });

  useEffect(() => {
    if (isMultiLevelDetail && detailId) {
      reportExpo('fanhui', {
        module: 'detail',
        contentid: detailId
      });
    }
  }, [detailId]);

  return (
    isMultiLevelDetail && (
      <GestureDetector gesture={gesture}>
        <Animated.View
          style={[$styles.wrapper, style]}
          entering={FadeIn}
          // exiting={FadeOut}
        >
          <View style={$styles.innerContainer}>
            <View style={{ transform: [{ rotateY: '180deg' }] }}>
              <ArrowIcon color={CommonColor.white2} />
            </View>
            <Text style={$styles.text}>回到首个作品</Text>
          </View>
        </Animated.View>
      </GestureDetector>
    )
  );
};

const $styles = createStyle({
  wrapper: {
    borderTopRightRadius: 14,
    borderBottomRightRadius: 14,
    overflow: 'hidden',
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    left: 0,
  },

  innerContainer: {
    height: 28,
    minWidth: 48,
    padding: 4,
    paddingRight: 12,
    paddingLeft: 6,
    gap: 2,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center'
  },

  text: {
    fontSize: 12,
    lineHeight: 17,
    fontWeight: '500',
    color: CommonColor.white2
  }
});
