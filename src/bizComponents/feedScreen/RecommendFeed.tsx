import {
  useDebounce,
  useDebounceFn,
  useMemoizedFn,
  useWhyDidYouUpdate
} from 'ahooks';
import { useGlobalSearchParams, useNavigation } from 'expo-router';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import React from 'react';
import { AppState, StyleProp, View, ViewStyle } from 'react-native';
import {
  runOnJS,
  useAnimatedReaction,
  useSharedValue
} from 'react-native-reanimated';
import { feedAFClient, feedClient } from '@/src/api';
import { ABTest, ABTestEvent } from '@/src/api/abTest';
import { showToast } from '@/src/components';
import { EmptyPlaceHolder } from '@/src/components/Empty';
import {
  InfiniteListRef,
  RequestScene
} from '@/src/components/infiniteList/typing';
import { showLogin } from '@/src/components/login';
import { showFollowModal } from '@/src/components/popup/followModal';
import { CardEntryScene } from '@/src/components/publishEntry/GameEntryCard';
import { GameEntryList } from '@/src/components/publishEntry/GameEntryList';
import { WaterFall2 } from '@/src/components/waterfall/WaterFall2';
import {
  EWaterFallTabReportType,
  EWaterFallTabType,
  WaterFallCardData
} from '@/src/components/waterfall/type';
import {
  FetchMethodPayloadType,
  useRequestFeed
} from '@/src/components/waterfall/useRequsetFeed';
import { BOTTOM_TAB_HEIGHT, LIST_BOTTOM_SAFE_HEIGHT } from '@/src/constants';
import { useSafeAreaInsetsStyle, useSafeBottomArea } from '@/src/hooks';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useAuthStore } from '@/src/store/authInfo';
import { useBrandStore } from '@/src/store/brand';
import {
  ConfigDefaultKey,
  ConfigType,
  useConfigStore
} from '@/src/store/config';
import { SwitchName, useControlStore } from '@/src/store/control';
import { EntryPreset } from '@/src/store/gameEntry';
import { usePreloadStore } from '@/src/store/preload';
import { useTeenModeStore } from '@/src/store/teenModeStore';
import { Theme } from '@/src/theme/colors/type';
import { $flex } from '@/src/theme/variable';
import { ListResponse, TabItemType } from '@/src/types';
import { getScreenSize, isIos } from '@/src/utils';
import { StyleSheet } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { CommonEventBus } from '@/src/utils/event';
import { safeParseJson } from '@/src/utils/opt/safeParseJson';
import { getGlobalParams, reportExpo } from '@/src/utils/report';
import { homePerformanceCollector } from '@/src/utils/report/homePageCollector';
import { PostDetailSource } from '@/src/utils/report/type';
import { saveImageWithWmk } from '@/src/utils/savePicture';
import { Text } from '@Components/text';
import { CellCardScene } from '../feedcard/types';
import { GuideEvent, GuideEventBus } from '../homeBottomTab/eventBus';
import { TrackType } from '../liveScreen/constants';
import { NestedScrollView } from '../nestedScrollView';
import { useTabGestureEventsHandlersDefault } from '../nestedScrollView/hooks/useTabGestureEventsHandlersDefault';
import { ReanimatedTabView } from '../reanimatedTabView';
import { useAppendCard } from './hooks/useAppendCard';
import { useFocusChange } from './hooks/useFocusChange';
import { useCachedTab } from './recommendSecondaryTab/cacheTab.hook';
import { DanceTogetherExtInfo } from '@/proto-registry/src/web/raccoon/common/dance_together_pb';
import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb';
import { BrandState } from '@/proto-registry/src/web/raccoon/common/state_pb';
import { useIsFocused } from '@react-navigation/native';
import * as APMModule from '@step.ai/apm-module';
import { useShallow } from 'zustand/react/shallow';
import { IpRecommendFeed } from './IpRecommendFeed';
import { AsyncAppendCard } from './asyncAppendingCard';
import { IpDisplayList } from './brandDisplayList';
import FakeTabContainer from './fakeTabContainer';
import { RecommendSecondaryTab, TabItemInfo } from './recommendSecondaryTab';
import { FeedScreenPageParams, RecSceneName } from './type';

const FEED_RESTORE_MAX = 2;

const FixedTab: TabItemInfo[] = [
  {
    title: '热门',
    key: 'recommend',
    isFire: true,
    brandId: 0
  },
  {
    title: '关注',
    key: 'follow',
    brandId: 0
  }
];

const failOffsetX = [-20, 20] as [number, number];
const activeOffsetY = [-20, 20] as [number, number];

const HeaderComponent = memo(
  ({
    isPure,
    pressMore,
    isSlideUp,
    onDiamondReady,
    list,
    activeIndex,
    onPress
  }: {
    isPure: boolean | undefined;
    pressMore: () => void;
    isSlideUp: boolean;
    onDiamondReady: () => void;
    activeIndex: string | undefined;
    list: TabItemInfo[];
    onPress: (key: string, tabItem?: TabItemInfo | undefined) => void;
  }) => {
    return (
      <View>
        {isPure ? null : (
          <GameEntryList
            preset={EntryPreset.HOME}
            scene={CardEntryScene.HOME}
            showMore={true}
            onPressMore={pressMore}
            active={!isSlideUp}
            onReady={onDiamondReady}
          />
        )}
        {isPure ? (
          <View
            style={{
              height: 1
            }}
          />
        ) : (
          <RecommendSecondaryTab<TabItemInfo>
            list={list}
            activeIndex={activeIndex}
            onPress={onPress}
            enableExpand={!isPure}
          />
        )}
      </View>
    );
  }
);

const InnerRecommendFeed = ({
  appendId,
  appendImageUrl,
  appendGameType,
  isAsyncAppend,
  needSaveVideo,
  pageTab,
  timestamp: tabUpdateTimestamp,
  active,
  pending,
  onFirstFeedDone,
  PagerViewGesture,
  isPure
}: FeedScreenPageParams & {
  active: boolean;
  pending: boolean;
  onFirstFeedDone?: () => void;
  PagerViewGesture?: any;
  isPure?: boolean;
}) => {
  const searchParams = useGlobalSearchParams();

  /** 组件 ref */
  const listRef1 = useRef<InfiniteListRef>(null);
  const listRef2 = useRef<InfiniteListRef>(null);
  const sheetRef = useRef<NestedScrollView>(null);

  /** hooks */
  const navigation = useNavigation();
  const isFocused = useIsFocused();
  const $safePaddingBottom = useSafeBottomArea();
  const { waitingCard, asyncAppendCard } = useAppendCard();

  /** 状态 */
  const [firstReqFinished, setReqFinished] = useState(false);
  const uid = useAuthStore(state => state.uid);
  const focusedUid = useFocusChange(uid);
  const { brandInfos } = useBrandStore(
    useShallow(state => ({
      brandInfos: state.brandInfos,
      brandShow: state.brandShow
    }))
  );
  const brandShow = useControlStore(
    useShallow(state => !state.checkIsOpen(SwitchName.DISABLE_IP_ICON))
  );

  const isTeenModeEnabled = useTeenModeStore(state => state.isTeenModeEnabled);

  const list = useMemo(() => {
    const entry = [...FixedTab];
    if (brandShow) {
      entry.push(
        ...brandInfos
          .filter(item => item.state === BrandState.ONLINE)
          .map(item => ({
            key: `ip_${item.brand}`,
            title: `#${item.displayName}`,
            brandId: item.brand,
            isNew: item.isNew,
            isHot: item.hot
          }))
      );
    }
    return entry;
  }, [brandInfos, brandShow]);

  const scrollableNameSV = useSharedValue<string>(FixedTab[0].key);

  const customListProps = useRef({
    renderAheadOffset: 250,
    applyWindowCorrection(offsetX, offsetY, windowCorrection) {
      return {
        ...windowCorrection,
        startCorrection: -10,
        endCorrection: Math.max(headerCompHeight.current - 140, 0)
      };
    }
  });

  /** 变量 */
  const prevUserState = useRef(uid);
  const headerCompHeight = useRef<number>(270);
  const activeHolder = useRef(0);

  const currentExposureCount = useRef(0);
  const guideExposureCount = useRef(16);
  const triggerCreationGuide = useRef(false);
  const triggerSearchGuide = useRef(false);
  const exposureIdSet = useRef(new Set<string>());

  const appState = useRef(AppState.currentState);
  const updateTimeRef = useRef(0);
  const hasSlideUp = useSharedValue(false);
  const [isSlideUp, setIsSlideUp] = useState(false);
  const recommendExtendedState = useRef({
    reportParams: {
      tab: EWaterFallTabReportType[EWaterFallTabType.RECOMMEND],
      from: PostDetailSource.FEED
    },
    emitHolderIndex: (index: number) => {
      activeHolder.current = index;
    },
    scene: CellCardScene.HOME
  });
  const followExtendedState = useRef({
    reportParams: {
      tab: EWaterFallTabReportType[EWaterFallTabType.FOLLOW],
      from: PostDetailSource.FEED
    }
  });

  useAnimatedReaction(
    () => {
      return hasSlideUp.value;
    },
    (result, prev) => {
      if (result !== prev) runOnJS(setIsSlideUp)(result);
    }
  );

  const { go2HomePage } = useChangeRoute();

  const {
    activeIndex,
    cachedTabs: cachedIpTabs,
    visitedTabs,
    onPress,
    resetDefaultTab
  } = useCachedTab({
    fixedTabs: [FixedTab[0]],
    isPageActive: active,
    defaultTab: FixedTab[0],
    tabList: list
  });

  const visitedIpTabs = useMemo(
    () => visitedTabs.filter(t => !FixedTab.find(ft => ft.key === t.key)),
    [visitedTabs]
  );

  /** 数据请求 */
  // 如果是青少年模式 recSceneName = ADOLESCENT_MODE_REC，否则维持HOME_TAB
  const fetchRecommendMethod = useMemoizedFn(async function fetchRecommend(
    payload: FetchMethodPayloadType
  ) {
    // APMModule.beginLaunchT2();
    updateTimeRef.current = Date.now();
    APMModule.beginLaunchSpan('request_feed');
    const startTime = Date.now();

    let res: ListResponse | undefined = undefined;
    const preloadRequest = usePreloadStore.getState().get();
    if (payload.scene === RequestScene.INIT && preloadRequest) {
      const startTime = Date.now();
      res = await preloadRequest;
      usePreloadStore.getState().clear();
      console.log('第一次=======', Date.now() - startTime);
    } else {
      res = await feedClient.allCards({
        recSceneName: isTeenModeEnabled
          ? RecSceneName.ADOLESCENT_MODE_REC
          : RecSceneName.HOME_TAB,
        reserved: {
          prefer_ip:
            (searchParams?.['invoke_ip'] as string) ||
            getGlobalParams()?.invoke_ip ||
            ''
        },
        pagination: payload.pagination,
        useHitBack: payload.useHitBack
      });
    }
    console.log('startTime---', Date.now() - startTime);
    APMModule.endLaunchSpan('request_feed');
    return res;
  });

  const fetchfollowingMethod = useMemoizedFn(
    async (payload: FetchMethodPayloadType) => {
      if (useAuthStore.getState().uid) {
        const res = await feedClient.followCards({
          pagination: payload.pagination
        });
        return res;
      }
      throw new Error('user_not_login');
    }
  );

  const {
    sourceData: recommendData,
    loading: recommendDataLoading,
    error: recommendError,
    hasMore: recommendHasMore,
    fetchList: fetchRecommendList,
    unshiftData: unshiftRecommendData,
    getSingleCard
  } = useRequestFeed({
    tag: 'recommend',
    fetchMethod: fetchRecommendMethod,
    onFirstDataRendered,
    onError: scene =>
      scene === RequestScene.REFRESHING
        ? showToast('刷新失败啦，请重试')
        : undefined
  });

  const {
    sourceData: followingData,
    loading: followingDataLoading,
    error: followingError,
    hasMore: followingHasMore,
    fetchList: fetchFollowingList
  } = useRequestFeed({
    tag: 'follow',
    fetchMethod: fetchfollowingMethod,
    onError: scene =>
      scene === RequestScene.REFRESHING
        ? showToast('刷新失败啦，请重试')
        : undefined
  });

  const getGuideConfig = useMemoizedFn(() => {
    useConfigStore
      .getState()
      .getConfig(ConfigType.guide, { key: ConfigDefaultKey })
      .then(res => {
        const config = res as Array<string>;
        if (config?.length) {
          const exposureCount = (
            safeParseJson(config[0]) as { exposureCount: number }
          ).exposureCount;
          guideExposureCount.current = exposureCount;
        }
      })
      .catch(() => {});
  });

  const onResourceExposure = useMemoizedFn((item: WaterFallCardData) => {
    if (item?.card?.resourceInfo) {
      reportExpo('resource_expo', {
        module: 'feed',
        resourceid: item.card?.id,
        picid:
          item.card?.resourceInfo?.resourceList[activeHolder.current]?.image
            ?.url,
        pic_order: activeHolder.current + ''
      });

      try {
        feedAFClient.uploadHomepageOperation({
          homepageOperationId: item?.card?.id
        });
      } catch (error) {
        errorReport(
          'uploadHomepageOperationError',
          ReportError.COMPONENTS,
          error
        );
      }
    }

    if (item.card?.id) {
      if (exposureIdSet.current.has(item.card?.id)) return;
      exposureIdSet.current.add(item.card?.id);
      currentExposureCount.current++;
      if (currentExposureCount.current >= guideExposureCount.current) {
        // 创作引导
        if (
          !triggerCreationGuide.current &&
          ABTest.variantMap?.[ABTestEvent.CREATION_GUIDE]
        ) {
          GuideEventBus.emit(GuideEvent.CREATION_GUIDE);
          triggerCreationGuide.current = true;
        }
        // 搜索引导
        if (
          !triggerSearchGuide.current &&
          ABTest.variantMap?.[ABTestEvent.FEED_SEARCH_GUIDE]
        ) {
          GuideEventBus.emit(GuideEvent.SEARCH_GUIDE);
          triggerSearchGuide.current = true;
        }
      }
    }
  });

  /** 推荐关注用户 */
  useEffect(() => {
    if (activeIndex === 'follow') {
      if (useAuthStore.getState().uid) {
        showFollowModal();
      }
    }
  }, [activeIndex]);

  const clickRefresh = useMemoizedFn(() => {
    sheetRef.current?.snapToInitial();

    if (activeIndex === 'recommend') {
      listRef1.current?.forceRefresh();
    } else if (activeIndex === 'follow') {
      listRef2.current?.forceRefresh();
    }
  });

  const onRecommendReady = useMemoizedFn(() => {
    homePerformanceCollector.markPerformanceTimestamp(
      'home_feed_available_timestamp'
    );
  });

  const onDiamondReady = useMemoizedFn(() => {
    homePerformanceCollector.markPerformanceTimestamp(
      'home_diamond_available_timestamp'
    );
  });

  const onRecommendInit = useMemoizedFn(() => {
    homePerformanceCollector.markPerformanceTimestamp(
      'home_feed_init_timestamp'
    );
  });

  const { run: debounceRefresh } = useDebounceFn(clickRefresh, {
    wait: 300
  });

  useEffect(() => {
    const handler = (info?: { tab: TabItemType }) => {
      if (info?.tab?.startsWith(TabItemType.HOME)) {
        debounceRefresh();
      }
    };
    const handler2 = () => {
      fetchRecommendList(RequestScene.INIT);
    };

    CommonEventBus.on('tabBarPressedWhenFocus', handler);
    CommonEventBus.on('forceRefreshRecommend', handler2);

    return () => {
      CommonEventBus.off('tabBarPressedWhenFocus', handler);
      CommonEventBus.off('forceRefreshRecommend', handler2);
    };
  }, []);

  console.log(
    'visitedTabs===>',
    visitedTabs,
    Boolean(visitedTabs.find(t => t.key === 'follow'))
  );

  // Fix 用户登录后应该刷新Feed 流
  useEffect(() => {
    // 从null变为user，触发一次滚动刷新
    if (uid && !prevUserState.current) {
      try {
        listRef1.current?.scrollTop();
        listRef2.current?.scrollTop();
        fetchRecommendList(RequestScene.INIT);
        fetchFollowingList(RequestScene.INIT);
        resetDefaultTab(true);
      } catch (e) {
        errorReport('fetchList', ReportError.COMPONENTS, e, 'RN.waterfall');
      }
    }
    prevUserState.current = uid;
  }, [uid]);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active' &&
        isFocused
      ) {
        if (Date.now() - updateTimeRef.current > 300 * 1000) {
          showToast('小狸已为您推荐至最新粮仓~', 2000);
          listRef1.current?.scrollTop();
          //   setTab(0);
          fetchRecommendList(RequestScene.INIT);
        }
      }
      appState.current = nextAppState;
    });
    return () => {
      subscription.remove();
    };
  }, [isFocused]);

  useEffect(() => {
    if (firstReqFinished) {
      onFirstDataRendered(4);
    }
  }, [firstReqFinished]);

  useEffect(() => {
    if (
      (pageTab === 'recommend' || pageTab === 'follow') &&
      pageTab !== activeIndex
    ) {
      onPress(pageTab);
      // setActiveIndex(pageTab);
    }
  }, [tabUpdateTimestamp]);

  // 发布后在首页插入卡片
  useEffect(() => {
    if (!appendId) {
      return;
    }

    const afterAppend = async (index?: number, isAsync?: boolean) => {
      const needShowOnFeed = index === 0;
      const card = needShowOnFeed
        ? await unshiftRecommendData(String(appendId))
        : await getSingleCard(String(appendId));

      if (needShowOnFeed) {
        // onTabChange(0);
        listRef1.current?.scrollTop();
        sheetRef.current?.snapToInitial();

        if (isAsync) {
          reportExpo('videocard_preview', {
            module: 'feed',
            gameType: card?.cards?.card?.gameType,
            type: TrackType[card?.cards?.card?.gameType ?? ''] ?? ''
          });
        }
      }

      const newCardInfo = card?.cards?.card?.cardExtInfo;
      // console.log('==lin==newCardInfo', JSON.stringify(newCardInfo));
      if (needSaveVideo === '1') {
        const url =
          (newCardInfo?.value.value as DanceTogetherExtInfo)?.video4dl?.url ||
          (newCardInfo?.value.value as LivePhotoExtInfo)?.downloadVideoUrl;
        if (url) {
          try {
            await saveImageWithWmk(url);
          } catch (error) {
            showToast('保存本地视频失败');
            errorReport('save local videoError', ReportError.ERROR, error);
          }
        }
      }

      CommonEventBus.emit('asyncCardPublished');
    };

    if (isAsyncAppend) {
      showToast('正在发布中');
      asyncAppendCard({
        appendId,
        appendImageUrl,
        appendGameType,
        onSuccess: (index?: number) => {
          afterAppend(index, true);
        }
      });
    } else {
      afterAppend(0);
    }
  }, [appendId, appendImageUrl, isAsyncAppend, needSaveVideo]);

  useEffect(() => {
    getGuideConfig();
  }, []);

  const pressMore = useCallback(() => {
    CommonEventBus.emit('expandGameEntry');
  }, []);

  useEffect(() => {
    scrollableNameSV.value = activeIndex || '';
  }, [activeIndex]);

  return (
    <View style={[$flex]}>
      <NestedScrollView
        ref={sheetRef}
        hasSlideUp={hasSlideUp}
        topInset={0}
        topPreserveInset={isPure ? 0 : 40}
        headerComponent={
          <HeaderComponent
            isPure={isPure}
            pressMore={pressMore}
            isSlideUp={isSlideUp}
            onDiamondReady={onDiamondReady}
            list={list}
            activeIndex={activeIndex}
            onPress={onPress}
          />
        }
        gestureEventsHandlersHook={useTabGestureEventsHandlersDefault}
        simultaneousWithExternalGesture={isIos ? undefined : PagerViewGesture}
        failOffsetX={failOffsetX}
        activeOffsetY={activeOffsetY}
        scrollableNameSV={scrollableNameSV}
      >
        <View style={$flex}>{renderFeed()}</View>
      </NestedScrollView>
      <AsyncAppendCard waitingCard={waitingCard} />
    </View>
  );

  function onFirstDataRendered(step: number) {
    // 1开始请求 2请求成功 3请求结束 4渲染结束
    try {
      if (step === 1) {
        console.log(new Date().getTime(), 'APM LOG', step);
        return APMModule.beginLaunchT2();
      }
      if (step === 2) {
        console.log(new Date().getTime(), 'APM LOG', step);
        APMModule.endLaunchT2();
        APMModule.beginLaunchT3();
        return;
      }
      if (step === 3) {
        setReqFinished(true);
      }
      if (step === 4) {
        console.log(new Date().getTime(), 'APM LOG', step);
        APMModule.endLaunchT3();
        onFirstFeedDone?.();
      }
    } catch (e) {
      console.warn('LOG APM ERROR', e);
    }
  }

  function renderFeed() {
    return (
      <>
        <FakeTabContainer
          key={'recommend'}
          active={activeIndex === 'recommend'}
        >
          <WaterFall2
            ref={listRef1}
            data={recommendData}
            loading={recommendDataLoading}
            error={recommendError}
            hasMore={recommendHasMore}
            onRequest={fetchRecommendList}
            footerStyle={[
              {
                paddingBottom:
                  $safePaddingBottom +
                  BOTTOM_TAB_HEIGHT +
                  LIST_BOTTOM_SAFE_HEIGHT
              }
            ]}
            onReady={onRecommendReady}
            onInit={onRecommendInit}
            isActive={active && activeIndex === 'recommend'}
            extendedState={recommendExtendedState.current}
            onCardExposure={onResourceExposure}
            customListProps={customListProps.current}
            scrollViewProps={{
              scrollViewName: 'recommend'
            }}
          />
        </FakeTabContainer>

        {Boolean(visitedTabs.find(t => t.key === 'follow')) && (
          <FakeTabContainer key={'follow'} active={activeIndex === 'follow'}>
            {focusedUid ? (
              <WaterFall2
                key={focusedUid}
                ref={listRef2}
                data={followingData}
                loading={followingDataLoading}
                error={followingError}
                hasMore={followingHasMore}
                onRequest={fetchFollowingList}
                footerStyle={[
                  {
                    paddingBottom:
                      $safePaddingBottom +
                      BOTTOM_TAB_HEIGHT +
                      LIST_BOTTOM_SAFE_HEIGHT
                  }
                ]}
                customEmptyProps={{
                  children: '快去关注别人！小狸没有东西看啦~'
                }}
                isActive={active && activeIndex === 'follow'}
                extendedState={followExtendedState.current}
                customListProps={customListProps.current}
                scrollViewProps={{
                  scrollViewName: 'follow'
                }}
              />
            ) : (
              <EmptyPlaceHolder
                buttonText="立即登录"
                theme={Theme.DARK}
                button
                type="needlogin"
                onButtonPress={showLogin}
                style={{
                  height: 400,
                  paddingBottom: $safePaddingBottom
                }}
              >
                登录账号，查看你关注的精彩内容
              </EmptyPlaceHolder>
            )}
          </FakeTabContainer>
        )}
        {visitedIpTabs.map(item => {
          const isRender = Boolean(
            cachedIpTabs.find(tab => tab.key === item.key)
          );
          return (
            <FakeTabContainer
              key={item.brandId}
              active={item.key === activeIndex}
            >
              <IpRecommendFeed
                key={item.brandId}
                isRender={isRender}
                brandId={item.brandId}
                active={active && item.key === activeIndex}
                pending={pending}
                scrollViewProps={{
                  scrollViewName: item.key
                }}
              />
            </FakeTabContainer>
          );
        })}
      </>
    );
  }
};

export const RecommendFeed = memo(InnerRecommendFeed, (prev, now) => {
  return prev.active === now.active && prev.pending === now.pending;
});
