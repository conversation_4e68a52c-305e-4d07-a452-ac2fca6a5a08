import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { useNavigation } from 'expo-router';
import { memo, useEffect, useRef } from 'react';
import { showToast } from '@/src/components';
import {
  InfiniteListRef,
  RequestScene
} from '@/src/components/infiniteList/typing';
import { WaterFall2 } from '@/src/components/waterfall/WaterFall2';
import {
  EWaterFallTabReportType,
  EWaterFallTabType
} from '@/src/components/waterfall/type';
import { useRequestFeed } from '@/src/components/waterfall/useRequsetFeed';
import { BOTTOM_TAB_HEIGHT, LIST_BOTTOM_SAFE_HEIGHT } from '@/src/constants';
import { useSafeBottomArea } from '@/src/hooks';
import { TabItemType } from '@/src/types';
import { getScreenSize } from '@/src/utils';
import { CommonEventBus } from '@/src/utils/event';
import { PostDetailSource } from '../../utils/report';
import { RecSceneName } from './type';

const EXTENEDED_STATE = {
  reportParams: {
    tab: EWaterFallTabReportType[EWaterFallTabType.RECOMMEND],
    from: PostDetailSource.TAG
  }
};
const InnerIpRecommendFeed = ({
  brandId,
  active,
  isRender,
  pending,
  scrollViewProps
}: {
  brandId: number;
  active: boolean;
  isRender: boolean;
  pending: boolean;
  scrollViewProps?: any;
}) => {
  const $safePaddingBottom = useSafeBottomArea();
  const { sourceData, loading, error, hasMore, fetchList } = useRequestFeed({
    defaultFetch: false,
    requestParams: {
      brand: brandId,
      recSceneName: RecSceneName.IP_LANDING
    },
    onError: scene =>
      scene === RequestScene.REFRESHING
        ? showToast('刷新失败啦，请重试')
        : undefined,
    tag: brandId.toString()
  });

  const listRef2 = useRef<InfiniteListRef>(null);
  const navigation = useNavigation();

  useEffect(() => {
    if (active && !sourceData.length) {
      fetchList(RequestScene.INIT);
    }
  }, [active]);

  const clickRefresh = useMemoizedFn(() => {
    if (active) {
      listRef2.current?.forceRefresh();
    }
  });

  const { run: debounceRefresh } = useDebounceFn(clickRefresh, {
    wait: 300
  });

  useEffect(() => {
    const handler = (info?: { tab: TabItemType }) => {
      if (info?.tab?.startsWith(TabItemType.HOME)) {
        debounceRefresh();
      }
    };

    CommonEventBus.on('tabBarPressedWhenFocus', handler);
    return () => {
      CommonEventBus.off('tabBarPressedWhenFocus', handler);
    };
  }, []);

  return (active || sourceData) && isRender ? (
    <WaterFall2
      key={brandId}
      ref={listRef2}
      data={sourceData}
      loading={loading}
      error={error}
      hasMore={hasMore}
      onRequest={fetchList}
      footerStyle={[
        {
          paddingBottom:
            $safePaddingBottom + BOTTOM_TAB_HEIGHT + LIST_BOTTOM_SAFE_HEIGHT
        }
      ]}
      customEmptyProps={{
        children: '暂无内容哦～去看看别的吧'
      }}
      isActive={active}
      extendedState={EXTENEDED_STATE}
      customListProps={{
        renderAheadOffset: 250
      }}
      scrollViewProps={scrollViewProps}
    />
  ) : null;
};

export const IpRecommendFeed = memo(InnerIpRecommendFeed);
