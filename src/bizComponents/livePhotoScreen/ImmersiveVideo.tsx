import { useMemoizedFn } from 'ahooks';
import { ImageLoadEventData, ImageSource } from 'expo-image';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ImageSourcePropType, InteractionManager, View } from 'react-native';
import {
  GestureStateChangeEvent,
  TapGestureHandlerEventPayload
} from 'react-native-gesture-handler';
import { DetailModal } from '@/src/bizComponents/detailModal';
import {
  Icon,
  IconTypes,
  Image,
  ImageProps,
  ImageWithTosProps,
  Text
} from '@/src/components';
import { TapGestureWrapper } from '@/src/components/gesture/TapGestureWrapper';
import { useTapThrottle } from '@/src/components/gesture/useTapThrottle';
import { BlurImage } from '@/src/components/image/BlurImage';
import { hideFollowGuideToast } from '@/src/components/popup/detailFollowToast';
import { BOTTOM_TAB_HEIGHT } from '@/src/constants';
import {
  useSafeBottomAddNav,
  useSafeBottomArea,
  useSafeBottomWithNav,
  useScreenSize
} from '@/src/hooks';
import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';
import { useRunOnce } from '@/src/hooks/useOneRunning';
import { useParams } from '@/src/hooks/useParams';
import { useBehaviorStore } from '@/src/store/behavior';
import { useDetailStore } from '@/src/store/detail';
import { useVideoFlow } from '@/src/store/video-flow';
import { useVideoFlowComment } from '@/src/store/video-flow-comment';
import { darkColors } from '@/src/theme';
import { GameType, RichCardInfo, VideoFLowScene } from '@/src/types';
import { StyleSheet, isIos } from '@/src/utils';
import {
  getLowResolutionVideoUrl,
  getVideoUrlFromCard
} from '@/src/utils/cardUtils';
import { checkWeakNetwork } from '@/src/utils/device/network';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { Event } from '@/src/utils/event';
import { formatTosUrl } from '@/src/utils/getTosUrl';
import {
  addCommonReportParams,
  report,
  reportClick,
  reportExpo
} from '@/src/utils/report';
import {
  IMMER_EVENT_KEYS,
  TImmersiveEventKeys
} from '../trendingScreen/ImmersiveDetail/constants';
import { ActionsLayer as ActionsLayerV2 } from '../trendingScreen/components/ActionsLayerV2';
import { SwipeToUserWrapper } from '../trendingScreen/components/SwiperToUserWrapper';
import { VideoProgress } from './actionsLayer/VideoProgress';
import {
  useImmersiveReport,
  useLeaveReport,
  useVideoLoadedReport,
  useVideoReport
} from './hooks/reportHooks';
import { useIsInSwiperView } from './hooks/useIsInSwipeView';
import {
  TVIDEO_EVENTS_KEYS,
  VIDEO_EVENTS,
  useVideoStatusUpdate
} from './hooks/useVideoStatusUpdate';
import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb';
import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
import { AVPlaybackStatusSuccess, ResizeMode, Video } from '@step.ai/expo-av';
import { useShallow } from 'zustand/react/shallow';
import { ScaleableVideo } from './ScaleableVideo';
import { TakeSameSheet } from './TakeSameSheet';
import { ActionsLayer } from './actionsLayer';
import { TVIDEO_FLOW_EVENT_KEYS, VIDEO_FLOW_EVENTS } from './constants';
import { bindwidthRecorder, startPlayTimeRecorder } from './timeRecord';

interface LivePhotoScreenProps {
  data?: RichCardInfo;
  isActive?: boolean;
  shouldLoadVideo?: boolean;
  isCurrent: boolean;
  index: number;
  eventBus: Event<TVIDEO_FLOW_EVENT_KEYS>;
  videoScene?: VideoFLowScene;
  onPause?: () => void;
  onPlay?: () => void;
  isInSwipeViewRef?: React.MutableRefObject<boolean>;
  safePaddingBottom?: number;
  portalHostName?: string;
}

const useVideoLayout = (
  displayVideoHeight = 724,
  displayVideoWidth = 375,
  customBottomHeight?: number
) => {
  const { width: screenWidth, height: screenHeight } = useScreenSize('screen');
  const safeBottom = useSafeBottomWithNav();

  return useMemo(() => {
    const cacuHeight = displayVideoWidth
      ? (screenWidth * displayVideoHeight) / displayVideoWidth
      : displayVideoHeight;
    const maxHeight = Math.max(
      Math.ceil(screenHeight - (customBottomHeight ?? safeBottom + 73)),
      0
    ); // 73为底部高度+进度条
    const ratio = displayVideoHeight
      ? displayVideoWidth / displayVideoHeight
      : 1;

    const height = ratio < 3 / 4 ? maxHeight : Math.min(maxHeight, cacuHeight);
    const marginTop = (maxHeight - height) / 2;
    // console.log(
    //   '### cacu',
    //   displayVideoHeight,
    //   displayVideoWidth,
    //   safeBottom,
    //   maxHeight,
    //   screenHeight,
    //   '### res: ',
    //   height,
    //   ratio
    // );
    return [height, marginTop, ratio];
  }, [
    displayVideoHeight,
    displayVideoWidth,
    safeBottom,
    screenHeight,
    screenWidth,
    customBottomHeight
  ]);
};

const useHandleSeek = (
  eventBus?: Event<TVIDEO_EVENTS_KEYS>,
  videoRef?: React.RefObject<Video>
) => {
  useEffect(() => {
    const listener = ({
      positionMillis = 0
    }: { positionMillis?: number } = {}) => {
      reportClick('barpoint_button', {
        module: 'videofeed',
        position: positionMillis
      });
      videoRef?.current?.setPositionAsync(positionMillis, {
        toleranceMillisAfter: 100,
        toleranceMillisBefore: 100
      });
    };
    eventBus?.on(VIDEO_EVENTS.seek, listener);
    return () => {
      eventBus?.off(VIDEO_EVENTS.seek, listener);
    };
  }, [eventBus]);
};

const useClick2PlayReport = (
  eventBus: Event<TVIDEO_EVENTS_KEYS>,
  cardId?: string
) => {
  const { id: firstCardId } = useParams<{
    id?: string;
  }>();
  useEffect(() => {
    const listener = state => {
      if (state?.isLoaded) {
        reportExpo(
          'videocard_click',
          {
            module: 'videofeed'
          },
          'play'
        );
        eventBus.off(VIDEO_EVENTS.start, listener);
      }
    };
    if (cardId && cardId === firstCardId) {
      eventBus.on(VIDEO_EVENTS.start, listener);
    }
    return () => {
      eventBus.off(VIDEO_EVENTS.start, listener);
    };
  }, [firstCardId, cardId, eventBus]);
};

const useReimagineAutoSeek = (
  eventBus: Event<TVIDEO_EVENTS_KEYS>,
  shouldPlay: boolean,
  data?: RichCardInfo
) => {
  useEffect(() => {
    if (data?.card?.cardExtInfo?.value?.case === 'reimagine' && shouldPlay) {
      const cardExtInfo = data?.card?.cardExtInfo?.value?.value;
      const { skipHeadReimagineTpls } = useBehaviorStore.getState();
      const { getAVG } = startPlayTimeRecorder;
      // console.log('cardExtInfo.templateId', cardExtInfo.templateId);
      // console.log('cardExtInfo.headVideoClip', cardExtInfo.headVideoClip);
      // console.log('avg', getAVG());
      if (
        cardExtInfo.templateId &&
        cardExtInfo.headVideoClip &&
        getAVG() < 5000
      ) {
        if (skipHeadReimagineTpls?.includes(cardExtInfo.templateId)) {
          eventBus.emit(VIDEO_EVENTS.seek, {
            positionMillis: Math.max(
              cardExtInfo.headVideoClip.duration - 1000,
              0
            )
          });
        }
      }
    }
  }, [data?.card, shouldPlay, eventBus]);
  useEffect(() => {
    const listener = (state: AVPlaybackStatusSuccess) => {
      const cardExtInfo = data?.card?.cardExtInfo;
      if (
        state.positionMillis &&
        cardExtInfo?.value?.case === 'reimagine' &&
        cardExtInfo?.value.value.templateId &&
        cardExtInfo?.value.value.headVideoClip &&
        state.positionMillis >=
          Math.max(cardExtInfo.value.value.headVideoClip?.duration - 1000, 1000)
      ) {
        const cardExtInfoValue = cardExtInfo.value.value;
        useBehaviorStore
          .getState()
          .addSkipHeadReimagineTpls(cardExtInfoValue.templateId);
        eventBus.off(VIDEO_EVENTS.progress, listener);
      }
    };
    if (shouldPlay) {
      eventBus.on(VIDEO_EVENTS.progress, listener);
    } else {
      eventBus.off(VIDEO_EVENTS.progress, listener);
    }
    return () => {
      eventBus.off(VIDEO_EVENTS.progress, listener);
    };
  }, [data?.card, eventBus, shouldPlay]);
};

export const ImmersiveVideo = memo(
  ({
    data,
    isActive: shouldPlay,
    eventBus: videoFlowEventBus,
    isCurrent,
    shouldLoadVideo = true,
    index,
    videoScene = VideoFLowScene.DISCOVER,
    onPause,
    onPlay,
    isInSwipeViewRef,
    safePaddingBottom,
    portalHostName
  }: LivePhotoScreenProps) => {
    const { card } = data ?? {};
    const { cardId: firstCardId } = useParams();
    const extInfo = card?.cardExtInfo?.value?.value;
    // console.log('### ', JSON.stringify(extInfo));
    // const { videoHeight, videoWidth } = extInfo || {};
    const { videoWidth, videoHeight } = {
      videoWidth:
        card?.displayVideoWidth ||
        (extInfo as { videoWidth?: number })?.videoWidth,
      videoHeight:
        card?.displayVideoHeight ||
        (extInfo as { videoHeight?: number })?.videoHeight
    };

    const [transitionEnd, setTransitionEnded] = useState(false);

    const videoRef = useRef<Video>(null);
    const timeoutRef = useRef<NodeJS.Timeout>();
    const isPlayingRef = useRef(false);
    const retryTimeRef = useRef(0);
    const { isLowEndDevice } = useDevicePerformance();
    const isWeakNewwork = useMemo(
      () => checkWeakNetwork(bindwidthRecorder.getEWMA()),
      []
    );
    const defaultSafePadding = useSafeBottomArea(20);
    const safeBottom = safePaddingBottom || defaultSafePadding;

    const bottomHeight = BOTTOM_TAB_HEIGHT + safeBottom;

    const [height, marginTop, ratio] = useVideoLayout(
      videoHeight,
      videoWidth,
      videoScene === VideoFLowScene.TRENDING ? bottomHeight : void 0
    );
    const { eventBus, onPlaybackStatusUpdate } = useVideoStatusUpdate();
    useVideoReport(eventBus, card?.gameType, Boolean(shouldPlay));
    useLeaveReport(Boolean(shouldPlay));
    useVideoLoadedReport(eventBus, shouldLoadVideo, data?.card?.id);
    useHandleSeek(eventBus, videoRef);
    useClick2PlayReport(eventBus, data?.card?.id);
    useReimagineAutoSeek(eventBus, Boolean(shouldPlay), data);

    const innerEventBus = useMemo(() => new Event<TImmersiveEventKeys>(), []);

    useImmersiveReport(innerEventBus, {
      cardId: card?.id,
      gameType: card?.gameType
    });
    const isMuted = useVideoFlow(s => s.isMuted);
    const { showComment, commentContentDefaultCollapse, openDetailModal } =
      useVideoFlowComment(
        useShallow(state => ({
          showComment: state.showComment,
          commentContentDefaultCollapse: state.commentContentDefaultCollapse,
          openDetailModal: state.openDetailModal
        }))
      );

    const [paused, setPaused] = useState(false);

    const isMutedRef = useRef(isMuted);
    const shouldPlayRef = useRef(shouldPlay);

    useEffect(() => {
      if (shouldPlay) {
        // 滑动回到当前页后自动播放
        setPaused(false);
      }
    }, [shouldPlay]);

    useEffect(() => {
      isMutedRef.current = isMuted;
    }, [isMuted]);
    useEffect(() => {
      shouldPlayRef.current = shouldPlay;
    }, [shouldPlay]);

    const onOnceTap = useMemoizedFn(() => {
      if (!paused) {
        onPause?.();
      } else {
        onPlay?.();
      }
      setPaused(i => !i);
      hideFollowGuideToast();
    });

    useEffect(() => {
      const lowResolution = Boolean(
        (isLowEndDevice || isWeakNewwork) && getLowResolutionVideoUrl(card)
      );
      addCommonReportParams('videofeed', {
        low_resolution: Number(lowResolution)
      });
    }, []);

    const displayVideoUrl = useMemo(
      () => getVideoUrlFromCard(data?.card, isLowEndDevice || isWeakNewwork),
      [data?.card?.id]
    ); // isLowEndDevice不加入deps，防止清晰度切换。

    // useEffect(() => {
    //   console.log(
    //     '### isLowEndDevice',
    //     isLowEndDevice,
    //     ', isWeakNewwork: ',
    //     isWeakNewwork
    //   );
    //   if (isCurrent && (isLowEndDevice || isWeakNewwork)) {
    //     console.log(
    //       '### 使用低分辨率视频',
    //       data?.card?.displayVideoUrl,
    //       displayVideoUrl
    //     );
    //   }
    // }, [data?.card?.id, isCurrent]);

    const videoSource =
      (shouldLoadVideo || shouldPlay) && displayVideoUrl
        ? { uri: displayVideoUrl }
        : void 0;

    useEffect(() => {
      const interactionPromise = InteractionManager.runAfterInteractions(() => {
        setTransitionEnded(true);
      });
      eventBus.on(VIDEO_EVENTS.progress, () => {
        isPlayingRef.current = true;
        retryTimeRef.current = 0;
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = undefined;
        }
      });
      return () => {
        interactionPromise.cancel();
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = undefined;
        }
      };
    }, []);

    useEffect(() => {
      const listener = () => {
        videoFlowEventBus?.emit(VIDEO_FLOW_EVENTS.VIDEO_LOADED, { index });
      };
      if (shouldPlay) {
        eventBus.on(VIDEO_EVENTS.progress, listener, true);
      } else {
        eventBus.off(VIDEO_EVENTS.progress, listener);
      }
      return () => {
        eventBus.off(VIDEO_EVENTS.progress, listener);
      };
    }, [videoFlowEventBus, shouldPlay, index, eventBus]);

    useEffect(() => {
      if (shouldPlay) {
        retryTimeRef.current = 0;
        isPlayingRef.current = false;
      }
    }, [shouldPlay]);

    useEffect(() => {
      const callback = async () => {
        // console.log(
        //   '重试开始-----',
        //   isPlayingRef.current,
        //   retryTimeRef.current,
        //   shouldPlay,
        //   videoSource,
        //   card?.id,
        //   index
        // );
        const retryVideoSource = getVideoUrlFromCard(card, true);
        if (isPlayingRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = undefined;
        }
        if (shouldPlayRef.current && card?.id && retryVideoSource) {
          console.log('### 重试使用低分辩率');
          // console.log('重试开始-----', retryTimeRef.current);
          try {
            await videoRef.current?.unloadAsync();
            await videoRef.current?.loadAsync(
              { uri: retryVideoSource },
              {
                shouldPlay: shouldPlayRef.current,
                isMuted: isMutedRef.current,
                progressUpdateIntervalMillis: 50,
                isLooping: true
              }
            );
          } catch (error) {
            console.log('retry error ====', error);
            reportExpo('video_error', {
              module: 'videofeed',
              retryTime: retryTimeRef.current,
              media_uri: retryVideoSource
            });
            errorReport('视频重试错误', ReportError.VIDEO_FLOW, error, {
              media_uri: retryVideoSource,
              retryTime: retryTimeRef.current
            });
          }
          // console.log(
          //   '重试结束-----',
          //   isPlayingRef.current,
          //   retryTimeRef.current,
          //   shouldPlay,
          //   videoSource,
          //   index
          // );
          retryTimeRef.current = Math.min(3, retryTimeRef.current + 1);
          timeoutRef.current = setTimeout(
            callback,
            (retryTimeRef.current + 1) * 3000
          ); // 已经重试了大概率弱网，所以下次重试慢一点
        }
      };
      if (shouldPlay) {
        timeoutRef.current = setTimeout(
          callback,
          (retryTimeRef.current + 1) * 3000
        );
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = undefined;
          videoRef.current?.unloadAsync();
        }
      }
      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = undefined;
        }
      };
    }, [shouldPlay, card]);

    useEffect(() => {
      if (card?.id) {
        useDetailStore.getState().requestCommonInfo({
          cardId: card?.id,
          gameType: data?.card?.gameType
        });
        useDetailStore
          .getState()
          .mapDetailFromRichCardInfo(data as RichCardInfo);
      }
    }, [card?.id]);

    useEffect(() => {
      // 上报一下缺少 displayVideoUrl 的 case
      if (card && !card?.displayVideoUrl) {
        errorReport(
          'missing displayVideoUrl',
          ReportError.VIDEO_FLOW,
          {
            card
          },
          undefined,
          'p2'
        );
      }
    }, [card]);

    const posterSource = useMemo(() => {
      const uri =
        card?.gameType === GameType.REIMAGINE
          ? (extInfo as ReimagineExtInfo)?.headVideoClip?.imageUrl
          : card?.displayImageUrl;
      return uri;
    }, [card?.displayImageUrl]);

    const onImageLoaded = useRunOnce(() => {
      innerEventBus.emit('onImageRendered');
    });

    const PosterComponent = useCallback(
      ({
        source,
        style,
        tosSize
      }: {
        source: ImageProps['source'];
        style: ImageProps['style'];
        tosSize?: ImageWithTosProps['tosSize'];
      }) =>
        source && (
          <Image
            tosSize={tosSize ?? 'size1'}
            source={source}
            onLoad={onImageLoaded}
            contentFit={'cover'}
            style={[{ width: '100%', height: '100%' }, style]}
          />
        ),
      []
    );

    const { onSwipeStatusChanged, isInSwipePreview } =
      useIsInSwiperView(isInSwipeViewRef);

    const iDots = useMemo(() => {
      if ((extInfo as ReimagineExtInfo)?.videoClip) {
        return (extInfo as ReimagineExtInfo).videoClip.map(i => i.fromTime);
      }
    }, [extInfo]);

    const onTwiceTap = useMemoizedFn(
      (params: GestureStateChangeEvent<TapGestureHandlerEventPayload>) => {
        console.log('### onDoubleClick');
        innerEventBus.emit(IMMER_EVENT_KEYS.onDoubleLike, params);
      }
    );

    const onLongTap = useMemoizedFn(
      (params: GestureStateChangeEvent<TapGestureHandlerEventPayload>) => {
        innerEventBus.emit(IMMER_EVENT_KEYS.onLongPressShare, params);
      }
    );
    const { onTapOnce, onTapTwice } = useTapThrottle(onOnceTap, onTwiceTap);

    const trendingProgressBar = useMemo(
      () => (
        <View
          style={{
            position: 'absolute',
            bottom: 8,
            alignItems: 'stretch',
            left: 0,
            right: 0
          }}
        >
          <VideoProgress
            style={{
              marginHorizontal: 12
            }}
            height={3}
            activeHeight={10}
            iDots={iDots}
            indicatorStyle={{
              width: 9,
              height: 14,
              borderRadius: 10
            }}
            iDotStyle={{
              backgroundColor: darkColors.white[300]
            }}
            backgoundColors={{
              bgColor: darkColors.white[100],
              inactive: darkColors.white[900],
              active: darkColors.white[900]
            }}
            videoEventBus={eventBus}
          />
        </View>
      ),
      [eventBus, iDots]
    );

    const ActionsLayerRender = useMemoizedFn(() => {
      switch (videoScene) {
        case VideoFLowScene.TRENDING:
          return (
            <ActionsLayerV2
              index={index}
              data={data}
              eventBus={innerEventBus}
              visible={!showComment && shouldPlay}
              paused={paused}
              progressBar={trendingProgressBar}
              progressBarHeight={8}
            >
              <TapGestureWrapper
                onOnceTap={onTapOnce}
                onTwiceTap={onTapTwice}
                onLongTap={onLongTap}
              >
                <View style={StyleSheet.absoluteFill} />
              </TapGestureWrapper>
            </ActionsLayerV2>
          );
        default:
          return (
            <ActionsLayer
              index={index}
              data={data}
              videoRef={videoRef}
              videoEventBus={eventBus}
              visible={!showComment && shouldPlay}
              onOnceTap={onOnceTap}
              paused={paused}
            />
          );
      }
    });

    const swipeScreenProps = useMemo(
      () => ({
        onSwipeStatusChanged,
        enabled: shouldPlay && !showComment,
        rootPage: videoScene === VideoFLowScene.TRENDING
      }),
      [onSwipeStatusChanged, videoScene, shouldPlay, showComment]
    );

    const hideComment = useMemoizedFn(() => {
      openDetailModal({ showComment: false });
    });

    return (
      <SwipeToUserWrapper
        user={data?.user}
        swipeScreenProps={swipeScreenProps}
        isActive={shouldPlay}
      >
        <View
          style={{ height: '100%', width: '100%', backgroundColor: '#000' }}
        >
          <BlurImage
            style={[StyleSheet.absoluteFill, { opacity: 0.4 }]}
            intensity={100}
            url={card?.displayImageUrl}
          />
          <ScaleableVideo
            ref={videoRef}
            source={videoSource}
            onPlaybackStatusUpdate={onPlaybackStatusUpdate}
            progressUpdateIntervalMillis={50}
            showLowQualityPoster={firstCardId === card?.id}
            isMuted={isMuted}
            shouldPlay={
              !paused && shouldPlay && transitionEnd && !isInSwipePreview
            }
            posterSource={posterSource as ImageSource}
            PosterComponent={PosterComponent}
            style={
              videoScene !== VideoFLowScene.TRENDING ? { borderRadius: 18 } : {}
            }
            isLooping={true}
            resizeMode={ResizeMode.COVER}
            scaleableProps={{
              height,
              marginTop,
              collapsedHeight: 220,
              collapse: isCurrent && showComment
            }}
          />
          {ActionsLayerRender()}
          {isCurrent && showComment && (
            <DetailModal
              detailId={card?.id}
              visible={isCurrent && showComment}
              onClose={hideComment}
              portalHostName={portalHostName}
              card={data}
              active={shouldPlay}
              initCollapse={commentContentDefaultCollapse}
            />
          )}
          <TakeSameSheet data={data} scene={videoScene} />
        </View>
      </SwipeToUserWrapper>
    );
  }
);
