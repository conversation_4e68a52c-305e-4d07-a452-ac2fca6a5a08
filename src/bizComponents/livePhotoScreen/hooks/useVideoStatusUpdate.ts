import { useMemoizedFn } from 'ahooks';
import { useCallback, useMemo, useRef } from 'react';
import { KeyEqualToValue } from '@/src/types';
import { Event } from '@/src/utils/event';
import { bindwidthRecorder } from '../timeRecord';
import { AVPlaybackStatus } from '@step.ai/expo-av';

export type TVIDEO_EVENTS_KEYS =
  | 'ended'
  | 'error'
  | 'pause'
  | 'play'
  | 'resume'
  | 'progress'
  | 'start'
  | 'buffering'
  | 'loaded'
  | 'seek'
  | 'seeking';

/**
 * @property play - 开始播放，包括重复播放
 * @property pause - 不稳定，手动暂停和缓冲暂停都会触发
 * @property resume - 不稳定，继续播放，手动暂停和缓冲暂停后播放都会触发
 * @property start - 启播，只有第一次播放时触发
 * @property buffering - 卡顿，暂时不准确，在isPlaying = false 且 isBuffering = true触发
 * @property seeking - 拖动进度条触发
 * @property seek - 进度条拖动完成触发
 *
 */
export const VIDEO_EVENTS: KeyEqualToValue<TVIDEO_EVENTS_KEYS> = {
  ended: 'ended',
  error: 'error',
  pause: 'pause',
  play: 'play',
  start: 'start',
  resume: 'resume',
  progress: 'progress',
  buffering: 'buffering',
  loaded: 'loaded',

  seek: 'seek',
  seeking: 'seeking'
};

export enum PlayState {
  initial = 'initial',
  playing = 'playing',
  paused = 'paused', // 只有在播放过，播放停止才会流转到这个状态，无法判断什么原因导致暂停
  ended = 'ended'
}

export const useVideoStatusUpdate = () => {
  const eventBus = useMemo(() => new Event<TVIDEO_EVENTS_KEYS>(), []);
  const loadStateRef = useRef<boolean>(false);
  // 只能使用hack方式判断启播
  const { updater } = bindwidthRecorder;
  const playStateRef = useRef<PlayState>(PlayState.initial);
  const onPlaybackStatusUpdate = useMemoizedFn((state: AVPlaybackStatus) => {
    if (!state.isLoaded) {
      loadStateRef.current = false;
      eventBus.emit(VIDEO_EVENTS.error, state);
    } else {
      if (state?.loadSpeed) {
        updater(state.loadSpeed);
      }
      if (!loadStateRef.current) {
        loadStateRef.current = true;
        eventBus.emit(VIDEO_EVENTS.loaded, state);
      }
      switch (playStateRef.current) {
        case PlayState.initial: {
          if (state.isPlaying) {
            eventBus.emit(VIDEO_EVENTS.start, state);
            // 每次开始播放都要触发
            eventBus.emit(VIDEO_EVENTS.play, state);
            // eventBus.emit(VIDEO_EVENTS.progress, state);
            playStateRef.current = PlayState.playing;
          }
          break;
        }
        case PlayState.ended: {
          if (state.isPlaying) {
            eventBus.emit(VIDEO_EVENTS.play, state);
            // eventBus.emit(VIDEO_EVENTS.progress, state);
            playStateRef.current = PlayState.playing;
          }
          break;
        }
        case PlayState.playing: {
          if (state.isPlaying) {
            // console.log('### 播放中');
            eventBus.emit(VIDEO_EVENTS.progress, state);
          } else {
            // console.log('### 暂停');
            if (state.isBuffering) {
              // console.log('### 缓冲');
              eventBus.emit(VIDEO_EVENTS.buffering);
            }
            eventBus.emit(VIDEO_EVENTS.pause, state);
            playStateRef.current = PlayState.paused;
          }
          break;
        }
        case PlayState.paused: {
          if (state.isPlaying) {
            // console.log('### 继续');
            eventBus.emit(VIDEO_EVENTS.resume, state);
            eventBus.emit(VIDEO_EVENTS.progress, state);
            playStateRef.current = PlayState.playing;
          }
          break;
        }
        default: {
          break;
        }
      }
      if (state.didJustFinish) {
        // console.log('### 重播');
        eventBus.emit(VIDEO_EVENTS.ended, state);
        playStateRef.current = PlayState.ended;
      }
    }
  });
  return {
    eventBus,
    onPlaybackStatusUpdate
  };
};
