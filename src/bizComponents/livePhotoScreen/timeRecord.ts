import { BINDWIDTH_TIER } from '@/src/utils/device/network';

/**
 * 指数加权移动平均：S[n] = alpha * a[n] + S[n] * (1 - alpha)
 * @param initialNumber 初始值
 * @param windowSize 移动平均的窗口大小
 * @param alpha 指数加权移动平均因子
 * @returns
 */
export const recorderFactory = (
  initialNumber = 3000,
  windowSize = 5,
  alpha = 0.5
) => {
  const window: number[] = [];
  let ewma = initialNumber;
  return {
    updater: (current: number) => {
      window.push(current);
      if (window.length > windowSize) {
        window.shift();
      }
      ewma = current * alpha + ewma * (1 - alpha);
    },
    getAVG: () => {
      if (window.length === 0) return initialNumber;
      const sum = window.reduce((acc, val) => acc + val, 0);
      return Math.round(sum / window.length);
    },
    getEWMA: () => ewma
  };
};

export const startPlayTimeRecorder = recorderFactory();

export const bindwidthRecorder = recorderFactory(BINDWIDTH_TIER.THIRD_G);
