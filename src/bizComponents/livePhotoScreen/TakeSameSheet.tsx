import { useMemoizedFn } from 'ahooks';
import { router } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { liveClient } from '@/src/api/livephoto';
import { GetImagegenProto } from '@/src/api/makephotov2';
import { UserPageTab } from '@/src/bizComponents/userScreen/constants';
import {
  Icon,
  IconTypes,
  Text,
  hideLoading,
  showLoading,
  showToast
} from '@/src/components';
import { ActionItemConfig, ActionSheet } from '@/src/components/actionSheet';
import CreditWrapper, { CreditTag } from '@/src/components/v2/credit-wrapper';
import { showMessage } from '@/src/components/v2/systemMessage';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useOneRunning } from '@/src/hooks/useOneRunning';
import { useAuthStore } from '@/src/store/authInfo';
import { useLiveStore } from '@/src/store/live';
import { darkColors, rowStyle } from '@/src/theme';
import { Theme } from '@/src/theme/colors/type';
import {
  GameType,
  InvokeType,
  RichCardInfo,
  TabItemType,
  VideoFLowScene
} from '@/src/types';
import { dp2px } from '@/src/utils';
import { CommonEventBus } from '@/src/utils/event';
import { Source, reportClick } from '@/src/utils/report';
import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb';

const GENERATING_DEFAULT_COVER = require('@/assets/image/generating-default-cover.png');

export interface TakeSameSheetProps {
  data?: RichCardInfo;
  scene?: VideoFLowScene;
}

interface ExtendedActionItem extends ActionItemConfig {
  subTitle?: string;
  showCredit?: boolean;
}

export const TakeSameSheet = ({ data, scene }: TakeSameSheetProps) => {
  const cardId = data?.card?.id;

  const [visible, setVisible] = useState(false);
  const { go2Create, go2HomePage } = useChangeRoute();

  useEffect(() => {
    const listener = (arg?: { cardId?: string }) => {
      if (cardId && cardId === arg?.cardId) {
        setVisible(true);
      }
    };
    if (data?.card?.cardExtInfo?.value.case === 'livephoto') {
      CommonEventBus.on('livePhotoTakeSame', listener);
    }
    return () => {
      CommonEventBus.off('livePhotoTakeSame', listener);
    };
  }, [cardId]);
  const onClose = useMemoizedFn(() => {
    setVisible(false);
  });

  const takeSameLive = useOneRunning(
    useCallback(async () => {
      reportClick('join_video', { module: 'videofeed', joinType: 1 });
      showLoading();
      const res = await useLiveStore.getState().checkCanCreate();
      if (res) {
        const res = await liveClient
          .makeCopyLivePhoto({ cardId })
          .catch(() => ({ suc: false, failMsg: '网络错误，请重试～' }));
        hideLoading();
        if (res.suc) {
          onClose?.();
          if (scene === VideoFLowScene.TRENDING) {
            go2HomePage({
              tab: TabItemType.TOPIC,
              appendId: '200',
              appendImageUrl: data?.card?.displayImageUrl ?? ''
            });
          } else {
            CommonEventBus.emit('asyncCardAppended');
            showMessage({
              type: 'button',
              title: '开始生成',
              content: '可在“我的 - 待发布” 查看进度',
              coverImgUrl:
                data?.card?.displayImageUrl ?? GENERATING_DEFAULT_COVER,
              actionText: '查看',
              style: {
                padding: dp2px(5),
                paddingRight: dp2px(16)
              },
              onClick: ({ onClose }) => {
                onClose?.();
                go2HomePage({
                  tab: TabItemType.PROFILE,
                  pageTab: UserPageTab.SECRET,
                  refresh: true
                });
              },
              duration: 4000,
              theme: Theme.LIGHT
            });
          }
        } else {
          showToast(res.failMsg);
        }
      } else {
        hideLoading();
        showToast('已有视频正在生成，请稍后');
      }
    }, [cardId])
  );

  const takeSamePhoto = useOneRunning(
    useCallback(async () => {
      // GetImagegenProto()
      reportClick('join_video', { module: 'videofeed', joinType: 2 });
      const extInfo = data?.card?.cardExtInfo?.value.value as LivePhotoExtInfo;
      console.log(
        '### 同款图片: ',
        extInfo.basePhotoGameType,
        extInfo.templateName,
        extInfo.basePhotoId
      );
      if (extInfo && cardId) {
        go2Create({
          gameType:
            (extInfo.basePhotoGameType as Exclude<
              GameType,
              | GameType.LIVE_PHOTO
              | GameType.INSTANCE
              | GameType.REIMAGINE
              | GameType.OTAKUDANCE
            >) || GameType.DRAWING,
          gameParams: {
            cardId,
            // extra: card?.extra ?? '{}',
            photoId: extInfo.basePhotoId ?? '',
            photoUrl: data?.card?.displayImageUrl
          },
          params: {
            source:
              scene === VideoFLowScene.TRENDING
                ? Source.REC_FLOW
                : Source.VIDEO_FEED,
            fromLiveTemplateId: extInfo.templateId
          }
        });
      } else {
        showToast('小狸走丢了～');
      }
      onClose?.();
    }, [cardId])
  );

  const actionItems: ExtendedActionItem[] = [
    {
      text: '同款视频',
      icon: 'live_take_same' as IconTypes,
      subTitle: '一键生成同款内容的动态LIVE',
      showCredit: true,
      onPress: takeSameLive
    },
    {
      text: '同款图片',
      icon: 'live_take_photo',
      subTitle: '炖同款静态图片',
      onPress: takeSamePhoto
    }
  ];

  // 防止sheet加载
  return visible ? (
    <ActionSheet
      onClose={onClose}
      operations={actionItems}
      menuItemStyle={{ height: 74 }}
      customRender={actionItemRender}
      visible={visible}
    />
  ) : null;
};

const $operationText: TextStyle = {
  fontSize: 15,
  lineHeight: 20,
  fontWeight: '600',
  color: darkColors.white[900]
};

const $subText: TextStyle = {
  fontSize: 12,
  lineHeight: 16,
  color: darkColors.white[400]
};

// 注意这个不是组件
const actionItemRender = (config: ExtendedActionItem) => {
  const { onPress, icon, text, theme, disabled, showCredit, subTitle } = config;

  return (
    <TouchableOpacity
      disabled={disabled}
      style={[
        {
          paddingHorizontal: 16,
          flex: 1,
          alignSelf: 'stretch',
          alignItems: 'center'
        }
      ]}
      onPress={() => onPress(() => void 0)}
    >
      <View
        style={{
          alignSelf: 'stretch',
          flexDirection: 'row',
          flex: 1,
          alignItems: 'center'
        }}
      >
        {typeof icon === 'string' ? (
          <Icon
            disabled={disabled}
            icon={icon as IconTypes}
            style={{
              width: 20,
              height: 20,
              opacity: disabled ? 0.6 : 1
            }}
          />
        ) : (
          icon
        )}
        <View
          style={{
            alignSelf: 'stretch',
            marginLeft: 12,
            justifyContent: 'center',
            gap: 6,
            flex: 1
          }}
        >
          {showCredit ? (
            <CreditWrapper
              gameType={GameType.LIVE_PHOTO}
              invokeType={InvokeType.INVOKE_LIVEPHOTO_VIDEO_MAKECOPY}
              buttonContainer={<Text style={[$operationText]}>{text}</Text>}
              $containerStyle={{
                alignSelf: 'flex-start',
                flexDirection: 'row',
                alignItems: 'center',
                gap: 6
              }}
              $cornerstyle={{ top: 0, position: 'relative', flex: void 0 }}
            />
          ) : (
            <Text style={[$operationText]}>{text}</Text>
          )}
          <Text style={[$subText]}>{subTitle}</Text>
        </View>
        <Icon
          disabled={disabled}
          icon={'thin_arrow_right'}
          size={16}
          containerStyle={{
            width: 16,
            position: 'absolute',
            right: 0
          }}
        />
      </View>
    </TouchableOpacity>
  );
};
