import {
  cancelAnimation,
  runOnJS,
  scrollTo,
  useAnimatedReaction,
  useDerivedValue,
  useSharedValue,
  useWorkletCallback,
  withDecay
} from 'react-native-reanimated';
import {
  ANIMATION_SOURCE,
  ANIMATION_STATE,
  GESTURE_SOURCE,
  SCROLLABLE_STATE,
  SHEET_STATE
} from '../constants';
import type {
  ScrollEventHandlerCallbackType,
  ScrollEventsHandlersHookType
} from '../types';
import { print } from '../utilities/logger';
import { useBottomSheetInternal } from './useBottomSheetInternal';

export type ScrollEventContextType = {
  initialContentOffsetY: number;
  shouldLockInitialPosition: boolean;
};

export const useScrollEventsHandlersDefault: ScrollEventsHandlersHookType = (
  scrollableRef,
  scrollableContentOffsetY,
  scrollViewName
) => {
  // hooks
  const {
    animatedPosition,
    animatedHighestSnapPoint,
    animatedSheetState,
    animatedScrollableState,
    animatedAnimationState,
    animatedScrollableContentOffsetY: rootScrollableContentOffsetY,
    animateToPosition,
    continueScrollCommand
  } = useBottomSheetInternal();

  // 用于驱动滚动的共享值
  const scrollY = useSharedValue(0);
  // 用于跟踪滚动动画状态
  const isAnimatingScroll = useSharedValue(false);

  // 监听并执行滚动命令
  useAnimatedReaction(
    () => continueScrollCommand.value,
    value => {
      'worklet';
      if (value.command === 'decay' && !isAnimatingScroll.value) {
        if (scrollViewName !== value.scrollableName) {
          console.log('scrollViewName', scrollViewName, value.scrollableName);
          return;
        }
        isAnimatingScroll.value = true;

        // 使用 withDecay 实现平滑的衰减滚动
        try {
          // 先取消任何正在进行的动画
          cancelAnimation(scrollY);

          // 设置初始值
          scrollY.value = 1;

          // 使用计算好的目标距离和缩放速度
          const calculatedTargetY = Math.max(0, value.targetOffset ?? 0);

          // 直接使用预计算的缩放速度值 (不再计算)
          const finalVelocity = (value.initialInnerVelocity ?? 0) * -1;

          // 使用动态计算的衰减率，如果没有提供则使用默认值
          const deceleration = value.deceleration ?? 0.9985;

          console.log('continueScrollCommand', {
            targetOffset: value.targetOffset,
            calculatedTargetY,
            finalVelocity,
            deceleration
          });

          scrollY.value = withDecay(
            {
              velocity: finalVelocity, // 使用预计算的速度
              deceleration: deceleration, // 使用动态衰减系数
              clamp: [0, calculatedTargetY] // 使用计算出的目标距离
            },
            isFinished => {
              // 动画完成回调
              if (isFinished) {
                isAnimatingScroll.value = false;
                console.log('[ScrollAnimation] Decay animation finished');
              }
            }
          );
          console.log('scrollY 2', scrollY.value);
        } catch (error) {
          // 出错时回退到简单滚动
          console.error('[ScrollAnimation] Error:', error);
          // @ts-expect-error: scrollableRef类型兼容性问题
          scrollTo(scrollableRef, 0, 1000, true);
          isAnimatingScroll.value = false;
        }

        // 重置命令标志
        continueScrollCommand.value = {
          scrollableName: '',
          command: 'none',
          targetOffset: 0,
          initialInnerVelocity: 0,
          deceleration: 0
        };
      }
    },
    [continueScrollCommand, scrollableRef]
  );

  // 监听scrollY的变化并执行实际滚动
  useDerivedValue(() => {
    'worklet';
    if (scrollableRef) {
      // @ts-expect-error: scrollableRef实际上是AnimatedRef<Scrollable>
      scrollTo(scrollableRef, 0, scrollY.value, false);
    }
  });

  const isTouchMoving = useSharedValue(false);
  const prevScrollOffset = useSharedValue(rootScrollableContentOffsetY.value);

  //#region callbacks
  const handleOnScroll: ScrollEventHandlerCallbackType<ScrollEventContextType> =
    useWorkletCallback(
      (params, context) => {
        const {
          contentOffset: { y },
          velocity
        } = params;

        const diff = prevScrollOffset.value - y;
        prevScrollOffset.value = y;
        /**
         * if sheet position is extended or fill parent, then we reset
         * `shouldLockInitialPosition` value to false.
         */
        if (animatedSheetState.value === SHEET_STATE.FILL_PARENT) {
          context.shouldLockInitialPosition = false;
        }

        runOnJS(print)({
          method: 'handleOnScroll',
          params: {
            lock: animatedScrollableState.value,
            op1:
              animatedPosition.value >= 0 &&
              y <= scrollableContentOffsetY.value,
            op2: animatedScrollableState.value === SCROLLABLE_STATE.LOCKED,
            op3: isTouchMoving.value
          }
        });

        if (
          animatedPosition.value >= 0 &&
          y <= scrollableContentOffsetY.value
        ) {
          // 下拉刷新
          return;
        } else if (animatedScrollableState.value === SCROLLABLE_STATE.LOCKED) {
          runOnJS(print)({
            method: 'handleOnScroll locking',
            params: {
              lock: animatedScrollableState.value
            }
          });
          const lockPosition = context.shouldLockInitialPosition
            ? (context.initialContentOffsetY ?? 0)
            : 0;
          // @ts-ignore
          scrollTo(scrollableRef, 0, lockPosition, false);
          scrollableContentOffsetY.value = lockPosition;
        } else if (
          animatedScrollableState.value === SCROLLABLE_STATE.UNLOCKED &&
          y <= 0 &&
          !isTouchMoving.value &&
          diff > 0
        ) {
          runOnJS(print)({
            method: 'handleOnScroll srolling',
            params: {
              lock: animatedScrollableState.value,
              des: animatedPosition.value
            }
          });
          animateToPosition(
            animatedPosition.value,
            ANIMATION_SOURCE.GESTURE,
            diff * 100,
            {
              type: 'decay'
            },
            3
          );

          const lockPosition = context.shouldLockInitialPosition
            ? (context.initialContentOffsetY ?? 0)
            : 0;
          // @ts-ignore
          scrollTo(scrollableRef, 0, lockPosition, false);
          scrollableContentOffsetY.value = lockPosition;
        }
      },
      [
        scrollableRef,
        scrollableContentOffsetY,
        animatedScrollableState,
        animatedSheetState
      ]
    );
  const handleOnBeginDrag: ScrollEventHandlerCallbackType<ScrollEventContextType> =
    useWorkletCallback(
      ({ contentOffset: { y } }, context) => {
        'worklet';
        // Cancel any ongoing scrollY animation driven by continueScrollCommand
        if (isAnimatingScroll.value) {
          cancelAnimation(scrollY);
          isAnimatingScroll.value = false;
        }
        // Reset the command to ensure no pending command re-triggers
        // It's generally good practice, though the isAnimatingScroll check might often suffice.
        continueScrollCommand.value = {
          scrollableName: '',
          command: 'none',
          targetOffset: 0,
          initialInnerVelocity: 0,
          deceleration: 0
        };

        scrollableContentOffsetY.value = y;
        rootScrollableContentOffsetY.value = y;
        context.initialContentOffsetY = y;
        prevScrollOffset.value = y;
        isTouchMoving.value = true;

        runOnJS(print)({
          method: 'handleOnBeginDrag',
          params: {
            y,
            lock: animatedScrollableState.value
          }
        });
        /**
         * if sheet position not extended or fill parent and the scrollable position
         * not at the top, then we should lock the initial scrollable position.
         */
        if (animatedSheetState.value !== SHEET_STATE.FILL_PARENT && y > 0) {
          context.shouldLockInitialPosition = true;
        } else {
          context.shouldLockInitialPosition = false;
        }
      },
      [
        scrollableContentOffsetY,
        animatedSheetState,
        rootScrollableContentOffsetY,
        scrollY,
        isAnimatingScroll,
        continueScrollCommand
      ]
    );
  const handleOnEndDrag: ScrollEventHandlerCallbackType<ScrollEventContextType> =
    useWorkletCallback(
      ({ contentOffset: { y } }, context) => {
        isTouchMoving.value = false;

        if (
          animatedPosition.value >= 0 &&
          y <= scrollableContentOffsetY.value
        ) {
          return;
        } else if (animatedScrollableState.value === SCROLLABLE_STATE.LOCKED) {
          runOnJS(print)({
            method: 'handleOnEndDrag',
            params: {
              lock: animatedScrollableState.value
            }
          });
          const lockPosition = context.shouldLockInitialPosition
            ? (context.initialContentOffsetY ?? 0)
            : 0;
          // @ts-ignore
          scrollTo(scrollableRef, 0, lockPosition, false);
          scrollableContentOffsetY.value = lockPosition;
          return;
        }
        if (animatedAnimationState.value !== ANIMATION_STATE.RUNNING) {
          scrollableContentOffsetY.value = y;
          rootScrollableContentOffsetY.value = y;
        }
      },
      [
        scrollableRef,
        scrollableContentOffsetY,
        animatedAnimationState,
        animatedScrollableState,
        rootScrollableContentOffsetY
      ]
    );
  const handleOnMomentumEnd: ScrollEventHandlerCallbackType<ScrollEventContextType> =
    useWorkletCallback(
      ({ contentOffset: { y }, velocity }, context) => {
        if (
          animatedPosition.value >= 0 &&
          y <= scrollableContentOffsetY.value
        ) {
          return;
        } else if (animatedScrollableState.value === SCROLLABLE_STATE.LOCKED) {
          runOnJS(print)({
            method: 'handleOnMomentumEnd',
            params: {
              lock: animatedScrollableState.value,
              velocity: velocity?.y
            }
          });
          const lockPosition = context.shouldLockInitialPosition
            ? (context.initialContentOffsetY ?? 0)
            : 0;
          // @ts-ignore
          scrollTo(scrollableRef, 0, lockPosition, false);
          scrollableContentOffsetY.value = 0;
          return;
        }
        if (animatedAnimationState.value !== ANIMATION_STATE.RUNNING) {
          scrollableContentOffsetY.value = y;
          rootScrollableContentOffsetY.value = y;
        }
      },
      [
        scrollableContentOffsetY,
        scrollableRef,
        animatedAnimationState,
        animatedScrollableState,
        rootScrollableContentOffsetY
      ]
    );

  // 点击status bar会触发
  const handleOnScrollToTop = useWorkletCallback(() => {
    runOnJS(print)({
      method: 'handleOnScrollToTop'
    });
    runOnJS(animateToPosition)(0, ANIMATION_SOURCE.USER);
  }, [animatedPosition]);
  //#endregion

  return {
    handleOnScroll,
    handleOnBeginDrag,
    handleOnEndDrag,
    handleOnMomentumEnd,
    handleOnScrollToTop
  };
};
