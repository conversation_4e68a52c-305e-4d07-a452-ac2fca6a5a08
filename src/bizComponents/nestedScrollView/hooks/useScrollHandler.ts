import {
  runOnJS,
  useAnimatedRef,
  useAnimatedScrollHandler,
  useSharedValue
} from 'react-native-reanimated';
import type { Scrollable, ScrollableEvent } from '../types';
import { workletNoop as noop, print } from '../utilities';
import { useScrollEventsHandlersDefault } from './useScrollEventsHandlersDefault';

export const useScrollHandler = (
  useScrollEventsHandlers = useScrollEventsHandlersDefault,
  onScroll?: ScrollableEvent,
  onScrollBeginDrag?: ScrollableEvent,
  onScrollEndDrag?: ScrollableEvent,
  onMomentumEnd?: ScrollableEvent,
  scrollViewName?: string
) => {
  // refs
  const scrollableRef = useAnimatedRef<Scrollable>();

  // variables
  const scrollableContentOffsetY = useSharedValue<number>(0);

  // hooks
  const {
    handleOnScroll = noop,
    handleOnBeginDrag = noop,
    handleOnEndDrag = noop,
    handleOnMomentumEnd = noop,
    handleOnMomentumBegin = noop,
    handleOnScrollToTop = noop
  } = useScrollEventsHandlers(
    scrollableRef,
    scrollableContentOffsetY,
    scrollViewName
  );

  // callbacks
  const scrollHandler = useAnimatedScrollHandler(
    {
      onScroll: (event, context) => {
        handleOnScroll(event, context);

        if (onScroll) {
          runOnJS(onScroll)({ nativeEvent: event });
        }
      },
      onBeginDrag: (event, context) => {
        handleOnBeginDrag(event, context);

        if (onScrollBeginDrag) {
          runOnJS(onScrollBeginDrag)({ nativeEvent: event });
        }
      },
      onEndDrag: (event, context) => {
        handleOnEndDrag(event, context);

        if (onScrollEndDrag) {
          runOnJS(onScrollEndDrag)({ nativeEvent: event });
        }
      },
      onMomentumBegin: handleOnMomentumBegin,
      onMomentumEnd: (event, context) => {
        handleOnMomentumEnd(event, context);

        if (onMomentumEnd) {
          runOnJS(onMomentumEnd)({ nativeEvent: event });
        }
      }
    },
    [
      handleOnScroll,
      handleOnBeginDrag,
      handleOnEndDrag,
      handleOnMomentumBegin,
      handleOnMomentumEnd,
      onScroll,
      onScrollBeginDrag,
      onScrollEndDrag,
      onMomentumEnd
    ]
  );

  return {
    scrollHandler,
    scrollableRef,
    scrollableContentOffsetY,
    onScrollToTop: handleOnScrollToTop
  };
};
