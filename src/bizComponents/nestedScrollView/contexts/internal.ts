import { RefObject, createContext } from 'react';
import type {
  PanGestureHandlerProps,
  State
} from 'react-native-gesture-handler';
import type Animated from 'react-native-reanimated';
import type {
  AnimateToPositionType,
  BottomSheetGestureProps,
  BottomSheetProps
} from '../components/nestedScrollView/types';
import type {
  ANIMATION_STATE,
  SCROLLABLE_DIRECTION,
  SCROLLABLE_STATE,
  SCROLLABLE_TYPE,
  SHEET_STATE
} from '../constants';
import type { Scrollable, ScrollableRef } from '../types';

export interface BottomSheetInternalContextType
  extends Partial<BottomSheetGestureProps>,
    Required<
      Pick<
        BottomSheetProps,
        | 'enableContentPanningGesture'
        | 'enableOverDrag'
        | 'enablePanDownToClose'
        | 'enableDynamicSizing'
        | 'overDragResistanceFactor'
      >
    > {
  // ---- animated states ----
  // 容器滚动动画状态
  animatedAnimationState: Animated.SharedValue<ANIMATION_STATE>;
  // 容器滚动位置状态
  animatedSheetState: Animated.SharedValue<SHEET_STATE>;
  // 滚动子容器是否可滚动
  animatedScrollableState: Animated.SharedValue<SCROLLABLE_STATE>;
  // content 响应手势状态
  animatedContentGestureState: Animated.SharedValue<State>;
  // 手势滑动方向
  animatedContentGestureDirection: Animated.SharedValue<SCROLLABLE_DIRECTION>;

  // ---- animated values ----
  // 容器滚动位置
  animatedPosition: Animated.SharedValue<number>;
  // 容器滚动 snap points index
  animatedIndex: Animated.SharedValue<number>;
  // 容器高度
  animatedContainerHeight: Animated.SharedValue<number>;
  // content 高度
  animatedContentHeight: Animated.SharedValue<number>;
  // 容器滚动阈值
  animatedHighestSnapPoint: Animated.SharedValue<number>;
  // 容器关闭阈值 - to be removed
  animatedClosedPosition: Animated.SharedValue<number>;
  //  - to be removed
  animatedFooterHeight: Animated.SharedValue<number>;
  // 滚动子容器类型
  animatedScrollableType: Animated.SharedValue<SCROLLABLE_TYPE>;
  // 滚动子容器滚动位置
  animatedScrollableContentOffsetY: Animated.SharedValue<number>;
  // 是否可以下拉刷新
  isScrollableRefreshable: Animated.SharedValue<boolean>;
  //  - to be removed
  isContentHeightFixed: Animated.SharedValue<boolean>;
  isInTemporaryPosition: Animated.SharedValue<boolean>;
  continueScrollCommand: Animated.SharedValue<{
    scrollableName: string;
    command: 'none' | 'decay';
    targetOffset?: number;
    initialInnerVelocity?: number;
    deceleration?: number;
  }>;
  // methods
  stopAnimation: () => void;
  animateToPosition: AnimateToPositionType;
  setScrollableRef: (ref: ScrollableRef) => void;
  removeScrollableRef: (
    ref: RefObject<Scrollable>,
    onRemove?: () => void
  ) => void;
}

export const BottomSheetInternalContext =
  createContext<BottomSheetInternalContextType | null>(null);

export const BottomSheetInternalProvider = BottomSheetInternalContext.Provider;
