import React, {
  forwardRef,
  memo,
  useCallback,
  useImperativeHandle,
  useMemo
} from 'react';
import { Platform, View } from 'react-native';
import { State } from 'react-native-gesture-handler';
import Animated, {
  ReduceMotion,
  WithSpringConfig,
  WithTimingConfig,
  cancelAnimation,
  interpolate,
  runOnJS,
  runOn<PERSON>,
  scrollTo,
  useAnimatedReaction,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  useWorkletCallback,
  withDecay
} from 'react-native-reanimated';
// import BottomSheetDebugView from '../bottomSheetDebugView';
import {
  ANIMATION_SOURCE,
  ANIMATION_STATE,
  SCROLLABLE_DIRECTION,
  SCROLLABLE_STATE,
  SHEET_STATE
} from '../../constants';
import {
  BottomSheetInternalProvider,
  BottomSheetProvider
} from '../../contexts/index';
import { useReactiveSharedValue, useScrollable } from '../../hooks';
import type { BottomSheetMethods, Insets } from '../../types';
import { animate, normalizeSnapPoint, print } from '../../utilities';
import BottomSheetContainer from '../bottomSheetContainer';
import BottomSheetDraggableView from '../bottomSheetDraggableView';
import BottomSheetGestureHandlersProvider from '../bottomSheetGestureHandlersProvider';
import { DecayConfig } from 'react-native-reanimated/lib/typescript/animation/decay/utils';
import {
  DEFAULT_ACCESSIBILITY_LABEL,
  DEFAULT_ACCESSIBILITY_ROLE,
  DEFAULT_ACCESSIBLE,
  DEFAULT_ANIMATE_ON_MOUNT,
  DEFAULT_DYNAMIC_SIZING,
  DEFAULT_ENABLE_CONTENT_PANNING_GESTURE,
  DEFAULT_ENABLE_HANDLE_PANNING_GESTURE,
  DEFAULT_ENABLE_OVER_DRAG,
  DEFAULT_ENABLE_PAN_DOWN_TO_CLOSE,
  DEFAULT_OVER_DRAG_RESISTANCE_FACTOR,
  INITIAL_CONTAINER_HEIGHT,
  INITIAL_CONTAINER_OFFSET,
  INITIAL_POSITION,
  INITIAL_SNAP_POINT,
  INITIAL_VALUE
} from './constants';
import { styles } from './styles';
import type { AnimateToPositionType, BottomSheetProps } from './types';

Animated.addWhitelistedUIProps({
  decelerationRate: true
});

// Helper worklet function to calculate inner scroll parameters
const calculateInnerScrollParams = (currentEffectiveVelocity: number) => {
  'worklet';
  const absEffectiveVelocity = Math.abs(currentEffectiveVelocity);
  const velocityFactorForInnerScroll = Math.sqrt(absEffectiveVelocity);

  // 1. Calculate target scroll distance (targetDistance)
  const minDistanceMultiplier = 35;
  const maxDistanceMultiplier = 120;
  const distanceInputMinVf = 10;
  const distanceInputMaxVf = 60;
  let currentDistanceMultiplier;
  if (velocityFactorForInnerScroll <= distanceInputMinVf) {
    currentDistanceMultiplier = minDistanceMultiplier;
  } else if (velocityFactorForInnerScroll >= distanceInputMaxVf) {
    currentDistanceMultiplier = maxDistanceMultiplier;
  } else {
    currentDistanceMultiplier =
      minDistanceMultiplier +
      ((maxDistanceMultiplier - minDistanceMultiplier) *
        (velocityFactorForInnerScroll - distanceInputMinVf)) /
        (distanceInputMaxVf - distanceInputMinVf);
  }
  const baseDistance = 50;
  const maxTargetDistance = 25000;
  const targetDistance = Math.min(
    maxTargetDistance,
    baseDistance + velocityFactorForInnerScroll * currentDistanceMultiplier
  );

  // 2. Calculate scaled velocity (scaledInnerDecayStartVelocity)
  const minSpeedFactor = 0.35;
  const maxSpeedFactor = 0.9;
  const speedInputMinVf = 10;
  const speedInputMaxVf = 60;
  let speedFactor;
  if (velocityFactorForInnerScroll <= speedInputMinVf) {
    speedFactor = minSpeedFactor;
  } else if (velocityFactorForInnerScroll >= speedInputMaxVf) {
    speedFactor = maxSpeedFactor;
  } else {
    speedFactor =
      minSpeedFactor +
      ((maxSpeedFactor - minSpeedFactor) *
        (velocityFactorForInnerScroll - speedInputMinVf)) /
        (speedInputMaxVf - speedInputMinVf);
  }
  const scaledInnerDecayStartVelocity = currentEffectiveVelocity * speedFactor;

  // 3. Calculate dynamic deceleration (decelerationForInnerScroll)
  const minInnerDeceleration = 0.9985;
  const maxInnerDeceleration = 0.9999;
  const decelInputMinVf = 10;
  const decelInputMaxVf = 60;
  let decelerationForInnerScroll;
  if (velocityFactorForInnerScroll <= decelInputMinVf) {
    decelerationForInnerScroll = minInnerDeceleration;
  } else if (velocityFactorForInnerScroll >= decelInputMaxVf) {
    decelerationForInnerScroll = maxInnerDeceleration;
  } else {
    decelerationForInnerScroll =
      minInnerDeceleration +
      ((maxInnerDeceleration - minInnerDeceleration) *
        (velocityFactorForInnerScroll - decelInputMinVf)) /
        (decelInputMaxVf - decelInputMinVf);
  }

  return {
    targetDistance,
    scaledInnerDecayStartVelocity,
    decelerationForInnerScroll
  };
};

type BottomSheet = BottomSheetMethods;

const BottomSheetComponent = forwardRef<BottomSheet, BottomSheetProps>(
  function BottomSheet(props, ref) {
    //#region extract props
    const {
      // animations configurations
      animationConfigs,
      // configurations
      enableContentPanningGesture = DEFAULT_ENABLE_CONTENT_PANNING_GESTURE,
      enableOverDrag = DEFAULT_ENABLE_OVER_DRAG,
      enablePanDownToClose = DEFAULT_ENABLE_PAN_DOWN_TO_CLOSE,
      enableDynamicSizing = DEFAULT_DYNAMIC_SIZING,
      overDragResistanceFactor = DEFAULT_OVER_DRAG_RESISTANCE_FACTOR,

      // styles
      style: _providedStyle,
      containerStyle: _providedContainerStyle,
      backgroundStyle: _providedBackgroundStyle,
      handleStyle: _providedHandleStyle,
      handleIndicatorStyle: _providedHandleIndicatorStyle,

      // hooks
      gestureEventsHandlersHook,

      // layout
      handleHeight: _providedHandleHeight,
      containerHeight: _providedContainerHeight,
      contentHeight: _providedContentHeight,
      containerOffset: _providedContainerOffset,
      topInset = 0,
      bottomInset = 0,
      topPreserveInset = 0,

      // animated callback shared values
      animatedPosition: _providedAnimatedPosition,
      animatedIndex: _providedAnimatedIndex,
      hasSlideUp: _providedHasSlideUp,

      // gestures
      simultaneousHandlers: _providedSimultaneousHandlers,
      requireExternalGestureToFail: _providedRequireExternalGestureToFail,
      simultaneousWithExternalGesture: _providedSimultaneousWithExternalGesture,
      activeOffsetX: _providedActiveOffsetX,
      activeOffsetY: _providedActiveOffsetY,
      failOffsetX: _providedFailOffsetX,
      failOffsetY: _providedFailOffsetY,

      // callbacks
      onChange: _providedOnChange,
      onClose: _providedOnClose,
      onAnimate: _providedOnAnimate,

      // private
      $modal = false,
      detached = false,

      // components
      children: Content,

      // accessibility
      accessible: _providedAccessible = DEFAULT_ACCESSIBLE,
      accessibilityLabel:
        _providedAccessibilityLabel = DEFAULT_ACCESSIBILITY_LABEL,
      accessibilityRole:
        _providedAccessibilityRole = DEFAULT_ACCESSIBILITY_ROLE,
      scrollableNameSV
    } = props;
    //#endregion

    //#region animations configurations
    const _providedAnimationConfigs = useMemo(() => {
      if (!animationConfigs) {
        return undefined;
      }

      if (ReduceMotion) {
        animationConfigs.reduceMotion = ReduceMotion.Never;
      }

      return animationConfigs;
    }, [animationConfigs]);
    //#endregion

    //#region layout variables
    /**
     * This variable is consider an internal variable,
     * that will be used conditionally in `animatedContainerHeight`
     */
    const _animatedContainerHeight = useReactiveSharedValue(
      _providedContainerHeight ?? INITIAL_CONTAINER_HEIGHT
    );
    /**
     * This is a conditional variable, where if the `BottomSheet` is used
     * in a modal, then it will subset vertical insets (top+bottom) from
     * provided container height.
     */
    const animatedContainerHeight = useDerivedValue(() => {
      const verticalInset = topInset + bottomInset;
      return $modal
        ? _animatedContainerHeight.value - verticalInset
        : _animatedContainerHeight.value;
    }, [$modal, topInset, bottomInset]);
    const animatedContainerOffset = useReactiveSharedValue(
      _providedContainerOffset ?? INITIAL_CONTAINER_OFFSET
    ) as Animated.SharedValue<Insets>;
    const animatedFooterHeight = useSharedValue(0);
    const animatedContentHeight = useSharedValue(INITIAL_CONTAINER_HEIGHT);
    const animatedHighestSnapPointReady = useSharedValue(false);
    const animatedHighestSnapPoint = useSharedValue(0);
    const animatedClosedPosition = useDerivedValue(() => {
      let closedPosition = animatedContainerHeight.value;

      if ($modal || detached) {
        closedPosition = animatedContainerHeight.value + bottomInset;
      }

      return closedPosition;
    }, [$modal, detached, bottomInset]);
    const animatedSheetHeight = useDerivedValue(() => {
      return animatedContainerHeight.value - animatedHighestSnapPoint.value;
    });
    const animatedCurrentIndex = useReactiveSharedValue(0);
    const animatedPosition = useSharedValue(INITIAL_POSITION);
    const animatedNextPosition = useSharedValue(INITIAL_VALUE);
    const animatedNextPositionIndex = useSharedValue(0);

    // conditional
    const isAnimatedOnMount = useSharedValue(false);
    const isContentHeightFixed = useSharedValue(false);
    const isLayoutCalculated = useDerivedValue(() => {
      let isContainerHeightCalculated = false;
      //container height was provided.
      if (
        _providedContainerHeight !== null ||
        _providedContainerHeight !== undefined
      ) {
        isContainerHeightCalculated = true;
      }
      // container height did set.
      if (animatedContainerHeight.value !== INITIAL_CONTAINER_HEIGHT) {
        isContainerHeightCalculated = true;
      }

      return isContainerHeightCalculated && animatedHighestSnapPointReady.value;
    });
    const isInTemporaryPosition = useSharedValue(false);
    const isForcedClosing = useSharedValue(false);

    const continueScrollCommand = useSharedValue<{
      command: 'none' | 'decay';
      targetOffset?: number;
      initialInnerVelocity?: number;
      deceleration?: number;
      scrollableName: string;
    }>({ command: 'none', scrollableName: '' });

    // gesture
    const animatedContentGestureState = useSharedValue<State>(
      State.UNDETERMINED
    );
    const animatedContentGestureDirection =
      useSharedValue<SCROLLABLE_DIRECTION>(SCROLLABLE_DIRECTION.UNKNOWN);
    //#endregion

    //#region hooks variables
    // scrollable variables
    const {
      animatedScrollableType,
      animatedScrollableContentOffsetY,
      isScrollableRefreshable,
      scrollableRef,
      setScrollableRef,
      removeScrollableRef
    } = useScrollable();
    //#endregion

    //#region state/dynamic variables
    // states
    const animatedAnimationState = useSharedValue(ANIMATION_STATE.UNDETERMINED);
    const animatedAnimationSource = useSharedValue<ANIMATION_SOURCE>(
      ANIMATION_SOURCE.MOUNT
    );
    const animatedSheetState = useDerivedValue<SHEET_STATE>(() => {
      // fill parent = 0
      if (animatedPosition.value <= animatedHighestSnapPoint.value) {
        return SHEET_STATE.FILL_PARENT;
      }

      return SHEET_STATE.OPENED;
    }, [
      animatedClosedPosition,
      animatedPosition,
      animatedSheetHeight,
      isInTemporaryPosition
    ]);

    const animatedScrollableState = useDerivedValue<SCROLLABLE_STATE>(() => {
      /**
       * if sheet state is fill parent, then unlock scrolling
       */
      if (animatedSheetState.value === SHEET_STATE.FILL_PARENT) {
        return SCROLLABLE_STATE.UNLOCKED;
      }

      return SCROLLABLE_STATE.LOCKED;
    });

    const animatedIndex = useDerivedValue(() => {
      return 0;
    }, []);
    //#endregion

    //#region private methods
    const handleOnChange = useCallback(
      function handleOnChange(index: number) {
        print({
          component: BottomSheet.name,
          method: handleOnChange.name,
          params: {
            index,
            animatedCurrentIndex: animatedCurrentIndex.value
          }
        });

        if (_providedOnChange) {
          _providedOnChange(index);
        }
      },
      [_providedOnChange, animatedCurrentIndex]
    );
    const handleOnAnimate = useCallback(
      function handleOnAnimate(toPoint: number) {
        print({
          component: BottomSheet.name,
          method: handleOnAnimate.name,
          params: {}
        });

        if (!_providedOnAnimate) {
          return;
        }

        // if (toIndex !== animatedCurrentIndex.value) {
        //   _providedOnAnimate(animatedCurrentIndex.value, 0);
        // }
      },
      [_providedOnAnimate, animatedCurrentIndex]
    );
    //#endregion

    //#region animation
    const stopAnimation = useWorkletCallback(() => {
      cancelAnimation(animatedPosition);
      isForcedClosing.value = false;
      animatedAnimationSource.value = ANIMATION_SOURCE.NONE;
      animatedAnimationState.value = ANIMATION_STATE.STOPPED;
    }, [animatedPosition, animatedAnimationState, animatedAnimationSource]);
    const animateToPositionCompleted = useWorkletCallback(
      function animateToPositionCompleted(isFinished?: boolean) {
        isForcedClosing.value = false;

        if (!isFinished) {
          return;
        }
        runOnJS(print)({
          component: BottomSheet.name,
          method: animateToPositionCompleted.name,
          params: {
            animatedCurrentIndex: animatedCurrentIndex.value,
            animatedNextPosition: animatedNextPosition.value,
            animatedNextPositionIndex: animatedNextPositionIndex.value
          }
        });

        animatedAnimationSource.value = ANIMATION_SOURCE.NONE;
        animatedAnimationState.value = ANIMATION_STATE.STOPPED;
        animatedNextPosition.value = INITIAL_VALUE;
        animatedNextPositionIndex.value = INITIAL_VALUE;
      }
    );
    const animateToPosition: AnimateToPositionType = useWorkletCallback(
      function animateToPosition(
        targetPosition: number,
        source: ANIMATION_SOURCE,
        velocity: number = 0,
        configs?: WithTimingConfig | WithSpringConfig | { type: 'decay' },
        tag?: number
      ) {
        const position = Math.min(
          Math.max(targetPosition, animatedHighestSnapPoint.value),
          0
        );
        const needMoreScroll =
          animatedHighestSnapPoint.value > targetPosition + velocity * 80;

        const afterAnimation = needMoreScroll
          ? () => {
              runOnJS(print)({
                component: BottomSheet.name,
                method: 'afterAnimation',
                params: {
                  // 这里打印会导致 scrollableRef.current 为 undefined
                  // go: Boolean(scrollableRef.current),
                  go2: animatedPosition.value === animatedHighestSnapPoint.value
                }
              });
              if (animatedPosition.value === animatedHighestSnapPoint.value) {
                if (
                  decayJsStartTime !== undefined &&
                  initialVelForDecay !== undefined &&
                  decelerationForImpactCalc !== undefined
                ) {
                  const impactTimeJS = Date.now();
                  const durationSeconds =
                    (impactTimeJS - decayJsStartTime) / 1000.0;
                  const EMPIRICAL_SLOPE_FACTOR = 1500;
                  const actualImpactVelocity =
                    initialVelForDecay *
                    Math.exp(
                      -(1.0 - decelerationForImpactCalc) *
                        EMPIRICAL_SLOPE_FACTOR *
                        durationSeconds
                    );
                  const innerParams =
                    calculateInnerScrollParams(actualImpactVelocity);
                  if (Math.abs(innerParams.scaledInnerDecayStartVelocity) > 1) {
                    continueScrollCommand.value = {
                      scrollableName: scrollableNameSV?.value || '',
                      command: 'decay',
                      targetOffset: Math.max(0, innerParams.targetDistance),
                      initialInnerVelocity:
                        innerParams.scaledInnerDecayStartVelocity,
                      deceleration: innerParams.decelerationForInnerScroll
                    };
                  } else {
                    continueScrollCommand.value = {
                      command: 'none',
                      scrollableName: scrollableNameSV?.value || ''
                    };
                  }
                } else {
                  // This else branch within decay's onComplete might be hit if decayJsStartTime etc. were somehow undefined,
                  // though they are set right before withDecay. Treat as fallback.
                  const innerParams = calculateInnerScrollParams(velocity); // velocity is original gesture velocity
                  if (Math.abs(innerParams.scaledInnerDecayStartVelocity) > 1) {
                    continueScrollCommand.value = {
                      scrollableName: scrollableNameSV?.value || '',
                      command: 'decay',
                      targetOffset: Math.max(0, innerParams.targetDistance),
                      initialInnerVelocity:
                        innerParams.scaledInnerDecayStartVelocity,
                      deceleration: innerParams.decelerationForInnerScroll
                    };
                  }
                }
              }
            }
          : undefined;

        // Variables for decay animation, defined here to be captured by onComplete closure
        let decayJsStartTime: number | undefined;
        let initialVelForDecay: number | undefined;
        let decelerationForImpactCalc: number | undefined;

        if (
          (position === animatedPosition.value &&
            !(configs && 'type' in configs)) ||
          position === undefined ||
          (animatedAnimationState.value === ANIMATION_STATE.RUNNING &&
            position === animatedNextPosition.value)
        ) {
          return;
        }

        runOnJS(print)({
          component: BottomSheet.name,
          method: animateToPosition.name,
          params: {
            needMoreScroll,
            tag
          }
        });

        stopAnimation();

        /**
         * set animation state to running, and source
         */
        animatedAnimationState.value = ANIMATION_STATE.RUNNING;
        animatedAnimationSource.value = source;

        /**
         * store next position
         */
        animatedNextPosition.value = position;

        /**
         * fire `onAnimate` callback
         */
        runOnJS(handleOnAnimate)(position);

        /**
         * force animation configs from parameters, if provided
         */
        if (configs !== undefined) {
          if ('type' in configs) {
            console.log('======,', {
              velocity,
              configs
            });
            decayJsStartTime = Date.now(); // Record start time for decay
            initialVelForDecay = velocity; // Record initial velocity for decay
            decelerationForImpactCalc =
              (configs as DecayConfig).deceleration ?? 0.998; // Record decel for decay

            animatedPosition.value = withDecay(
              {
                velocity,
                clamp: [animatedHighestSnapPoint.value, 0]
              },
              () => {
                animateToPositionCompleted();
                afterAnimation?.();
              }
            );
          } else {
            if (ReduceMotion) {
              configs.reduceMotion = ReduceMotion.Never;
            }
            animatedPosition.value = animate({
              point: position,
              configs,
              velocity,
              onComplete: animateToPositionCompleted
            });
          }
        } else {
          /**
           * use animationConfigs callback, if provided
           */
          animatedPosition.value = animate({
            point: position,
            velocity,
            configs: _providedAnimationConfigs,
            onComplete: animateToPositionCompleted
          });
        }
      },
      [
        handleOnAnimate,
        _providedAnimationConfigs,
        scrollableRef,
        animatedHighestSnapPoint,
        animatedPosition
      ]
    );
    //#endregion

    //#region public methods
    const handleSnapToPosition = useWorkletCallback(
      function handleSnapToPosition(
        position: number | string,
        animationConfigs?: WithSpringConfig | WithTimingConfig
      ) {
        print({
          component: BottomSheet.name,
          method: handleSnapToPosition.name,
          params: {
            position
          }
        });

        /**
         * normalized provided position.
         */
        const nextPosition = normalizeSnapPoint(
          position,
          animatedContainerHeight.value
        );

        /**
         * exit method if :
         * - layout is not calculated.
         * - already animating to next position.
         * - sheet is forced closing.
         */
        if (
          !isLayoutCalculated ||
          nextPosition === animatedNextPosition.value ||
          isForcedClosing.value
        ) {
          return;
        }

        /**
         * mark the new position as temporary.
         */
        isInTemporaryPosition.value = false;

        runOnUI(animateToPosition)(
          nextPosition,
          ANIMATION_SOURCE.USER,
          0,
          animationConfigs,
          7
        );
      },
      [
        animateToPosition,
        bottomInset,
        topInset,
        isLayoutCalculated,
        isForcedClosing,
        animatedContainerHeight,
        animatedPosition
      ]
    );

    const handleSnapToTop = useCallback(
      function handleSnapToTop(
        animationConfigs?: WithSpringConfig | WithTimingConfig
      ) {
        const nextPosition = animatedHighestSnapPoint.value;

        /**
         * exit method if :
         * - layout is not calculated.
         * - already animating to next position.
         * - sheet is forced closing.
         */
        if (
          !isLayoutCalculated.value ||
          nextPosition === animatedNextPosition.value ||
          isForcedClosing.value
        ) {
          return;
        }

        /**
         * reset temporary position variable.
         */
        isInTemporaryPosition.value = false;

        runOnUI(animateToPosition)(
          nextPosition,
          ANIMATION_SOURCE.USER,
          0,
          animationConfigs,
          6
        );
      },
      [
        animateToPosition,
        isForcedClosing,
        isLayoutCalculated,
        isInTemporaryPosition,
        animatedNextPosition,
        animatedClosedPosition
      ]
    );

    const handleSnapToInitial = useCallback(
      function handleSnapToInitial(
        animationConfigs?: WithSpringConfig | WithTimingConfig
      ) {
        const nextPosition = 0;

        /**
         * exit method if :
         * - layout is not calculated.
         * - already animating to next position.
         * - sheet is forced closing.
         */
        if (
          !isLayoutCalculated.value ||
          nextPosition === animatedNextPosition.value ||
          isForcedClosing.value
        ) {
          return;
        }

        /**
         * reset temporary position variable.
         */
        isInTemporaryPosition.value = false;

        runOnUI(animateToPosition)(
          nextPosition,
          ANIMATION_SOURCE.USER,
          0,
          animationConfigs,
          5
        );
      },
      [
        animateToPosition,
        isForcedClosing,
        isLayoutCalculated,
        isInTemporaryPosition,
        animatedNextPosition,
        animatedClosedPosition
      ]
    );

    useImperativeHandle(ref, () => ({
      snapToPosition: handleSnapToPosition,
      snapToTop: handleSnapToTop,
      snapToInitial: handleSnapToInitial
    }));
    //#endregion

    //#region contexts variables
    const internalContextVariables = useMemo(
      () => ({
        enableContentPanningGesture,
        enableDynamicSizing,
        overDragResistanceFactor,
        enableOverDrag,
        enablePanDownToClose,
        animatedAnimationState,
        animatedSheetState,
        animatedScrollableState,
        animatedContentGestureState,
        animatedContentGestureDirection,
        animatedScrollableType,
        animatedIndex,
        animatedPosition,
        animatedContentHeight,
        animatedClosedPosition,
        animatedFooterHeight,
        animatedContainerHeight,
        animatedHighestSnapPoint,
        animatedScrollableContentOffsetY,
        isInTemporaryPosition,
        isContentHeightFixed,
        isScrollableRefreshable,
        simultaneousHandlers: _providedSimultaneousHandlers,
        requireExternalGestureToFail: _providedRequireExternalGestureToFail,
        simultaneousWithExternalGesture:
          _providedSimultaneousWithExternalGesture,
        activeOffsetX: _providedActiveOffsetX,
        activeOffsetY: _providedActiveOffsetY,
        failOffsetX: _providedFailOffsetX,
        failOffsetY: _providedFailOffsetY,
        animateToPosition,
        stopAnimation,
        setScrollableRef,
        removeScrollableRef,
        continueScrollCommand
      }),
      [
        animatedIndex,
        animatedPosition,
        animatedContentHeight,
        animatedScrollableType,
        animatedContentGestureState,
        animatedContentGestureDirection,
        animatedClosedPosition,
        animatedFooterHeight,
        animatedContainerHeight,
        animatedAnimationState,
        animatedSheetState,
        animatedHighestSnapPoint,
        animatedScrollableState,
        animatedScrollableContentOffsetY,
        isScrollableRefreshable,
        isContentHeightFixed,
        isInTemporaryPosition,
        enableContentPanningGesture,
        overDragResistanceFactor,
        enableOverDrag,
        enablePanDownToClose,
        enableDynamicSizing,
        _providedSimultaneousHandlers,
        _providedRequireExternalGestureToFail,
        _providedSimultaneousWithExternalGesture,
        _providedActiveOffsetX,
        _providedActiveOffsetY,
        _providedFailOffsetX,
        _providedFailOffsetY,
        setScrollableRef,
        removeScrollableRef,
        animateToPosition,
        stopAnimation,
        continueScrollCommand
      ]
    );
    const externalContextVariables = useMemo(
      () => ({
        animatedIndex,
        animatedPosition,
        snapToPosition: handleSnapToPosition,
        snapToTop: handleSnapToTop,
        snapToInitial: handleSnapToInitial
      }),
      [
        animatedIndex,
        animatedPosition,
        handleSnapToPosition,
        handleSnapToTop,
        handleSnapToInitial
      ]
    );
    //#endregion

    //#region styles
    const containerAnimatedStyle = useAnimatedStyle(() => {
      // runOnJS(print)({
      //   method: 'containerAnimatedStyle',
      //   params: {
      //     animatedPosition: animatedPosition.value
      //   }
      // });
      return {
        // opacity:
        // Platform.OS === 'android' && animatedIndex.value === -1 ? 0 : 1,
        transform: [
          {
            translateY: animatedPosition.value
          }
        ]
      };
    }, [animatedPosition, animatedIndex]);
    // const containerStyle = useMemo(
    //   () => [_providedStyle, styles.container, containerAnimatedStyle],
    //   [_providedStyle, containerAnimatedStyle]
    // );
    // const containerStyle = useAnimatedStyle(() => {
    //   return {
    //     ..._providedStyle,
    //     ...styles.container,
    //     ...containerAnimatedStyle
    //   };
    // });
    const contentContainerAnimatedStyle = useAnimatedStyle(() => {
      /**
       * if content height was provided, then we skip setting
       * calculated height.
       */
      // if (_providedContentHeight) {
      //   return {};
      // }

      return {
        height: animatedSheetHeight.value
      };
    }, [animatedSheetHeight]);
    const contentContainerStyle = useMemo(
      () => [styles.contentContainer, contentContainerAnimatedStyle],
      [contentContainerAnimatedStyle]
    );
    /**
     * added safe area to prevent the sheet from floating above
     * the bottom of the screen, when sheet being over dragged or
     * when the sheet is resized.
     */
    const contentMaskContainerAnimatedStyle = useAnimatedStyle(() => {
      if (detached) {
        return {
          overflow: 'visible'
        };
      }
      return {
        paddingBottom: animatedContainerHeight.value
      };
    }, [detached]);
    const contentMaskContainerStyle = useMemo(
      () => [styles.contentMaskContainer, contentMaskContainerAnimatedStyle],
      [contentMaskContainerAnimatedStyle]
    );
    //#endregion

    //#region effects
    /**
     * React to `isLayoutCalculated` change, to insure that the sheet will
     * appears/mounts only when all layout is been calculated.
     *
     * @alias OnMount
     */
    useAnimatedReaction(
      () => isLayoutCalculated.value,
      _isLayoutCalculated => {
        /**
         * exit method if:
         * - layout is not calculated yet.
         * - already did animate on mount.
         */
        if (!_isLayoutCalculated || isAnimatedOnMount.value) {
          return;
        }

        let nextPosition = 0;

        runOnJS(print)({
          component: BottomSheet.name,
          method: 'useAnimatedReaction::OnMount',
          params: {
            isLayoutCalculated: _isLayoutCalculated,
            nextPosition
          }
        });

        /**
         * here we exit method early because the next position
         * is out of the screen, this happens when `snapPoints`
         * still being calculated.
         */
        if (
          nextPosition === INITIAL_POSITION ||
          nextPosition === animatedClosedPosition.value
        ) {
          isAnimatedOnMount.value = true;
          animatedCurrentIndex.value = 0;
          return;
        }

        animatedPosition.value = nextPosition;
        isAnimatedOnMount.value = true;
      },
      []
    );

    /**
     * sets provided animated position
     */
    useAnimatedReaction(
      () => ({
        _animatedPosition: animatedPosition.value,
        _animatedHighestSnapPoint: animatedHighestSnapPoint.value,
        _isLayoutCalculated: isLayoutCalculated.value
      }),
      ({
        _animatedPosition,
        _animatedHighestSnapPoint,
        _isLayoutCalculated
      }) => {
        if (_providedAnimatedPosition) {
          _providedAnimatedPosition.value = _animatedPosition + topInset;
        }
        if (_providedHasSlideUp) {
          _providedHasSlideUp.value =
            _isLayoutCalculated &&
            _animatedPosition - _animatedHighestSnapPoint <= 10;
        }
      }
    );

    /**
     * sets provided animated index
     */
    useAnimatedReaction(
      () => animatedIndex.value,
      _animatedIndex => {
        if (_providedAnimatedIndex) {
          _providedAnimatedIndex.value = _animatedIndex;
        }
      }
    );

    /**
     * React to internal variables to detect change in snap position.
     *
     * @alias OnChange
     */
    useAnimatedReaction(
      () => ({
        _animatedIndex: animatedIndex.value,
        _animatedPosition: animatedPosition.value,
        _animationState: animatedAnimationState.value,
        _contentGestureState: animatedContentGestureState.value
      }),
      ({ _animatedIndex, _animationState, _contentGestureState }) => {
        /**
         * exit the method if animation state is not stopped.
         */
        if (_animationState !== ANIMATION_STATE.STOPPED) {
          return;
        }

        /**
         * exit the method if animated index value
         * has fraction, e.g. 1.99, 0.52
         */
        if (_animatedIndex % 1 !== 0) {
          return;
        }

        /**
         * exit the method if there any active gesture.
         */
        const hasNoActiveGesture =
          _contentGestureState === State.END ||
          _contentGestureState === State.UNDETERMINED ||
          _contentGestureState === State.CANCELLED;
        if (!hasNoActiveGesture) {
          return;
        }

        /**
         * if the index is not equal to the current index,
         * than the sheet position had changed and we trigger
         * the `onChange` callback.
         */
        if (_animatedIndex !== animatedCurrentIndex.value) {
          runOnJS(print)({
            component: BottomSheet.name,
            method: 'useAnimatedReaction::OnChange',
            params: {
              animatedCurrentIndex: animatedCurrentIndex.value,
              animatedIndex: _animatedIndex
            }
          });

          // animatedCurrentIndex.value = _animatedIndex;
          runOnJS(handleOnChange)(_animatedIndex);
        }

        /**
         * if index is `-1` than we fire the `onClose` callback.
         */
        if (_animatedIndex === -1 && _providedOnClose) {
          runOnJS(print)({
            component: BottomSheet.name,
            method: 'useAnimatedReaction::onClose',
            params: {
              animatedCurrentIndex: animatedCurrentIndex.value,
              animatedIndex: _animatedIndex
            }
          });
          runOnJS(_providedOnClose)();
        }
      },
      [handleOnChange, _providedOnClose]
    );

    return (
      <BottomSheetProvider value={externalContextVariables}>
        {/* @ts-expect-error simulataneous handlers */}
        <BottomSheetInternalProvider value={internalContextVariables}>
          <BottomSheetGestureHandlersProvider
            gestureEventsHandlersHook={gestureEventsHandlersHook}
          >
            <BottomSheetContainer
              key="BottomSheetContainer"
              shouldCalculateHeight={!$modal}
              containerHeight={_animatedContainerHeight}
              containerOffset={animatedContainerOffset}
              topInset={topInset}
              bottomInset={bottomInset}
              detached={detached}
              style={_providedContainerStyle}
            >
              <Animated.View
                style={[
                  _providedStyle,
                  styles.container,
                  containerAnimatedStyle
                ]}
              >
                <BottomSheetDraggableView
                  key="BottomSheetRootDraggableView"
                  style={contentContainerStyle}
                >
                  {props.headerComponent ? (
                    <View
                      style={{
                        zIndex: 99
                      }}
                      onLayout={e => {
                        animatedHighestSnapPoint.value =
                          -1 * e.nativeEvent.layout.height + topPreserveInset;
                        animatedHighestSnapPointReady.value = true;
                      }}
                    >
                      {props.headerComponent}
                    </View>
                  ) : null}

                  {typeof Content === 'function' ? <Content /> : Content}
                </BottomSheetDraggableView>
              </Animated.View>
            </BottomSheetContainer>
          </BottomSheetGestureHandlersProvider>
        </BottomSheetInternalProvider>
      </BottomSheetProvider>
    );
  }
);

const BottomSheet = memo(BottomSheetComponent);
BottomSheet.displayName = 'BottomSheet';

export default BottomSheet;
