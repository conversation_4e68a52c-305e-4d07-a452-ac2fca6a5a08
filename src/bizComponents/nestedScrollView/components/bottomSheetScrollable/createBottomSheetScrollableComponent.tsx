import React, {
  forwardRef,
  useContext,
  useImperativeHandle,
  useMemo
} from 'react';
import { Gesture } from 'react-native-gesture-handler';
import { useAnimatedProps, useAnimatedStyle } from 'react-native-reanimated';
import {
  SCROLLABLE_DECELERATION_RATE_MAPPER,
  SCROLLABLE_STATE,
  type SCROLLABLE_TYPE
} from '../../constants';
import { BottomSheetDraggableContext } from '../../contexts/gesture';
import {
  useBottomSheetInternal,
  useScrollHandler,
  useScrollableSetter,
  useStableCallback
} from '../../hooks';
import { ScrollableContainer } from './ScrollableContainer';

export function createBottomSheetScrollableComponent<T, P>(
  type: SCROLLABLE_TYPE,
  // biome-ignore lint: to be addressed!
  ScrollableComponent: any
) {
  return forwardRef<T, P>((props, ref) => {
    // props
    const {
      // hooks
      focusHook,
      scrollEventsHandlersHook,
      // props
      enableFooterMarginAdjustment = false,
      overScrollMode = 'never',
      keyboardDismissMode = 'interactive',
      showsVerticalScrollIndicator = true,
      isActive = true,
      style,
      refreshing,
      onRefresh,
      progressViewOffset,
      refreshControl,
      // events
      onScroll,
      onScrollBeginDrag,
      onScrollEndDrag,
      onContentSizeChange,
      scrollViewName,
      ...rest
      // biome-ignore lint: to be addressed!
    }: any = props;

    //#region hooks
    const draggableGesture = useContext(BottomSheetDraggableContext);
    const {
      scrollableRef,
      scrollableContentOffsetY,
      scrollHandler,
      onScrollToTop
    } = useScrollHandler(
      scrollEventsHandlersHook,
      onScroll,
      onScrollBeginDrag,
      onScrollEndDrag,
      undefined,
      scrollViewName
    );
    const {
      animatedFooterHeight,
      animatedContentHeight,
      animatedScrollableState,
      enableDynamicSizing
    } = useBottomSheetInternal();
    //#endregion

    if (!draggableGesture) {
      throw "'Scrollable' cannot be used out of the BottomSheet!";
    }

    //#region variables
    const scrollableAnimatedProps = useAnimatedProps(
      () => ({
        decelerationRate:
          SCROLLABLE_DECELERATION_RATE_MAPPER[animatedScrollableState.value],
        showsVerticalScrollIndicator: showsVerticalScrollIndicator
          ? animatedScrollableState.value === SCROLLABLE_STATE.UNLOCKED
          : showsVerticalScrollIndicator
      }),
      [animatedScrollableState, showsVerticalScrollIndicator]
    );

    const nativeGesture = useMemo(
      () =>
        Gesture.Native()
          // @ts-ignore
          .simultaneousWithExternalGesture(draggableGesture)
          .shouldCancelWhenOutside(false),
      [draggableGesture]
    );
    //#endregion

    //#region callbacks
    const handleContentSizeChange = useStableCallback(
      (contentWidth: number, contentHeight: number) => {
        if (enableDynamicSizing) {
          animatedContentHeight.value =
            contentHeight +
            (enableFooterMarginAdjustment ? animatedFooterHeight.value : 0);
        }

        if (onContentSizeChange) {
          onContentSizeChange(contentWidth, contentHeight);
        }
      }
    );
    //#endregion

    //#region styles
    const containerAnimatedStyle = useAnimatedStyle(
      () => ({
        marginBottom: enableFooterMarginAdjustment
          ? animatedFooterHeight.value
          : 0
      }),
      [animatedFooterHeight.value, enableFooterMarginAdjustment]
    );
    const containerStyle = useMemo(() => {
      return enableFooterMarginAdjustment
        ? [
            ...(style ? ('length' in style ? style : [style]) : []),
            containerAnimatedStyle
          ]
        : style;
    }, [enableFooterMarginAdjustment, style, containerAnimatedStyle]);
    //#endregion

    //#region effects
    // @ts-ignore
    useImperativeHandle(ref, () => scrollableRef.current);
    useScrollableSetter(
      scrollableRef,
      type,
      scrollableContentOffsetY,
      onRefresh !== undefined,
      isActive
    );
    //#endregion

    //#region render
    return (
      <ScrollableContainer
        ref={scrollableRef}
        nativeGesture={nativeGesture}
        animatedProps={scrollableAnimatedProps}
        overScrollMode={overScrollMode}
        keyboardDismissMode={keyboardDismissMode}
        refreshing={refreshing}
        scrollEventThrottle={16}
        progressViewOffset={progressViewOffset}
        style={containerStyle}
        onRefresh={onRefresh}
        onScroll={scrollHandler}
        onScrollToTop={onScrollToTop}
        onContentSizeChange={handleContentSizeChange}
        ScrollableComponent={ScrollableComponent}
        refreshControl={refreshControl}
        {...rest}
      />
    );
    //#endregion
  });
}
