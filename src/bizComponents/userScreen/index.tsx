import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { router, useFocusEffect, useRootNavigationState } from 'expo-router';
import React, {
  memo,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import { InteractionManager, StyleProp, TextStyle, View } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import PagerView from 'react-native-pager-view';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withSpring
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getGoodsList, visitHomePage } from '@/src/api/goods';
import { usePullDownGestureHandlersFactory } from '@/src/bizComponents/nestedScrollView/hooks/usePullDownGestureHandlers';
import { GoodsWallBg } from '@/src/bizComponents/userScreen/components/GoodsWallBg';
import { Icon, Screen } from '@/src/components';
import { AlbumSheet } from '@/src/components/album';
import { AlbumFromType } from '@/src/components/album/const';
import { PagePerformance } from '@/src/components/common/pagePerformance';
import { LOGIN_SCENE } from '@/src/constants';
import { useAuthState, usePersistFn, useScreenSize } from '@/src/hooks';
import { useSafeBottomArea } from '@/src/hooks/useSafeAreaInsetsStyle';
import { useAuthStore } from '@/src/store/authInfo';
import { useGoodsShefStore } from '@/src/store/goods_shef';
import { useHomePageTabStore } from '@/src/store/homePageTab';
import { usePerformanceStore } from '@/src/store/performance';
import { usePublishStore } from '@/src/store/publish';
import { useStorageStore } from '@/src/store/storage';
import { useUserInfoStore } from '@/src/store/userInfo';
import { darkTheme, typography } from '@/src/theme';
import {
  FirstTriggerType,
  Pagination,
  TabItemType,
  UserSocialStat
} from '@/src/types';
import { dp2px, isIos } from '@/src/utils';
import { CommonEventBus } from '@/src/utils/event';
import { safeParseJson } from '@/src/utils/opt/safeParseJson';
import { reportClick, reportExpo } from '@/src/utils/report';
import { userPerformanceCollector } from '@/src/utils/report/userPageCollector';
import { Image } from '@Components/image';
import { MaskArea } from '@Components/maskArea';
import { StyleSheet } from '@Utils/StyleSheet';
import { useParams } from '../../hooks/useParams';
import { useWorklet } from '../../hooks/useWorklet';
import { ReportError, errorReport } from '../../utils/error-log';
import { GoodsHomeState } from '../goodsHome/types';
import { NestedScrollView } from '../nestedScrollView';
import ProfileGuideModal from '../profile/GuideModal';
import { GoodsButton } from './components/GoodsButton';
import { ANIMATION_PARAMS, GoodsTopTip } from './components/GoodsTopTip';
import { UserHeader } from './components/UserHeader';
import { UserPanel } from './components/UserPanel';
import { LikesFlowList } from './components/likeFlowList';
import { MyRoleFlowList } from './components/myRoleList';
import { CreateRoleBtnWithAnimation } from './components/myRoleList/CreateRoleBtn';
import { SecretsFlowList } from './components/secretFlowList';
import { WorksFlowList } from './components/workFlowList';
import { useHandleSwipeBack } from './hooks/useHandleSwipeBack';
import { useRetentionPopup } from './hooks/useRetentionPopup';
import { GetPlaceRsp } from '@/proto-registry/src/web/raccoon/goods/goods_pb';
import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';
import { PartialMessage } from '@bufbuild/protobuf';
import { useIsFocused } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';
import {
  RefreshTriggerType,
  UserPageTab,
  authorPageTabMap,
  authorTabs,
  visitorPageTabMap,
  visitorTabs
} from './constants';
import { FeedbackSheet } from './feedback';
import { RefreshTrigger } from './types';
import { fetchSecretCount, filterArrangementsForLazyLoad } from './utils';

function User({
  isRootPage = false,
  pageTab: queryPageTab,
  refresh: queryRefresh,
  timestamp: queryTimestamp,
  id: propId,
  fromSwipePreview,
  preloadProfile,
  onBack
}: {
  isRootPage?: boolean;
  pageTab?: string;
  refresh?: string;
  timestamp?: string;
  id?: string;
  fromSwipePreview?: boolean;
  preloadProfile?: PartialMessage<UserProfile>;
  onBack?: () => void;
}) {
  const [currentTab, setCurrentTab] = useState<UserPageTab>(UserPageTab.WORKS);
  const pagerRef = useRef<PagerView>(null);
  const nestedScrollViewRef = useRef<NestedScrollView>(null);
  const [pagerViewReady, setPagerViewReady] = useState<boolean>(false);
  const { width: screenWidth, height: screenHeight } = useScreenSize('window');
  const [showAlbum, setShowAlbum] = useState(false);
  const userId = useAuthStore(useShallow(state => state.uid));
  const $safePaddingBottom = useSafeBottomArea();
  const isFocused = useIsFocused();
  const { loginIntercept } = useAuthState();
  const safeTop = useSafeAreaInsets().top;
  const { currentTab: currentHomePageTab } = useHomePageTabStore(
    useShallow(state => ({
      currentTab: state.currentTab
    }))
  );
  const rootNavState = useRootNavigationState();
  const [isActive, setIsActive] = useState<boolean>(
    !isRootPage || currentHomePageTab === TabItemType.PROFILE
  );

  // 使用useRef保存最新的依赖项值，避免useEffect频繁触发
  const depsRef = useRef({
    currentHomePageTab,
    isRootPage,
    rootNavRoutes: rootNavState.routes
  });

  // 更新ref中的依赖值
  useEffect(() => {
    depsRef.current = {
      currentHomePageTab,
      isRootPage,
      rootNavRoutes: rootNavState.routes
    };
  }, [currentHomePageTab, isFocused, isRootPage, rootNavState.routes]);

  // 使用RAF异步计算isActive状态
  const updateIsActiveState = useCallback(() => {
    InteractionManager.runAfterInteractions(() => {
      const { currentHomePageTab, isRootPage, rootNavRoutes } = depsRef.current;

      if (isRootPage) {
        // 根页面，如果当前 homeTab 是 profile，也就是从个人页打开其他页面
        // 或者根页面栈中只有一个页面，也就是在首屏而没有堆栈
        setIsActive(
          currentHomePageTab === TabItemType.PROFILE ||
            rootNavRoutes.length === 1
        );
      }
    });
  }, []);

  // 监听依赖项变化，使用RAF异步更新状态
  useEffect(() => {
    updateIsActiveState();
  }, [
    currentHomePageTab,
    isRootPage,
    rootNavState.routes.length,
    updateIsActiveState
  ]);

  const store = useStorageStore();
  const __setStorage = store.__setStorage;

  /** userinfo */
  const params = useParams<{
    id?: string;
  }>();

  const id = propId || params.id || userId;
  const { user } = useAuthStore(
    useShallow(state => ({
      user: state.userInfo
    }))
  );

  const { profile, stat, syncUserInfo, fandomWall, goodsWallRes } =
    useUserInfoStore(
      useShallow(state => {
        const userInfo = id ? state.getUserInfo(id) : undefined;
        return {
          profile: userInfo?.profile,
          stat: userInfo?.stat,
          syncUserInfo: state.syncUserInfo,
          fandomWall: userInfo?.fandomWall,
          goodsWallRes: userInfo?.goodsWallRes
        };
      })
    );

  // 判断是否有谷子墙（通过fandomWall中是否有物品判断）
  const hasGoodsWall = useMemo(() => {
    if (!fandomWall?.arrangements || fandomWall?.arrangements?.length === 0) {
      return false;
    }
    const filteredArrangements = filterArrangementsForLazyLoad(
      fandomWall?.arrangements
    );
    return (
      filteredArrangements.initialVisibleGoods.length > 0 ||
      filteredArrangements.remainingGoods.length > 0
    );
  }, [fandomWall?.arrangements]);

  // 使用预加载数据
  useLayoutEffect(() => {
    if (id && preloadProfile) {
      // 将预加载的用户资料添加到 store 中
      // 注意：这里我们只预加载 profile，其他数据会通过正常请求获取
      const userInfoStore = useUserInfoStore.getState();
      const existingUserInfo = userInfoStore.getUserInfo(id);

      if (!existingUserInfo) {
        // 如果没有数据，添加一个只有基本信息的记录
        // 注意：这里我们创建了一个不完整的记录，其他字段会在后续请求中填充
        userInfoStore.__users = [
          ...userInfoStore.__users,
          {
            uid: id,
            profile: preloadProfile as PartialMessage<UserProfile>,
            stat: {} as PartialMessage<UserSocialStat>,
            goodsWallRes: {} as PartialMessage<GetPlaceRsp>
          }
        ];
      }
    }
  }, [id, preloadProfile]);

  const isMine =
    (isRootPage === true && !params.id) || profile?.uid === user?.uid;

  const currentUser = useMemo(() => {
    if (isMine) {
      return user;
    }
    return profile;
  }, [isMine, user, profile]);

  const pageTabMap = useMemo(
    () => (isMine ? authorPageTabMap : visitorPageTabMap),
    [isMine]
  );
  const [secretsCount, setSecretsCount] = useState<number | null>(null);

  const countStr = useMemo(() => {
    if (secretsCount) {
      return secretsCount > 99 ? '99+' : secretsCount.toString();
    }
    return '';
  }, [secretsCount]);

  const countTextStyle: TextStyle = useMemo(() => {
    return {
      width: countStr.length * 8.5,
      lineHeight: 15,
      fontFamily: typography.fonts.Barlow.SemiBold
    };
  }, [countStr.length]);

  // 为待发布标签创建一个可以立即反映数量变化的渲染函数
  const renderSecretTabContent = useMemoizedFn(
    ({
      textStyle
    }: {
      isActive: boolean;
      textStyle: StyleProp<TextStyle>[];
    }) => {
      return (
        <>
          <Animated.Text
            style={[...textStyle, { width: 46 }]}
            numberOfLines={1}
          >
            待发布
          </Animated.Text>
          <Animated.Text
            style={[...textStyle, countTextStyle]}
            numberOfLines={1}
          >
            {countStr}
          </Animated.Text>
        </>
      );
    }
  );
  const pageTabConfig = useMemo(() => {
    // 修改作者标签配置，为待发布标签添加数量
    if (isMine) {
      const updatedConfig = [...authorTabs];
      // 找到待发布标签并添加数量
      const secretsTabIndex = updatedConfig.findIndex(
        tab => tab.key === UserPageTab.SECRET
      );
      if (!secretsCount || secretsTabIndex === -1) {
        return updatedConfig;
      }

      updatedConfig[secretsTabIndex] = {
        ...updatedConfig[secretsTabIndex],
        renderItem: renderSecretTabContent,
        width: 63 + 8.5 * countStr.length
      };

      return updatedConfig;
    }
    return visitorTabs;
  }, [isMine, secretsCount, renderSecretTabContent, countStr.length]);

  const [profileGuideModalVisible, setProfileGuideModalVisible] = useState<
    boolean | null
  >(null);

  /** 点击底部 tab 刷新 */
  useEffect(() => {
    const handleTabBarPress = () =>
      onRefreshData(RefreshTriggerType.TAB_BAR_PRESSED);
    CommonEventBus.on('tabBarPressedWhenFocus', handleTabBarPress);
    return () => {
      CommonEventBus.off('tabBarPressedWhenFocus', handleTabBarPress);
    };
  }, []);

  const shouldShowProfileGuide =
    isRootPage &&
    userId &&
    isMine &&
    !queryPageTab &&
    !user?.gender &&
    !user?.birthday;

  /** 资料引导 */
  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (!userId || !shouldShowProfileGuide || !isFocused) return;
      const profileGuideRecord =
        safeParseJson<Record<string, boolean | undefined>>(
          useStorageStore.getState().profileGuideRecord
        ) || {};
      const hasShown = profileGuideRecord[userId];
      if (!hasShown) {
        setProfileGuideModalVisible(true);
        useStorageStore
          .getState()
          .updateJsonRecord('profileGuideRecord', record => ({
            ...record,
            [userId]: true
          }));
      }
    });
  }, [shouldShowProfileGuide, userId, isFocused]);

  /** profile 的一些引导 */
  const [isFeedbackShow, setIsFeedbackShow] = useState(false);
  const [isSeqToastShow, setIsSeqToastShow] = useState(false);

  const [hasCreatedGoods, setHasCreatedGoods] = useState(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [isMaskLoaded, setIsMaskLoaded] = useState(false);

  // 监听GOODS_MASK加载完成事件
  useEffect(() => {
    const handleMaskLoaded = (data: { loaded: boolean }) => {
      // 使用InteractionManager确保其他UI渲染完成后再显示背景
      if (data.loaded) {
        InteractionManager.runAfterInteractions(() => {
          setIsMaskLoaded(true);
        });
      }
    };

    CommonEventBus.on('goodsMaskLoaded', handleMaskLoaded);

    return () => {
      CommonEventBus.off('goodsMaskLoaded', handleMaskLoaded);
    };
  }, []);

  const checkEnterGoods = useMemoizedFn(() => {
    loginIntercept(
      () => {
        enterGoods();
      },
      { scene: LOGIN_SCENE.GOODS }
    );
  });

  // 添加一个刷新待发布数量的函数
  const refreshSecretCount = useMemoizedFn(async () => {
    if (id) {
      // 使用InteractionManager确保UI渲染完成后再执行
      const count = await fetchSecretCount();
      setSecretsCount(count);
      console.log('[LogPrefix][User][refreshSecretCount] 待发布数量:', count);
    }
  });

  // 监听卡片删除事件，更新待发布数量，并在条件满足时获取首次数据
  useEffect(() => {
    if (id && isRootPage && isFocused && secretsCount === null) {
      // 首次加载：secretsCount为null时获取
      refreshSecretCount();
    }
  }, [id, isRootPage, isFocused, secretsCount, refreshSecretCount]);

  useEffect(() => {
    if (id && isRootPage && isFocused) {
      // 监听事件
      CommonEventBus.on('secretCountChanged', refreshSecretCount);
    }
    return () => {
      CommonEventBus.off('secretCountChanged', refreshSecretCount);
    };
  }, [id, isFocused, isRootPage, refreshSecretCount]);

  // FIXME(fuxiao): 如果用 true 会导致 refocus 不触发刷新，需要调查
  const { setTimeoutWorklet, clearTimeoutWorklet } = useWorklet(false);
  const timerSharedValue = useSharedValue(0);
  const timerSharedValue2 = useSharedValue(0);
  const timerSharedValue3 = useSharedValue(0);
  const refreshWhenRefocus = usePersistFn(() => {
    if (id) {
      useUserInfoStore.getState().syncFandomWall(id);
    }
  });

  // tab 触发刷新
  const [refreshTrigger, setRefreshTrigger] = useState<RefreshTrigger>({
    timestamp: 0,
    type: RefreshTriggerType.UNKNOWN
  });
  const { run: onRefreshData } = useDebounceFn(
    (type: RefreshTriggerType) => {
      // NOTE(fuxiao): refocus 不刷新，滚动后强制刷新数据而不滚动，FlowList 会有空白的问题
      if (type !== RefreshTriggerType.REFOCUSED) {
        // FIXME(fuxiao): 这里需要调查为什么触发的时间戳比接受的时间戳早 150ms，性能有问题
        setRefreshTrigger({ timestamp: Date.now(), type });
      } else if (id) {
        // refocus 只触发痛墙刷新
        setTimeoutWorklet(timerSharedValue, refreshWhenRefocus, 800);
      }
      setTimeoutWorklet(timerSharedValue2, refreshSecretCount, 500);
      if (id && type === RefreshTriggerType.PULL_DOWN) {
        syncUserInfo(id);
        setTimeoutWorklet(timerSharedValue3, finishRefreshing, 1000);
      }
    },
    {
      wait: 500,
      leading: true
    }
  );

  const GOODS_SHEF_TRIGGER = isMine
    ? FirstTriggerType.ENTER_OWN_FANDOM_WALL
    : FirstTriggerType.ENTER_OTHER_FANDOM_WALL;

  const { checkIsAllowed } = useGoodsShefStore(
    useShallow(state => ({
      checkIsAllowed: state.checkIsAllowed,
      finishSequence: state.finishSequence
    }))
  );

  const enterGoods = usePersistFn(() => {
    if (isMine) {
      if (hasCreatedGoods) {
        router.navigate({
          pathname:
            `/goods/home?state=${GoodsHomeState.Me}` as RelativePathString
        });
      } else {
        router.navigate({
          pathname: '/goods'
        });
      }
    } else {
      if (profile?.uid) {
        router.navigate({
          pathname:
            `/goods/home?state=${GoodsHomeState.Guest}&uid=${profile?.uid}` as RelativePathString
        });
      }
    }
  });

  const syncGoodsShefToast = useCallback(async () => {
    const res = await checkIsAllowed(GOODS_SHEF_TRIGGER);
    console.log(res, 'isallowed ====', GOODS_SHEF_TRIGGER);
    setIsSeqToastShow(res);
  }, []);

  const syncGoodsList = useCallback(
    async (userId: string) => {
      if (!isFocused) {
        return;
      }
      try {
        const res = await getGoodsList({
          pagination: {
            cursor: '',
            size: 20
          } as Pagination,
          uid: userId
        });
        setHasCreatedGoods(!!res?.goods?.length);
      } catch (error) {
        errorReport('syncGoodsList', ReportError.Goods, error);
        setHasCreatedGoods(false); // 错误时确保状态明确
      } finally {
        // 无论成功失败，都标记加载完成
        setInitialLoadComplete(true);
      }
    },
    [isFocused]
  );

  // 主数据获取 useEffect (只负责触发请求)
  useEffect(() => {
    if (id) {
      setInitialLoadComplete(false); // 重置标记当 id 变化时
      // 立即请求用户基本信息和FandomWall（最高优先级）
      syncUserInfo(id).then(() => {
        // 其他请求延迟执行（低优先级）
        InteractionManager.runAfterInteractions(() => {
          // 获取谷子列表（低优先级）
          syncGoodsList(id); // 此调用将最终设置 initialLoadComplete 为 true
        });
      });
    }
  }, [id, syncUserInfo, syncGoodsList]);

  // 处理依赖于加载结果的逻辑 (visitHomePage, syncGoodsShefToast)
  useEffect(() => {
    // 确保初始加载已完成且有有效 id
    if (!initialLoadComplete || !id || !isFocused) {
      return;
    }

    // 访问他人主页逻辑
    if (!isMine && id) {
      visitHomePage({ targetUid: id });
    }

    // 物品栏引导提示逻辑
    if ((hasCreatedGoods && !isMine) || isMine) {
      syncGoodsShefToast();
    }
  }, [
    initialLoadComplete,
    id,
    isMine,
    hasCreatedGoods,
    isFocused,
    syncGoodsShefToast
  ]);

  useEffect(() => {
    if (isFocused) {
      reportExpo('invite_icon', { module: 'user' }, true);
    }
  }, [isFocused]);

  /** 新手引导的 痛墙 bounce */
  const { lockBounce } = useGoodsShefStore(
    useShallow(state => ({
      lockBounce: state.lockBounce
    }))
  );

  const isLoadBouncing = useRef(false);

  const resetLoadBouncing = () => {
    isLoadBouncing.current = false;
  };

  /**是否可回退 */

  const pageScroll = useCallback(
    (e: { nativeEvent: { position: number; offset: number } }) => {
      const pos = e.nativeEvent.position + e.nativeEvent.offset;

      if (pos >= 0 && pos < (isMine ? 4 : 2)) {
        $animatedTabIndictor.value = pos;
      }
    },
    [isMine]
  );

  useEffect(() => {
    setTimeout(() => {
      reportExpo('all', {
        module: 'user',
        identity_status: isMine ? '0' : '1'
      });
    });
  }, []);

  /** tab更新 */
  useEffect(() => {
    if (pagerViewReady && queryPageTab && queryPageTab !== currentTab) {
      if (pagerRef.current) {
        const tab = pageTabMap[queryPageTab as UserPageTab];
        pagerRef.current?.setPage(tab || 0);
      }
    }
  }, [queryPageTab, queryTimestamp, pagerViewReady]);

  const $animatedTabIndictor = useSharedValue(0);
  const $animatedScrollPosition = useSharedValue(0);

  const { checkRetentionAndReport } = useRetentionPopup({
    isMine,
    stat,
    id,
    isFocused,
    profile
  });

  // 添加一个 ref 来判断是否是首次挂载
  const isFirstRootFocusRef = useRef(true);
  const {
    getGestureHandlers,
    pullDownProgress,
    isRefreshing,
    finishRefreshing,
    resetAllStates
  } = usePullDownGestureHandlersFactory({
    onPullDownEnd: useMemoizedFn((progress: number) => {
      console.log('[LogPrefix][GoodsTopTip] 下拉结束，进度:', progress);
      if (progress >= ANIMATION_PARAMS.PULL_GOODS_THRESHOLD) {
        // 打开痛墙
        checkEnterGoods();
      } else if (progress >= ANIMATION_PARAMS.PULL_REFRESH_THRESHOLD) {
        // 触发刷新
        onRefreshData(RefreshTriggerType.PULL_DOWN);
      }
    }),
    scrollPosition: $animatedScrollPosition
  });

  useFocusEffect(
    useCallback(() => {
      reportExpo(
        'user_expo',
        {
          module: 'user',
          userId: id
        },
        true
      );
      // 在页面聚焦时刷新待发布数量，但跳过首次挂载
      if (isRootPage && isFirstRootFocusRef.current) {
        isFirstRootFocusRef.current = false;
      } else if (isRootPage) {
        // focus 触发刷新
        onRefreshData(RefreshTriggerType.REFOCUSED);
      }

      console.log('====== useFocusEffect 触发重置 ======');
      // 移除直接调用resetAllStates，避免循环刷新
      // resetAllStates();

      return () => {
        console.log('====== useFocusEffect 卸载重置 ======');
        clearTimeoutWorklet(timerSharedValue);
        clearTimeoutWorklet(timerSharedValue2);
        clearTimeoutWorklet(timerSharedValue3);
        resetAllStates(isLoadBouncing.current);
      };
    }, [id, isRootPage])
  );

  // 处理返回事件，用于 SwipeScreen 的 beforeBackPressed
  useHandleSwipeBack(checkRetentionAndReport);

  // 处理返回按钮点击
  const handleBack = useCallback(() => {
    // 先重置所有状态
    resetAllStates();
    if (onBack) {
      onBack();
    } else {
      const shouldShowPopup = checkRetentionAndReport();
      if (!shouldShowPopup) {
        router.back();
      }
    }
    // 否则阻止返回，让弹窗处理
  }, [checkRetentionAndReport, resetAllStates, onBack]);

  // 处理图集按钮点击
  const handleAlbumPress = useCallback(() => {
    reportClick('album_button', {
      module: 'user',
      identity_status: isMine ? '0' : '1'
    });

    usePublishStore.getState().getAlbumPhotos(true);
    usePublishStore.getState().getHistoryPhotos(true);
    setShowAlbum(true);
    usePerformanceStore.getState().recordStart('make_photo_photo_set_render', {
      performance_type: 'render',
      performance_key: AlbumFromType.USER
    });
  }, [isMine]);

  const PagerViewGesture = Gesture.Native().shouldCancelWhenOutside(false);

  const scrollableNameSV = useSharedValue<UserPageTab>(UserPageTab.WORKS);

  useEffect(() => {
    scrollableNameSV.value = currentTab;
  }, [currentTab]);

  const renderPageView = useMemoizedFn(() => {
    // NOTE(fuxiao): 如果这里判断 id 返回 View 或者 null，会导致 Android 无法滑动
    const commonParams = {
      id: id || '',
      $safePaddingBottom: $safePaddingBottom,
      queryRefresh: queryRefresh,
      queryPageTab: queryPageTab,
      queryTimestamp: queryTimestamp,
      refreshTrigger,
      isRootPage,
      currentTab,
      nestedScrollViewRef
    };

    // 这里有点坑，在pageview 里面直接控制会报错，所以在这里处理
    const children = isMine
      ? [
          <WorksFlowList key={0} {...commonParams} />,
          <SecretsFlowList key={1} {...commonParams} />,
          <LikesFlowList key={2} {...commonParams} />,
          <MyRoleFlowList key={3} {...commonParams} />
        ]
      : [
          <WorksFlowList key={0} {...commonParams} />,
          <LikesFlowList key={2} {...commonParams} />
        ];

    return (
      <PagerView
        ref={pagerRef}
        style={{
          flex: 1,
          backgroundColor: darkTheme.background.page
        }}
        onPageSelected={e => {
          const position = e.nativeEvent.position;
          // 修复查找对应标签的逻辑
          const tab = Object.entries(pageTabMap).find(
            ([_, value]) => value === position
          );
          if (pagerViewReady && tab?.[0]) {
            const newTab = (tab?.[0] as UserPageTab) || UserPageTab.WORKS;
            setCurrentTab(newTab);
            // 当切换到待发布标签时刷新数量
            if (newTab === UserPageTab.SECRET) {
              refreshSecretCount();
            }
          }
        }}
        // overdrag
        onTouchStart={e => {
          // setTouchOffsetX(e.nativeEvent.pageX);
        }}
        onPageScroll={pageScroll}
        onLayout={() => {
          setTimeout(() => {
            setPagerViewReady(true);
          }, 1000);
        }}
      >
        {children}
      </PagerView>
    );
  });

  useLayoutEffect(() => {
    if (id) {
      userPerformanceCollector.markPerformanceTimestamp(
        'user_init_timestamp',
        id,
        isRootPage,
        fromSwipePreview
      );
    }
    return () => {
      // 组件卸载时清理性能收集器
      userPerformanceCollector.clear();
    };
  }, [id, isRootPage, fromSwipePreview]);

  useEffect(() => {
    if (id) {
      userPerformanceCollector.markPerformanceTimestamp(
        'user_render_done_timestamp',
        id
      );
    }
  }, [id]);

  // 在作品列表开始加载时标记
  useEffect(() => {
    if (currentTab === UserPageTab.WORKS && id) {
      userPerformanceCollector.markPerformanceTimestamp(
        'user_works_init_timestamp',
        id
      );
    }
  }, [currentTab, id]);

  const hasSlideUp = useSharedValue(false);

  // 创建橡皮筋效果的动画样式
  const pullDownAnimatedStyle = useAnimatedStyle(() => {
    // 检查是否有下拉值时应用变换
    if (pullDownProgress.value <= 0 && !isLoadBouncing.current) {
      return { transform: [{ translateY: 0 }] };
    }

    return {
      transform: [{ translateY: pullDownProgress.value }]
    };
  });

  // 添加引导动画效果
  useEffect(() => {
    if (
      isMaskLoaded &&
      isFocused &&
      useStorageStore.getState().userGoodsHasbeBounced < 5 &&
      (isMine || (!isMine && hasCreatedGoods)) &&
      !isLoadBouncing.current &&
      !lockBounce &&
      profileGuideModalVisible !== true
    ) {
      const nextCount = useStorageStore.getState().userGoodsHasbeBounced + 1;
      isLoadBouncing.current = true;

      // 使用 pullDownProgress 进行动画
      pullDownProgress.value = withDelay(
        1000,
        withRepeat(
          withSpring(ANIMATION_PARAMS.PULL_GOODS_THRESHOLD + 20, {
            damping: 80
          }),
          4,
          true,
          () => {
            runOnJS(__setStorage)({
              userGoodsHasbeBounced: nextCount
            });
            runOnJS(resetLoadBouncing)();
            pullDownProgress.value = 0;
          }
        )
      );
    }
  }, [
    isMine,
    hasCreatedGoods,
    profileGuideModalVisible,
    lockBounce,
    isMaskLoaded,
    isFocused
  ]);

  useEffect(() => {
    // 引导红点 塔罗+活动中心 +1 <3
    useStorageStore.getState().__setStorage({
      tarotGuideCount: useStorageStore.getState().tarotGuideCount + 1
    });
  }, []);

  return (
    <PagePerformance pathname="user">
      <Screen
        theme="dark"
        key={id}
        safeAreaEdges={[]}
        headerShown={false}
        backgroundView={
          <GoodsWallBg
            visible={isMaskLoaded && isActive}
            safeTop={safeTop}
            id={id ? id : undefined}
          />
        }
      >
        <UserHeader
          profile={profile}
          stat={stat}
          hasSlideUp={hasSlideUp}
          isMine={isMine}
          isRootPage={isRootPage}
          onBack={handleBack}
          safeTop={safeTop}
        />
        <GoodsTopTip
          isMine={isMine}
          hasGoods={hasCreatedGoods}
          goodsLikes={goodsWallRes?.likes}
          isGoodsTipAllowed={isSeqToastShow}
          checkEnterGoods={checkEnterGoods}
          safeTop={safeTop}
          scrollPosition={$animatedScrollPosition}
          pullDownProgress={pullDownProgress}
          isRefreshing={isRefreshing}
        />
        <Animated.View style={[{ flex: 1 }, pullDownAnimatedStyle]}>
          <NestedScrollView
            scrollableNameSV={scrollableNameSV}
            animatedPosition={$animatedScrollPosition}
            hasSlideUp={hasSlideUp}
            topPreserveInset={dp2px(88) + safeTop}
            topInset={0}
            simultaneousWithExternalGesture={
              isIos ? undefined : PagerViewGesture
            }
            gestureEventsHandlersHook={getGestureHandlers}
            failOffsetX={[-20, 20]}
            activeOffsetY={[-20, 20]}
            ref={nestedScrollViewRef}
            headerComponent={
              <>
                <UserPanel
                  currentUser={currentUser as UserProfile}
                  stat={stat}
                  isMine={isMine}
                  showFeedBack={() => setIsFeedbackShow(true)}
                  hasGoodsWall={hasGoodsWall}
                  currentTab={currentTab}
                  $animatedIndictor={$animatedTabIndictor}
                  pagerRef={pagerRef}
                  onAlbumPress={handleAlbumPress}
                  safeTop={safeTop}
                  hasSlideUp={hasSlideUp}
                  checkEnterGoods={checkEnterGoods}
                  tabConfig={pageTabConfig}
                />
                <GoodsButton
                  isMine={isMine}
                  checkEnterGoods={checkEnterGoods}
                  hasGoodsWall={hasGoodsWall}
                  safeTop={safeTop}
                />
              </>
            }
          >
            {isIos ? (
              renderPageView()
            ) : (
              <GestureDetector gesture={PagerViewGesture}>
                {renderPageView()}
              </GestureDetector>
            )}
          </NestedScrollView>
          <View
            style={[
              {
                width: '100%',
                height: screenHeight,
                position: 'relative',
                pointerEvents: 'box-none'
              }
            ]}
          >
            {currentUser?.uid && isFeedbackShow && (
              <View
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  flex: 1
                }}
              >
                <FeedbackSheet
                  isVisible={isFeedbackShow}
                  userId={currentUser?.uid}
                  onClose={() => {
                    setIsFeedbackShow(false);
                  }}
                />
              </View>
            )}

            {profileGuideModalVisible && (
              <ProfileGuideModal
                visible={profileGuideModalVisible}
                onClose={() => setProfileGuideModalVisible(false)}
              />
            )}
          </View>

          {isMine && currentTab === UserPageTab.MY_ROLE && (
            <CreateRoleBtnWithAnimation isRootPage={isRootPage} />
          )}
          {!fromSwipePreview && <MaskArea />}
          {showAlbum && (
            <AlbumSheet
              callWhere={AlbumFromType.USER}
              isVisible={showAlbum}
              onClose={() => {
                setShowAlbum(false);
              }}
            />
          )}
        </Animated.View>
      </Screen>
    </PagePerformance>
  );
}

export const UserScreen = memo(User, (prev, now) => {
  return (
    prev.timestamp === now.timestamp &&
    prev.fromSwipePreview === now.fromSwipePreview
  );
});
