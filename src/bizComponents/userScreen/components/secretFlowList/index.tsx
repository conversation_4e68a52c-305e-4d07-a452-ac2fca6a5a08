import { useLockFn } from 'ahooks';
import { memo, useEffect, useMemo, useRef } from 'react';
import { ScrollView } from 'react-native';
import { useSharedValue } from 'react-native-reanimated';
import { feedClient } from '@/src/api';
import {
  getSecretPhotoFeedLayoutProvider,
  renderSecretPhotoItem
} from '@/src/bizComponents/feedcard/secretCard';
import { CellCardScene } from '@/src/bizComponents/feedcard/types';
import { hideLoading, showLoading } from '@/src/components';
import {
  InfiniteListRef,
  RequestScene
} from '@/src/components/infiniteList/typing';
import {
  IWaterFallProps,
  WaterFallCardData
} from '@/src/components/waterfall/type';
import {
  FetchMethodPayloadType,
  useRequestFeed
} from '@/src/components/waterfall/useRequsetFeed';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useAsyncMessage } from '@/src/store/asyncMessage';
import { useLiveStore } from '@/src/store/live';
import { GameType } from '@/src/types';
import { CommonEventBus } from '@/src/utils/event';
import { usePersistFn } from '../../../../hooks';
import { useWorklet } from '../../../../hooks/useWorklet';
import { UserPageTab } from '../../constants';
import { useTabRefreshEffect } from '../../hooks/useTabRefreshEffect';
import { FlowCommonProps } from '../../types';
import { onRefreshError } from '../../utils';
import { BaseWaterFlowList } from '../baseFlowList';
import {
  AsyncCardInfo,
  AsyncCardStatus
} from '@/proto-registry/src/web/raccoon/common/asynccard_pb';
import { useIsFocused } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';

/**
 * 视频待发布页列表数据
 * 相关的 format 操作可以在这里处理
 * 抹平视频列表数据结构的差异
 * @param payload
 * @returns
 */
async function fetchSecretFeedMethod(payload: FetchMethodPayloadType) {
  return feedClient
    .userAsyncCards({
      pagination: payload.pagination
    })
    .then(res => {
      const formatter = useLiveStore.getState().formatAyncCardInfo;
      // 后端数据格式不一致，紧急上线，我们 format 一下
      res.cards = res.cards.map(item => {
        return formatter(item);
      });
      return res;
    });
}

// 适配器函数：转换为 WaterFall2 所需的类型
const adaptedGetLayoutProvider = (
  dataRef: React.MutableRefObject<WaterFallCardData[]>
) => {
  // 将 WaterFallCardData[] 类型安全地转换为 AsyncCardInfo[]
  const adaptedDataRef = {
    get current() {
      return dataRef.current as unknown as AsyncCardInfo[];
    }
  } as React.MutableRefObject<AsyncCardInfo[]>;

  return getSecretPhotoFeedLayoutProvider(adaptedDataRef);
};

// 适配器函数：转换为 WaterFall2 所需的类型
const adaptedRenderItem = (
  type: string | number,
  data: WaterFallCardData,
  index: number,
  extendedState?: object,
  layoutInfo?: { x: number; y: number }
) => {
  // 将 WaterFallCardData 类型安全地转换为 AsyncCardInfo
  return renderSecretPhotoItem(
    type,
    data as unknown as AsyncCardInfo,
    index,
    extendedState,
    layoutInfo
  );
};

export const SecretsFlowList = memo((props: FlowCommonProps) => {
  const {
    id,
    $safePaddingBottom,
    queryRefresh,
    queryPageTab,
    queryTimestamp,
    isRootPage,
    currentTab,
    refreshTrigger,
    nestedScrollViewRef
  } = props;

  const { go2Create } = useChangeRoute();
  const infiniteListRef = useRef<InfiniteListRef>(null);
  // 获取页面是否有焦点状态
  const isFocused = useIsFocused();

  // 获取加密内容流是否需要刷新的标记
  const { secretFlowNeedsRefresh, setSecretFlowNeedsRefresh } = useAsyncMessage(
    useShallow(state => ({
      secretFlowNeedsRefresh: state.secretFlowNeedsRefresh,
      setSecretFlowNeedsRefresh: state.setSecretFlowNeedsRefresh
    }))
  );

  const {
    fetchList,
    sourceData: secretSourceData,
    ...secretReturnData
  } = useRequestFeed({
    defaultFetch: false,
    fetchMethod: fetchSecretFeedMethod,
    onDataCallback: cards => {
      // 不再区分 store 管理，统一维护
      useLiveStore.getState().updateLiveCardMap(cards);
    },
    onAllCardsCallback: cards => {
      useAsyncMessage.getState().updateProcessingList?.(cards);
    },
    onError: onRefreshError
  });

  const lockFetchList = useLockFn(fetchList);

  // 监听发布事件，设置刷新标记
  useEffect(() => {
    const onCardEvent = () => {
      setSecretFlowNeedsRefresh(true);
      // 触发更新待发布数量的事件
      CommonEventBus.emit('secretCountChanged');
    };

    CommonEventBus.on('asyncCardAppended', onCardEvent);
    CommonEventBus.on('asyncCardPublished', onCardEvent);

    return () => {
      CommonEventBus.off('asyncCardAppended', onCardEvent);
      CommonEventBus.off('asyncCardPublished', onCardEvent);
    };
  }, [setSecretFlowNeedsRefresh]);

  // 当有内容更新且当前在加密内容标签页时，并且页面有焦点时，刷新列表
  useEffect(() => {
    if (
      currentTab === UserPageTab.SECRET &&
      secretFlowNeedsRefresh &&
      isFocused
    ) {
      infiniteListRef.current?.forceRefresh();
      // 重置刷新标记
      setSecretFlowNeedsRefresh(false);
    }
  }, [
    currentTab,
    secretFlowNeedsRefresh,
    setSecretFlowNeedsRefresh,
    isFocused
  ]);

  // 初始化加载
  useEffect(() => {
    lockFetchList(RequestScene.INIT);
  }, [id, lockFetchList]);

  // 监听 URL 参数刷新
  useEffect(() => {
    if (queryRefresh && queryPageTab === UserPageTab.SECRET) {
      infiniteListRef.current?.forceRefresh();
    }
  }, [queryRefresh, queryTimestamp, queryPageTab]);

  useTabRefreshEffect(
    currentTab === UserPageTab.SECRET,
    refreshTrigger,
    infiniteListRef,
    nestedScrollViewRef
  );

  const validSecretSourceData = useMemo(() => {
    return (
      secretSourceData?.filter(i => {
        // 类型断言为 AsyncCardInfo
        const asyncCard = i as unknown as AsyncCardInfo;
        return asyncCard.status !== AsyncCardStatus.PUBLISH;
      }) || []
    );
  }, [secretSourceData]);

  const waterfallProps: Partial<IWaterFallProps> = useMemo(
    () => ({
      renderItem: adaptedRenderItem,
      getLayoutProvider: adaptedGetLayoutProvider,
      extendedState: {
        scene: CellCardScene.MY
      },
      customEmptyProps: {
        children: '小狸在等你的作品！',
        button: true,
        buttonText: '去创作',
        onButtonPress: () => {
          go2Create({
            gameType: GameType.LIVE_PHOTO
          });
        },
        type: 'darkProfile'
      },
      reportParams: {
        module: 'user',
        type: 'secret_video'
      }
    }),
    [go2Create]
  );

  return (
    <BaseWaterFlowList
      pageTabKey={UserPageTab.SECRET}
      data={{
        ...secretReturnData,
        sourceData: validSecretSourceData
      }}
      fetchList={lockFetchList}
      infiniteListRef={infiniteListRef}
      scrollViewProps={{
        style: { paddingTop: 5 },
        scrollViewName: UserPageTab.SECRET
      }}
      waterfallProps={waterfallProps}
      $safePaddingBottom={$safePaddingBottom}
      isRootPage={isRootPage}
      currentTab={currentTab}
    />
  );
});

SecretsFlowList.displayName = 'SecretsFlowList';
