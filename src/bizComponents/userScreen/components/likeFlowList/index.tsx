import { useLockFn, useMemoizedFn } from 'ahooks';
import { memo, useEffect, useMemo, useRef } from 'react';
import { ScrollView } from 'react-native';
import { feedClient } from '@/src/api';
import { CellCardScene } from '@/src/bizComponents/feedcard/types';
import { hideLoading, showLoading } from '@/src/components';
import {
  InfiniteListRef,
  RequestScene
} from '@/src/components/infiniteList/typing';
import { IWaterFallProps } from '@/src/components/waterfall/type';
import {
  FetchMethodPayloadType,
  useRequestFeed
} from '@/src/components/waterfall/useRequsetFeed';
import { useHistoryStore } from '@/src/store/histroy';
import { PostDetailSource } from '../../../../utils/report';
import { UserPageTab } from '../../constants';
import { useTabRefreshEffect } from '../../hooks/useTabRefreshEffect';
import { FlowCommonProps } from '../../types';
import { onRefreshError } from '../../utils';
import { BaseWaterFlowList } from '../baseFlowList';
import { useIsFocused } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';

export const LikesFlowList = memo((props: FlowCommonProps) => {
  const {
    id,
    $safePaddingBottom,
    queryRefresh,
    queryTimestamp,
    queryPageTab,
    isRootPage,
    currentTab,
    refreshTrigger,
    nestedScrollViewRef
  } = props;

  const infiniteListRef = useRef<InfiniteListRef>(null);
  // 获取页面是否有焦点状态
  const isFocused = useIsFocused();

  // 获取点赞刷新状态
  const { likeFlowNeedsRefresh, setLikeFlowNeedsRefresh } = useHistoryStore(
    useShallow(state => ({
      likeFlowNeedsRefresh: state.likeFlowNeedsRefresh,
      setLikeFlowNeedsRefresh: state.setLikeFlowNeedsRefresh
    }))
  );

  const fetchLikeFeedMethod = useMemoizedFn(
    (payload: FetchMethodPayloadType) => {
      return feedClient.userLikesCards({
        uid: id ?? '',
        pagination: payload.pagination
      });
    }
  );
  const { fetchList, ...data } = useRequestFeed({
    defaultFetch: false,
    fetchMethod: fetchLikeFeedMethod,
    onError: onRefreshError
  });

  const lockFetchList = useLockFn(fetchList);

  useEffect(() => {
    lockFetchList(RequestScene.INIT);
  }, [id, lockFetchList]);

  // 当有点赞信息更新且当前在点赞标签页时，并且页面有焦点时，刷新列表
  useEffect(() => {
    if (currentTab === UserPageTab.LIKE && likeFlowNeedsRefresh && isFocused) {
      infiniteListRef.current?.forceRefresh();
      // 重置刷新标记
      setLikeFlowNeedsRefresh(false);
    }
  }, [currentTab, likeFlowNeedsRefresh, setLikeFlowNeedsRefresh, isFocused]);

  useEffect(() => {
    if (queryRefresh && queryPageTab === UserPageTab.LIKE) {
      infiniteListRef.current?.forceRefresh();
    }
  }, [queryRefresh, queryTimestamp, queryPageTab]);

  useTabRefreshEffect(
    currentTab === UserPageTab.LIKE,
    refreshTrigger,
    infiniteListRef,
    nestedScrollViewRef
  );

  const waterfallProps: Partial<IWaterFallProps> = useMemo(
    () => ({
      customEmptyProps: {
        children: '快去赞点作品来！',
        type: 'darkProfile'
      },
      extendedState: {
        scene: CellCardScene.LIKE
      },
      reportParams: {
        module: 'user',
        from: PostDetailSource.USER
      }
    }),
    []
  );

  return (
    <BaseWaterFlowList
      scrollViewProps={{
        scrollViewName: UserPageTab.LIKE
      }}
      pageTabKey={UserPageTab.LIKE}
      data={data}
      fetchList={lockFetchList}
      infiniteListRef={infiniteListRef}
      waterfallProps={waterfallProps}
      $safePaddingBottom={$safePaddingBottom}
      isRootPage={isRootPage}
      currentTab={currentTab}
    />
  );
});

LikesFlowList.displayName = 'LikesFlowList';
