import { useLockFn } from 'ahooks';
import { memo, useEffect, useMemo, useRef } from 'react';
import { feedClient } from '@/src/api';
import { CellCardScene } from '@/src/bizComponents/feedcard/types';
import {
  InfiniteListRef,
  RequestScene
} from '@/src/components/infiniteList/typing';
import {
  FetchMethodPayloadType,
  useRequestFeed
} from '@/src/components/waterfall/useRequsetFeed';
import { usePersistFn } from '@/src/hooks';
import { useAsyncMessage } from '@/src/store/asyncMessage';
import { useVideoFlow } from '@/src/store/video-flow';
import { CardType, PlainType, RichCardInfo } from '@/src/types';
import { CommonEventBus } from '@/src/utils/event';
import { PostDetailSource } from '@/src/utils/report';
import { userPerformanceCollector } from '@/src/utils/report/userPageCollector';
import { UserPageTab } from '../../constants';
import { useTabRefreshEffect } from '../../hooks/useTabRefreshEffect';
import { FlowCommonProps } from '../../types';
import { onRefreshError } from '../../utils';
import { BaseWaterFlowList } from '../baseFlowList';
import { useIsFocused } from '@react-navigation/native';
import { useShallow } from 'zustand/react/shallow';

export const WorksFlowList = memo((props: FlowCommonProps) => {
  const {
    id,
    $safePaddingBottom,
    queryRefresh,
    queryTimestamp,
    queryPageTab,
    isRootPage,
    currentTab,
    refreshTrigger,
    nestedScrollViewRef
  } = props;

  const infiniteListRef = useRef<InfiniteListRef>(null);
  // 获取页面是否有焦点状态
  const isFocused = useIsFocused();

  // 获取作品流是否需要刷新的标记
  const { worksFlowNeedsRefresh, setWorksFlowNeedsRefresh } = useAsyncMessage(
    useShallow(state => ({
      worksFlowNeedsRefresh: state.worksFlowNeedsRefresh,
      setWorksFlowNeedsRefresh: state.setWorksFlowNeedsRefresh
    }))
  );

  async function fetchUserFeedkMethod(payload: FetchMethodPayloadType) {
    return feedClient.userCreatedCards({
      uid: id ?? ''!,
      pagination: payload.pagination
    });
  }
  const { fetchList, ...data } = useRequestFeed({
    defaultFetch: false,
    fetchMethod: fetchUserFeedkMethod,
    onError: onRefreshError,
    onDataCallback: cards => {
      if (cards?.length > 0) {
        userPerformanceCollector.markPerformanceTimestamp(
          'user_works_available_timestamp',
          id
        );
      }
    }
  });

  const lockFetchList = useLockFn(fetchList);

  useEffect(() => {
    lockFetchList(RequestScene.INIT);
  }, [id, lockFetchList]);

  // 监听发布新内容事件，设置刷新标记
  useEffect(() => {
    const onCardEvent = () => {
      setWorksFlowNeedsRefresh(true);
    };

    CommonEventBus.on('cardPublished', onCardEvent);
    CommonEventBus.on('asyncCardPublished', onCardEvent);

    return () => {
      CommonEventBus.off('cardPublished', onCardEvent);
      CommonEventBus.off('asyncCardPublished', onCardEvent);
    };
  }, [setWorksFlowNeedsRefresh]);

  // 当有内容更新且当前在作品标签页时，并且页面有焦点时，刷新列表
  useEffect(() => {
    if (
      currentTab === UserPageTab.WORKS &&
      worksFlowNeedsRefresh &&
      isFocused
    ) {
      infiniteListRef.current?.forceRefresh();
      // 重置刷新标记
      setWorksFlowNeedsRefresh(false);
    }
  }, [
    currentTab,
    worksFlowNeedsRefresh,
    infiniteListRef,
    setWorksFlowNeedsRefresh,
    isFocused
  ]);

  useEffect(() => {
    if (queryRefresh && queryPageTab === UserPageTab.WORKS) {
      infiniteListRef.current?.forceRefresh();
    }
  }, [queryRefresh, queryTimestamp, queryPageTab, infiniteListRef]);

  useTabRefreshEffect(
    currentTab === UserPageTab.WORKS,
    refreshTrigger,
    infiniteListRef,
    nestedScrollViewRef
  );

  const onLeave = usePersistFn((leaveData?: PlainType<RichCardInfo>) => {
    if (leaveData?.card?.id) {
      const cards =
        data.sourceData?.filter(i => i.card?.type === CardType.VIDEO) ?? [];
      useVideoFlow
        .getState()
        .setCards(leaveData.card.id, cards, data?.getPagination?.());
    }
  });

  const waterfallProps = useMemo(
    () => ({
      extendedState: {
        scene: CellCardScene.MY,
        onLeave
      },
      reportParams: {
        module: 'user',
        from: PostDetailSource.USER
      }
    }),
    [onLeave]
  );

  return (
    <BaseWaterFlowList
      pageTabKey={UserPageTab.WORKS}
      scrollViewProps={{
        scrollViewName: UserPageTab.WORKS
      }}
      data={data}
      fetchList={lockFetchList}
      infiniteListRef={infiniteListRef}
      waterfallProps={waterfallProps}
      $safePaddingBottom={$safePaddingBottom}
      isRootPage={isRootPage}
      currentTab={currentTab}
    />
  );
});

WorksFlowList.displayName = 'WorksFlowList';
