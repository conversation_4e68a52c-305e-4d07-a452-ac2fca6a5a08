import { useMemoizedFn } from 'ahooks';
import {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react';
import { View } from 'react-native';
import { Image, ImageProps } from '@/src/components';
import { TWaterFallInternalEvent } from '@/src/components/waterfall/type';
import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';
import { PlainType } from '@/src/types';
import { isIos } from '@/src/utils';
import { getVideoUrlFromCard } from '@/src/utils/cardUtils';
import { checkWeakNetwork } from '@/src/utils/device/network';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { Event } from '@/src/utils/event';
import { formatTosUrl } from '@/src/utils/getTosUrl';
import { FeedRichCardInfo } from '../../feedScreen/type';
import { useVideoStatusUpdate } from '../../livePhotoScreen/hooks/useVideoStatusUpdate';
import { bindwidthRecorder } from '../../livePhotoScreen/timeRecord';
import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb.js';
import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb.js';
import { useIsFocused } from '@react-navigation/native';
import { Video, VideoProps } from '@step.ai/expo-av';
import { ResizeMode } from '@step.ai/expo-av';
import { OtakudanceExtInfo } from '@step.ai/proto-gen/raccoon/common/otakudance_pb';
import ImageRender from './ImageRender';

interface VideoRenderProps extends VideoProps {
  data: PlainType<FeedRichCardInfo>;
  index?: number;
  eventBus?: Event<TWaterFallInternalEvent>;
  videoRef?: React.MutableRefObject<Video>;
  onReady?: () => void;
}

const PosterComponent = ({
  source,
  style,
  onReady
}: {
  source: ImageProps['source'];
  style: ImageProps['style'];
  onReady?: () => void;
}) =>
  source && (
    <Image
      tosSize={'size4'}
      source={source}
      contentFit={'cover'}
      style={[style, { width: '100%', height: '100%' }]}
      onLoad={onReady}
    />
  );
PosterComponent.displayName = 'PosterComponent';

const VideoRender = forwardRef<{ stop: () => Promise<void> }, VideoRenderProps>(
  ({ data, index, eventBus, onReady }, ref) => {
    const [showVideo, setShowVideo] = useState(false);
    const [isError, setIsError] = useState(false);
    const videoRef = useRef<Video>(null);
    const isFocused = useIsFocused();
    const { isLowEndDevice } = useDevicePerformance();
    const isWeakNewwork = useMemo(
      () => checkWeakNetwork(bindwidthRecorder.getEWMA()),
      []
    );

    const [lastLoadPoster, setLastLoadPoster] = useState<string | undefined>();
    const [lastLoadVideo, setLastLoadVideo] = useState<string | undefined>();

    const { onPlaybackStatusUpdate } = useVideoStatusUpdate();

    useImperativeHandle(ref, () => {
      return {
        async stop() {
          await videoRef.current?.stopAsync();
        }
      };
    });

    useEffect(() => {
      const listener = ({ index: firstVideoCardIndex }: { index?: number }) => {
        // console.log(
        //   '### firstVideoCardIndex',
        //   firstVideoCardIndex,
        //   index
        //   // data?.card
        // );
        if (index === firstVideoCardIndex) {
          setShowVideo(true);
        } else {
          setShowVideo(false);
        }
      };
      eventBus?.on('firstVisiableVideoCard', listener);
      return () => {
        eventBus?.off('firstVisiableVideoCard', listener);
      };
    }, [index, eventBus]);

    const videoSource = useMemo(() => {
      const uri = getVideoUrlFromCard(
        data?.card,
        isLowEndDevice || isWeakNewwork
      );
      return uri ? { uri } : void 0;
    }, [data.card?.id]); // isLowEndDevice不加入deps，防止清晰度切换。

    // useEffect(() => {
    //   console.log('### isLowEndDevice', isLowEndDevice);
    //   if (showVideo && isLowEndDevice) {
    //     console.log(
    //       '### 使用低分辨率视频',
    //       data?.card?.displayVideoUrl,
    //       videoSource
    //     );
    //   }
    // }, [data?.card?.id, showVideo]);

    const posterSource = useMemo(() => {
      const cardExtInfo = data?.card?.cardExtInfo?.value?.value;

      const url =
        data?.card?.displayImageUrl ||
        (cardExtInfo as LivePhotoExtInfo | ReimagineExtInfo)
          ?.videoCoverImgUrl ||
        (cardExtInfo as OtakudanceExtInfo)?.coverImage?.url;

      return url;
    }, [data.card]);

    const realSource = showVideo && isFocused ? videoSource : void 0;

    const onPosterReady = useMemoizedFn(() => {
      setLastLoadPoster(posterSource);
      onReady?.();
    });

    const onVideoReady = useMemoizedFn(() => {
      setLastLoadVideo(realSource?.uri);
      onReady?.();
    });

    useEffect(() => {
      if (realSource) {
        videoRef?.current?.playAsync();
      } else {
        videoRef?.current?.stopAsync();
      }
    }, [realSource]);

    return (
      <View
        style={{
          opacity: 1,
          width: '100%',
          height: '100%',
          position: 'relative'
        }}
      >
        <PosterComponent
          source={posterSource}
          style={{
            position: 'absolute',
            opacity:
              Boolean(lastLoadPoster) && lastLoadPoster !== posterSource ? 0 : 1
          }}
          onReady={onPosterReady}
        />
        {Boolean(videoSource) && !isError ? (
          <Video
            ref={videoRef}
            source={realSource}
            shouldPlay={true}
            isMuted={true}
            onPlaybackStatusUpdate={onPlaybackStatusUpdate}
            usePoster
            // posterSource={posterSource}
            // PosterComponent={PosterComponent}
            // status={{
            //   androidImplementation: 'MediaPlayer'
            // }}
            onError={error => {
              errorReport(
                '[cellCard] 视频播放错误',
                ReportError.COMPONENTS,
                error
              );
              setIsError(true);
            }}
            style={{
              width: '100%',
              height: '100%',
              opacity: lastLoadVideo !== realSource?.uri ? 0 : 1
            }}
            isLooping={true}
            resizeMode={ResizeMode.COVER}
            onLoad={onVideoReady}
          />
        ) : null}
      </View>
    );
  }
);

export default memo(VideoRender);
