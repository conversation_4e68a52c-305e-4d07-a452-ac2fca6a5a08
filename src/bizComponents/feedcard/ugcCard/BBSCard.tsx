import { useMemoizedFn } from 'ahooks';
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {
  Animated,
  GestureResponderEvent,
  Pressable,
  StyleProp,
  View,
  ViewStyle
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Text } from '@/src/components';
import { showToast } from '@/src/components';
import { SimpleCommentRichText } from '@/src/components/comment/SimpleCommentRichText';
import { EVALUATION_IMAGE_COUNT } from '@/src/components/waterfall/constant';
import { FALLBACK_POST_IMAGE } from '@/src/constants';
import { useAuthState } from '@/src/hooks/useAuthState';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useTeenModeGuard } from '@/src/hooks/useTeenModeGuard';
import { useBBSStore } from '@/src/store/bbs';
import { useRandomAvatarStore } from '@/src/store/randomAvatar';
import { darkTheme, typography } from '@/src/theme';
import { $relative } from '@/src/theme/variable';
import { $CARD_STYLES } from '@/src/theme/variable';
import { GameType, RichCardInfo } from '@/src/types';
import { dp2px } from '@/src/utils';
import { StyleSheet, fixedPx } from '@/src/utils/StyleSheet';
import { getInitialPostData, shouldShowFeedDiscuss } from '@/src/utils/bbs';
import { globalCache } from '@/src/utils/cache/cacheEvent';
import { formatDate } from '@/src/utils/opt/transDate';
import { formatNumber } from '@/src/utils/opt/transNum';
import { reportClick } from '@/src/utils/report';
import { PostDetailSource } from '@/src/utils/report/type';
import { adaptVoteInfo } from '@/src/utils/voteUtils';
import { Image } from '@Components/image';
import { useCurrentPageKey } from '../../../hooks/useCurrentPageKey';
import { useAuthStore } from '../../../store/authInfo';
import { Theme } from '../../../theme/colors/type';
import { DislikeGuide } from '../cellcard/DislikeGuide';
import { useDislikeFeedback, useLayoutSize } from '../cellcard/utils';
import { CellCardScene, ICellCardProps } from '../types';
import { BBSPostType } from '@step.ai/proto-gen/raccoon/common/bbs_pb';
import { useShallow } from 'zustand/react/shallow';
import { DiscussBar } from './DiscussBar';
import { BBSVoteCard } from './VoteCard';
import CardUserInfo from './cardUserInfo';

function BBSNormalCard(props: {
  data: ICellCardProps['data'];
  onCardReady?: () => void;
}) {
  const { data, onCardReady } = props;
  const hasDiscuss =
    data.card?.cardExtInfo?.value?.case === 'bbs' &&
    shouldShowFeedDiscuss(data.card?.cardExtInfo?.value?.value);

  return (
    <>
      <View style={[$relative, styles.$imageContainer, $CARD_STYLES]}>
        <Image
          style={{
            width: '100%',
            aspectRatio: 1
          }}
          source={data.card?.displayImageUrl || ''}
          // 对文字图片压缩没有用
          tosSize={undefined}
          onLoad={onCardReady}
        />
        {hasDiscuss && (
          <DiscussBar
            cardId={data.card?.id}
            hideAvatar
            heatScore={
              data.card?.cardExtInfo?.value?.case === 'bbs'
                ? data?.card?.cardExtInfo?.value?.value?.heatScore
                : undefined
            }
            style={{
              margin: 10,
              marginTop: 0
            }}
          />
        )}
      </View>

      {/* <View style={[styles.$cardInfo, styles.$cardInfoDark]}> */}
      {/* <CardUserInfo
        data={data}
        onLeave={onLeave}
        reportParams={reportParams}
        theme={Theme.DARK}
        // containerStyle={{
        //   marginTop: hasDiscuss ? 0 : dp2px(10)
        // }}
        containerStyle={{
          height: 16,
          marginTop: dp2px(14),
          paddingHorizontal: 10
        }}
      /> */}
      {/* </View> */}
    </>
  );
}

function FeedTransferCard(props: {
  data: ICellCardProps['data'];
  onCardReady?: () => void;
}) {
  const { data, onCardReady } = props;
  const formattedTime = useMemo(
    () => formatDate(Number(data.card?.createAt) * 1000),
    [data.card?.createAt]
  );
  const { value } = data.card?.cardExtInfo || {};

  const [useFallbackImage, setUseFallbackImage] = useState(
    value?.case === 'bbs' &&
      'referPostInfo' in value.value &&
      !value.value.referPostInfo?.title
  );

  useEffect(() => {
    onCardReady?.();
  }, []);

  if (value?.case !== 'bbs') return null;
  const post = value.value;
  if (!post) return null;

  return (
    <View style={styles.$contentContainer}>
      <View>
        <View style={styles.$contentHeader}>
          <Text style={styles.$title} numberOfLines={3}>
            {post?.content?.trim() ?? ''}
          </Text>
          <View style={styles.$timeContainer}>
            <Text style={styles.$timeText}>转发于{formattedTime}</Text>
          </View>
        </View>
        <View style={styles.$transferContentBox}>
          <Text style={styles.$transferText} numberOfLines={2}>
            {post.referPostInfo?.title?.trim() || '原贴已被删除'}
          </Text>
          {useFallbackImage ? (
            <View
              style={[
                styles.$transferImage,
                {
                  backgroundColor: StyleSheet.darkTheme.background.input,
                  alignItems: 'center',
                  justifyContent: 'center'
                }
              ]}
            >
              <Image
                source={FALLBACK_POST_IMAGE}
                style={{
                  width: 18,
                  height: 18
                }}
                native
                contentFit="cover"
                tosSize="size12"
              />
            </View>
          ) : (
            <Image
              source={post.referPostInfo?.imageUrl}
              style={styles.$transferImage}
              contentFit="cover"
              tosSize="size10"
              onError={() => setUseFallbackImage(true)}
            />
          )}
        </View>
      </View>

      {/* <CardUserInfo
        data={data}
        onLeave={props.onLeave}
        reportParams={props.reportParams}
        theme={Theme.DARK}
        containerStyle={{
          height: 16,
          marginTop: dp2px(14),
          paddingHorizontal: 10
        }}
      /> */}
    </View>
  );
}

// UGC 卡片已经在 src/bizComponents/feedcard/ugcCard/index.tsx 中 memo 化
export function BBSCard(
  props: ICellCardProps & {
    containerStyle?: StyleProp<ViewStyle>;
    getUgcReportParams?: () => Record<
      string,
      string | boolean | number | undefined
    >;
  }
) {
  const {
    index,
    data,
    onLeave,
    getUgcReportParams,
    containerStyle: $containerStyle,
    onCardReady,
    scene
  } = props;
  const { go2Detail } = useChangeRoute();
  const { card } = data;
  const currentPageKey = useCurrentPageKey();

  // 如果提供了自定义的 getUgcReportParams 就使用它，否则使用我们创建的函数
  const reportParams = useMemo(() => {
    const params = getUgcReportParams?.() || {};
    // 根据场景设置 from 属性
    if (scene && !params.from) {
      const fromSource = getBBSCardReportSource(scene, currentPageKey).from;
      if (fromSource) {
        params.from = fromSource;
      }
    }
    return params;
  }, [getUgcReportParams, scene, currentPageKey]);

  const { teenModeGuard } = useTeenModeGuard();
  const { sizeRef, onLayout } = useLayoutSize();
  const dislikeFeedback = useDislikeFeedback(data, sizeRef, props.scene);

  const onPress = useMemoizedFn((e: GestureResponderEvent) => {
    if (!teenModeGuard()) return;

    globalCache.set('lastTouchPoint', {
      x: e.nativeEvent.pageX - e.nativeEvent.locationX,
      y: e.nativeEvent.pageY - e.nativeEvent.locationY
    });

    reportClick('content_button', {
      ...reportParams
    });

    if (!card || !card.id) return;

    onLeave?.();
    go2Detail({
      gameType: GameType.BBS,
      gameParams: {
        cardId: card.id,
        data: getInitialPostData(data)
      },
      // @ts-expect-error 上报参数
      routeParams: {
        discuss_tab: reportParams?.discuss_tab as string,
        hot_comment: reportParams?.hot_comment as number,
        ...(reportParams?.from ? { from: reportParams.from } : {})
      }
    });
    return;
  });

  const onBBSCardReady = useMemoizedFn(() => {
    if (index !== undefined && index < EVALUATION_IMAGE_COUNT) {
      onCardReady?.(index);
    }
  });

  const renderCardContent = useMemo(() => {
    const { value } = data.card?.cardExtInfo || {};
    if (value?.case !== 'bbs') return null;
    const postType = value?.value?.postType;
    if (
      postType === BBSPostType.BBS_POST_TYPE_TEXT ||
      postType === BBSPostType.BBS_POST_TYPE_TEXT_AND_IMAGE
    ) {
      return <BBSNormalCard data={data} onCardReady={onBBSCardReady} />;
    }
    if (postType === BBSPostType.BBS_POST_TYPE_TRANSFER) {
      return <FeedTransferCard data={data} onCardReady={onBBSCardReady} />;
    }
    if (postType === BBSPostType.BBS_POST_TYPE_VOTE) {
      return (
        <BBSVoteCard
          data={data}
          onCardPress={onPress}
          onCardReady={onBBSCardReady}
        />
      );
    }
  }, [data?.card?.id]);

  const commentLines = useMemo(
    () => getBbsCommentLines(data as RichCardInfo),
    [data]
  );

  const renderComment = useCallback(() => {
    return (
      Boolean(commentLines) && (
        <View
          style={{
            height: 20 + commentLines * 18,
            padding: 10,
            backgroundColor: StyleSheet.darkTheme.background.tag
          }}
        >
          <Text
            style={{
              color: StyleSheet.darkTheme.text.primary,
              lineHeight: 18,
              fontSize: 12
            }}
            key={data.comment?.commentId || 'CommentRichTextWrapper'}
            numberOfLines={commentLines}
          >
            <SimpleCommentRichText
              key={'CommentRichText-' + data.comment?.commentId}
              comment={data.comment?.content}
              normalTextstyle={{
                fontSize: 12,
                lineHeight: 18,
                color: StyleSheet.darkTheme.text.disabled
              }}
              prefix={
                <Text
                  key={'CommentRichTextPrefix-' + data.comment?.commentId}
                  style={{
                    fontFamily: typography.fonts.feed,
                    lineHeight: 18,
                    color: darkTheme.text.primary,
                    fontSize: 12
                  }}
                >
                  {`热评 `}
                </Text>
              }
            />
          </Text>
        </View>
      )
    );
  }, [data.comment?.content, commentLines]);

  return (
    <Pressable
      style={[styles.$cardContainer, $CARD_STYLES, { flex: 1 }]}
      onPress={onPress}
      onLayout={onLayout}
      onLongPress={dislikeFeedback}
      delayLongPress={1000}
    >
      <View
        style={{
          borderBottomLeftRadius: $CARD_STYLES.borderRadius,
          borderBottomRightRadius: $CARD_STYLES.borderRadius,
          overflow: 'hidden'
        }}
      >
        <View
          style={{
            position: 'relative',
            backgroundColor: StyleSheet.darkTheme.background.transparent
            // backgroundColor: 'red'
          }}
        >
          <LinearGradient
            colors={['rgba(22, 22, 26, 0)', 'rgba(22, 22, 26, 0.4)']}
            style={{
              position: 'absolute',
              height: 100,
              width: '100%',
              bottom: 0,
              zIndex: -1
            }}
          />
          {renderCardContent}
        </View>

        {renderComment()}
      </View>
      <CardUserInfo
        data={data}
        onLeave={props.onLeave}
        reportParams={props.reportParams}
        theme={Theme.DARK}
        containerStyle={{
          height: 16,
          marginTop: 10,
          paddingHorizontal: 8
        }}
      />
      <DislikeGuide
        size={sizeRef.current}
        onLongPress={dislikeFeedback}
        index={props.index}
        eventBus={props.eventBus}
        createrUid={data?.user?.uid}
      />
    </Pressable>
  );
}

// 这个函数用于获取根据当前场景确定的上报参数
export const getBBSCardReportSource = (
  scene: CellCardScene,
  currentPageKey?: string
) => {
  let fromSource: PostDetailSource | undefined;
  switch (scene) {
    case CellCardScene.HOME:
    case CellCardScene.FOLLOW:
      fromSource = PostDetailSource.FEED;
      break;
    case CellCardScene.DISCUSS: {
      if (currentPageKey?.startsWith('feed')) {
        fromSource = PostDetailSource.BBS_FEED;
      }
      break;
    }
    case CellCardScene.SEARCH:
      fromSource = PostDetailSource.SEARCH;
      break;
    case CellCardScene.MY:
    case CellCardScene.LIKE:
      fromSource = PostDetailSource.USER;
      break;
    case CellCardScene.IP:
    case CellCardScene.TOPIC_WORLD:
      fromSource = PostDetailSource.TAG;
      break;
    case CellCardScene.ROLE_COLLECTION:
      fromSource = PostDetailSource.CHARACTER_INFO_SHOW;
      break;
    default:
      fromSource = PostDetailSource.UNKNOWN;
      break;
  }

  return {
    from: fromSource
  };
};

export const BBS_HOT_COMMENT = 13;

export const getBbsCommentLines = (data: RichCardInfo) => {
  if (!data.comment?.content?.length) return 0;
  return Number(data.comment?.content?.length) > BBS_HOT_COMMENT ? 2 : 1;
};

const styles = StyleSheet.create({
  $cardContainer: {
    // backgroundColor: StyleSheet.darkTheme.background.transparent,
    overflow: 'hidden'
  },
  $cardInfo: {
    paddingTop: 10,
    paddingHorizontal: 10
  },
  $cardInfoDark: {
    paddingBottom: 10
  },
  $imageContainer: {
    overflow: 'hidden'
    // backgroundColor: StyleSheet.darkTheme.background.transparent
  },
  $discussContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: fixedPx(6),
    paddingRight: fixedPx(10),
    marginTop: fixedPx(12),
    marginBottom: fixedPx(12),
    alignSelf: 'flex-start',
    backgroundColor: StyleSheet.darkTheme.background.toast,
    height: fixedPx(26),
    borderRadius: fixedPx(13)
  },
  $avatarContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  $discussAvatar: {
    width: fixedPx(14),
    height: fixedPx(14),
    borderRadius: fixedPx(7),
    borderColor: StyleSheet.darkTheme.background.card,
    borderWidth: 1
  },
  $discussText: {
    color: StyleSheet.darkTheme.text.primary,
    fontSize: 12,
    fontFamily: typography.fonts.baba.medium,
    marginLeft: fixedPx(4)
  },
  $title: {
    fontSize: 13,
    lineHeight: fixedPx(18),
    color: StyleSheet.darkTheme.text.primary,
    fontFamily: typography.fonts.pingfangSC.normal,
    fontWeight: '600'
  },
  $contentContainer: {
    paddingHorizontal: 10,
    paddingTop: 18,
    paddingBottom: 16
  },
  $contentHeader: {
    // paddingHorizontal: 6
    // backgroundColor: 'red'
  },
  $transferContentBox: {
    marginTop: 14,
    paddingHorizontal: 8,
    backgroundColor: StyleSheet.darkTheme.background.toast,
    borderRadius: 8,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 8,
    height: 52
  },
  $transferText: {
    flex: 1,
    fontSize: 11,
    lineHeight: fixedPx(18),
    color: StyleSheet.darkTheme.text.secondary,
    fontWeight: '400',
    fontFamily: typography.fonts.pingfangSC.normal
  },
  $transferImage: {
    width: 36,
    height: 36,
    borderRadius: 4
  },
  $timeContainer: {
    marginTop: 4
  },
  $timeText: {
    fontSize: 11,
    lineHeight: fixedPx(14),
    color: StyleSheet.darkTheme.text.disabled
  }
});
