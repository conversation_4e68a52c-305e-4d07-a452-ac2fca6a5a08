import { useMemoizedFn } from 'ahooks';
import React, { useMemo, useRef } from 'react';
import {
  Dimensions,
  Platform,
  Pressable,
  StyleProp,
  Text,
  TextStyle,
  View,
  ViewStyle
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Image, showToast } from '@/src/components';
import { EntryMediaList } from '@/src/components/publishEntry/constant';
import { useChangeRoute } from '@/src/hooks/useChangeRoute';
import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';
import { typography } from '@/src/theme';
import { CardType, GameTemplateExtInfo, GameType } from '@/src/types';
import { StyleSheet, dp2px } from '@/src/utils';
import { formatNumber } from '@/src/utils/opt/transNum';
import { reportClick } from '@/src/utils/report';
import { DislikeGuide } from '../cellcard/DislikeGuide';
import { useDislikeFeedback, useLayoutSize } from '../cellcard/utils';
import { ICellCardProps } from '../types';
import VideoRender from '../ugcCard/VideoRender';

const DEVICE_WIDTH = Dimensions.get('window').width;
const cardWidth = (DEVICE_WIDTH - 14.5) / 2;
const posterWidth = cardWidth - dp2px(10);

const ICON_CAMERA = require('@Assets/icon/icon-camera-transparent.png');
const ICON_PLAY = require('@Assets/icon/icon-play-game.png');

// 玩法卡片已经在 src/bizComponents/feedcard/entryCard/index.tsx 中 memo 化
export const GameTemplateCard = (
  props: ICellCardProps & {
    containerStyle?: StyleProp<ViewStyle>;
  }
) => {
  const {
    data,
    index,
    onLeave,
    reportParams,
    eventBus,
    containerStyle: $containerStyle
  } = props;

  const { gameType, displayImageUrl, displayVideoUrl } = data.card || {};
  const { isLowEndDevice } = useDevicePerformance();
  const templateInfo = data.card?.imitationCardInfo
    ?.value as GameTemplateExtInfo;
  const {
    templateId,
    templateName,
    playUserCnt,
    bgImageUrl,
    bgImageHeight,
    bgImageWidth
  } = templateInfo || {};

  const ratio =
    bgImageHeight && bgImageWidth ? bgImageHeight / bgImageWidth : 1;
  const posterHeight = cardWidth * ratio + 5 - dp2px(25 + 10);
  const backupInfo = gameType ? EntryMediaList[gameType] : undefined;

  const isInstance = gameType === GameType.INSTANCE;

  const videoRef = useRef<{ stop: () => Promise<void> }>(null);

  const { go2Create } = useChangeRoute();
  const { sizeRef, onLayout } = useLayoutSize();
  const dislikeFeedback = useDislikeFeedback(data, sizeRef, props.scene);

  const colors = useMemo(
    () => backupInfo?.gradientList || ['#FE4C5E', '#FE9678'],
    [backupInfo]
  );

  const onPress = useMemoizedFn(async () => {
    reportClick('templatecard', {
      module: 'feed',
      gameType,
      cardType: CardType.GAME_TEMPLATE,
      templateName,
      templateId,
      ...reportParams
    });

    if (gameType) {
      onLeave?.();
      await videoRef.current?.stop();
      go2Create({
        gameType,
        gameParams: {
          templateId: templateId || '',
          stayPlayground: true
        }
      });
    } else {
      showToast('哎呀出错啦');
    }
  });

  return (
    <Pressable
      onPress={onPress}
      onLayout={onLayout}
      delayLongPress={1000}
      onLongPress={dislikeFeedback}
      style={[
        {
          height: '100%',
          width: '100%',
          borderRadius: 10,
          overflow: 'hidden'
        },
        $containerStyle
      ]}
    >
      {/* background */}
      {bgImageUrl ? (
        <Image
          source={bgImageUrl}
          tosSize="size3"
          style={{
            height: '100%',
            width: '100%',
            resizeMode: 'stretch'
          }}
        />
      ) : (
        <LinearGradient
          colors={colors}
          start={{ x: 0, y: 1 }}
          end={{ x: 1, y: 1 }}
          style={{
            borderRadius: 10,
            height: '100%',
            width: '100%'
          }}
        >
          <View style={$backup}>
            <Text style={$backupTitle}>{backupInfo?.title}</Text>
            <Image
              source={backupInfo?.icon}
              style={{
                height: dp2px(16),
                width: dp2px(16)
              }}
            />
          </View>
        </LinearGradient>
      )}

      {/* participant info */}
      <View style={$participant}>
        <Text numberOfLines={1} style={$participantText}>
          {playUserCnt ? formatNumber(playUserCnt) : '- '}人正在玩
        </Text>
      </View>

      <View
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: cardWidth * ratio + 5 - dp2px(30),
          width: cardWidth
        }}
      >
        {/* content */}
        <View
          style={{
            flex: 1,
            alignItems: 'center'
          }}
        >
          {displayVideoUrl && !isLowEndDevice ? (
            <View
              style={{
                height: posterHeight,
                width: posterWidth,
                borderRadius: 10,
                overflow: 'hidden'
              }}
            >
              <VideoRender
                data={data}
                ref={videoRef}
                index={index}
                eventBus={eventBus}
              />
            </View>
          ) : (
            <Image
              source={displayImageUrl}
              tosSize="size3"
              style={{
                height: posterHeight,
                width: posterWidth,
                borderRadius: 10,
                resizeMode: 'cover'
              }}
            />
          )}
        </View>

        {/* mask */}
        <LinearGradient
          colors={['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.4)']}
          start={{ x: 1, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            height: 72,
            width: '100%'
          }}
        ></LinearGradient>

        {/* tag */}
        <LinearGradient
          colors={colors}
          start={{ x: 0, y: 1 }}
          end={{ x: 1, y: 1 }}
          style={$tag}
        >
          <Image
            style={{ height: 18, width: 18, top: -2 }}
            source={isInstance ? ICON_PLAY : ICON_CAMERA}
          />
          <Text style={$text}>{isInstance ? '开始闯关' : '使用模版'}</Text>
        </LinearGradient>
      </View>
      <DislikeGuide
        size={sizeRef.current}
        onLongPress={dislikeFeedback}
        index={props.index}
        eventBus={props.eventBus}
        createrUid={data?.user?.uid}
      />
    </Pressable>
  );
};

const $participant: ViewStyle = {
  position: 'absolute',
  top: 0,
  left: dp2px(100),
  ...Platform.select({
    ios: {
      height: dp2px(24)
    },
    android: {
      height: dp2px(22)
    }
  }),
  right: 0,
  flexDirection: 'row',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center'
};

const $participantText: TextStyle = {
  fontSize: 10,
  color: StyleSheet.darkTheme.text.secondary
};
const $tag: ViewStyle = {
  backgroundColor: 'rgba(247, 247, 247, 1)',
  borderRadius: 50,
  height: dp2px(28),
  width: dp2px(92),
  position: 'absolute',
  bottom: dp2px(11),
  right: dp2px(11),
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  gap: 2
};

const $text: TextStyle = {
  color: StyleSheet.darkTheme.background.page,
  fontSize: 12,
  lineHeight: 20,
  fontWeight: '600'
};

const $backup: ViewStyle = {
  height: dp2px(22),
  width: '50%',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  paddingLeft: 16
};

const $backupTitle: TextStyle = {
  fontFamily: typography.fonts.baba.heavy,
  fontSize: 14,
  lineHeight: 24
};
