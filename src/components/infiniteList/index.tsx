import {
  useDebounce,
  useDebounceFn,
  useMemoizedFn,
  useWhyDidYouUpdate
} from 'ahooks';
import {
  ForwardedRef,
  LegacyRef,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react';
import {
  ActivityIndicator,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Pressable,
  RefreshControl,
  ScrollView,
  StyleProp,
  View,
  ViewStyle
} from 'react-native';
import Animated, {
  SharedValue,
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import {
  BaseLayoutProvider,
  DataProvider,
  LayoutProvider,
  RecyclerListView
} from 'recyclerlistview-masonrylayoutmanager';
import {
  BottomSheetContext,
  BottomSheetScrollView
} from '@/src/bizComponents/nestedScrollView';
import { CommonColor } from '@/src/theme/colors/common';
import { Theme } from '@/src/theme/colors/type';
import { StyleSheet, getScreenSize, isIos } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { Text } from '@Components/text';
import { EmptyPlaceHolder } from '../Empty';
import { WaterFallCardData } from '../waterfall/type';
import { useIsFocused } from '@react-navigation/native';
import { ScrollEvent } from 'recyclerlistview-masonrylayoutmanager/dist/reactnative/core/scrollcomponent/BaseScrollView';
import { CustomNestedInnerScrollView } from './CustomNestedInnerScrollView';
import { CustomScrollView } from './CustomScrollView';
import {
  DataProviderItemType,
  ICustomScrollViewProps,
  IInfiniteListProps,
  InfiniteListRef,
  RequestScene
} from './typing';

const SCROLL_REQUEST_THRESHOLD = getScreenSize('height') * 2;
const SCROLL_LOCK_THRESHOLD = getScreenSize('height');
const SCROLL_REQUEST_BOTTOM_DISTANCE = getScreenSize('height') * 2;
// const SCROLL_REQUEST_BOTTOM_DISTANCE = 10;
const SCROLL_THROTTLE = 16;

const getEmptyLayoutProvider = () =>
  new LayoutProvider(
    () => {
      return 'Empty';
    },
    (type, dim) => {
      dim.width = getScreenSize('width');
      dim.height = 1;
    }
  );

function rowHasChanged<T>(
  r1: DataProviderItemType<T>,
  r2: DataProviderItemType<T>
) {
  const r1Data = r1 as WaterFallCardData;
  const r2Data = r2 as WaterFallCardData;

  // AsyncCardInfo 没有 card 属性，兜底
  if (!r1Data?.card || !r2Data?.card) {
    return r1Data !== r2Data;
  }

  // 检查 card.id 是否不同
  const idChanged = r1Data?.card?.id !== r2Data?.card?.id;
  if (idChanged) {
    return true;
  }

  // 检查 socialStat 是否变化（点赞数或点赞状态）
  const likeCountChanged =
    r1Data?.socialStat?.beingLikeds !== r2Data?.socialStat?.beingLikeds;
  const likeStatusChanged =
    r1Data?.socialStat?.liked !== r2Data?.socialStat?.liked;
  if (likeCountChanged || likeStatusChanged) {
    return true;
  }

  // 如果以上都没有变化，则认为 Row 未改变
  return false;
}

function InfiniteListView<T>(
  props: IInfiniteListProps<T>,
  ref: ForwardedRef<InfiniteListRef>
) {
  const {
    data,
    loading,
    error,
    hasMore,
    showLoadingWhenHasMoreEmpty,
    footerStyle: $customFooterStyle,
    getLayoutProvider,
    onRequest,
    enablePullRefresh = true,
    isActive = true
  } = props;

  const isFocused = useIsFocused();
  const isInit = useRef(true);
  const dataRef = useRef<T[]>([]);

  const [dataProvider, setDataProvider] = useState(
    new DataProvider(
      rowHasChanged /*,
      index => {
        const d = dataRef.current?.[index];
        return dataRef[index]?.card?.imitationCardInfo.case ===
          'gameTemplateInfo'
          ? d?.card?.imitationCardInfo.value.templateId + index
          : d?.card?.id || d?.cardId || index;
      }*/
    ).cloneWithRows(props.data || [])
  );
  const [layoutProvider, setLayoutProvider] = useState<BaseLayoutProvider>(
    getLayoutProvider(dataRef)
  );
  const [refreshing, setRefreshing] = useState(false);
  const [manuallyRefreshing, setManuallyRefreshing] = useState(false);
  const [manuallyScrolling, setManuallyScrollingState] = useState(false);

  const scrollY = useRef<number>(0);
  const scrollViewRef = useRef<ScrollView | null>(null);
  const needRestoreFirstItemExpo = useRef(false);
  const context = useContext(BottomSheetContext);
  const isInNestedScrollView = useRef(context !== null);
  const manuallyScrollTop = useSharedValue<number | undefined>(undefined);
  const hasMoreRef = useRef<boolean>(false);
  const manuallyScrollingRef = useRef<boolean>(false);

  const setManuallyScrolling = useCallback(state => {
    setManuallyScrollingState(state);
    manuallyScrollingRef.current = state;
  }, []);

  const { run: loadMore } = useDebounceFn(
    () => {
      onRequest?.(RequestScene.LOAD_MORE);
    },
    {
      wait: 100,
      leading: true,
      trailing: true
    }
  );

  const { run: setResetTimeout } = useDebounceFn(
    () => {
      setManuallyScrolling(false);
    },
    {
      wait: 2000,
      leading: false,
      trailing: true
    }
  );

  const onScroll = useCallback(
    (rawEvent: ScrollEvent, offsetX: number, offsetY: number) => {
      const { nativeEvent } = rawEvent;
      const total =
        nativeEvent.contentOffset.y +
        (nativeEvent.layoutMeasurement?.height || 0);
      if (
        total + SCROLL_REQUEST_THRESHOLD >
          +(nativeEvent.contentSize?.height || 0) &&
        offsetY > scrollY.current &&
        hasMoreRef.current
      ) {
        loadMore();
      }

      scrollY.current = offsetY;

      if (offsetY <= SCROLL_LOCK_THRESHOLD && manuallyScrollingRef.current) {
        setManuallyScrolling(false);
      }

      props.onScroll?.({
        offsetX,
        offsetY
      });
    },
    []
  );

  const defaultRenderContentContainer = useMemoizedFn(
    (
      contentContainerProps?: { style?: ViewStyle },
      children?: React.ReactNode
    ) => {
      if (
        (loading || (hasMoreRef.current && showLoadingWhenHasMoreEmpty)) &&
        data?.length === 0
      ) {
        return props.renderLoading?.() || <Text>加载中</Text>;
      }

      if (error && data?.length === 0) {
        return (
          <EmptyPlaceHolder
            theme={Theme.DARK}
            style={{ height: 400 }}
            buttonText="刷新"
            button={Boolean(onRequest)}
            onButtonPress={() => onRequest?.(RequestScene.INIT)}
          >
            哎呀，小狸走丢了
          </EmptyPlaceHolder>
        );
      }

      if (data?.length === 0) {
        if (props.renderEmpty) {
          return props.renderEmpty();
        }
        const { children, style, ...rest } = props.customEmptyProps || {};
        return (
          <EmptyPlaceHolder
            style={[{ height: 400 }, style]}
            theme={Theme.DARK}
            {...rest}
          >
            {children || '小狸两手空空哦~'}
          </EmptyPlaceHolder>
        );
      }

      return <View style={[contentContainerProps?.style]}>{children}</View>;
    }
  );

  /**
   * content 容器
   */
  const renderContentContainer = useMemoizedFn(
    (
      contentContainerProps?: { style?: ViewStyle },
      children?: React.ReactNode
    ) => {
      if (props.renderContentContainer) {
        return props.renderContentContainer(contentContainerProps, props =>
          defaultRenderContentContainer(props, children)
        );
      } else {
        return defaultRenderContentContainer(contentContainerProps, children);
      }
    }
  );

  const onReachListEnd = useCallback(() => {
    if (hasMoreRef.current) {
      loadMore();
    }
  }, []);

  /**
   * 页脚元素
   * @returns
   */
  const renderFooter = useMemoizedFn(() => {
    if (props.renderFooter) {
      return props.renderFooter();
    }

    const hasContent = Boolean(data?.length);

    if (loading && hasContent) {
      return (
        <ActivityIndicator
          size="small"
          style={[{ margin: 10 }, $customFooterStyle]}
          color={StyleSheet.darkTheme.text.primary}
        />
      );
    }
    if (!hasMore && hasContent) {
      return (
        <View
          style={[
            {
              alignItems: 'center',
              marginTop: 12
            },
            $customFooterStyle
          ]}
        >
          <Text style={{ color: StyleSheet.darkTheme.text.secondary }}>
            小狸是有底线的~
          </Text>
        </View>
      );
    }
    if (error && hasContent) {
      return (
        <View
          style={[
            {
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 12
            },
            $customFooterStyle
          ]}
        >
          <Text style={{ color: StyleSheet.darkTheme.text.secondary }}>
            小狸走丢了，
          </Text>
          <Pressable
            onPress={() => {
              onRequest?.(RequestScene.LOAD_MORE);
            }}
          >
            <Text style={{ color: StyleSheet.darkColors.orange.primary }}>
              重试
            </Text>
          </Pressable>
        </View>
      );
    }
    return <View style={[{ height: 30 }, $customFooterStyle]}></View>;
  });

  const renderItem = useMemoizedFn(renderItemWrapper);

  const onVisibleIndicesChangedWrapper = useCallback(
    (
      all: number[], // 所有当前可见的 item index
      now: number[], // 进场 item index
      notNow: number[] // 出场 item index
    ) => {
      if (all.length === 1 && !data?.length) {
        // 实际为empty
        needRestoreFirstItemExpo.current = true;
        props.customListProps?.onVisibleIndicesChanged?.([], [], []);
        return;
      }

      if (needRestoreFirstItemExpo.current) {
        props.customListProps?.onVisibleIndicesChanged?.(
          all,
          [0, ...now],
          notNow
        );
      } else {
        props.customListProps?.onVisibleIndicesChanged?.(all, now, notNow);
      }
      needRestoreFirstItemExpo.current = false;
    },
    []
  );

  const onRefresh = useMemoizedFn(async (isManual?: boolean) => {
    try {
      if (isIos && isManual) {
        setManuallyRefreshing(true);
      } else {
        setRefreshing(true);
      }
      await onRequest?.(RequestScene.REFRESHING);
    } finally {
      setRefreshing(false);
      setManuallyRefreshing(false);
    }
    setLayoutProvider(getLayoutProvider(dataRef));
  });

  // const layoutProvider = useMemo(() => {
  //   return getLayoutProvider(dataRef);
  // }, []);

  useEffect(() => {
    dataRef.current = data || [];
    if (data?.length) {
      const source = dataProvider.cloneWithRows(data);
      setDataProvider(source);
      if (refreshing || manuallyRefreshing) {
        setLayoutProvider(getLayoutProvider(dataRef));
      }
    } else {
      const placeholderData = [{ type: 'empty' }];
      const source = dataProvider.cloneWithRows(placeholderData);
      setDataProvider(source);
      // setLayoutProvider(getEmptyLayoutProvider());
    }
  }, [data, manuallyRefreshing, refreshing]);

  useEffect(() => {
    hasMoreRef.current = hasMore || false;
  }, [hasMore]);

  useEffect(() => {
    if (data?.length) {
      if (
        isInit.current &&
        data.length === 1 &&
        needRestoreFirstItemExpo.current
      ) {
        // 补充曝光事件
        onVisibleIndicesChangedWrapper([0], [], []);
      }
      isInit.current = false;
    } else {
      isInit.current = true;
    }
  }, [data]);

  useImperativeHandle(
    ref,
    () => ({
      scrollTop: () => {
        if (scrollY.current !== 0) {
          manuallyScrollTop.value = 0;
          setManuallyScrolling(true);
          setResetTimeout();
        }
        scrollViewRef.current?.scrollTo(0, 0, true);
      },
      forceRefresh: async (animation = true) => {
        console.log('forceRefresh', animation, onRefresh);
        if (scrollY.current !== 0) {
          manuallyScrollTop.value = 0;
          setManuallyScrolling(true);
          setResetTimeout();
        }
        await onRefresh(true);
        scrollViewRef.current?.scrollTo(0, 0, animation);
      },
      scrollToY: y => {
        if (scrollY.current !== 0) {
          manuallyScrollTop.value = 0;
          setManuallyScrolling(true);
          setResetTimeout();
        }
        scrollViewRef.current?.scrollTo(0, 5, true);
      }
    }),
    []
  );

  return (
    <RecyclerListView
      extendedState={props.extendedState}
      renderAheadOffset={250}
      onEndReached={onReachListEnd}
      onEndReachedThreshold={SCROLL_REQUEST_BOTTOM_DISTANCE}
      dataProvider={dataProvider}
      layoutProvider={layoutProvider}
      rowRenderer={renderItem}
      renderFooter={renderFooter}
      renderContentContainer={renderContentContainer}
      scrollThrottle={SCROLL_THROTTLE}
      onScroll={onScroll}
      // @ts-ignore
      externalScrollView={
        props?.externalScrollView
          ? props?.externalScrollView
          : isInNestedScrollView.current
            ? CustomNestedInnerScrollView
            : CustomScrollView
      }
      scrollViewProps={{
        showsVerticalScrollIndicator: false,
        refreshing,
        onRefresh: enablePullRefresh ? onRefresh : undefined,
        ...props.scrollViewProps,
        ref: (node: ScrollView) => {
          scrollViewRef.current = node;
          if (props.scrollViewProps?.ref !== undefined) {
            props.scrollViewProps.ref.current = node;
          }
        },
        isActive,
        manuallyScrollTop,
        manuallyRefreshing,
        manuallyScrolling,
        hideRefresh: props?.scrollViewProps?.hideRefresh || false,
        lockScroll: props?.scrollViewProps?.lockScroll || false,
        scrollViewName: props?.scrollViewProps?.scrollViewName || ''
      }}
      {...props.customListProps}
      canChangeSize={isFocused ? props.customListProps?.canChangeSize : false}
      onVisibleIndicesChanged={onVisibleIndicesChangedWrapper}
      errorHandler={e => {
        errorReport('RecyclerListViewInnerError', ReportError.COMPONENTS, e);
      }}
    />
  );

  function renderItemWrapper(
    type: string | number,
    data: T,
    index: number,
    extendedState?: object
  ) {
    // tricky：外部有需要判断 item 所处的 column index，因此这里暴露 item 的 layout 信息
    // @ts-ignore
    const { x, y, width, height } = this || {};
    return props.renderItem(type, data, index, extendedState, {
      x,
      y,
      width,
      height
    });
  }
}

export const InfiniteList = forwardRef(InfiniteListView) as <T>(
  props: IInfiniteListProps<T> & {
    ref?: ForwardedRef<InfiniteListRef>;
  }
) => ReturnType<typeof InfiniteListView>;
