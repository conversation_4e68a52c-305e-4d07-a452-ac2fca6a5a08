import * as React from 'react';
import {
  ColorValue,
  Image as NativeImage,
  StyleProp,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle
} from 'react-native';
import { colorsUI } from '@/src/theme';
import { Image, ImageStyle } from '@Components/image';
import { ViewProps } from 'react-native-svg/lib/typescript/fabric/utils';

export type IconTypes = keyof typeof iconRegistry;

const sizeMap: Record<string, { containerSize: number; size: number }> = {
  xs: {
    size: 16,
    containerSize: 24
  },
  sm: {
    size: 20,
    containerSize: 32
  },
  md: {
    size: 24,
    containerSize: 40
  },
  lg: {
    size: 32,
    containerSize: 52
  }
};

export type sizeKeys = keyof typeof sizeMap;

export interface IconProps extends TouchableOpacityProps {
  icon: IconTypes;

  preset?: 'circle' | 'default';

  color?: ColorValue;

  native?: boolean;

  size?: number | sizeKeys;

  /**
   * Style overrides for the icon image
   */
  style?: StyleProp<ImageStyle>;

  /**
   * Style overrides for the icon container
   */
  containerStyle?: StyleProp<ViewStyle>;

  onPress?: TouchableOpacityProps['onPress'];
}
export function Icon(props: IconProps) {
  const {
    icon,
    color,
    size = 24,
    style: $imageStyleOverride,
    containerStyle: $containerStyleOverride,
    preset = 'default',
    native,
    ...WrapperProps
  } = props;

  const isPressable = !!WrapperProps.onPress;
  const Wrapper = WrapperProps.onPress
    ? TouchableOpacity
    : (View as React.ComponentType<TouchableOpacityProps>);

  let iconSize = 24;
  let containerSize = 0;
  if (typeof size !== 'number') {
    iconSize = sizeMap[size].size;
    containerSize = sizeMap[size].containerSize;
  } else {
    iconSize = size;
    containerSize =
      iconSize + (typeof props.hitSlop === 'number' ? props.hitSlop : 16);
  }

  return (
    <Wrapper
      accessibilityRole={isPressable ? 'imagebutton' : undefined}
      {...WrapperProps}
      style={[
        preset === 'circle' && {
          backgroundColor: color || colorsUI.Text.brand.brand,
          width: containerSize,
          height: containerSize,
          alignContent: 'center',
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: containerSize / 2
        },
        $containerStyleOverride
      ]}
    >
      {native ? (
        <NativeImage
          style={[
            $imageStyle,
            { tintColor: preset === 'circle' ? '#fff' : color },
            iconSize ? { width: iconSize, height: iconSize } : {},
            $imageStyleOverride
          ]}
          source={iconRegistry[icon]}
        />
      ) : (
        <Image
          transition={null}
          style={[
            $imageStyle,
            { tintColor: preset === 'circle' ? '#fff' : color },
            iconSize ? { width: iconSize, height: iconSize } : {},
            $imageStyleOverride
          ]}
          source={iconRegistry[icon]}
        />
      )}
    </Wrapper>
  );
}

export const iconRegistry = {
  materialize: require('@Assets/icon/materize-icon.png'),
  add_gray_bg: require('@Assets/icon/ip-list-add.png'),
  flip: require('@Assets/icon/makephoto/icon-flip.png'),
  puzzle: require('@Assets/icon/makephoto/puzzle.png'),
  arrow_right: require('@Assets/icon/icon-action-arrow.png'),
  thin_arrow_right: require('@Assets/icon/thin-arrow-right.png'),
  hot: require('@Assets/icon/goods-fire.png'),
  delete_fill: require('@Assets/icon/icon-delete-fill.png'),
  delete_active: require('@Assets/icon/icon-delete-active.png'),
  layers: require('@Assets/icon/icon-layers.png'),
  star: require('@Assets/icon/star.png'),
  keyboard: require('@Assets/icon/makephoto/icon-keyboard.png'),
  confirm_circle: require('@Assets/icon/confirm-circle.png'),
  edit_solid: require('@Assets/icon/edit_solid.png'),
  gesture: require('@Assets/icon/icon_gesture.png'),
  edit_remove: require('@Assets/icon/makephoto/edit_remove.png'),
  edit_down: require('@Assets/icon/makephoto/edit_down.png'),
  template: require('@Assets/icon/makephoto/icon-template.png'),
  text: require('@Assets/icon/makephoto/icon-text.png'),
  sticker: require('@Assets/icon/makephoto/icon-sticker.png'),
  livephoto: require('@Assets/icon/makephoto/icon-livephoto.png'),
  loading: require('@Assets/icon/loading.png'),
  role_star: require('@Assets/icon/role_star_icon.png'),
  write: require('@Assets/icon/write_icon.png'),
  select_highlight: require('@Assets/icon/select_highlight.png'),
  playing_highlight: require('@Assets/icon/playing_highlight.png'),
  ban: require('@Assets/icon/icon_ban.png'),
  like: require('@Assets/icon/like.png'),
  music: require('@Assets/icon/music_icon.png'),
  back: require('@Assets/icon/cref-search/back.png'),
  fail_fill: require('@Assets/icon/fail-fill.png'),
  choose_fill: require('@Assets/icon/choose-fill.png'),
  choose_unfill: require('@Assets/icon/choose-unfill.png'),
  // menu_outline: require("@Assets/icon/menu_outline.png"),
  close_outline: require('@Assets/icon/close_outline.png'),
  close_top: require('@Assets/icon/close-top.png'),
  // addbubble_outline: require("@Assets/icon/addbubble_outline.png"),
  arrow_right_light: require('@Assets/icon/arrow_right_light.png'),
  // chat: require("@Assets/icon/chat.png"),
  more: require('@Assets/icon/more.png'),
  ellipsis_common: require('@Assets/icon/icon-ellipsis-common.png'),
  // fileupload_outline: require("@Assets/icon/fileupload-outline.png"),
  // image_outline: require("@Assets/icon/image-outline.png"),
  up_arrow: require('@Assets/icon/icon-uparrow.png'),
  delete: require('@Assets/icon/delete.png'),
  delete2: require('@Assets/icon/icon-delete.png'),
  delete_image: require('@Assets/icon/icon-delete-image.png'),
  credit_minus: require('@Assets/icon/credit/credit-minus.png'),
  credit_plus: require('@Assets/icon/credit/credit-plus.png'),
  v2_plus: require('@Assets/icon/credit/flash-v2-plus.png'),
  // TODO: minus
  v2_minus: require('@Assets/icon/credit/flash-v2-minus.png'),
  gray_lightning: require('@Assets/icon/credit/gray-lightning.png'),
  green_lightning: require('@Assets/icon/credit/green-lightning.png'),
  lock: require('@Assets/icon/credit/lock.png'),
  setting_account: require('@Assets/icon/setting_account.png'),
  setting_feedback: require('@Assets/icon/setting_feedback.png'),
  setting_share: require('@Assets/icon/setting_share.png'),
  setting_about: require('@Assets/icon/setting_about.png'),
  setting_arrow_right: require('@Assets/icon/setting_arrow_right.png'),
  setting_arrow_right_light: require('@Assets/icon/setting_arrow_right_light.png'),
  setting_arrow_right_bold: require('@Assets/icon/setting_arrow_right_bold.png'),
  right_outline: require('@Assets/icon/right_outline.png'),
  right_outline2: require('@Assets/icon/right_outline2.png'),
  share: require('@Assets/icon/icon-share.png'),
  drop: require('@Assets/icon/icon-drop.png'),
  takephoto: require('@Assets/icon/icon-takephoto.png'),
  takephoto_primary: require('@Assets/icon/icon-takephoto-primary.png'),
  takephoto_reimagine: require('@Assets/icon/icon-reimagine-takephoto.png'),
  close: require('@Assets/icon/icon-close.png'),
  close2: require('@Assets/icon/icon-close2.png'),
  close6: require('@Assets/icon/icon-close6.png'),
  logo: require('@Assets/icon/icon-logo.png'),
  heart: require('@Assets/icon/icon-heart.png'),
  comment: require('@Assets/icon/icon-comment.png'),
  setting: require('@Assets/icon/icon-setting.png'),
  setting_pw: require('@Assets/icon/setting_pw.png'),
  edit: require('@Assets/icon/icon-edit.png'),
  icon_photo_edit: require('@Assets/icon/icon-photo-edit.png'),
  opt_edit: require('@Assets/icon/icon-opt-edit.png'),
  edit_v2: require('@Assets/icon/icon-edit-2.png'),
  report: require('@Assets/icon/icon-report.png'),
  image: require('@Assets/icon/icon-image.png'),
  photo_set: require('@Assets/icon/icon-photoset.png'),
  electronic: require('@Assets/icon/message/electronic.png'),
  security: require('@Assets/icon/icon-security.png'),
  complain: require('@Assets/icon/icon-complain.png'),
  about: require('@Assets/icon/icon-about.png'),
  teenmode: require('@Assets/icon/icon-teenmode.png'),
  signoff: require('@Assets/icon/icon-signoff.png'),
  drop2: require('@Assets/icon/icon-drop2.png'),
  // report: require('@Assets/image/comment/report.png'),
  input_clear: require('@Assets/icon/icon-input_clear.png'),
  share_close: require('@Assets/icon/icon-share-close.png'),
  download_btn: require('@Assets/icon/icon-download.png'),
  publish: require('@Assets/icon/icon-publish.png'),
  reply_btn: require('@Assets/icon/message/icon-reply.png'),
  like_btn: require('@Assets/icon/message/icon-like-btn.png'),
  choose_like: require('@Assets/icon/message/icon-choose-like.png'),
  onclik_like: require('@Assets/icon/message/icon-on-like.png'),
  same_state: require('@Assets/icon/message/icon-same-dark.png'),
  attention_state: require('@Assets/icon/message/icon-attention-dark.png'),
  like_state: require('@Assets/icon/message/icon-like-btn-dark.png'),
  arrow_icon: require('@Assets/icon/arrow-icon.png'),
  arrow_icon2: require('@Assets/icon/arrow-icon2.png'),
  entry_world: require('@Assets/icon/entry-world.png'),
  new_like: require('@Assets/icon/icon_new_like.png'),
  new_unlike: require('@Assets/icon/icon_new_unlike.png'),
  new_unlike_dark: require('@Assets/icon/icon_new_unlike_dark.png'),

  icon_add: require('@Assets/icon/icon-add.png'),
  icon_add_sharp: require('@Assets/icon/add-sharp.png'),

  fire3: require('@Assets/icon/icon-fire3.png'),

  message_state: require('@Assets/icon/message/icon-message-dark.png'),
  xl_hand: require('@Assets/icon/xl_hand.png'),
  load_point: require('@Assets/icon/icon-load.png'),
  inspiration: require('@Assets/icon/makephoto/icon-inspiration.png'),
  inspiration_edit: require('@Assets/icon/makephoto/icon-edit-inspiration.png'),
  makephoto_drop: require('@Assets/icon/makephoto/icon-drop.png'),
  makephoto_roll: require('@Assets/icon/makephoto/icon-roll.png'),
  makephoto_add: require('@Assets/icon/makephoto/icon-add.png'),
  makephoto_remove: require('@Assets/icon/makephoto/icon-remove.png'),
  makephoto_refresh: require('@Assets/icon/makephoto/icon-refresh.png'),
  makephoto_add_full: require('@Assets/icon/makephoto/icon-add-full.png'),
  makephoto_checked: require('@Assets/icon/makephoto/icon-checked.png'),
  makephoto_edit: require('@Assets/icon/makephoto/icon-tag-edit.png'),
  makephoto_retry: require('@Assets/icon/makephoto/icon-retry.png'),
  makephoto_preview_add: require('@Assets/makephoto/icon-add.png'),
  makephoto_adduser: require('@Assets/icon/makephoto/icon-addrole.png'),
  makephoto_editred: require('@Assets/icon/makephoto/icon-edit-red.png'),
  makephoto_up: require('@Assets/icon/makephoto/icon-up.png'),
  makephoto_remove2: require('@Assets/icon/makephoto/icon-remove-dark.png'),
  makephoto_clear: require('@Assets/makephoto/icon-clear.png'),
  makephoto_download: require('@Assets/icon/makephoto/icon-download.png'),
  makephoto_rolling: require('@Assets/makephoto/icon-thinking.png'),
  makephoto_rolling_stop: require('@Assets/makephoto/icon-stop.png'),
  makephoto_submit: require('@Assets/icon/makephoto/icon-submit.png'),
  makephoto_delete: require('@Assets/icon/makephoto/icon-delete.png'),
  makephoto_return: require('@Assets/icon/makephoto/icon-return.png'),
  makephoto_red_remove: require('@Assets/icon/makephoto/icon-red-remove.png'),
  makephoto_role_indicator1: require('@Assets/icon/makephoto/selected-role-indicator.png'),
  makephoto_role_indicator2: require('@Assets/icon/makephoto/selected-role-indicator2.png'),
  makephoto_double_role: require('@Assets/makephoto/double-icon.png'),
  makephoto_point_loading: require('@Assets/makephoto/icon-loading.png'),
  makePhoto_add_text: require('@Assets/makephoto/add-text.png'),
  makePhoto_edit_exit: require('@Assets/makephoto/edit-exit.png'),
  makePhoto_edit_submit: require('@Assets/makephoto/edit-submit.png'),
  makePhoto_edit_add_text: require('@Assets/makephoto/edit-add-text.png'),
  makePhoto_edit_ban: require('@Assets/makephoto/edit-ban.png'),
  makePhoto_edit_delete_text: require('@Assets/makephoto/edit_delete_text.png'),
  makePhoto_edit_add: require('@Assets/icon/edit-add-icon.png'),
  makePhoto_edit_rotate: require('@Assets/icon/edit-rotate-icon.png'),
  makephoto_list: require('@Assets/makephoto/icon-list.png'),
  makephoto_right: require('@Assets/icon/icon-right-white.png'),
  makephoto_keyboard_white: require('@Assets/icon/icon-keyboard-white.png'),
  makephoto_publish_validate_safe: require('@Assets/icon/makephoto_publish_validate_safe.png'),
  publish_delete: require('@Assets/icon/icon_delete_publish.png'),
  album_close: require('@Assets/icon/icon_album_close.png'),
  link_outline: require('@Assets/icon/link-outline.png'),
  sref_back_outline: require('@Assets/icon/icon-back-outline.png'),
  sref_close_outline: require('@Assets/icon/icon-close-outline.png'),
  sref_card_checked: require('@Assets/icon/icon-mysref-checked.png'),

  world_setting: require('@Assets/icon/world/icon-setting.png'),
  world_voice: require('@Assets/icon/world/icon-voice.png'),
  world_music: require('@Assets/icon/world/icon-music.png'),
  world_confirm: require('@Assets/icon/world/icon-confirm.png'),
  world_section: require('@Assets/icon/world/icon-section.png'),
  // chevronup_outline: require("@Assets/icon/chevronup-outline.png"),
  // chevrondown_outline: require("@Assets/icon/chevrondown-outline.png"),
  // websearch_color: require("@Assets/icon/color/websearch-color.png"),
  // code_color: require("@Assets/icon/color/code-color.png"),
  // warn_fill: require("@Assets/icon/color/warn-fill.png"),
  // file_outline: require("@Assets/icon/file-outline.png"),
  // dot: require("@Assets/icon/dot.png"),
  // link_ref: require("@Assets/icon/color/link-ref.png"),
  // doc_ref: require("@Assets/icon/color/doc-ref.png"),
  // image_ref: require("@Assets/icon/color/image-ref.png"),

  // avatar
  image_pick: require('@Assets/icon/pick-image.png'),
  bbs_pick_image: require('@Assets/icon/bbs-pick-image.png'),
  camera: require('@Assets/icon/camera.png'),
  download: require('@Assets/icon/download.png'),
  pendant: require('@Assets/icon/pendant.png'),
  createChecked: require('@Assets/icon/icon-createCheck.png'),
  checked: require('@Assets/icon/icon-checked.png'),

  // parallel-world
  close_dark_fill: require('@Assets/icon/close-dark-fill.png'),
  close_dark_border: require('@Assets/icon/close-dark-border.png'),
  share_outline_light: require('@Assets/icon/icon-share-light.png'),
  comment_light: require('@Assets/icon/icon-comment-light.png'),
  word_line: require('@Assets/icon/icon-word-line.png'),
  continue_current_word: require('@Assets/icon/icon-continue-current-world.png'),
  icon_edit_glow: require('@Assets/icon/icon-edit-glow.png'),
  icon_ai_glow: require('@Assets/icon/icon-ai-glow.png'),
  icon_ai: require('@Assets/icon/icon-ai.png'),
  ai_dark: require('@Assets/icon/icon-ai-dark.png'),
  icon_edit_pw: require('@Assets/icon/icon-edit-pw.png'),
  icon_edit_pw_dark: require('@Assets/icon/icon-edit-pw-dark.png'),
  icon_save_pw: require('@Assets/icon/icon-save-pw.png'),
  icon_ai_stroked: require('@Assets/icon/icon-ai-stroked.png'),
  icon_edit_pw_grey: require('@Assets/icon/icon-edit-pw-grey.png'),
  reload: require('@Assets/icon/icon-reload.png'),
  reload2: require('@Assets/icon/icon-reload2.png'),
  fold: require('@Assets/icon/icon-fold.png'),
  fold_single: require('@Assets/icon/icon-fold-single.png'),

  publish_pw: require('@Assets/icon/icon-publish-pw.png'),
  li_help: require('@Assets/icon/icon-li-help.png'),
  li_help2: require('@Assets/icon/icon-li-help2.png'),
  cancel: require('@Assets/icon/icon-cancel.png'),
  back_pw: require('@Assets/icon/icon-back-pw.png'),
  pw_black: require('@Assets/icon/icon-pw-black.png'),
  close_pw: require('@Assets/icon/icon-close-pw.png'),
  pw_icon_white: require('@Assets/image/parallel-world/parallel-react-white.png'),
  icon_audio_play: require('@Assets/icon/world/icon-play.png'),
  icon_audio_playing: require('@Assets/icon/world/icon-playing.png'),

  // emoji
  comment_keyboard: require('@Assets/icon/icon-keyboard.png'),
  comment_ai_emoji: require('@Assets/icon/icon-ai-emoji.png'),
  comment_emoji: require('@Assets/icon/icon-emoji.png'),
  comment_emoji_light: require('@Assets/icon/icon-emoji-light.png'),

  emoji_add: require('@Assets/icon/icon-emoji-add.png'),
  emoji_added: require('@Assets/icon/icon-emoji-added.png'),
  emoji_same: require('@Assets/icon/icon-emoji-same.png'),
  emoji_prompt_submit: require('@Assets/emoji/icon-submit.png'),
  switch: require('@Assets/icon/icon-switch.png'),
  pause: require('@Assets/icon/icon-pause.png'),
  reload3: require('@Assets/icon/icon-reload3.png'),
  reload4: require('@Assets/icon/icon-reload4.png'),

  // topic
  topic_default: require('@Assets/icon/topic-default-icon.png'),
  home_search: require('@Assets/icon/home-search.png'),
  goto_icon: require('@Assets/image/topic/topic-entry.png'),
  search: require('@Assets/image/search/search.png'),
  search_top1: require('@Assets/image/search/search-top1.png'),
  search_top2: require('@Assets/image/search/search-top2.png'),
  search_top3: require('@Assets/image/search/search-top3.png'),
  search_hot: require('@Assets/image/search/search-hot.png'),
  search_clear: require('@Assets/image/search/search-clear.png'),
  search_hot_icon: require('@Assets/icon/search/hot.png'),
  search_new_icon: require('@Assets/icon/search/new.png'),
  search_origin: require('@Assets/icon/search/search-origin.png'),
  search_gray: require('@Assets/icon/search/search-gray.png'),
  // sref
  upload_image_icon: require('@Assets/icon/icon-upload-image.png'),
  // fame
  fame_takephoto: require('@Assets/icon/icon-takephoto2.png'),
  fame_world: require('@Assets/icon/icon-world.png'),
  fame_takephoto_outline: require('@Assets/icon/icon-takephoto3.png'),
  ellipsis: require('@Assets/icon/icon-ellipsis.png'),

  cref_back: require('@Assets/icon/cref-search/back.png'),
  cref_search_clear: require('@Assets/icon/cref-search/clear.png'),
  cref_search: require('@Assets/icon/cref-search/search.png'),
  cref_search_plus: require('@Assets/icon/cref-search/plus.png'),
  cref_search_plus2: require('@Assets/icon/cref-search/plus2.png'),
  cref_search_right: require('@Assets/icon/cref-search/right.png'),
  cref_search_confirm: require('@Assets/icon/cref-search/confirm.png'),
  cref_search_confirm2: require('@Assets/icon/cref-search/confirm2.png'),
  cref_sheet_plus: require('@Assets/icon/cref/cref-sheet-plus.png'),
  credit_sheet_checked: require('@Assets/icon/cref/cref-sheet-checked.png'),
  cref_sheet_close: require('@Assets/icon/cref/cref-sheet-close.png'),
  cref_sheet_close_dark: require('@Assets/icon/cref/cref-sheet-close_dark.png'),
  cref_roleset_plus: require('@Assets/icon/cref-search/roleset_plus.png'),

  // goods
  self_goods_pull: require('@Assets/icon/goods/self_pull.png'),
  self_goods_like: require('@Assets/icon/goods/self_like.png'),

  toast_success: require('@Assets/icon/toast/success.png'),
  toast_more: require('@Assets/icon/toast/more.png'),

  // guide
  skip: require('@Assets/icon/icon-skip.png'),

  // topic
  eye: require('@Assets/icon/icon-eye.png'),
  topic_tag: require('@Assets/icon/icon-topic-tag.png'),
  enter: require('@Assets/icon/icon-enter.png'),
  minus: require('@Assets/icon/icon-minus.png'),
  minus_light: require('@Assets/icon/icon-minus-light.png'),
  alert: require('@Assets/icon/icon-alert.png'),
  close3: require('@Assets/icon/icon-close3.png'),
  close5: require('@Assets/icon/icon-close5.png'),
  // album
  icon_album: require('@Assets/icon/icon-album.png'),
  icon_album2: require('@Assets/icon/icon-album2.png'),
  // emoji
  emoji_delete: require('@Assets/icon/emoji_delete.png'),
  emoji_disable_delete: require('@Assets/icon/emoji_disable_delete.png'),
  emoji_pause: require('@Assets/emoji/emoji-pause.png'),
  close4: require('@Assets/icon/icon-close4.png'),
  // bot
  bot_chat: require('@Assets/image/bot/newChat.png'),
  bot_pause: require('@Assets/image/bot/pause.png'),
  upOutlined: require('@Assets/icon/up-outlined.png'),
  bot_tts_play: require('@Assets/image/bot/audio_play.png'),
  bot_tts_pause: require('@Assets/image/bot/audio_pause.png'),
  thinking: require('@Assets/image/bot/thinking.png'),
  // role
  male: require('@Assets/role/icon-male.png'),
  female: require('@Assets/role/icon-female.png'),
  gender_other: require('@Assets/role/icon-gender-other.png'),
  add_role_raccoon: require('@Assets/role/add-role-raccoon.png'),
  role_new: require('@Assets/role/role-new.png'),
  icon_confirm: require('@Assets/icon/icon-confirm.png'),
  icon_rotate: require('@Assets/icon/icon-rotate.png'),
  role_hot: require('@Assets/role/icon_role_hot.png'),
  role_hot_create: require('@Assets/role/icon_role_hot_creat.png'),
  role_man: require('@Assets/role/icon_role_man.png'),
  role_woman: require('@Assets/role/icon_role_woman.png'),
  icon_power: require('@Assets/role/icon_battery_power.png'),
  icon_hot: require('@Assets/role/icon_hot.png'),
  role_credit_arrow: require('@Assets/icon/icon_credit_right_arrow.png'),
  role_gift_add: require('@Assets/icon/icon_gift_add.png'),
  gift_input_clear: require('@Assets/icon/icon_close_input.png'),
  role_to_create: require('@Assets/icon/icon_to_create.png'),
  share_edit: require('@Assets/icon/edit_share.png'),
  share_remove: require('@Assets/icon/icon-remove.png'),

  // livephoto
  async_card_del: require('@Assets/icon/livephoto/delete-icon.png'),
  live_play: require('@Assets/icon/livephoto/video-play.png'),
  windmill: require('@Assets/icon/livephoto/windmill.png'),
  live_take_same: require('@Assets/icon/livephoto/live-photo-take-same.png'),
  live_take_photo: require('@Assets/icon/livephoto/live-photo-take-photo.png'),

  // video-magic
  video_magic_delete: require('@Assets/icon/video-magic/delete.png'),
  video_magic_info: require('@Assets/icon/video-magic/info.png'),
  video_magic_arrow_right: require('@Assets/icon/video-magic/arrow_right.png'),
  video_magic_status_progress: require('@Assets/icon/video-magic/progress.png'),
  video_magic_status_error: require('@Assets/icon/video-magic/error.png'),
  video_magic_history_info: require('@Assets/icon/video-magic/history_info.png'),
  video_magic_thumb_play: require('@Assets/icon/video-magic/thumb_play.png'),
  video_magic_local_image: require('@Assets/icon/video-magic/local_image.png'),
  video_magic_local_mine: require('@Assets/icon/video-magic/local_mine.png'),
  video_magic_remove_image_content: require('@Assets/icon/video-magic/remove_img_content.png'),
  video_magic_role_generate: require('@Assets/icon/video-magic/role_generate.png'),
  video_magic_role_select: require('@Assets/icon/video-magic/role_select.png'),
  video_magic_role_trash: require('@Assets/icon/video-magic/role_trash.png'),
  video_tts_script_send: require('@Assets/icon/video-magic/video_tts_script_send.png'),
  video_tts_script_delete: require('@Assets/icon/video-magic/video_tts_script_delete.png'),
  video_magic_role_voice: require('@Assets/icon/video-magic/role_voice.png'),
  video_magic_role_plus: require('@Assets/icon/video-magic/role_plus.png'),
  video_magic_role_icon: require('@Assets/icon/video-magic/play.png'),
  video_magic_tts_edit: require('@Assets/icon/video-magic/tts_edit.png'),
  video_magic_tts_trash: require('@Assets/icon/video-magic/tts_trash.png'),
  video_magic_video_history: require('@Assets/icon/video-magic/video_history.png'),
  video_magic_pinch: require('@Assets/icon/video-magic/pinch.png'),
  video_magic_draggie: require('@Assets/icon/video-magic/draggie.png'),
  video_magic_left_top_corner: require('@Assets/icon/video-magic/left_top_corner.png'),
  video_magic_right_top_corner: require('@Assets/icon/video-magic/right_top_corner.png'),
  video_magic_left_bottom_corner: require('@Assets/icon/video-magic/left_bottom_corner.png'),
  video_magic_right_bottom_corner: require('@Assets/icon/video-magic/right_bottom_corner.png'),
  video_magic_edit_icon: require('@Assets/icon/video-magic/edit_icon.png'),
  video_magic_seg_plus: require('@Assets/icon/video-magic/seg_plus.png'),
  video_magic_validate_tts: require('@Assets/icon/video-magic/validate_tts.png'),
  video_magic_bang_play: require('@Assets/icon/video-magic/bang_play.png'),
  video_magic_bang_pause: require('@Assets/icon/video-magic/bang_pause.png'),

  publish_cover: require('@Assets/icon/publish/cover.png'),

  // 梦境
  at_icon: require('@Assets/icon/at-icon.png'),
  dream_example_checked: require('@Assets/playground/dream/check.png'),
  dream_retry: require('@Assets/playground/dream/retry.png'),
  add_role: require('@Assets/role/add-role.png'),
  no_add_role: require('@Assets/role/no-add-role.png'),
  role_can_see: require('@Assets/role/role-can-see.png'),
  role_create: require('@Assets/role/role-create.png'),

  // decision
  rank: require('@Assets/icon/icon-rank.png'),
  fire: require('@Assets/icon/icon-fire.png'),
  fire2: require('@Assets/icon/icon-fire2.png'),

  // PK
  pk_retry: require('@Assets/icon/icon-pk-retry.png'),
  pk_enter: require('@Assets/icon/icon-pk-enter.png'),
  icon_pk: require('@Assets/icon/icon-pk.png'),
  vote: require('@Assets/icon/icon-vote.png'),
  topic: require('@Assets/icon/icon-topic.png'),
  bbs_publish: require('@Assets/icon/bbs_publish.png'),
  pk_tab: require('@Assets/icon/pk.png'),
  pk_lleft: require('@Assets/pk/icon-lleft.png'),
  pk_left: require('@Assets/pk/icon-left.png'),
  pk_rright: require('@Assets/pk/icon-rright.png'),
  pk_right: require('@Assets/pk/icon-right.png'),

  //
  // 视频编辑器
  video_selected: require('@Assets/playground/videoMagic/video-selected.png'),
  profile_female: require('@Assets/icon/profile/gender-female.png'),
  profile_male: require('@Assets/icon/profile/gender-male.png'),
  profile_other: require('@Assets/icon/profile/gender-other.png'),
  profile_selected: require('@Assets/icon/profile/gender-selected.png'),
  // house dance
  unselected_role: require('@Assets/houseDance/unselected-role.png'),
  special_effects: require('@Assets/houseDance/special-effects.png'),
  dance_checked: require('@Assets/houseDance/icon-check.png'),

  feed_like: require('@Assets/icon/feed/like.png'),
  feed_unlike: require('@Assets/icon/feed/unlike.png'),
  feed_hot_fire: require('@Assets/icon/hot.png'),

  // bbs
  tick: require('@Assets/icon/bbs/tick.png'),
  bbs_like: require('@Assets/icon/bbs/like.png'),
  bbs_unlike: require('@Assets/icon/bbs/unlike.png'),
  bbs_comment: require('@Assets/icon/bbs/comment.png'),
  bbs_transfer: require('@Assets/icon/bbs/bbs_transfer.png'),
  topic_banner: require('@Assets/icon/topic-banner.png'),
  expand: require('@Assets/icon/bbs/expand.png'),
  collapse: require('@Assets/icon/bbs/collapse.png'),
  tool_arrow: require('@Assets/icon/bbs/tool-arrow.png'),
  bullet_list: require('@Assets/icon/bbs/bullet-list.png'),
  order_list: require('@Assets/icon/bbs/order-list.png'),
  rich_text: require('@Assets/icon/bbs/icon-rich-text.png'),

  // 新春
  wechat: require('@Assets/playground/boomWen/wechat.png'),
  boom_music: require('@Assets/playground/boomWen/boom-music.png'),
  boom_camera: require('@Assets/playground/boomWen/camera.png'),
  boom_img_picker: require('@Assets/playground/boomWen/img-picker.png'),
  go_dance_prefix: require('@Assets/playground/boomWen/go-dance-prefix.png'),
  enter_arrow: require('@Assets/playground/boomWen/enter-arrow.png'),
  msg_vector: require('@Assets/playground/boomWen/msg-vector.png'),
  message_btn: require('@Assets/playground/boomWen/message-btn.png'),
  takephoto_dancetogether: require('@Assets/icon/takephoto-dancetogether.png'),

  // avatar-pendant
  locked: require('@Assets/icon/locked.png'),
  dressup_lock: require('@Assets/icon/dressup-center/lock.png'),

  up_new: require('@Assets/icon/up_new.png'),

  // 活动中心
  welfare_done: require('@Assets/icon/welfare/done.png'),

  // 图集
  duntu_icon: require('@Assets/icon/gallery/duntu.png'),
  warning: require('@Assets/icon/icon-warning.png'),
  message: require('@Assets/icon/icon-message.png'),
  message_comment: require('@Assets/icon/message-comment.png'),

  // mall 商城
  mall_battery_flashing: require('@Assets/icon/mall/battery-flashing.png'),
  mall_question: require('@Assets/icon/mall/question.png'),
  mall_arrow_right: require('@Assets/icon/mall/arrow-right.png'),
  mall_copy: require('@Assets/icon/mall/copy.png'),
  wechat_pay: require('@Assets/icon/wechat-pay.png'),
  ali_pay: require('@Assets/icon/ali-pay.png'),

  // 塔罗
  switch_prev: require('@Assets/icon/icon-switch-prev.png'),
  switch_next: require('@Assets/icon/icon-switch-next.png'),

  // 基本图标
  world_common: require('@Assets/icon/icon-world-common.png'),
  video_common: require('@Assets/icon/icon-video-common.png'),

  qq_dark: require('@Assets/icon/qq_dark.png'),
  wx_light: require('@Assets/icon/wx_light.png')
};

const $imageStyle: ImageStyle = {
  resizeMode: 'contain'
};
