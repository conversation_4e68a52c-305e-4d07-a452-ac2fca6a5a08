import { useEffect, useState } from 'react';
import { Platform, View } from 'react-native';
import { useKeyboard } from '@/src/hooks';
import { StyleSheet, isIos } from '@/src/utils';
import { Text } from '../text';

export const AiTag = () => {
  const nativeShowKeyBoard = useKeyboard();
  const [showKeyboard, setShowKeyboard] = useState(false);
  useEffect(() => {
    if (Platform.OS === 'android') {
      setShowKeyboard(nativeShowKeyBoard);
    }
  }, [nativeShowKeyBoard]);
  return (
    <View
      style={[
        styles.container,
        {
          bottom: isIos ? 15 : 0
        }
      ]}
    >
      <Text style={[styles.text, { opacity: showKeyboard ? 0 : 1 }]}>
        内容由AI生成
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    zIndex: 5000,
    position: 'absolute',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    left: 0,
    right: 0,
    bottom: 15
  },
  text: {
    fontSize: 10,
    letterSpacing: -0.3,
    fontFamily: 'PingFang SC',
    color: 'rgba(255, 255, 255, 0.2)',
    textAlign: 'left'
  }
});
