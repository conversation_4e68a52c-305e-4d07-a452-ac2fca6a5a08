import { router } from 'expo-router';
import { useState } from 'react';
import React from 'react';
import { ViewStyle } from 'react-native';
import { usePersistFn } from '@/src/hooks';
import { useCheckCredit } from '@/src/hooks/useCheckCredit';
import {
  getDefaultPhotoTextGroups,
  useMakePhotoEdit
} from '@/src/store/makePhotoEdit';
import {
  PageState,
  PlayType,
  useMakePhotoStoreV2
} from '@/src/store/makePhotoV2';
import { GameType, InvokeType } from '@/src/types';
import { StyleSheet } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { reportClick } from '@/src/utils/report';
import { uuid } from '@/src/utils/uuid';
import { Screen } from '@Components/screen/index';
import { showConfirm } from '../../popup/confirmModalGlobal/Confirm';
import { PannelBg } from '../pannel/PannelBg';
import { Credits } from '../previewView/components/Credits';
import { RightBtn } from '../rightBtn';
import { shapeArray } from '../utils';
import { EmojiTmpl } from '@/proto-registry/src/web/raccoon/emoji/emoji_pb';
import { showToast } from '@step.ai/number-auth';
import { useShallow } from 'zustand/react/shallow';
import { EmojiDiy } from './EmojiDiy';
import { EmojiEffect } from './EmojiEffect';
import { EmojiPreview } from './EmojiPrview';

const $container: ViewStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  width: '100%',
  height: '100%',
  alignItems: 'center',
  zIndex: 1
};

const $rightWrap: ViewStyle = {
  width: 95,
  position: 'absolute',
  right: 0,
  height: 35
};

interface PanelProps {
  title: string;
}
const BG = require('@Assets/makephoto/bg-dark.png');
export function EmojiPannel(props: PanelProps) {
  const [loading, showLoading] = useState(false);
  const { check } = useCheckCredit();
  const { pageState, role1 } = useMakePhotoStoreV2(
    useShallow(state => ({
      role1: state.role1,
      pageState: state.pageState
    }))
  );
  function handleError(err) {
    errorReport('[genemoji error boundary]', ReportError.COMPONENTS, err);
    const reason = err.reason;
    if (err?.code === 2) {
      showToast(reason);
    } else {
      showToast('炖表情包失败，请重试~');
    }
    useMakePhotoStoreV2.getState().changePageState(PageState.diy);
  }
  const clearState = usePersistFn(
    (prompt: string = '', template?: EmojiTmpl) => {
      useMakePhotoEdit.setState({
        photoTextGroups: getDefaultPhotoTextGroups(template?.n ?? 1)
      });
      useMakePhotoStoreV2.getState().setState({
        emojiPrompt: prompt,
        emojiTemplateId: template?.id ?? '',
        emojiMakeType: {
          type: template ? 'template' : 'prompt',
          template
        },
        emojiPhotos: [],
        emojiInputVisible: false,
        emojiRoles: prompt ? [role1] : Array(template?.n).fill(role1),
        emojiOriginRole: role1
      });
    }
  );
  const onMakeEmoji = (
    prompt: string = '',
    template?: EmojiTmpl,
    isRetry: boolean = false
  ) => {
    clearState(prompt, template);
    const id = uuid();
    useMakePhotoStoreV2.setState({
      emojiGenId: id
    });
    useMakePhotoStoreV2.getState().changePageState(PageState.effect);
    useMakePhotoStoreV2
      .getState()
      .genEmoji(isRetry, id)
      .then(res => {
        if (
          shapeArray(res).length > 0 &&
          useMakePhotoStoreV2.getState().pageState === PageState.effect
        ) {
          setTimeout(() => {
            useMakePhotoStoreV2.getState().changePageState(PageState.preview);
            useMakePhotoStoreV2.getState().setState({
              emojiUploadLoading: true
            });
          }, 500);
        } else if (id === useMakePhotoStoreV2.getState().emojiGenId) {
          useMakePhotoStoreV2.getState().changePageState(PageState.diy);
        }
      })
      .catch(err => {
        handleError(err);
      });
  };

  const handleBack = () => {
    if (pageState === PageState.effect || pageState === PageState.preview) {
      reportClick('loading_return_button', { module: 'create' });
      useMakePhotoStoreV2.getState().setState({
        pageState: PageState.diy,
        emojiEditing: false,
        emojiPageType: '',
        emojiRegenIndex: 0,
        emojiGenId: '',
        emojiPhotos: [],
        emojiUploadPhotoSet: {}
      });
      const { emojiOriginRole } = useMakePhotoStoreV2.getState() || {};
      if (emojiOriginRole) {
        useMakePhotoStoreV2.getState().setState({
          role1: emojiOriginRole
        });
      }
    } else {
      showConfirm({
        title: '确定退出炖图吗？',
        confirmText: '确认退出',
        onConfirm: ({ close }) => {
          close();
          router.back();
        }
      });
    }
  };
  const onRetry = async () => {
    const type = useMakePhotoStoreV2.getState().emojiMakeType;
    reportClick('preview_recreate_button', {
      module: 'create',
      create_type: 1 // 没有单张重炖
    });
    const res = await check(
      type.template
        ? InvokeType.INVOKE_EMOJI_TMPL_REDO
        : InvokeType.INVOKE_EMOJI_SINGEL_REDO,
      GameType.EMOJI
    );
    if (!res) {
      return;
    }
    onMakeEmoji(
      type.type === 'template'
        ? ''
        : useMakePhotoStoreV2.getState().emojiPrompt,
      type.template,
      true
    );
  };
  const handleToDrawing = () => {
    reportClick('creation_changepage_button', {
      module: 'create',
      type: '1'
    });
    useMakePhotoStoreV2.getState().setState({
      playType: PlayType.drawing,
      pageState: PageState.diy
    });
  };

  return (
    <Screen
      theme="dark"
      title="炖表情"
      onBack={handleBack}
      withWaterMark={pageState === PageState.preview}
      rightStyle={$rightWrap}
      backgroundView={
        <PannelBg customBg={BG} loading={loading} showLoading={showLoading} />
      }
      leftStyle={{
        zIndex: 100
      }}
      titleContainerStyle={styles.headerTitleContainer}
      titleStyle={styles.headerTitle}
      style={[$container]}
      headerLeft={() => <Credits />}
      safeAreaEdges={['top']}
      headerRight={() => {
        return (
          <>
            {pageState === PageState.diy && (
              <RightBtn
                type="emoji"
                style={$rightWrap}
                onPress={handleToDrawing}
              />
            )}
          </>
        );
      }}
    >
      {/* 输入prompt 选择模版 */}
      {pageState === PageState.diy && <EmojiDiy onMakeEmoji={onMakeEmoji} />}
      {/* 炖表情包 */}
      {pageState === PageState.effect && <EmojiEffect />}
      {/* 后编辑 */}
      {pageState === PageState.preview && <EmojiPreview onRetry={onRetry} />}
    </Screen>
  );
}
const styles = StyleSheet.create({
  headerTitleContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center'
  },
  headerTitle: { paddingBottom: 2 }
});
