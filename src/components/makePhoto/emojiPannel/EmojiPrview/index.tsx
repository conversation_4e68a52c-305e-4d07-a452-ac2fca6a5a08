import { router } from 'expo-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import React from 'react';
import { TouchableOpacity, View, ViewStyle } from 'react-native';
import Animated, {
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import Carousel, { ICarouselInstance } from 'react-native-reanimated-carousel';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { PersistPhotos } from '@/src/api/makephotov2';
import TextPanel from '@/src/bizComponents/PhotoEditor/components/TextPanel';
import { HEADER_HEIGHT } from '@/src/components/screen';
import { usePersistFn, useScreenSize } from '@/src/hooks';
import { useSafeAreaInsetsStyle } from '@/src/hooks/useSafeAreaInsetsStyle';
import { EditPageState, useMakePhotoEdit } from '@/src/store/makePhotoEdit';
import {
  EmojiPhoto,
  PageState,
  useMakePhotoStoreV2
} from '@/src/store/makePhotoV2';
import { AlbumType, usePublishStore } from '@/src/store/publish';
import { useStorageStore } from '@/src/store/storage';
import { centerStyle, imageFullStyle } from '@/src/theme';
import { GameType } from '@/src/types';
import { dp2px } from '@/src/utils';
import { reportClick } from '@/src/utils/report';
import { Image, ImageStyle } from '@Components/image';
import { StyleSheet } from '@Utils/StyleSheet';
import { hideLoading, showLoading } from '../../../loading';
import { PrimaryButton } from '../../../primaryButton';
import { showToast } from '../../../toast';
import { PhotoPreview } from '../../previewView/components/PhotoPreview';
import { SaveToAlbum } from '../../previewView/components/SaveToAlbumButton';
import { EMOJI_ARROW } from '../EmojiEffect/loading/consts';
import { EmojiRoleCapsule } from '../EmojiRoleCapsule';
import { EmojiCardV2 } from './components/EmojiCardV2';
import { GridStatic } from './components/EmojiGrid';
import { RegenBtn } from './components/RegenBtn';
import { EmojiGridShot, EmojiShot } from './components/type';
import { useShallow } from 'zustand/react/shallow';

const SAVE_BTN = require('@Assets/makephoto/save-album.png');

const $saveWrap: ViewStyle = {
  flex: 0,
  width: dp2px(36),
  height: dp2px(54),
  marginRight: dp2px(18),
  position: 'relative'
};

const $btnSave: ViewStyle = {
  ...StyleSheet.rowStyle,
  width: dp2px(36),
  height: dp2px(54)
};

const $saveImg: ImageStyle = {
  ...imageFullStyle,
  resizeMode: 'contain'
};

const $tipImg: ImageStyle = {
  position: 'absolute',
  top: -dp2px(41),
  left: -16,
  height: dp2px(41),
  width: dp2px(134),
  resizeMode: 'contain'
};

const $swiperWrap: ViewStyle = {
  width: '100%',
  height: dp2px(356),
  marginTop: dp2px(106)
};
const $arrowImg: ImageStyle = {
  width: dp2px(16),
  height: dp2px(260),
  position: 'absolute',
  resizeMode: 'cover',
  right: 0,
  top: 32,
  zIndex: -1
};

const SAVE_TIP = require('@Assets/makephoto/save-tip.png');
interface SaveProps {
  showSave?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
}

export const SaveBtn = (props: SaveProps) => {
  const [showTip, setShowTip] = useState(false);
  const handlePress = () => {
    if (showTip) {
      const cache = useStorageStore.getState().makephotoCache || {};
      const count = Number(cache?.saveTipCount) || 0;
      useStorageStore.getState().__setStorage({
        makephotoCache: {
          ...cache,
          saveTipCount: count + 1
        }
      });
    }

    setShowTip(false);
    if (props.onPress) {
      props.onPress();
    }
  };

  useEffect(() => {
    const cache = useStorageStore.getState().makephotoCache || {};
    if (Number(cache?.saveTipCount) >= 3) return;
    setShowTip(true);
  }, []);

  const renderTip = () => {
    if (!showTip || !props.onPress) return null;
    return <Image style={$tipImg} source={SAVE_TIP} />;
  };

  return (
    <View
      style={[
        $saveWrap,
        props?.style,
        {
          opacity: props.showSave ? 1 : 0.3
        }
      ]}
    >
      {renderTip()}
      <TouchableOpacity style={$btnSave} onPress={handlePress}>
        <Image source={SAVE_BTN} style={$saveImg} />
      </TouchableOpacity>
    </View>
  );
};

interface PreviewViewProps {
  onBack?: () => void;
  onRetry: () => void;
  showAlbum?: () => void;
  resetLoading?: () => void;
}

export function EmojiPreview(props: PreviewViewProps) {
  const {
    photos = [],
    regenIndex = 0,
    emojiPrompt,
    emojiEditing,
    emojiMakeType
  } = useMakePhotoStoreV2(
    useShallow(state => ({
      photos: state.emojiPhotos,
      emojiPrompt: state.emojiPrompt,
      regenIndex: state.emojiRegenIndex,
      emojiEditing: state.emojiEditing,
      emojiMakeType: state.emojiMakeType
    }))
  );
  const { selectedTextId } = useMakePhotoEdit(
    useShallow(state => ({
      selectedTextId: state.selectedEditId,
      setSelectedTextId: state.setSelectedTextId
    }))
  );
  const [currentIndex, setCurrentIndex] = useState(0);
  const indexRef = useRef(0);
  const { width } = useScreenSize('window');
  const emojiRef = useRef<(EmojiShot | null)[]>([]);
  const gridRef = useRef<EmojiGridShot>(null);
  const swiperRef = useRef<ICarouselInstance | null>(null);
  const insets = useSafeAreaInsets();
  useEffect(() => {
    setCurrentIndex(regenIndex || 0);
  }, [regenIndex]);
  const handleUpload: () => Promise<EmojiPhoto[]> = async () => {
    const newPhotos = photos.filter(
      r => r !== null && r.isSafe
    ) as EmojiPhoto[];
    useMakePhotoStoreV2.getState().setState({
      publishPhotos: newPhotos.map(r => {
        return {
          ...r,
          url: r.wholeImageUrl
        };
      })
    });
    return newPhotos;
  };

  const saveImage = async () => {
    if (isTemplate(currentIndex)) {
      return;
    }
    reportClick('preview_save_button', { module: 'create' });
    const curIdx = photoCount > 1 ? currentIndex - 1 : currentIndex;
    const savedPhoto = photos[curIdx];
    console.log('savedPhoto', savedPhoto);
    if (!savedPhoto?.isSafe) {
      showToast('呜呜小狸炖糊了，无法保存哦');
      return;
    }
    if (!savedPhoto?.photoId) {
      showToast('保存失败，请重试');
      return;
    }
    showLoading();
    PersistPhotos({ photoIds: [savedPhoto?.photoId] })
      .then(res => {
        const { ok } = res || {};
        showToast('保存成功');
        hideLoading();
      })
      .catch(e => {
        console.log(e);
        showToast('保存失败');
        hideLoading();
      });

    reportClick('savetolishi', { module: 'create' });
  };

  const setPhotos = async (first?: Partial<EmojiPhoto> | null) => {
    // 不自动保存我的图集
    const { historyPhotos, albumPhotos } = usePublishStore.getState();
    const publishPhotos = useMakePhotoStoreV2.getState().publishPhotos;
    let finalPhotos = [...publishPhotos];
    if (first) {
      finalPhotos = [first, ...publishPhotos];
    }
    let index = 0;
    function predict<T>(i: T | false): i is T {
      return Boolean(i);
    }

    const newPhotos = finalPhotos
      .map(item => {
        if (!item?.photoId && !item?.imageId) return false;
        if (!item?.isSafe) return false;
        index += 1;
        return {
          ...item,
          gameType: GameType.EMOJI,
          albumType: albumPhotos.map(i => i.photoId).includes(item.photoId)
            ? AlbumType.album
            : AlbumType.history,
          num: index
        };
      })
      .filter(predict);
    usePublishStore.getState().setPhotos(newPhotos);
  };

  const handleNext = async () => {
    reportClick('preview_next_button', { module: 'create' });
    try {
      showLoading();
      const publishPhotos = await handleUpload().catch(err => []);

      if (publishPhotos?.length > 0) {
        const gridPhoto =
          photoCount > 1 && (await gridRef.current?.handleSaveAndCreate());
        const subPhotoIds = publishPhotos?.map(
          (item: EmojiPhoto) => item.photoId
        );
        const firstPhoto = gridPhoto
          ? {
              ...gridPhoto,
              url: gridPhoto?.image_url || '',
              imageId: gridPhoto?.image_id,
              subPhotoIds: subPhotoIds,
              isSafe: true,
              tmplId: photos[0].tmplId
            }
          : null;
        setPhotos(firstPhoto);
      }
      console.log('=====publishPhotospubl ishPhotos========', publishPhotos);
      router.navigate({
        pathname: '/publish',
        params: {
          photoId: photos[currentIndex]?.photoId || '',
          fromType: 'emoji'
        }
      });
      hideLoading();
    } catch (err) {
      hideLoading();
    }
  };
  function changeIndexFromPreview(index: number) {
    swiperRef?.current?.scrollTo({ index, animated: false });
  }
  const photoCount = useMemo(() => {
    return emojiMakeType.template ? emojiMakeType.template.n + 1 : 1;
  }, [emojiMakeType]);
  const isTemplate = usePersistFn(index => {
    if (index === 0 && photoCount > 1) {
      return true;
    }
    return false;
  });

  const renderItem = (data: { item: number; index: number }) => {
    const { index } = data || {};
    const curIdx = photoCount > 1 ? index - 1 : index; // 数量>1 第一张显示拼图效果
    const item = curIdx >= 0 ? photos[curIdx] : {};
    return (
      <View
        id={'make-photo-' + index}
        style={{
          ...centerStyle,
          flexDirection: 'row',
          width: width,
          height: dp2px(356),
          position: 'relative'
        }}
      >
        {isTemplate(index) ? (
          <View style={styles.template}>
            <Image style={$arrowImg} source={EMOJI_ARROW} />
            <GridStatic ref={gridRef} onClick={changeIndexFromPreview} />
          </View>
        ) : (
          <EmojiCardV2
            ref={ref => {
              emojiRef.current[curIdx] = ref;
            }}
            realIndex={curIdx}
            size={dp2px(280)}
            data={item}
          />
        )}
        {index === 0 && !emojiEditing && photoCount === 1 && (
          <RegenBtn
            index={index}
            photoCount={photoCount}
            onRetry={props.onRetry}
          />
        )}
      </View>
    );
  };
  const renderBottom = () => {
    if (emojiEditing) {
      return (
        <TextPanel
          currentIndex={photoCount > 1 ? currentIndex - 1 : currentIndex}
          selectedTextId={selectedTextId}
          onFocus={() => {
            useMakePhotoEdit.setState({
              photoTextEditing: true
            });
          }}
          onSubmit={() => {
            useMakePhotoEdit.setState({
              photoTextEditing: false,
              editPageState: EditPageState.beforePublish
            });
            setTimeout(() => {
              const idx = photoCount > 1 ? currentIndex - 1 : currentIndex;
              showLoading();
              emojiRef.current?.[idx]
                ?.handleSaveAndCreate()
                .then(res => {
                  if (res) {
                    useMakePhotoStoreV2.setState({
                      emojiPhotos: photos.map((r, i) => {
                        return (i === idx ? res : r) as EmojiPhoto;
                      })
                    });
                  }
                })
                .finally(() => {
                  hideLoading();
                });
            }, 200);
            useMakePhotoEdit.getState().setSelectedTextId('');
            useMakePhotoStoreV2.setState({
              emojiEditing: false
            });
          }}
        />
      );
    }
    return (
      <PreviewBottom
        onSaveImage={saveImage}
        onNext={handleNext}
        style={{
          marginBottom: insets.bottom
        }}
        showSave={!isTemplate(currentIndex)}
      />
    );
  };

  return (
    <Animated.View
      entering={FadeIn.duration(100)}
      style={[StyleSheet.absoluteFill]}
    >
      <EmojiRoleCapsule state={PageState.preview} />
      <Carousel
        ref={swiperRef}
        style={$swiperWrap}
        data={Array(photoCount).fill(1)}
        renderItem={renderItem}
        defaultIndex={regenIndex}
        onSnapToItem={setCurrentIndex}
        width={width}
        height={dp2px(356)}
        loop={false}
        enabled={!emojiEditing}
        onProgressChange={(
          _offsetProgress: number,
          absoluteProgress: number
        ) => {
          const idx = swiperRef.current?.getCurrentIndex() || 0;

          if (absoluteProgress > 0.5 || idx === 0) {
            setCurrentIndex(idx);
          }
        }}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 1,
          parallaxScrollingOffset: 10,
          parallaxAdjacentItemScale: 0.95
        }}
      />
      {photoCount > 1 && (
        <View
          style={[
            StyleSheet.rowStyle,
            {
              marginTop: 10,
              justifyContent: 'center',
              width: '100%',
              pointerEvents: emojiEditing ? 'none' : 'auto'
            }
          ]}
        >
          <PhotoPreview
            photos={[
              {
                url: 'https://resource.lipuhome.com/resource/img/test/20250213/300f21e6dde86eb4030d47e7eb5d0bdf.png'
              },
              ...photos.map(p => {
                return {
                  url:
                    p.isSafe && p.rawUrl
                      ? p.rawUrl
                      : 'https://resource.lipuhome.com/resource/img/test/20250220/49bcde16821d39c9cb5ababbf06de948.png'
                };
              })
            ]}
            selectedIndex={currentIndex}
            setIndex={changeIndexFromPreview}
            photoCount={photoCount}
          />
        </View>
      )}
      {currentIndex === 0 && !emojiEditing && photoCount > 1 && (
        <RegenBtn index={0} photoCount={photoCount} onRetry={props.onRetry} />
      )}
      {renderBottom()}
      <View
        style={[
          styles.save,
          {
            opacity: isTemplate(currentIndex) ? 0.3 : 1
          }
        ]}
      >
        <SaveToAlbum onSaveImage={saveImage} />
      </View>
    </Animated.View>
  );
}

function PreviewBottom({
  onNext,
  style: $customStyle
}: {
  showSave: boolean;
  onSaveImage: () => void;
  onNext: () => void;
  style?: ViewStyle;
}) {
  const [disable, setDisable] = useState(true);
  const opacity = useSharedValue(0.5);
  const $containerInsets = useSafeAreaInsetsStyle(['top', 'bottom']);
  const { emojiUploadLoading } = useMakePhotoStoreV2(
    useShallow(state => ({
      emojiUploadLoading: state.emojiUploadLoading
    }))
  );

  const $animateStyle = useAnimatedStyle(() => ({
    opacity: opacity.value
  }));

  useEffect(() => {
    if (emojiUploadLoading) {
      setDisable(true);
    } else {
      setTimeout(() => {
        setDisable(false);
      }, 500);
    }
  }, [emojiUploadLoading]);

  useEffect(() => {
    if (!disable) {
      opacity.value = withTiming(1, { duration: 500 });
    } else {
      opacity.value = withTiming(0.5, { duration: 500 });
    }
  }, [disable]);

  const { width } = useScreenSize('window');

  const handleNext = () => {
    if (disable) return;
    onNext();
  };

  return (
    <Animated.View
      style={[
        StyleSheet.rowStyle,
        {
          justifyContent: 'center',
          position: 'absolute',
          width: width - 32,
          left: 16,
          right: 16,
          bottom: 20
        },
        $animateStyle,
        $customStyle
      ]}
    >
      <PrimaryButton
        width={335}
        height={44}
        useDp2px
        usingGradient={false}
        onPress={handleNext}
      >
        下一步
      </PrimaryButton>
    </Animated.View>
  );
}
const styles = StyleSheet.create({
  save: {
    position: 'absolute',
    right: 16,
    top: -HEADER_HEIGHT,
    height: HEADER_HEIGHT,
    alignItems: 'center',
    justifyContent: 'center'
  },
  template: {
    width: 353
  }
});
