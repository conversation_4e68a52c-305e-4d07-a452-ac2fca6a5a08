import { Image } from 'expo-image';
import { ReactNode, memo, useEffect, useMemo, useState } from 'react';
import { type TextStyle, View, type ViewStyle } from 'react-native';
import { makePhotoClient } from '@/src/api/makephoto';
import { Text } from '@/src/components';
import { usePersistFn } from '@/src/hooks';
import { useMakePhotoEdit } from '@/src/store/makePhotoEdit';
import { PageState, useMakePhotoStoreV2 } from '@/src/store/makePhotoV2';
import { useStorageStore } from '@/src/store/storage';
import { rowStyle, typography } from '@/src/theme';
import { GameType, InvokeType } from '@/src/types';
import { dp2px } from '@/src/utils';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { PrimaryButton } from '../../primaryButton';
import { showToast } from '../../toast';
import CreditWrapper from '../../v2/credit-wrapper';
import { PromptCatalog } from '../promptCatalog';
import { useShallow } from 'zustand/react/shallow';

const ICONS = {
  SELECTED: require('@Assets/icon/makephoto/icon-selected.png'),
  REQUIRED: require('@Assets/icon/makephoto/icon-required.png'),
  SELECTED_REQUIRED: require('@Assets/icon/makephoto/icon-selected-required.png')
};

const TabItems: {
  label: string;
  content: ReactNode;
  selectedIcon: ReactNode;
  required?: ReactNode;
}[] = [
  {
    label: '炖图元素',
    content: <PromptCatalog />,
    selectedIcon: (
      <Image
        source={ICONS.SELECTED}
        style={{
          height: dp2px(30),
          width: dp2px(84),
          position: 'absolute',
          resizeMode: 'cover',
          top: 8,
          left: 1
        }}
      />
    )
  }
];

export const $tabTitleStyle: ViewStyle = {
  alignItems: 'center'
};
export const $tabTitleTextStyle: TextStyle = {
  color: 'white',
  fontSize: dp2px(13),
  fontFamily: typography.fonts.pingfangSC.normal,
  fontWeight: '500'
};

export const $selectedTextStyle: TextStyle = {
  fontWeight: '500',
  opacity: 1
};

export const BottomTab = memo(() => {
  const {
    style,
    role1,
    role2,
    hasDouble,
    currentRole,
    pageState,
    photoLoading,
    takePhoto,
    changePageState,
    sref,
    additionPrompts,
    additionPrompts2,
    presetPrompts,
    presetPrompts2,
    photoSize,
    referenceCardId,
    referenceProtoId
  } = useMakePhotoStoreV2(
    useShallow(state => ({
      style: state.style,
      photoLoading: state.photoLoading,
      role1: state.role1,
      role2: state.role2,
      hasDouble: Boolean(state.useDouble && state.role1 && state.role2),
      currentRole: state.currentRole,
      pageState: state.pageState,
      promptLoading: state.promptLoading,
      changePageState: state.changePageState,
      takePhoto: state.takePhoto,
      sref: state.sref,
      additionPrompts: state.additionPrompts,
      additionPrompts2: state.additionPrompts2,
      presetPrompts: state.presetPrompts,
      presetPrompts2: state.presetPrompts2,
      photoSize: state.photoSize,
      referenceCardId: state.referenceCardId,
      referenceProtoId: state.referenceProtoId
    }))
  );

  const [invokeType, setInvokeType] = useState<InvokeType>(
    InvokeType.INVOKE_DRAWING_GEN
  );

  useEffect(() => {
    if (pageState === PageState.diy) {
      setInvokeType(InvokeType.INVOKE_DRAWING_GEN);
    }
  }, [pageState]);

  const btnIsClick = useMemo(() => {
    return role1 && style;
  }, [role1, style]);

  const gotoGenerate = usePersistFn(async () => {
    if (!style) {
      showToast('选择风格才能生成哦！');
      return;
    }

    if (!role1) {
      showToast('需要选角色才能炖图哦！');
      return;
    }
    const st = Date.now();

    // before reseting memory all drawing tips
    useStorageStore.getState().__setStorage({
      drawingDraft: {
        style: style,
        role1: role1,
        role2: role2,
        hasDouble: hasDouble,
        currentRole: currentRole,
        pageState: pageState,
        sref: sref,
        additionPrompts: additionPrompts,
        additionPrompts2: additionPrompts2,
        presetPrompts: presetPrompts,
        presetPrompts2: presetPrompts2,
        photoSize: photoSize,
        referenceCardId: referenceCardId,
        referenceProtoId: referenceProtoId
      }
    });

    useMakePhotoEdit.getState().reset();

    const prompt = useMakePhotoStoreV2.getState().getAllPrompt();
    if (prompt) {
      const checkPrompts = await makePhotoClient
        .checkPhotoPrompt({
          prompt
        })
        .catch(e => {
          errorReport('checkPhotoPrompt', ReportError.MAKE_PHOTO, e);
        });

      if (checkPrompts?.ban) {
        showToast(checkPrompts.toast);
        return;
      }
    }
    takePhoto({ st });
  });

  useEffect(() => {
    console.log('photoLoading----', photoLoading);
    if (photoLoading) {
      changePageState(PageState.effect);
    }
  }, [photoLoading]);

  return (
    <View>
      <View
        style={[
          rowStyle,
          $tabTitleStyle,
          {
            paddingTop: 20,
            paddingBottom: 16,
            alignItems: 'center',
            justifyContent: 'center'
          }
        ]}
      >
        <View
          style={{
            width: dp2px(74),
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'row'
          }}
          key={TabItems[0].label}
        >
          <Text style={$tabTitleTextStyle} key={TabItems[0].label}>
            {TabItems[0].label}
          </Text>
        </View>
      </View>
      <View>{TabItems[0].content}</View>
      <View
        style={{
          ...rowStyle,
          justifyContent: 'center',
          marginTop: dp2px(16),
          opacity: btnIsClick ? 1 : 0.3
        }}
      >
        <CreditWrapper
          invokeType={invokeType!}
          gameType={GameType.DRAWING}
          signal={role1?.ip}
          $cornerstyle={{
            top: dp2px(-8),
            minWidth: 18
          }}
          buttonContainer={
            <PrimaryButton onPress={gotoGenerate}>生成</PrimaryButton>
          }
        />
      </View>
    </View>
  );
});

BottomTab.displayName = 'BottomTab';
