import { router } from 'expo-router';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef
} from 'react';
import { TouchableOpacity, View } from 'react-native';
import { ErrorRes } from '@/src/api/websocket/stream_connect';
import ToastInner from '@/src/bizComponents/credit/toast';
import { usePersistFn } from '@/src/hooks';
import { showNoBatteryTip } from '@/src/hooks/useCheckCredit';
import { useSafeAreaInsetsStyle } from '@/src/hooks/useSafeAreaInsetsStyle';
import {
  GoToSimplePreviewType,
  PageState,
  useMakePhotoStoreV2
} from '@/src/store/makePhotoV2';
import { clickEffect } from '@/src/utils/clickeffect';
import { ReportError, errorReport } from '@/src/utils/error-log';
import { Icon } from '@Components/icons';
import { showToast } from '@Components/toast';
import { Video, VideoHandle } from '@Components/video';
import { StyleSheet } from '@Utils/StyleSheet';
import { useParams } from '../../../hooks/useParams';
import { PointsCode } from '@/proto-registry/src/web/raccoon/errorcode/errorcode_pb';
import { useShallow } from 'zustand/react/shallow';

const V1_SOURCE = require('@Assets/mp4/new01.mp4');
const V2_SOURCE = require('@Assets/mp4/new02.mp4');
const V3_SOURCE = require('@Assets/mp4/new03.mp4');
const V5_SOURCE = require('@Assets/mp4/new05.mp4');

const VD1_SOURCE = require('@Assets/mp4/vd1.mp4');
const VD2_SOURCE = require('@Assets/mp4/vd2.mp4');
const VD3_SOURCE = require('@Assets/mp4/vd3.mp4');
const VD5_SOURCE = require('@Assets/mp4/vd5.mp4');

const $wrap = StyleSheet.createRectStyle({
  // marginTop: PAGE_TOP,
  top: 0,
  left: 0
});

interface LoadingViewProps {
  onBack: () => void;
}

export interface LoadingViewRef {
  reset: () => void;
}
export const LoadingView = forwardRef(
  (props: LoadingViewProps, ref: React.ForwardedRef<LoadingViewRef>) => {
    const {
      game_type,
      game_source,
      invoke_type,
      brand_ip
    }: {
      game_type?: string;
      game_source?: string;
      invoke_type?: string;
      brand_ip?: string;
    } = useParams();
    const videoRef = useRef<VideoHandle>(null);
    const canPreview = useRef(0);
    const $containerInsets = useSafeAreaInsetsStyle(['top', 'bottom']);
    const finishedRef = useRef(false);
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const resetRef = useRef(false);

    const { pageState, photoLoading, retryState, role2 } = useMakePhotoStoreV2(
      useShallow(state => ({
        pageState: state.pageState,
        photoLoading: state.photoLoading,
        retryState: state.retryState,
        role2: state.role2
      }))
    );

    useImperativeHandle(ref, () => ({
      reset
    }));

    const reset = useCallback(async () => {
      console.log('reset----');
      resetRef.current = true;
      await videoRef.current?.reset();
      useMakePhotoStoreV2.setState({
        photoLoading: false
      });
    }, []);

    const show = useCallback(() => {
      resetRef.current = false;
      videoRef.current?.show();
    }, []);

    const clearTimer = useCallback(() => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    }, []);
    useEffect(() => {
      if (!photoLoading) return;
      const { pendingTakePhoto, changePageState } =
        useMakePhotoStoreV2.getState();
      pendingTakePhoto()
        .then(res => {
          if (res) {
            canPreview.current = 1;
          }
          if (resetRef.current) return;
        })
        .catch((e: ErrorRes) => {
          // 边界积分处理
          if (e instanceof ErrorRes) {
            errorReport(
              '[makephoto error boundary]',
              ReportError.COMPONENTS,
              e
            );
            const reason = e.reason;
            if (
              reason?.includes(PointsCode.POINTS_ERR_INSUFFICIENT_POINTS + '')
            ) {
              showNoBatteryTip();
            } else {
              showToast('炖图失败，请重试~');
            }
          } else {
            showToast('炖图失败，请重试~');
          }
          changePageState(PageState.diy);
          videoRef.current?.hide();
          videoRef.current?.reset();
          console.log('pendingTakePhoto error', e);
          // 回到上一步
        });
    }, [photoLoading]);

    useEffect(() => {
      if (PageState.effect === pageState) {
        finishedRef.current = false;
        show();
        clickEffect();
      } else {
        clearTimer();
      }
    }, [pageState]);

    useEffect(() => {
      if (PageState.effect !== pageState) {
        finishedRef.current = true;
        clearTimer();
      }
      return () => {
        clearTimer();
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pageState]);

    useEffect(() => {
      if (role2) {
        videoRef.current?.changeSources([
          { source: VD1_SOURCE, autoNext: true },
          { source: VD2_SOURCE, loop: true },
          { source: VD3_SOURCE },
          { source: VD5_SOURCE }
        ]);
      } else {
        videoRef.current?.changeSources([
          { source: V1_SOURCE, autoNext: true },
          { source: V2_SOURCE, loop: true },
          { source: V3_SOURCE },
          { source: V5_SOURCE }
        ]);
      }
    }, [role2]);

    useEffect(() => {
      if (retryState) {
        show();
        canPreview.current = 0;
        finishedRef.current = false;
        useMakePhotoStoreV2.getState().setRetryState(0);
      }
    }, [retryState]);
    const videoSources = useMemo(() => {
      if (role2) {
        return [
          { source: VD1_SOURCE, autoNext: true },
          { source: VD2_SOURCE, loop: true },
          { source: VD3_SOURCE },
          { source: VD5_SOURCE }
        ];
      } else {
        return [
          { source: V1_SOURCE, autoNext: true },
          { source: V2_SOURCE, loop: true },
          { source: V3_SOURCE },
          { source: V5_SOURCE }
        ];
      }
    }, [role2]);
    const go2Preview = usePersistFn(() => {
      if (game_type && GoToSimplePreviewType.includes(game_type)) {
        router.replace({
          pathname: '/preview',
          params: {
            game_type,
            game_source: game_source ?? '',
            invoke_type: invoke_type ?? '',
            ip: brand_ip ?? ''
          }
        });
      } else {
        useMakePhotoStoreV2.getState().changePageState(PageState.preview);
      }
    });

    const onFinish = usePersistFn(async (index: number) => {
      if (index === 1 && canPreview.current) {
        videoRef.current?.next();
      }
      if (index === 2) {
        await videoRef.current?.next();
        go2Preview();
      }
    });
    const onAllFinished = usePersistFn(() => {
      console.log('onAllFinished----');
      reset();
      finishedRef.current = true;
    });

    return (
      <View
        style={[
          {
            position: 'absolute',
            zIndex: 10
          },
          $wrap
        ]}
        pointerEvents={pageState === PageState.effect ? 'auto' : 'none'}
      >
        {(pageState === PageState.effect ||
          pageState === PageState.preview) && (
          <Video
            videos={videoSources}
            onFinish={onFinish}
            onAllFinished={onAllFinished}
            ref={videoRef}
          />
        )}
        {/* 这里使用 photoLoading 做状态同步，没切换到后编辑时，返回入口保持 */}
        {!!photoLoading &&
          (pageState === PageState.effect ||
            pageState === PageState.preview) && (
            <TouchableOpacity
              style={{
                position: 'absolute',
                top: Number($containerInsets.paddingTop) + 10,
                left: 16
              }}
              onPress={async () => {
                await reset();
                useMakePhotoStoreV2.getState().changePageState(PageState.diy);
              }}
            >
              <Icon
                icon="back"
                color={StyleSheet.currentColors.white}
                size={24}
              />
            </TouchableOpacity>
          )}
      </View>
    );
  }
);
